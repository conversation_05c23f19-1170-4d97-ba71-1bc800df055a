﻿using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ ØªÙ‚Ø±ÙŠØ± Ø§Ù„Ø±ÙˆØ§ØªØ¨ Ø§Ù„Ù…ÙØµÙ„
    /// Detailed Payroll Report Form
    /// </summary>
    public partial class PayrollReportForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;

        // UI Controls
        private ComboBox monthComboBox;
        private ComboBox yearComboBox;
        private ComboBox employeeComboBox;
        private Button generateReportButton;
        private DataGridView payrollDataGridView;
        private Label totalBaseSalaryLabel;
        private Label totalAllowancesLabel;
        private Label totalDeductionsLabel;
        private Label totalNetSalaryLabel;
        private Label employeeCountLabel;

        public PayrollReportForm(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            InitializeComponent();
            LoadInitialData();
        }

        private void InitializeComponent()
        {
            this.Text = "ØªÙ‚Ø±ÙŠØ± Ø§Ù„Ø±ÙˆØ§ØªØ¨ Ø§Ù„Ù…ÙØµÙ„";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
        }

        private void CreateControls()
        {
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 4,
                ColumnCount = 1,
                Padding = new Padding(20)
            };

            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 100));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 50));

            // Filter Panel
            var filterPanel = CreateFilterPanel();
            mainPanel.Controls.Add(filterPanel, 0, 0);

            // Report DataGridView
            payrollDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F)
            };

            SetupPayrollColumns();
            mainPanel.Controls.Add(payrollDataGridView, 0, 1);

            // Summary Panel
            var summaryPanel = CreateSummaryPanel();
            mainPanel.Controls.Add(summaryPanel, 0, 2);

            // Buttons Panel
            var buttonsPanel = CreateButtonsPanel();
            mainPanel.Controls.Add(buttonsPanel, 0, 3);

            this.Controls.Add(mainPanel);
        }

        private Panel CreateFilterPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            // Month selection
            var monthLabel = new Label
            {
                Text = "Ø§Ù„Ø´Ù‡Ø±:",
                Location = new Point(800, 15),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            monthComboBox = new ComboBox
            {
                Location = new Point(650, 15),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Year selection
            var yearLabel = new Label
            {
                Text = "Ø§Ù„Ø³Ù†Ø©:",
                Location = new Point(580, 15),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            yearComboBox = new ComboBox
            {
                Location = new Point(480, 15),
                Size = new Size(80, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Employee selection
            var employeeLabel = new Label
            {
                Text = "Ø§Ù„Ù…ÙˆØ¸Ù:",
                Location = new Point(400, 15),
                Size = new Size(60, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            employeeComboBox = new ComboBox
            {
                Location = new Point(200, 15),
                Size = new Size(180, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            generateReportButton = new Button
            {
                Text = "Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±",
                Location = new Point(80, 15),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            generateReportButton.Click += GenerateReport_Click;

            // Second row for additional info
            employeeCountLabel = new Label
            {
                Text = "Ø¹Ø¯Ø¯ Ø§Ù„Ù…ÙˆØ¸ÙÙŠÙ†: 0",
                Location = new Point(650, 50),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            panel.Controls.AddRange(new Control[] 
            { 
                monthLabel, monthComboBox, yearLabel, yearComboBox, 
                employeeLabel, employeeComboBox, generateReportButton, employeeCountLabel 
            });

            return panel;
        }

        private void SetupPayrollColumns()
        {
            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "EmployeeName",
                HeaderText = "Ø§Ø³Ù… Ø§Ù„Ù…ÙˆØ¸Ù",
                DataPropertyName = "EmployeeName",
                Width = 150
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Position",
                HeaderText = "Ø§Ù„Ù…Ù†ØµØ¨",
                DataPropertyName = "Position",
                Width = 120
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "BaseSalary",
                HeaderText = "Ø§Ù„Ø±Ø§ØªØ¨ Ø§Ù„Ø£Ø³Ø§Ø³ÙŠ",
                DataPropertyName = "BaseSalary",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Allowances",
                HeaderText = "Ø§Ù„Ø¨Ø¯Ù„Ø§Øª",
                DataPropertyName = "Allowances",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Overtime",
                HeaderText = "Ø§Ù„Ø¥Ø¶Ø§ÙÙŠ",
                DataPropertyName = "Overtime",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Deductions",
                HeaderText = "Ø§Ù„Ø®ØµÙˆÙ…Ø§Øª",
                DataPropertyName = "Deductions",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "NetSalary",
                HeaderText = "ØµØ§ÙÙŠ Ø§Ù„Ø±Ø§ØªØ¨",
                DataPropertyName = "NetSalary",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle 
                { 
                    Format = "C",
                    BackColor = Color.FromArgb(230, 255, 230)
                }
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PaymentDate",
                HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø¯ÙØ¹",
                DataPropertyName = "PaymentDate",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "Ø§Ù„Ø­Ø§Ù„Ø©",
                DataPropertyName = "Status",
                Width = 80
            });
        }

        private Panel CreateSummaryPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            totalBaseSalaryLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø±ÙˆØ§ØªØ¨ Ø§Ù„Ø£Ø³Ø§Ø³ÙŠØ©: 0.00",
                Location = new Point(650, 10),
                Size = new Size(250, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            totalAllowancesLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø¨Ø¯Ù„Ø§Øª: 0.00",
                Location = new Point(650, 35),
                Size = new Size(250, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Green
            };

            totalDeductionsLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø®ØµÙˆÙ…Ø§Øª: 0.00",
                Location = new Point(650, 60),
                Size = new Size(250, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Red
            };

            totalNetSalaryLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ ØµØ§ÙÙŠ Ø§Ù„Ø±ÙˆØ§ØªØ¨: 0.00",
                Location = new Point(300, 35),
                Size = new Size(250, 20),
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.Blue
            };

            panel.Controls.AddRange(new Control[] 
            { 
                totalBaseSalaryLabel, totalAllowancesLabel, totalDeductionsLabel, totalNetSalaryLabel 
            });

            return panel;
        }

        private Panel CreateButtonsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill
            };

            var exportButton = new Button
            {
                Text = "ØªØµØ¯ÙŠØ± Ø¥Ù„Ù‰ Excel",
                Location = new Point(20, 10),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            exportButton.Click += ExportToExcel_Click;

            var printButton = new Button
            {
                Text = "Ø·Ø¨Ø§Ø¹Ø©",
                Location = new Point(160, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            printButton.Click += Print_Click;

            var payslipButton = new Button
            {
                Text = "ÙƒØ´Ù Ø±Ø§ØªØ¨ ÙØ±Ø¯ÙŠ",
                Location = new Point(260, 10),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(241, 196, 15),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            payslipButton.Click += GeneratePayslip_Click;

            panel.Controls.AddRange(new Control[] { exportButton, printButton, payslipButton });
            return panel;
        }

        private async void LoadInitialData()
        {
            try
            {
                // Load months
                var months = new[]
                {
                    new { Value = 0, Text = "Ø¬Ù…ÙŠØ¹ Ø§Ù„Ø´Ù‡ÙˆØ±" },
                    new { Value = 1, Text = "ÙŠÙ†Ø§ÙŠØ±" },
                    new { Value = 2, Text = "ÙØ¨Ø±Ø§ÙŠØ±" },
                    new { Value = 3, Text = "Ù…Ø§Ø±Ø³" },
                    new { Value = 4, Text = "Ø£Ø¨Ø±ÙŠÙ„" },
                    new { Value = 5, Text = "Ù…Ø§ÙŠÙˆ" },
                    new { Value = 6, Text = "ÙŠÙˆÙ†ÙŠÙˆ" },
                    new { Value = 7, Text = "ÙŠÙˆÙ„ÙŠÙˆ" },
                    new { Value = 8, Text = "Ø£ØºØ³Ø·Ø³" },
                    new { Value = 9, Text = "Ø³Ø¨ØªÙ…Ø¨Ø±" },
                    new { Value = 10, Text = "Ø£ÙƒØªÙˆØ¨Ø±" },
                    new { Value = 11, Text = "Ù†ÙˆÙÙ…Ø¨Ø±" },
                    new { Value = 12, Text = "Ø¯ÙŠØ³Ù…Ø¨Ø±" }
                };

                monthComboBox.DataSource = months;
                monthComboBox.DisplayMember = "Text";
                monthComboBox.ValueMember = "Value";
                monthComboBox.SelectedValue = DateTime.Now.Month;

                // Load years
                var currentYear = DateTime.Now.Year;
                var years = new List<object> { new { Value = 0, Text = "Ø¬Ù…ÙŠØ¹ Ø§Ù„Ø³Ù†ÙˆØ§Øª" } };
                for (int year = currentYear - 5; year <= currentYear + 1; year++)
                {
                    years.Add(new { Value = year, Text = year.ToString() });
                }

                yearComboBox.DataSource = years;
                yearComboBox.DisplayMember = "Text";
                yearComboBox.ValueMember = "Value";
                yearComboBox.SelectedValue = currentYear;

                // Load employees
                var employees = await _unitOfWork.Employees.GetAllAsync();
                var employeeList = new List<object> { new { Value = 0, Text = "Ø¬Ù…ÙŠØ¹ Ø§Ù„Ù…ÙˆØ¸ÙÙŠÙ†" } };
                employeeList.AddRange(employees.Select(e => new { Value = e.Id, Text = e.FullName }));

                employeeComboBox.DataSource = employeeList;
                employeeComboBox.DisplayMember = "Text";
                employeeComboBox.ValueMember = "Value";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void GenerateReport_Click(object? sender, EventArgs e)
        {
            try
            {
                var selectedMonth = (int)monthComboBox.SelectedValue;
                var selectedYear = (int)yearComboBox.SelectedValue;
                var selectedEmployeeId = (int)employeeComboBox.SelectedValue;

                // Build filter criteria
                var payrolls = await _unitOfWork.Payrolls.GetAllAsync();
                
                if (selectedMonth > 0)
                    payrolls = payrolls.Where(p => p.PaymentDate.HasValue && p.PaymentDate.Value.Month == selectedMonth);

                if (selectedYear > 0)
                    payrolls = payrolls.Where(p => p.PaymentDate.HasValue && p.PaymentDate.Value.Year == selectedYear);
                
                if (selectedEmployeeId > 0)
                    payrolls = payrolls.Where(p => p.EmployeeId == selectedEmployeeId);

                // Get employee data
                var employees = await _unitOfWork.Employees.GetAllAsync();
                var employeesDict = employees.ToDictionary(e => e.Id, e => e);

                // Create report data
                var reportData = payrolls.Select(p => new PayrollReportItem
                {
                    EmployeeName = employeesDict.TryGetValue(p.EmployeeId, out var emp) ? emp.FullName : "ØºÙŠØ± Ù…Ø¹Ø±ÙˆÙ",
                    Position = emp?.Position ?? "ØºÙŠØ± Ù…Ø­Ø¯Ø¯",
                    BaseSalary = p.BaseSalary,
                    Allowances = p.Allowances,
                    Overtime = 0, // p.OvertimePay - field doesn't exist in current model
                    Deductions = p.Deductions,
                    NetSalary = p.NetSalary,
                    PaymentDate = p.PaymentDate,
                    Status = "Ù…Ø¯ÙÙˆØ¹" // p.Status - field doesn't exist in current model
                }).OrderBy(r => r.EmployeeName).ToList();

                // Display results
                payrollDataGridView.DataSource = reportData;
                UpdateSummaryLabels(reportData);
                employeeCountLabel.Text = $"Ø¹Ø¯Ø¯ Ø§Ù„Ù…ÙˆØ¸ÙÙŠÙ†: {reportData.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateSummaryLabels(List<PayrollReportItem> reportData)
        {
            var totalBaseSalary = reportData.Sum(r => r.BaseSalary);
            var totalAllowances = reportData.Sum(r => r.Allowances + r.Overtime);
            var totalDeductions = reportData.Sum(r => r.Deductions);
            var totalNetSalary = reportData.Sum(r => r.NetSalary);

            totalBaseSalaryLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø±ÙˆØ§ØªØ¨ Ø§Ù„Ø£Ø³Ø§Ø³ÙŠØ©: {totalBaseSalary:C}";
            totalAllowancesLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø¨Ø¯Ù„Ø§Øª ÙˆØ§Ù„Ø¥Ø¶Ø§ÙÙŠ: {totalAllowances:C}";
            totalDeductionsLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø®ØµÙˆÙ…Ø§Øª: {totalDeductions:C}";
            totalNetSalaryLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ ØµØ§ÙÙŠ Ø§Ù„Ø±ÙˆØ§ØªØ¨: {totalNetSalary:C}";
        }

        private void ExportToExcel_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("ØªØµØ¯ÙŠØ± Ø¥Ù„Ù‰ Excel - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Print_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("Ø§Ù„Ø·Ø¨Ø§Ø¹Ø© - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void GeneratePayslip_Click(object? sender, EventArgs e)
        {
            if (payrollDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ù…ÙˆØ¸Ù Ù…Ù† Ø§Ù„Ù‚Ø§Ø¦Ù…Ø©", "ØªÙ†Ø¨ÙŠÙ‡",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("ÙƒØ´Ù Ø§Ù„Ø±Ø§ØªØ¨ Ø§Ù„ÙØ±Ø¯ÙŠ - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    public class PayrollReportItem
    {
        public string EmployeeName { get; set; }
        public string Position { get; set; }
        public decimal BaseSalary { get; set; }
        public decimal Allowances { get; set; }
        public decimal Overtime { get; set; }
        public decimal Deductions { get; set; }
        public decimal NetSalary { get; set; }
        public DateTime? PaymentDate { get; set; }
        public string Status { get; set; }
    }
}



