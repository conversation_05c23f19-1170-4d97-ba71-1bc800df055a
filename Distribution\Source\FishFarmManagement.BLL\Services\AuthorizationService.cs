using FishFarmManagement.BLL.Services.Interfaces;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Threading.Tasks;

namespace FishFarmManagement.BLL.Services
{
    public class AuthorizationService : IAuthorizationService
    {
        private readonly IAuthenticationService _authenticationService;
        private readonly ILogger<AuthorizationService> _logger;

        public AuthorizationService(IAuthenticationService authenticationService, ILogger<AuthorizationService> logger)
        {
            _authenticationService = authenticationService;
            _logger = logger;
        }

        public Task<bool> HasPermissionAsync(string permissionName)
        {
            var user = _authenticationService.GetCurrentUser();
            if (user?.Role?.Permissions == null)
            {
                return Task.FromResult(false);
            }

            // The logic that caused CS1503 on line 105 is corrected here.
            // We simply check if the permission exists in the user's role permissions.
            var hasPermission = user.Role.Permissions.Any(p => p.Name == permissionName);

            return Task.FromResult(hasPermission);
        }

        public Task<bool> IsSystemAdminAsync()
        {
            var user = _authenticationService.GetCurrentUser();
            // Assuming "Admin" is the role name for system administrators
            return Task.FromResult(user?.Role?.RoleName == "Admin");
        }
    }
}