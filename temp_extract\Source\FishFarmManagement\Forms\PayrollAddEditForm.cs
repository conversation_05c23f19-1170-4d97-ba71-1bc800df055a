﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¶Ø§ÙØ©/ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„Ø±Ø§ØªØ¨
    /// Payroll add/edit form
    /// </summary>
    public partial class PayrollAddEditForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;
        private readonly Employee _employee;
        private readonly Payroll? _existingPayroll;
        private readonly bool _isEditMode;

        // UI Controls
        private DateTimePicker payrollDatePicker;
        private NumericUpDown baseSalaryNumericUpDown;
        private NumericUpDown allowancesNumericUpDown;
        private NumericUpDown deductionsNumericUpDown;
        private Label netSalaryLabel;
        private TextBox notesTextBox;
        private Button saveButton;
        private Button cancelButton;

        public PayrollAddEditForm(IUnitOfWork unitOfWork, ILogger logger, Employee employee, Payroll? existingPayroll = null)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _employee = employee ?? throw new ArgumentNullException(nameof(employee));
            _existingPayroll = existingPayroll;
            _isEditMode = existingPayroll != null;

            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = _isEditMode ? "ØªØ¹Ø¯ÙŠÙ„ Ø±Ø§ØªØ¨" : "Ø¥Ø¶Ø§ÙØ© Ø±Ø§ØªØ¨ Ø¬Ø¯ÙŠØ¯";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
            LayoutControls();
        }

        private void CreateControls()
        {
            // Employee info
            var employeeInfoLabel = new Label
            {
                Text = $"Ø§Ù„Ù…ÙˆØ¸Ù: {_employee.FullName} - {_employee.Position}",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                Size = new Size(400, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Payroll Date
            var payrollDateLabel = new Label
            {
                Text = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø±Ø§ØªØ¨:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            payrollDatePicker = new DateTimePicker
            {
                Size = new Size(200, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // Base Salary
            var baseSalaryLabel = new Label
            {
                Text = "Ø§Ù„Ø±Ø§ØªØ¨ Ø§Ù„Ø£Ø³Ø§Ø³ÙŠ:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            baseSalaryNumericUpDown = new NumericUpDown
            {
                Size = new Size(200, 23),
                Minimum = 0,
                Maximum = decimal.MaxValue,
                DecimalPlaces = 2,
                ThousandsSeparator = true,
                Value = _employee.BaseSalary
            };
            baseSalaryNumericUpDown.ValueChanged += CalculateNetSalary;

            // Allowances
            var allowancesLabel = new Label
            {
                Text = "Ø§Ù„Ø¨Ø¯Ù„Ø§Øª:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            allowancesNumericUpDown = new NumericUpDown
            {
                Size = new Size(200, 23),
                Minimum = 0,
                Maximum = decimal.MaxValue,
                DecimalPlaces = 2,
                ThousandsSeparator = true
            };
            allowancesNumericUpDown.ValueChanged += CalculateNetSalary;

            // Deductions
            var deductionsLabel = new Label
            {
                Text = "Ø§Ù„Ø®ØµÙˆÙ…Ø§Øª:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            deductionsNumericUpDown = new NumericUpDown
            {
                Size = new Size(200, 23),
                Minimum = 0,
                Maximum = decimal.MaxValue,
                DecimalPlaces = 2,
                ThousandsSeparator = true
            };
            deductionsNumericUpDown.ValueChanged += CalculateNetSalary;

            // Net Salary
            var netSalaryTitleLabel = new Label
            {
                Text = "ØµØ§ÙÙŠ Ø§Ù„Ø±Ø§ØªØ¨:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            netSalaryLabel = new Label
            {
                Size = new Size(200, 23),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.Green,
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Notes
            var notesLabel = new Label
            {
                Text = "Ø§Ù„Ù…Ù„Ø§Ø­Ø¸Ø§Øª:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            notesTextBox = new TextBox
            {
                Size = new Size(350, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                MaxLength = 500
            };

            // Buttons
            saveButton = new Button
            {
                Text = _isEditMode ? "Ø­ÙØ¸ Ø§Ù„ØªØ¹Ø¯ÙŠÙ„Ø§Øª" : "Ø¥Ø¶Ø§ÙØ©",
                Size = new Size(100, 30),
                DialogResult = DialogResult.OK
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "Ø¥Ù„ØºØ§Ø¡",
                Size = new Size(100, 30),
                DialogResult = DialogResult.Cancel
            };

            this.Controls.AddRange(new Control[]
            {
                employeeInfoLabel,
                payrollDateLabel, payrollDatePicker,
                baseSalaryLabel, baseSalaryNumericUpDown,
                allowancesLabel, allowancesNumericUpDown,
                deductionsLabel, deductionsNumericUpDown,
                netSalaryTitleLabel, netSalaryLabel,
                notesLabel, notesTextBox,
                saveButton, cancelButton
            });
        }

        private void LayoutControls()
        {
            int x = 20;
            int y = 20;
            int spacing = 35;

            // Employee info
            this.Controls[0].Location = new Point(x + 50, y);
            y += 40;

            // Payroll Date
            this.Controls[1].Location = new Point(x + 250, y);
            this.Controls[2].Location = new Point(x + 30, y);
            y += spacing;

            // Base Salary
            this.Controls[3].Location = new Point(x + 250, y);
            this.Controls[4].Location = new Point(x + 30, y);
            y += spacing;

            // Allowances
            this.Controls[5].Location = new Point(x + 250, y);
            this.Controls[6].Location = new Point(x + 30, y);
            y += spacing;

            // Deductions
            this.Controls[7].Location = new Point(x + 250, y);
            this.Controls[8].Location = new Point(x + 30, y);
            y += spacing;

            // Net Salary
            this.Controls[9].Location = new Point(x + 250, y);
            this.Controls[10].Location = new Point(x + 30, y);
            y += spacing + 10;

            // Notes
            this.Controls[11].Location = new Point(x + 250, y);
            this.Controls[12].Location = new Point(x + 30, y);
            y += 80;

            // Buttons
            this.Controls[13].Location = new Point(x + 130, y);
            this.Controls[14].Location = new Point(x + 250, y);
        }

        private void LoadData()
        {
            if (_existingPayroll != null)
            {
                payrollDatePicker.Value = new DateTime(_existingPayroll.Year, _existingPayroll.Month, 1);
                baseSalaryNumericUpDown.Value = _existingPayroll.BaseSalary;
                allowancesNumericUpDown.Value = _existingPayroll.Allowances;
                deductionsNumericUpDown.Value = _existingPayroll.Deductions;
                notesTextBox.Text = _existingPayroll.Notes;
            }
            
            CalculateNetSalary(null, EventArgs.Empty);
        }

        private void CalculateNetSalary(object sender, EventArgs e)
        {
            var netSalary = baseSalaryNumericUpDown.Value + allowancesNumericUpDown.Value - deductionsNumericUpDown.Value;
            netSalaryLabel.Text = netSalary.ToString("C");
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                this.Cursor = Cursors.WaitCursor;
                saveButton.Enabled = false;

                var payroll = _existingPayroll ?? new Payroll();
                
                payroll.EmployeeId = _employee.Id;
                payroll.Month = payrollDatePicker.Value.Month;
                payroll.Year = payrollDatePicker.Value.Year;
                payroll.BaseSalary = baseSalaryNumericUpDown.Value;
                payroll.Allowances = allowancesNumericUpDown.Value;
                payroll.Deductions = deductionsNumericUpDown.Value;
                payroll.NetSalary = baseSalaryNumericUpDown.Value + allowancesNumericUpDown.Value - deductionsNumericUpDown.Value;
                payroll.Notes = notesTextBox.Text.Trim();

                if (_isEditMode)
                {
                    payroll.UpdateModificationDate();
                    await _unitOfWork.Payrolls.UpdateAsync(payroll);
                    MessageBox.Show("ØªÙ… ØªØ­Ø¯ÙŠØ« Ø§Ù„Ø±Ø§ØªØ¨ Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _unitOfWork.Payrolls.AddAsync(payroll);
                    MessageBox.Show("ØªÙ… Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ø±Ø§ØªØ¨ Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                await _unitOfWork.SaveChangesAsync();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­ÙØ¸ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø±Ø§ØªØ¨");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø­ÙØ¸ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                saveButton.Enabled = true;
            }
        }

        private bool ValidateInput()
        {
            if (baseSalaryNumericUpDown.Value <= 0)
            {
                MessageBox.Show("Ø§Ù„Ø±Ø§ØªØ¨ Ø§Ù„Ø£Ø³Ø§Ø³ÙŠ Ù…Ø·Ù„ÙˆØ¨", "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                baseSalaryNumericUpDown.Focus();
                return false;
            }

            return true;
        }
    }
}



