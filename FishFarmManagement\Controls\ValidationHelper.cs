using System;
using System.Drawing;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace FishFarmManagement.Controls
{
    /// <summary>
    /// مساعد التحقق من صحة البيانات
    /// Validation helper
    /// </summary>
    public static class ValidationHelper
    {
        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// Validate email address
        /// </summary>
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة رقم الهاتف السعودي
        /// Validate Saudi phone number
        /// </summary>
        public static bool IsValidSaudiPhone(string phone)
        {
            if (string.IsNullOrWhiteSpace(phone))
                return false;

            // Remove spaces and special characters
            phone = Regex.Replace(phone, @"[\s\-\(\)]", "");

            // Saudi phone patterns
            var patterns = new[]
            {
                @"^05\d{8}$",           // 05xxxxxxxx
                @"^\+9665\d{8}$",       // +9665xxxxxxxx
                @"^9665\d{8}$",         // 9665xxxxxxxx
                @"^5\d{8}$"             // 5xxxxxxxx
            };

            foreach (var pattern in patterns)
            {
                if (Regex.IsMatch(phone, pattern))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// التحقق من صحة رقم الهوية السعودي
        /// Validate Saudi national ID
        /// </summary>
        public static bool IsValidSaudiNationalId(string nationalId)
        {
            if (string.IsNullOrWhiteSpace(nationalId) || nationalId.Length != 10)
                return false;

            if (!Regex.IsMatch(nationalId, @"^\d{10}$"))
                return false;

            // Luhn algorithm for Saudi national ID
            int sum = 0;
            for (int i = 0; i < 9; i++)
            {
                int digit = int.Parse(nationalId[i].ToString());
                if (i % 2 == 0)
                {
                    digit *= 2;
                    if (digit > 9)
                        digit = digit / 10 + digit % 10;
                }
                sum += digit;
            }

            int checkDigit = (10 - (sum % 10)) % 10;
            return checkDigit == int.Parse(nationalId[9].ToString());
        }

        /// <summary>
        /// التحقق من صحة رقم الإقامة
        /// Validate residence number
        /// </summary>
        public static bool IsValidResidenceNumber(string residenceNumber)
        {
            if (string.IsNullOrWhiteSpace(residenceNumber))
                return false;

            return Regex.IsMatch(residenceNumber, @"^\d{10}$");
        }

        /// <summary>
        /// التحقق من قوة كلمة المرور
        /// Validate password strength
        /// </summary>
        public static PasswordStrength GetPasswordStrength(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                return PasswordStrength.VeryWeak;

            int score = 0;

            // Length
            if (password.Length >= 8) score++;
            if (password.Length >= 12) score++;

            // Character types
            if (Regex.IsMatch(password, @"[a-z]")) score++;
            if (Regex.IsMatch(password, @"[A-Z]")) score++;
            if (Regex.IsMatch(password, @"\d")) score++;
            if (Regex.IsMatch(password, @"[!@#$%^&*(),.?""':;{}|<>]")) score++;

            return score switch
            {
                0 or 1 => PasswordStrength.VeryWeak,
                2 => PasswordStrength.Weak,
                3 or 4 => PasswordStrength.Medium,
                5 => PasswordStrength.Strong,
                _ => PasswordStrength.VeryStrong
            };
        }

        /// <summary>
        /// إضافة تلميح للتحكم
        /// Add tooltip to control
        /// </summary>
        public static void AddTooltip(Control control, string message, ToolTip? tooltip = null)
        {
            if (tooltip == null)
            {
                tooltip = new ToolTip
                {
                    IsBalloon = true,
                    ToolTipIcon = ToolTipIcon.Info,
                    ToolTipTitle = "معلومة"
                };
            }

            tooltip.SetToolTip(control, message);
        }

        /// <summary>
        /// إضافة تحقق مباشر للتحكم
        /// Add real-time validation to control
        /// </summary>
        public static void AddRealTimeValidation(TextBox textBox, Func<string, bool> validator, string errorMessage)
        {
            var errorProvider = new ErrorProvider();
            
            textBox.Validating += (sender, e) =>
            {
                if (!validator(textBox.Text))
                {
                    errorProvider.SetError(textBox, errorMessage);
                    textBox.BackColor = Color.FromArgb(255, 235, 235);
                }
                else
                {
                    errorProvider.SetError(textBox, "");
                    textBox.BackColor = Color.White;
                }
            };

            textBox.TextChanged += (sender, e) =>
            {
                if (validator(textBox.Text))
                {
                    errorProvider.SetError(textBox, "");
                    textBox.BackColor = Color.White;
                }
            };
        }

        /// <summary>
        /// تنسيق حقل النص للأرقام فقط
        /// Format text field for numbers only
        /// </summary>
        public static void MakeNumericOnly(TextBox textBox, bool allowDecimal = false)
        {
            textBox.KeyPress += (sender, e) =>
            {
                if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar))
                {
                    if (allowDecimal && e.KeyChar == '.' && !textBox.Text.Contains("."))
                    {
                        return; // Allow decimal point
                    }
                    e.Handled = true;
                }
            };
        }

        /// <summary>
        /// تنسيق حقل النص للنص العربي فقط
        /// Format text field for Arabic text only
        /// </summary>
        public static void MakeArabicOnly(TextBox textBox)
        {
            textBox.KeyPress += (sender, e) =>
            {
                if (!char.IsControl(e.KeyChar) && !IsArabicCharacter(e.KeyChar) && e.KeyChar != ' ')
                {
                    e.Handled = true;
                }
            };
        }

        /// <summary>
        /// تنسيق حقل النص للنص الإنجليزي فقط
        /// Format text field for English text only
        /// </summary>
        public static void MakeEnglishOnly(TextBox textBox)
        {
            textBox.KeyPress += (sender, e) =>
            {
                if (!char.IsControl(e.KeyChar) && !char.IsLetter(e.KeyChar) && e.KeyChar != ' ')
                {
                    e.Handled = true;
                }
            };
        }

        /// <summary>
        /// التحقق من أن الحرف عربي
        /// Check if character is Arabic
        /// </summary>
        private static bool IsArabicCharacter(char c)
        {
            return (c >= 0x0600 && c <= 0x06FF) || (c >= 0x0750 && c <= 0x077F);
        }

        /// <summary>
        /// إضافة تأثير التركيز للتحكم
        /// Add focus effect to control
        /// </summary>
        public static void AddFocusEffect(Control control, Color focusColor = default)
        {
            if (focusColor == default)
                focusColor = Color.FromArgb(173, 216, 230);

            var originalColor = control.BackColor;

            control.Enter += (s, e) => control.BackColor = focusColor;
            control.Leave += (s, e) => control.BackColor = originalColor;
        }

        /// <summary>
        /// إضافة تأثير الحوم للزر
        /// Add hover effect to button
        /// </summary>
        public static void AddHoverEffect(Button button, Color hoverColor = default)
        {
            if (hoverColor == default)
                hoverColor = Color.FromArgb(button.BackColor.R - 20, button.BackColor.G - 20, button.BackColor.B - 20);

            var originalColor = button.BackColor;

            button.MouseEnter += (s, e) => button.BackColor = hoverColor;
            button.MouseLeave += (s, e) => button.BackColor = originalColor;
        }
    }

    /// <summary>
    /// قوة كلمة المرور
    /// Password strength levels
    /// </summary>
    public enum PasswordStrength
    {
        VeryWeak,
        Weak,
        Medium,
        Strong,
        VeryStrong
    }

    /// <summary>
    /// مكون عرض الرسائل التوضيحية
    /// Tooltip display component
    /// </summary>
    public class EnhancedTooltip : ToolTip
    {
        public EnhancedTooltip()
        {
            this.IsBalloon = true;
            this.ToolTipIcon = ToolTipIcon.Info;
            this.ToolTipTitle = "معلومة";
            this.AutoPopDelay = 5000;
            this.InitialDelay = 500;
            this.ReshowDelay = 100;
            this.ShowAlways = true;
        }

        public void SetInfoTooltip(Control control, string message)
        {
            this.ToolTipIcon = ToolTipIcon.Info;
            this.ToolTipTitle = "معلومة";
            this.SetToolTip(control, message);
        }

        public void SetWarningTooltip(Control control, string message)
        {
            this.ToolTipIcon = ToolTipIcon.Warning;
            this.ToolTipTitle = "تحذير";
            this.SetToolTip(control, message);
        }

        public void SetErrorTooltip(Control control, string message)
        {
            this.ToolTipIcon = ToolTipIcon.Error;
            this.ToolTipTitle = "خطأ";
            this.SetToolTip(control, message);
        }
    }

    /// <summary>
    /// مكون عرض الإشعارات
    /// Notification display component
    /// </summary>
    public class NotificationPanel : Panel
    {
        private System.Windows.Forms.Timer fadeTimer;
        private int opacity = 255;

        public NotificationPanel()
        {
            this.Size = new Size(300, 60);
            this.BackColor = Color.FromArgb(46, 204, 113);
            this.BorderStyle = BorderStyle.FixedSingle;
            this.Visible = false;

            fadeTimer = new System.Windows.Forms.Timer { Interval = 50 };
            fadeTimer.Tick += FadeTimer_Tick;
        }

        public void ShowSuccess(string message, int duration = 3000)
        {
            ShowNotification(message, Color.FromArgb(46, 204, 113), duration);
        }

        public void ShowError(string message, int duration = 5000)
        {
            ShowNotification(message, Color.FromArgb(231, 76, 60), duration);
        }

        public void ShowWarning(string message, int duration = 4000)
        {
            ShowNotification(message, Color.FromArgb(241, 196, 15), duration);
        }

        public void ShowInfo(string message, int duration = 3000)
        {
            ShowNotification(message, Color.FromArgb(52, 152, 219), duration);
        }

        private void ShowNotification(string message, Color backgroundColor, int duration)
        {
            this.BackColor = backgroundColor;
            this.Controls.Clear();

            var label = new Label
            {
                Text = message,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            this.Controls.Add(label);
            this.Visible = true;
            this.BringToFront();

            opacity = 255;

            // Auto hide after duration
            var hideTimer = new System.Windows.Forms.Timer { Interval = duration };
            hideTimer.Tick += (s, e) =>
            {
                hideTimer.Stop();
                fadeTimer.Start();
            };
            hideTimer.Start();
        }

        private void FadeTimer_Tick(object? sender, EventArgs e)
        {
            opacity -= 15;
            if (opacity <= 0)
            {
                fadeTimer.Stop();
                this.Visible = false;
                opacity = 255;
            }
            else
            {
                // Apply opacity effect (simplified)
                this.BackColor = Color.FromArgb(opacity, this.BackColor.R, this.BackColor.G, this.BackColor.B);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                fadeTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
