@echo off
chcp 65001 >nul
title تثبيت نظام إدارة مزرعة الأسماك - Fish Farm Management Installation

echo ========================================
echo    تثبيت نظام إدارة مزرعة الأسماك
echo    Fish Farm Management Installation
echo ========================================
echo.

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo تحذير: يُنصح بتشغيل التثبيت كمدير
    echo Warning: It's recommended to run installation as Administrator
    echo.
    echo هل تريد المتابعة؟ (Y/N)
    echo Do you want to continue? (Y/N)
    set /p choice=
    if /i "%choice%" neq "Y" exit /b 0
    echo.
)

:: إنشاء مجلد البرنامج
set "INSTALL_DIR=%ProgramFiles%\FishFarmManagement"
echo إنشاء مجلد التثبيت: %INSTALL_DIR%
echo Creating installation directory: %INSTALL_DIR%

if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%" 2>nul
    if %ERRORLEVEL% neq 0 (
        echo خطأ: لا يمكن إنشاء مجلد التثبيت
        echo Error: Cannot create installation directory
        echo سيتم التثبيت في المجلد الحالي
        echo Installing in current directory instead
        set "INSTALL_DIR=%CD%"
    )
)

:: نسخ الملفات
echo.
echo نسخ ملفات البرنامج...
echo Copying program files...

xcopy "*.exe" "%INSTALL_DIR%\" /Y /Q >nul 2>&1
xcopy "*.dll" "%INSTALL_DIR%\" /Y /Q >nul 2>&1
xcopy "*.json" "%INSTALL_DIR%\" /Y /Q >nul 2>&1
xcopy "*.txt" "%INSTALL_DIR%\" /Y /Q >nul 2>&1

:: إنشاء المجلدات الضرورية
echo إنشاء المجلدات الضرورية...
echo Creating necessary directories...

mkdir "%INSTALL_DIR%\Logs" 2>nul
mkdir "%INSTALL_DIR%\Backups" 2>nul
mkdir "%INSTALL_DIR%\Reports" 2>nul
mkdir "%INSTALL_DIR%\Temp" 2>nul
mkdir "%INSTALL_DIR%\Documentation" 2>nul

:: إنشاء اختصار على سطح المكتب
echo إنشاء اختصار على سطح المكتب...
echo Creating desktop shortcut...

set "DESKTOP=%USERPROFILE%\Desktop"
set "SHORTCUT=%DESKTOP%\نظام إدارة مزرعة الأسماك.lnk"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\FishFarmManagement.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'نظام إدارة مزرعة الأسماك'; $Shortcut.Save()" 2>nul

:: إنشاء اختصار في قائمة ابدأ
echo إنشاء اختصار في قائمة ابدأ...
echo Creating start menu shortcut...

set "STARTMENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
mkdir "%STARTMENU%\Fish Farm Management" 2>nul
set "STARTSHORTCUT=%STARTMENU%\Fish Farm Management\نظام إدارة مزرعة الأسماك.lnk"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTSHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\FishFarmManagement.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'نظام إدارة مزرعة الأسماك'; $Shortcut.Save()" 2>nul

:: إضافة إلى قائمة البرامج المثبتة
echo تسجيل البرنامج في النظام...
echo Registering program in system...

reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FishFarmManagement" /v "DisplayName" /t REG_SZ /d "نظام إدارة مزرعة الأسماك" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FishFarmManagement" /v "DisplayVersion" /t REG_SZ /d "1.0.1" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FishFarmManagement" /v "Publisher" /t REG_SZ /d "طارق حسين صالح" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FishFarmManagement" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FishFarmManagement" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\uninstall.bat" /f >nul 2>&1

:: إنشاء ملف إلغاء التثبيت
echo إنشاء ملف إلغاء التثبيت...
echo Creating uninstall file...

(
echo @echo off
echo chcp 65001 ^>nul
echo title إلغاء تثبيت نظام إدارة مزرعة الأسماك
echo.
echo هل أنت متأكد من إلغاء تثبيت البرنامج؟ ^(Y/N^)
echo set /p choice=
echo if /i "%%choice%%" neq "Y" exit /b 0
echo.
echo إلغاء تثبيت البرنامج...
echo rd /s /q "%INSTALL_DIR%" 2^>nul
echo del "%DESKTOP%\نظام إدارة مزرعة الأسماك.lnk" 2^>nul
echo rd /s /q "%STARTMENU%\Fish Farm Management" 2^>nul
echo reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FishFarmManagement" /f ^>nul 2^>^&1
echo echo تم إلغاء التثبيت بنجاح
echo pause
) > "%INSTALL_DIR%\uninstall.bat"

echo.
echo ========================================
echo    تم التثبيت بنجاح!
echo    Installation Completed Successfully!
echo ========================================
echo.
echo مجلد التثبيت: %INSTALL_DIR%
echo Installation Directory: %INSTALL_DIR%
echo.
echo يمكنك الآن تشغيل البرنامج من:
echo You can now run the program from:
echo - سطح المكتب - Desktop
echo - قائمة ابدأ - Start Menu
echo - مجلد التثبيت - Installation Directory
echo.
echo هل تريد تشغيل البرنامج الآن؟ (Y/N)
echo Do you want to run the program now? (Y/N)
set /p runchoice=
if /i "%runchoice%" equ "Y" (
    start "" "%INSTALL_DIR%\FishFarmManagement.exe"
)

echo.
echo شكراً لاستخدام نظام إدارة مزرعة الأسماك!
echo Thank you for using Fish Farm Management System!
pause
