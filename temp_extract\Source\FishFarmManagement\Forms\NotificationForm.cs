﻿using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ø§ÙØ°Ø© Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª ÙˆØ§Ù„Ø¥Ø´Ø¹Ø§Ø±Ø§Øª
    /// Notifications and alerts management form
    /// </summary>
    public partial class NotificationForm : Form
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger<NotificationForm> _logger;
        private DataGridView notificationsDataGridView;
        private ComboBox typeFilterComboBox;
        private ComboBox priorityFilterComboBox;
        private ComboBox statusFilterComboBox;
        private Button markAllReadButton;
        private Button deleteSelectedButton;
        private Button refreshButton;
        private Label unreadCountLabel;
        private Panel filterPanel;
        private Panel actionPanel;

        public NotificationForm(IServiceProvider serviceProvider)
        {
            _notificationService = serviceProvider.GetRequiredService<INotificationService>();
            _logger = serviceProvider.GetRequiredService<ILogger<NotificationForm>>();
            InitializeComponent();
            _ = LoadNotificationsAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª ÙˆØ§Ù„Ø¥Ø´Ø¹Ø§Ø±Ø§Øª";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
            SetupEventHandlers();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(52, 73, 94),
                Padding = new Padding(20, 10, 20, 10)
            };

            var titleLabel = new Label
            {
                Text = "Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª ÙˆØ§Ù„Ø¥Ø´Ø¹Ø§Ø±Ø§Øª",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 25)
            };

            unreadCountLabel = new Label
            {
                Text = "Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª ØºÙŠØ± Ø§Ù„Ù…Ù‚Ø±ÙˆØ¡Ø©: 0",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(300, 30)
            };

            headerPanel.Controls.AddRange(new Control[] { titleLabel, unreadCountLabel });

            // Filter panel
            filterPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(236, 240, 241),
                Padding = new Padding(20, 10, 20, 10)
            };

            var typeLabel = new Label
            {
                Text = "Ø§Ù„Ù†ÙˆØ¹:",
                Font = new Font("Segoe UI", 10F),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            typeFilterComboBox = new ComboBox
            {
                Font = new Font("Segoe UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Location = new Point(80, 17),
                Width = 150
            };
            typeFilterComboBox.Items.AddRange(new object[] { "Ø§Ù„ÙƒÙ„", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª", "ØªØ­Ø°ÙŠØ±", "Ø®Ø·Ø£", "Ù†Ø¬Ø­", "Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡", "Ø§Ù„ØªØºØ°ÙŠØ©", "Ø§Ù„ØµØ­Ø©", "Ø§Ù„Ù…Ø®Ø²ÙˆÙ†", "Ø§Ù„Ø¥Ù†ØªØ§Ø¬", "Ù…Ø§Ù„ÙŠ", "Ø§Ù„Ù†Ø¸Ø§Ù…", "Ø§Ù„ØµÙŠØ§Ù†Ø©" });
            typeFilterComboBox.SelectedIndex = 0;

            var priorityLabel = new Label
            {
                Text = "Ø§Ù„Ø£ÙˆÙ„ÙˆÙŠØ©:",
                Font = new Font("Segoe UI", 10F),
                AutoSize = true,
                Location = new Point(250, 20)
            };

            priorityFilterComboBox = new ComboBox
            {
                Font = new Font("Segoe UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Location = new Point(320, 17),
                Width = 120
            };
            priorityFilterComboBox.Items.AddRange(new object[] { "Ø§Ù„ÙƒÙ„", "Ù…Ù†Ø®ÙØ¶Ø©", "Ø¹Ø§Ø¯ÙŠØ©", "Ø¹Ø§Ù„ÙŠØ©", "Ø­Ø±Ø¬Ø©" });
            priorityFilterComboBox.SelectedIndex = 0;

            var statusLabel = new Label
            {
                Text = "Ø§Ù„Ø­Ø§Ù„Ø©:",
                Font = new Font("Segoe UI", 10F),
                AutoSize = true,
                Location = new Point(460, 20)
            };

            statusFilterComboBox = new ComboBox
            {
                Font = new Font("Segoe UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Location = new Point(520, 17),
                Width = 120
            };
            statusFilterComboBox.Items.AddRange(new object[] { "Ø§Ù„ÙƒÙ„", "ÙÙŠ Ø§Ù„Ø§Ù†ØªØ¸Ø§Ø±", "ØªÙ… Ø§Ù„Ø¥Ø±Ø³Ø§Ù„", "ØªÙ… Ø§Ù„Ù‚Ø±Ø§Ø¡Ø©", "ØªÙ… Ø§Ù„ØªØ¬Ø§Ù‡Ù„", "Ù…Ø¤Ø±Ø´Ù" });
            statusFilterComboBox.SelectedIndex = 0;

            refreshButton = new Button
            {
                Text = "ØªØ­Ø¯ÙŠØ«",
                Font = new Font("Segoe UI", 10F),
                Size = new Size(80, 30),
                Location = new Point(660, 15),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            filterPanel.Controls.AddRange(new Control[] { typeLabel, typeFilterComboBox, priorityLabel, priorityFilterComboBox, statusLabel, statusFilterComboBox, refreshButton });

            // Action panel
            actionPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(236, 240, 241),
                Padding = new Padding(20, 15, 20, 15)
            };

            markAllReadButton = new Button
            {
                Text = "ÙˆØ¶Ø¹ Ø¹Ù„Ø§Ù…Ø© Ù…Ù‚Ø±ÙˆØ¡ Ø¹Ù„Ù‰ Ø§Ù„ÙƒÙ„",
                Font = new Font("Segoe UI", 10F),
                Size = new Size(180, 30),
                Location = new Point(20, 15),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            deleteSelectedButton = new Button
            {
                Text = "Ø­Ø°Ù Ø§Ù„Ù…Ø­Ø¯Ø¯",
                Font = new Font("Segoe UI", 10F),
                Size = new Size(100, 30),
                Location = new Point(220, 15),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            actionPanel.Controls.AddRange(new Control[] { markAllReadButton, deleteSelectedButton });

            // DataGridView
            notificationsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = true,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(52, 73, 94),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Segoe UI", 10F),
                    SelectionBackColor = Color.FromArgb(52, 152, 219),
                    SelectionForeColor = Color.White
                }
            };

            SetupDataGridViewColumns();

            this.Controls.AddRange(new Control[] { notificationsDataGridView, actionPanel, filterPanel, headerPanel });
        }

        private void SetupDataGridViewColumns()
        {
            notificationsDataGridView.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "Ø§Ù„Ù…Ø¹Ø±Ù", Width = 60, Visible = false },
                new DataGridViewTextBoxColumn { Name = "Title", HeaderText = "Ø§Ù„Ø¹Ù†ÙˆØ§Ù†", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "Message", HeaderText = "Ø§Ù„Ø±Ø³Ø§Ù„Ø©", Width = 300 },
                new DataGridViewTextBoxColumn { Name = "Type", HeaderText = "Ø§Ù„Ù†ÙˆØ¹", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Priority", HeaderText = "Ø§Ù„Ø£ÙˆÙ„ÙˆÙŠØ©", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "Status", HeaderText = "Ø§Ù„Ø­Ø§Ù„Ø©", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "CreatedDate", HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø¥Ù†Ø´Ø§Ø¡", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "ReadAt", HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ù‚Ø±Ø§Ø¡Ø©", Width = 120 }
            });
        }

        private void SetupEventHandlers()
        {
            refreshButton.Click += RefreshButton_Click;
            markAllReadButton.Click += MarkAllReadButton_Click;
            deleteSelectedButton.Click += DeleteSelectedButton_Click;
            typeFilterComboBox.SelectedIndexChanged += FilterComboBox_SelectedIndexChanged;
            priorityFilterComboBox.SelectedIndexChanged += FilterComboBox_SelectedIndexChanged;
            statusFilterComboBox.SelectedIndexChanged += FilterComboBox_SelectedIndexChanged;
            notificationsDataGridView.CellDoubleClick += NotificationsDataGridView_CellDoubleClick;
        }

        private async Task LoadNotificationsAsync()
        {
            try
            {
                var notifications = await _notificationService.GetActiveNotificationsAsync();
                ApplyFilters(notifications);
                await UpdateUnreadCount();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª");
                MessageBox.Show("Ø­Ø¯Ø« Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ApplyFilters(List<Notification> notifications)
        {
            var filteredNotifications = notifications.AsEnumerable();

            // Filter by type
            if (typeFilterComboBox.SelectedIndex > 0)
            {
                var selectedType = GetNotificationTypeFromText(typeFilterComboBox.SelectedItem.ToString()!);
                filteredNotifications = filteredNotifications.Where(n => n.Type == selectedType);
            }

            // Filter by priority
            if (priorityFilterComboBox.SelectedIndex > 0)
            {
                var selectedPriority = GetNotificationPriorityFromText(priorityFilterComboBox.SelectedItem.ToString()!);
                filteredNotifications = filteredNotifications.Where(n => n.Priority == selectedPriority);
            }

            // Filter by status
            if (statusFilterComboBox.SelectedIndex > 0)
            {
                var selectedStatus = GetNotificationStatusFromText(statusFilterComboBox.SelectedItem.ToString()!);
                filteredNotifications = filteredNotifications.Where(n => n.Status == selectedStatus);
            }

            var dataSource = filteredNotifications.Select(n => new
            {
                Id = n.Id,
                Title = n.Title,
                Message = n.Message.Length > 100 ? n.Message.Substring(0, 100) + "..." : n.Message,
                Type = n.GetTypeText(),
                Priority = n.GetPriorityText(),
                Status = n.GetStatusText(),
                CreatedDate = n.CreatedDate.ToString("yyyy-MM-dd HH:mm"),
                ReadAt = n.ReadAt?.ToString("yyyy-MM-dd HH:mm") ?? ""
            }).ToList();

            notificationsDataGridView.DataSource = dataSource;
        }

        private async Task UpdateUnreadCount()
        {
            try
            {
                var unreadCount = await _notificationService.GetUnreadCountAsync();
                unreadCountLabel.Text = $"Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª ØºÙŠØ± Ø§Ù„Ù…Ù‚Ø±ÙˆØ¡Ø©: {unreadCount}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ø¯ÙŠØ« Ø¹Ø¯Ø¯ Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª ØºÙŠØ± Ø§Ù„Ù…Ù‚Ø±ÙˆØ¡Ø©");
            }
        }

        private NotificationType GetNotificationTypeFromText(string text)
        {
            return text switch
            {
                "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª" => NotificationType.Info,
                "ØªØ­Ø°ÙŠØ±" => NotificationType.Warning,
                "Ø®Ø·Ø£" => NotificationType.Error,
                "Ù†Ø¬Ø­" => NotificationType.Success,
                "Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡" => NotificationType.WaterQuality,
                "Ø§Ù„ØªØºØ°ÙŠØ©" => NotificationType.Feeding,
                "Ø§Ù„ØµØ­Ø©" => NotificationType.Health,
                "Ø§Ù„Ù…Ø®Ø²ÙˆÙ†" => NotificationType.Inventory,
                "Ø§Ù„Ø¥Ù†ØªØ§Ø¬" => NotificationType.Production,
                "Ù…Ø§Ù„ÙŠ" => NotificationType.Financial,
                "Ø§Ù„Ù†Ø¸Ø§Ù…" => NotificationType.System,
                "Ø§Ù„ØµÙŠØ§Ù†Ø©" => NotificationType.Maintenance,
                _ => NotificationType.Info
            };
        }

        private NotificationPriority GetNotificationPriorityFromText(string text)
        {
            return text switch
            {
                "Ù…Ù†Ø®ÙØ¶Ø©" => NotificationPriority.Low,
                "Ø¹Ø§Ø¯ÙŠØ©" => NotificationPriority.Normal,
                "Ø¹Ø§Ù„ÙŠØ©" => NotificationPriority.High,
                "Ø­Ø±Ø¬Ø©" => NotificationPriority.Critical,
                _ => NotificationPriority.Normal
            };
        }

        private NotificationStatus GetNotificationStatusFromText(string text)
        {
            return text switch
            {
                "ÙÙŠ Ø§Ù„Ø§Ù†ØªØ¸Ø§Ø±" => NotificationStatus.Pending,
                "ØªÙ… Ø§Ù„Ø¥Ø±Ø³Ø§Ù„" => NotificationStatus.Sent,
                "ØªÙ… Ø§Ù„Ù‚Ø±Ø§Ø¡Ø©" => NotificationStatus.Read,
                "ØªÙ… Ø§Ù„ØªØ¬Ø§Ù‡Ù„" => NotificationStatus.Dismissed,
                "Ù…Ø¤Ø±Ø´Ù" => NotificationStatus.Archived,
                _ => NotificationStatus.Pending
            };
        }

        private async void private async void private async void RefreshButton_Click(object? sender, EventArgs e)
        {
            await LoadNotificationsAsync();
        }

        private async void private async void private async void MarkAllReadButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("Ù‡Ù„ ØªØ±ÙŠØ¯ ÙˆØ¶Ø¹ Ø¹Ù„Ø§Ù…Ø© Ù…Ù‚Ø±ÙˆØ¡ Ø¹Ù„Ù‰ Ø¬Ù…ÙŠØ¹ Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§ØªØŸ", "ØªØ£ÙƒÙŠØ¯", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    await _notificationService.MarkAllAsReadAsync();
                    await LoadNotificationsAsync();
                    MessageBox.Show("ØªÙ… ÙˆØ¶Ø¹ Ø¹Ù„Ø§Ù…Ø© Ù…Ù‚Ø±ÙˆØ¡ Ø¹Ù„Ù‰ Ø¬Ù…ÙŠØ¹ Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙˆØ¶Ø¹ Ø¹Ù„Ø§Ù…Ø© Ù…Ù‚Ø±ÙˆØ¡ Ø¹Ù„Ù‰ Ø¬Ù…ÙŠØ¹ Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª");
                MessageBox.Show("Ø­Ø¯Ø« Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø¹Ù…Ù„ÙŠØ©", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteSelectedButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (notificationsDataGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("ÙŠØ±Ø¬Ù‰ ØªØ­Ø¯ÙŠØ¯ ØªÙ†Ø¨ÙŠÙ‡ Ù„Ù„Ø­Ø°Ù", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show($"Ù‡Ù„ ØªØ±ÙŠØ¯ Ø­Ø°Ù {notificationsDataGridView.SelectedRows.Count} ØªÙ†Ø¨ÙŠÙ‡ØŸ", "ØªØ£ÙƒÙŠØ¯ Ø§Ù„Ø­Ø°Ù", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    foreach (DataGridViewRow row in notificationsDataGridView.SelectedRows)
                    {
                        var notificationId = (int)row.Cells["Id"].Value;
                        await _notificationService.DeleteNotificationAsync(notificationId);
                    }

                    await LoadNotificationsAsync();
                    MessageBox.Show("ØªÙ… Ø­Ø°Ù Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª Ø§Ù„Ù…Ø­Ø¯Ø¯Ø©", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª");
                MessageBox.Show("Ø­Ø¯Ø« Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void private async void private async void FilterComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            await LoadNotificationsAsync();
        }

        private async void private async void private async void NotificationsDataGridView_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var notificationId = (int)notificationsDataGridView.Rows[e.RowIndex].Cells["Id"].Value;
                await _notificationService.MarkAsReadAsync(notificationId);
                await LoadNotificationsAsync();
            }
        }
    }
}



