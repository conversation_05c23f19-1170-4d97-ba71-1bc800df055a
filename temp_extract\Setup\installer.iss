; ملف تثبيت نظام إدارة مزرعة الأسماك
; Fish Farm Management System Installer

#define MyAppName "نظام إدارة مزرعة الأسماك"
#define MyAppNameEn "Fish Farm Management System"
#define MyAppVersion "1.0.1"
#define MyAppPublisher "طارق حسين صالح"
#define MyAppPublisherEn "<PERSON><PERSON>q <PERSON>"
#define MyAppURL "mailto:<EMAIL>"
#define MyAppExeName "FishFarmManagement.exe"

[Setup]
; معلومات التطبيق
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName={#MyAppName}
AppNameEn={#MyAppNameEn}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherEn={#MyAppPublisherEn}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppNameEn}
DefaultGroupName={#MyAppName}
DisableProgramGroupPage=yes
DisableWelcomePage=no
LicenseFile=..\LICENSE
OutputDir=Output
OutputBaseFilename=FishFarmManagement-Setup
SetupIconFile=..\FishFarmManagement\Resources\icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
WizardImageFile=..\Setup\Images\wizard-image.bmp
WizardSmallImageFile=..\Setup\Images\wizard-small.bmp
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; دعم اللغة العربية
LanguageDetectionMethod=locale
ShowLanguageDialog=no

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
; الملفات الرئيسية
Source: "..\FishFarmManagement\bin\Release\net8.0-windows\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; ملفات .NET Runtime (إذا لم تكن مثبتة)
Source: "..\Setup\Dependencies\windowsdesktop-runtime-8.0.0-win-x64.exe"; DestDir: "{tmp}"; Flags: deleteafterinstall; Check: not IsDotNetInstalled

; ملفات التوثيق
Source: "..\README.md"; DestDir: "{app}\Documentation"; Flags: ignoreversion
Source: "..\DOCUMENTATION.md"; DestDir: "{app}\Documentation"; Flags: ignoreversion
Source: "..\INSTALLATION.md"; DestDir: "{app}\Documentation"; Flags: ignoreversion
Source: "..\LICENSE"; DestDir: "{app}"; Flags: ignoreversion

; ملفات العينة
Source: "..\Setup\Samples\*"; DestDir: "{app}\Samples"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: quicklaunchicon

[Run]
; تشغيل التطبيق بعد التثبيت
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

; تثبيت .NET Runtime إذا لزم الأمر
Filename: "{tmp}\windowsdesktop-runtime-8.0.0-win-x64.exe"; Parameters: "/quiet /norestart"; StatusMsg: "Installing .NET Runtime..."; Check: not IsDotNetInstalled

[Code]
// التحقق من تثبيت .NET Runtime
function IsDotNetInstalled: Boolean;
var
  Success: Boolean;
  InstallPath: String;
  Release: Cardinal;
begin
  Success := RegQueryDWordValue(HKLM, 'SOFTWARE\Microsoft\NET Core Setup\InstalledVersions\x64\sharedhost', 'Version', Release);
  if Success then
  begin
    Result := Release >= $08000000; // .NET 8.0 or higher
  end
  else
  begin
    Result := False;
  end;
end;

// التحقق من متطلبات النظام
function InitializeSetup(): Boolean;
begin
  Result := True;
  
  // التحقق من نظام التشغيل
  if not IsWin64 then
  begin
    MsgBox('هذا التطبيق يتطلب نظام Windows 64-bit.', mbError, MB_OK);
    Result := False;
    exit;
  end;
  
  // التحقق من إصدار Windows
  if GetWindowsVersion < $06030000 then // Windows 8.1
  begin
    MsgBox('هذا التطبيق يتطلب Windows 8.1 أو أحدث.', mbError, MB_OK);
    Result := False;
    exit;
  end;
end;

// إنشاء مجلدات إضافية
procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    // إنشاء مجلد النسخ الاحتياطية
    CreateDir(ExpandConstant('{app}\Backups'));
    
    // إنشاء مجلد التقارير
    CreateDir(ExpandConstant('{app}\Reports'));
    
    // إنشاء مجلد السجلات
    CreateDir(ExpandConstant('{app}\Logs'));
  end;
end;

[Registry]
; إعدادات التسجيل
Root: HKLM; Subkey: "SOFTWARE\{#MyAppPublisherEn}\{#MyAppNameEn}"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "SOFTWARE\{#MyAppPublisherEn}\{#MyAppNameEn}"; ValueType: string; ValueName: "Version"; ValueData: "{#MyAppVersion}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "SOFTWARE\{#MyAppPublisherEn}\{#MyAppNameEn}"; ValueType: string; ValueName: "Publisher"; ValueData: "{#MyAppPublisherEn}"; Flags: uninsdeletekey

; إضافة إلى قائمة البرامج المثبتة
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppNameEn}"; ValueType: string; ValueName: "DisplayName"; ValueData: "{#MyAppName}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppNameEn}"; ValueType: string; ValueName: "UninstallString"; ValueData: "{uninstallexe}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppNameEn}"; ValueType: string; ValueName: "DisplayIcon"; ValueData: "{app}\{#MyAppExeName}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppNameEn}"; ValueType: string; ValueName: "Publisher"; ValueData: "{#MyAppPublisherEn}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppNameEn}"; ValueType: string; ValueName: "URLInfoAbout"; ValueData: "{#MyAppURL}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppNameEn}"; ValueType: dword; ValueName: "NoModify"; ValueData: 1; Flags: uninsdeletekey
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppNameEn}"; ValueType: dword; ValueName: "NoRepair"; ValueData: 1; Flags: uninsdeletekey

[UninstallDelete]
; حذف الملفات والمجلدات عند إلغاء التثبيت
Type: filesandordirs; Name: "{app}\Backups"
Type: filesandordirs; Name: "{app}\Reports"
Type: filesandordirs; Name: "{app}\Logs"
Type: files; Name: "{app}\license.dat"
Type: files; Name: "{app}\FishFarmDatabase.db"

[CustomMessages]
; رسائل مخصصة باللغة العربية
arabic.LaunchProgram=تشغيل نظام إدارة مزرعة الأسماك
arabic.AdditionalIcons=أيقونات إضافية:
arabic.CreateDesktopIcon=إنشاء أيقونة على سطح المكتب
arabic.CreateQuickLaunchIcon=إنشاء أيقونة في شريط التشغيل السريع
arabic.UninstallProgram=إلغاء تثبيت {#MyAppName}
arabic.InstallingDotNetRuntime=جاري تثبيت .NET Runtime...
arabic.DotNetRuntimeRequired=يتطلب هذا التطبيق .NET Runtime 8.0 أو أحدث.
arabic.SystemRequirements=متطلبات النظام:
arabic.WindowsVersion=Windows 8.1 أو أحدث
arabic.Architecture=64-bit
arabic.DiskSpace=500 MB مساحة فارغة
arabic.RAM=4 GB ذاكرة عشوائية 