using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// مركز التكلفة
    /// Cost center
    /// </summary>
    public class CostCenter : BaseEntity
    {
        [Required(ErrorMessage = "معرف الدورة الإنتاجية مطلوب")]
        public int CycleId { get; set; }

        [Required(ErrorMessage = "اسم مركز التكلفة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم مركز التكلفة يجب أن يكون أقل من 100 حرف")]
        public string CenterName { get; set; } = string.Empty;

        [Required(ErrorMessage = "رمز مركز التكلفة مطلوب")]
        [StringLength(20, ErrorMessage = "رمز مركز التكلفة يجب أن يكون أقل من 20 حرف")]
        public string CenterCode { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// الميزانية المخصصة
        /// Allocated budget
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الميزانية يجب أن تكون أكبر من الصفر")]
        [Column(TypeName = "decimal(15,2)")]
        public decimal AllocatedBudget { get; set; }

        /// <summary>
        /// الإنفاق الفعلي
        /// Actual spending
        /// </summary>
        [Column(TypeName = "decimal(15,2)")]
        public decimal ActualSpending { get; set; }

        /// <summary>
        /// حالة مركز التكلفة
        /// Cost center status
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "نشط"; // نشط، متوقف، مغلق

        // Navigation Properties
        [ForeignKey("CycleId")]
        public virtual ProductionCycle ProductionCycle { get; set; } = null!;

        public virtual ICollection<TransactionDetail> TransactionDetails { get; set; } = new List<TransactionDetail>();

        /// <summary>
        /// حساب الرصيد المتبقي من الميزانية
        /// Calculate remaining budget balance
        /// </summary>
        public decimal GetRemainingBudget()
        {
            return AllocatedBudget - ActualSpending;
        }

        /// <summary>
        /// حساب نسبة الإنفاق
        /// Calculate spending percentage
        /// </summary>
        public decimal GetSpendingPercentage()
        {
            if (AllocatedBudget <= 0) return 0;
            return (ActualSpending / AllocatedBudget) * 100;
        }

        /// <summary>
        /// التحقق من تجاوز الميزانية
        /// Check if budget is exceeded
        /// </summary>
        public bool IsBudgetExceeded()
        {
            return ActualSpending > AllocatedBudget;
        }

        /// <summary>
        /// التحقق من قرب نفاد الميزانية (أكثر من 90%)
        /// Check if budget is nearly exhausted (more than 90%)
        /// </summary>
        public bool IsBudgetNearlyExhausted()
        {
            return GetSpendingPercentage() >= 90;
        }

        /// <summary>
        /// تحديث الإنفاق الفعلي
        /// Update actual spending
        /// </summary>
        public void UpdateActualSpending()
        {
            ActualSpending = TransactionDetails
                .Where(td => td.Transaction.Status == "مرحل")
                .Sum(td => td.DebitAmount);
        }

        /// <summary>
        /// الحصول على إجمالي المصروفات حسب نوع الحساب
        /// Get total expenses by account type
        /// </summary>
        public decimal GetExpensesByAccountType(int accountTypeId)
        {
            return TransactionDetails
                .Where(td => td.Account.AccountTypeId == accountTypeId && 
                           td.Transaction.Status == "مرحل")
                .Sum(td => td.DebitAmount);
        }

        /// <summary>
        /// الحصول على تقرير مفصل للمصروفات
        /// Get detailed expense report
        /// </summary>
        public Dictionary<string, decimal> GetDetailedExpenseReport()
        {
            return TransactionDetails
                .Where(td => td.Transaction.Status == "مرحل" && td.DebitAmount > 0)
                .GroupBy(td => td.Account.AccountName)
                .ToDictionary(g => g.Key, g => g.Sum(td => td.DebitAmount));
        }
    }
}
