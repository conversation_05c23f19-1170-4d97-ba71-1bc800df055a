@echo off
echo ========================================
echo Fish Farm Management - Commercial Build
echo ========================================
echo.

:: Check for .NET SDK
echo [1/8] Checking .NET SDK...
dotnet --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: .NET SDK not installed
    echo Please install .NET 8.0 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)
echo ✓ .NET SDK available

:: Check for solution file
echo.
echo [2/8] Checking project files...
if not exist "FishFarmManagement.sln" (
    echo ERROR: Solution file FishFarmManagement.sln not found
    echo Make sure to run this script from the correct directory
    pause
    exit /b 1
)
echo ✓ Solution file found

:: Clean project
echo.
echo [3/8] Cleaning project...
echo   - Cleaning solution...
dotnet clean FishFarmManagement.sln --verbosity quiet >nul 2>&1

echo   - Removing bin and obj folders...
for /d %%i in (bin obj) do if exist "%%i" rmdir /s /q "%%i" >nul 2>&1
for /d %%i in (FishFarmManagement*) do (
    if exist "%%i\bin" rmdir /s /q "%%i\bin" >nul 2>&1
    if exist "%%i\obj" rmdir /s /q "%%i\obj" >nul 2>&1
)
echo ✓ Project cleaned

:: Restore packages
echo.
echo [4/8] Restoring packages...
dotnet restore FishFarmManagement.sln --verbosity quiet
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to restore packages
    echo Trying to restore with more details...
    dotnet restore FishFarmManagement.sln
    pause
    exit /b 1
)
echo ✓ Packages restored

:: Build projects
echo.
echo [5/8] Building projects...
dotnet build FishFarmManagement.sln --configuration Release --verbosity quiet
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to build solution
    echo Trying to build with more details...
    dotnet build FishFarmManagement.sln --configuration Release
    pause
    exit /b 1
)
echo ✓ All projects built successfully

:: Run tests
echo.
echo [6/8] Running tests...
if exist "FishFarmManagement.Tests\FishFarmManagement.Tests.csproj" (
    dotnet test FishFarmManagement.Tests\FishFarmManagement.Tests.csproj --configuration Release --verbosity quiet --no-build --logger "console;verbosity=minimal" >nul 2>&1
    if %ERRORLEVEL% neq 0 (
        echo WARNING: Some tests failed, but build will continue
    ) else (
        echo ✓ Tests passed successfully
    )
) else (
    echo WARNING: Test project not found, skipping tests
)

:: Create distribution folders
echo.
echo [7/8] Creating distribution folders...
if not exist "Distribution" mkdir "Distribution"
if not exist "Distribution\Setup" mkdir "Distribution\Setup"
if not exist "Distribution\Portable" mkdir "Distribution\Portable"
if not exist "Distribution\Documentation" mkdir "Distribution\Documentation"
if not exist "Distribution\Samples" mkdir "Distribution\Samples"
echo ✓ Distribution folders created

:: Copy built files
echo.
echo [8/8] Copying built files...

:: Check if built files exist
if not exist "FishFarmManagement\bin\Release\net8.0-windows" (
    echo ERROR: Built files directory not found
    echo Make sure the build was successful
    pause
    exit /b 1
)

:: Copy portable application
echo   - Creating portable version...
xcopy "FishFarmManagement\bin\Release\net8.0-windows\*" "Distribution\Portable\" /E /I /Y /Q >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to copy files for portable version
    pause
    exit /b 1
)

:: Add additional folders for portable version
echo   - Adding additional folders...
for %%i in (Backups Reports Logs Updates) do (
    if not exist "Distribution\Portable\%%i" mkdir "Distribution\Portable\%%i"
)

:: Create README for portable version
echo   - Creating README file...
(
echo # Fish Farm Management System - Portable Version v1.0.1
echo.
echo This is the portable version of Fish Farm Management System.
echo.
echo ## To Run:
echo 1. Double-click on FishFarmManagement.exe
echo 2. A 30-day trial license will be created automatically
echo 3. Use these credentials to login:
echo    - Username: admin
echo    - Password: Admin123!
echo.
echo ## New Features in v1.0.1:
echo - Automatic Update System
echo - Advanced Performance Monitor
echo - Smart Notifications
echo - Multi-format Export
echo - Backup Compression
echo - Security Enhancements
echo.
echo ## Technical Support:
echo Email: <EMAIL>
echo.
echo © 2024 Tarek Hussein Saleh Ahmed. All rights reserved.
) > "Distribution\Portable\README.txt"

echo ✓ Portable version created

:: Copy documentation files
echo   - Copying documentation files...
for %%i in (README.md DOCUMENTATION.md INSTALLATION.md LICENSE CHANGELOG.md COMMERCIAL_README.md COMMERCIAL_USER_GUIDE.md) do (
    if exist "%%i" copy "%%i" "Distribution\Documentation\" >nul 2>&1
)
echo ✓ Documentation files copied

:: Create sample files
echo   - Creating sample files...
(
echo # Sample Files for Fish Farm Management System v1.0.1
echo.
echo This folder contains sample files to help you get started with the system.
echo.
echo ## Contents:
echo - Sample database with demo data
echo - Sample reports in various formats
echo - Sample configuration files
echo - Usage examples
echo.
echo ## To Use the Files:
echo 1. Copy files to the program folder
echo 2. Run the program and explore the demo data
echo 3. Use reports as templates for your own reports
echo.
echo For more help: <EMAIL>
) > "Distribution\Samples\README.txt"
echo ✓ Sample files created

:: Create ZIP files
echo   - Creating compressed distribution files...

:: Portable version
powershell -Command "try { Compress-Archive -Path 'Distribution\Portable\*' -DestinationPath 'Distribution\FishFarmManagement-v1.0.1-Portable.zip' -Force } catch { Write-Host 'Error creating portable ZIP' }" >nul 2>&1

:: Documentation files
powershell -Command "try { Compress-Archive -Path 'Distribution\Documentation\*' -DestinationPath 'Distribution\FishFarmManagement-v1.0.1-Documentation.zip' -Force } catch { Write-Host 'Error creating documentation ZIP' }" >nul 2>&1

:: Sample files
powershell -Command "try { Compress-Archive -Path 'Distribution\Samples\*' -DestinationPath 'Distribution\FishFarmManagement-v1.0.1-Samples.zip' -Force } catch { Write-Host 'Error creating samples ZIP' }" >nul 2>&1

echo ✓ Compressed distribution files created

echo.
echo ========================================
echo    Build Successful!
echo ========================================
echo.
echo Available files in Distribution folder:
echo.
echo 📦 FishFarmManagement-v1.0.1-Portable.zip
echo    - Portable version (direct run)
echo    - No installation required
echo    - Ready for immediate use
echo.
echo 📋 FishFarmManagement-v1.0.1-Documentation.zip
echo    - Complete documentation files
echo    - User and developer guides
echo    - Usage examples
echo.
echo 🎯 FishFarmManagement-v1.0.1-Samples.zip
echo    - Sample files for quick start
echo    - Demo database
echo    - Sample reports
echo.
echo 📁 Portable\ (uncompressed portable version)
echo    - Ready for direct execution
echo    - FishFarmManagement.exe
echo.
echo For inquiries and support: <EMAIL>
echo.
echo ✅ Program ready for commercial distribution!
echo.
pause
