using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة الأدوية
    /// Medication management service interface
    /// </summary>
    public interface IMedicationService : IBusinessService<Medication, int>
    {
        /// <summary>
        /// الحصول على الأدوية النشطة
        /// Get active medications
        /// </summary>
        Task<IEnumerable<Medication>> GetActiveMedicationsAsync();

        /// <summary>
        /// الحصول على الأدوية منتهية الصلاحية
        /// Get expired medications
        /// </summary>
        Task<IEnumerable<Medication>> GetExpiredMedicationsAsync();

        /// <summary>
        /// الحصول على الأدوية قريبة انتهاء الصلاحية
        /// Get medications expiring soon
        /// </summary>
        Task<IEnumerable<Medication>> GetMedicationsExpiringSoonAsync(int daysAhead = 30);

        /// <summary>
        /// البحث في الأدوية
        /// Search medications
        /// </summary>
        Task<IEnumerable<Medication>> SearchMedicationsAsync(string searchTerm);

        /// <summary>
        /// الحصول على الأدوية بالنوع
        /// Get medications by type
        /// </summary>
        Task<IEnumerable<Medication>> GetMedicationsByTypeAsync(string medicationType);

        /// <summary>
        /// إضافة استخدام دواء للحوض
        /// Add medication usage to pond
        /// </summary>
        Task<PondMedication> AddPondMedicationAsync(int pondId, int medicationId, 
            decimal quantity, DateTime applicationDate, string reasonForUse, 
            string? veterinarianName = null, int withdrawalPeriodDays = 0, string? notes = null);

        /// <summary>
        /// الحصول على استخدامات الأدوية للحوض
        /// Get medication usages for pond
        /// </summary>
        Task<IEnumerable<PondMedication>> GetPondMedicationsAsync(int pondId, 
            DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// الحصول على استخدامات الأدوية للدورة
        /// Get medication usages for cycle
        /// </summary>
        Task<IEnumerable<PondMedication>> GetCycleMedicationsAsync(int cycleId, 
            DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// حساب إجمالي تكلفة الأدوية
        /// Calculate total medication cost
        /// </summary>
        Task<decimal> CalculateTotalMedicationCostAsync(int? pondId = null, 
            int? cycleId = null, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// الحصول على تقرير استخدام الأدوية
        /// Get medication usage report
        /// </summary>
        Task<MedicationUsageReport> GetMedicationUsageReportAsync(
            DateTime startDate, DateTime endDate, int? pondId = null);

        /// <summary>
        /// التحقق من فترة السحب
        /// Check withdrawal period
        /// </summary>
        Task<WithdrawalPeriodCheck> CheckWithdrawalPeriodAsync(int pondId, DateTime harvestDate);

        /// <summary>
        /// الحصول على تنبيهات الأدوية
        /// Get medication alerts
        /// </summary>
        Task<MedicationAlerts> GetMedicationAlertsAsync();

        /// <summary>
        /// تحديث سعر الدواء
        /// Update medication price
        /// </summary>
        Task<bool> UpdateMedicationPriceAsync(int medicationId, decimal newPrice);

        /// <summary>
        /// تحديث تاريخ انتهاء الصلاحية
        /// Update expiry date
        /// </summary>
        Task<bool> UpdateExpiryDateAsync(int medicationId, DateTime newExpiryDate);
    }

    /// <summary>
    /// تقرير استخدام الأدوية
    /// Medication usage report
    /// </summary>
    public class MedicationUsageReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalMedicationCost { get; set; }
        public int TotalApplications { get; set; }
        public List<MedicationUsageSummary> MedicationSummaries { get; set; } = new();
        public List<PondMedicationSummary> PondSummaries { get; set; } = new();
        public List<MedicationByReason> ReasonSummaries { get; set; } = new();
    }

    /// <summary>
    /// ملخص استخدام الدواء
    /// Medication usage summary
    /// </summary>
    public class MedicationUsageSummary
    {
        public int MedicationId { get; set; }
        public string MedicationName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public decimal TotalQuantityUsed { get; set; }
        public decimal TotalCost { get; set; }
        public int ApplicationCount { get; set; }
        public decimal AverageQuantityPerApplication { get; set; }
    }

    /// <summary>
    /// ملخص أدوية الحوض
    /// Pond medication summary
    /// </summary>
    public class PondMedicationSummary
    {
        public int PondId { get; set; }
        public string PondNumber { get; set; } = string.Empty;
        public decimal TotalMedicationCost { get; set; }
        public int TotalApplications { get; set; }
        public List<string> MedicationsUsed { get; set; } = new();
    }

    /// <summary>
    /// الأدوية حسب السبب
    /// Medications by reason
    /// </summary>
    public class MedicationByReason
    {
        public string Reason { get; set; } = string.Empty;
        public decimal TotalCost { get; set; }
        public int ApplicationCount { get; set; }
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// فحص فترة السحب
    /// Withdrawal period check
    /// </summary>
    public class WithdrawalPeriodCheck
    {
        public int PondId { get; set; }
        public string PondNumber { get; set; } = string.Empty;
        public DateTime ProposedHarvestDate { get; set; }
        public bool CanHarvest { get; set; }
        public List<WithdrawalPeriodViolation> Violations { get; set; } = new();
        public DateTime? EarliestSafeHarvestDate { get; set; }
    }

    /// <summary>
    /// انتهاك فترة السحب
    /// Withdrawal period violation
    /// </summary>
    public class WithdrawalPeriodViolation
    {
        public int MedicationId { get; set; }
        public string MedicationName { get; set; } = string.Empty;
        public DateTime ApplicationDate { get; set; }
        public int WithdrawalPeriodDays { get; set; }
        public DateTime SafeHarvestDate { get; set; }
        public int DaysRemaining { get; set; }
    }

    /// <summary>
    /// تنبيهات الأدوية
    /// Medication alerts
    /// </summary>
    public class MedicationAlerts
    {
        public List<Medication> ExpiredMedications { get; set; } = new();
        public List<Medication> ExpiringSoonMedications { get; set; } = new();
        public List<WithdrawalPeriodAlert> WithdrawalPeriodAlerts { get; set; } = new();
        public List<LowStockAlert> LowStockAlerts { get; set; } = new();
    }

    /// <summary>
    /// تنبيه فترة السحب
    /// Withdrawal period alert
    /// </summary>
    public class WithdrawalPeriodAlert
    {
        public int PondId { get; set; }
        public string PondNumber { get; set; } = string.Empty;
        public string MedicationName { get; set; } = string.Empty;
        public DateTime ApplicationDate { get; set; }
        public DateTime SafeHarvestDate { get; set; }
        public int DaysRemaining { get; set; }
    }

    /// <summary>
    /// تنبيه نقص المخزون
    /// Low stock alert
    /// </summary>
    public class LowStockAlert
    {
        public int MedicationId { get; set; }
        public string MedicationName { get; set; } = string.Empty;
        public decimal CurrentStock { get; set; }
        public decimal MinimumStock { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
