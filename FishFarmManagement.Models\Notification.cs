using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// نموذج التنبيهات والإشعارات
    /// Notifications and alerts model
    /// </summary>
    [Table("Notifications")]
    public class Notification : BaseEntity
    {
        [Required(ErrorMessage = "عنوان التنبيه مطلوب")]
        [StringLength(200, ErrorMessage = "عنوان التنبيه يجب أن يكون أقل من 200 حرف")]
        public string Title { get; set; } = string.Empty;

        [Required(ErrorMessage = "رسالة التنبيه مطلوبة")]
        [StringLength(1000, ErrorMessage = "رسالة التنبيه يجب أن تكون أقل من 1000 حرف")]
        public string Message { get; set; } = string.Empty;

        [Required(ErrorMessage = "نوع التنبيه مطلوب")]
        public NotificationType Type { get; set; }

        [Required(ErrorMessage = "أولوية التنبيه مطلوبة")]
        public NotificationPriority Priority { get; set; }

        [Required(ErrorMessage = "حالة التنبيه مطلوبة")]
        public NotificationStatus Status { get; set; }

        public DateTime? ReadAt { get; set; }

        public DateTime? ScheduledAt { get; set; }

        [StringLength(100)]
        public string? RelatedEntityType { get; set; }

        public int? RelatedEntityId { get; set; }

        [StringLength(500)]
        public string? ActionUrl { get; set; }

        public bool IsAutoGenerated { get; set; } = false;

        [StringLength(100)]
        public string? CreatedBy { get; set; }

        [StringLength(500)]
        public string? AdditionalData { get; set; }

        public bool IsRead => Status == NotificationStatus.Read;

        public bool IsPending => Status == NotificationStatus.Pending;

        public bool IsScheduled => ScheduledAt.HasValue && ScheduledAt > DateTime.Now;

        public string GetPriorityText()
        {
            return Priority switch
            {
                NotificationPriority.Low => "منخفضة",
                NotificationPriority.Normal => "عادية",
                NotificationPriority.High => "عالية",
                NotificationPriority.Critical => "حرجة",
                _ => "غير محدد"
            };
        }

        public string GetTypeText()
        {
            return Type switch
            {
                NotificationType.Info => "معلومات",
                NotificationType.Warning => "تحذير",
                NotificationType.Error => "خطأ",
                NotificationType.Success => "نجح",
                NotificationType.WaterQuality => "جودة المياه",
                NotificationType.Feeding => "التغذية",
                NotificationType.Health => "الصحة",
                NotificationType.Inventory => "المخزون",
                NotificationType.Production => "الإنتاج",
                NotificationType.Financial => "مالي",
                NotificationType.System => "النظام",
                NotificationType.Maintenance => "الصيانة",
                _ => "غير محدد"
            };
        }

        public string GetStatusText()
        {
            return Status switch
            {
                NotificationStatus.Pending => "في الانتظار",
                NotificationStatus.Sent => "تم الإرسال",
                NotificationStatus.Read => "تم القراءة",
                NotificationStatus.Dismissed => "تم التجاهل",
                NotificationStatus.Archived => "مؤرشف",
                _ => "غير محدد"
            };
        }
    }

    /// <summary>
    /// نوع التنبيه
    /// Notification type
    /// </summary>
    public enum NotificationType
    {
        Info = 1,           // معلومات عامة
        Warning = 2,        // تحذير
        Error = 3,          // خطأ
        Success = 4,        // نجح
        WaterQuality = 5,   // جودة المياه
        Feeding = 6,        // التغذية
        Health = 7,         // الصحة
        Inventory = 8,      // المخزون
        Production = 9,     // الإنتاج
        Financial = 10,     // مالي
        System = 11,        // النظام
        Maintenance = 12    // الصيانة
    }

    /// <summary>
    /// أولوية التنبيه
    /// Notification priority
    /// </summary>
    public enum NotificationPriority
    {
        Low = 1,      // منخفضة
        Normal = 2,   // عادية
        High = 3,     // عالية
        Critical = 4  // حرجة
    }

    /// <summary>
    /// حالة التنبيه
    /// Notification status
    /// </summary>
    public enum NotificationStatus
    {
        Pending = 1,    // في الانتظار
        Sent = 2,       // تم الإرسال
        Read = 3,       // تم القراءة
        Dismissed = 4,  // تم التجاهل
        Archived = 5    // مؤرشف
    }
}
