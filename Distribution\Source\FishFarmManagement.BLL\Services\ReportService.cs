using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة التقارير
    /// Report service
    /// </summary>
    public class ReportService : IReportService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ReportService> _logger;

        public ReportService(IUnitOfWork unitOfWork, ILogger<ReportService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<DailyProductionReport> GetDailyProductionReportAsync(DateTime date)
        {
            try
            {
                var ponds = await _unitOfWork.Ponds.FindAsync(p => p.Status == "نشط");
                
                var report = new DailyProductionReport
                {
                    ReportDate = date,
                    TotalActivePonds = ponds.Count(),
                    TotalFishCount = ponds.Sum(p => p.FishCount),
                    TotalBiomass = ponds.Sum(p => p.FishCount * p.AverageWeight),
                    PondData = new List<PondDailyData>()
                };

                // Get feed consumption for the day
                var feedConsumptions = await _unitOfWork.FeedConsumptions
                    .FindAsync(fc => fc.FeedingDate.Date == date.Date);
                
                report.TotalFeedConsumed = feedConsumptions.Sum(fc => fc.Quantity);
                report.TotalFeedCost = feedConsumptions.Sum(fc => fc.Quantity * fc.FeedType.PricePerKg);

                // Get mortality for the day
                var mortalities = await _unitOfWork.FishMortalities
                    .FindAsync(fm => fm.MortalityDate.Date == date.Date);
                
                report.TotalMortality = mortalities.Sum(m => m.DeadFishCount);
                report.MortalityRate = report.TotalFishCount > 0 ? 
                    (decimal)report.TotalMortality / report.TotalFishCount * 100 : 0;

                // Build pond data
                foreach (var pond in ponds)
                {
                    var pondFeed = feedConsumptions.Where(fc => fc.PondId == pond.Id).Sum(fc => fc.Quantity);
                    var pondMortality = mortalities.Where(m => m.PondId == pond.Id).Sum(m => m.DeadFishCount);

                    report.PondData.Add(new PondDailyData
                    {
                        PondId = pond.Id,
                        PondNumber = pond.PondNumber,
                        FishCount = pond.FishCount,
                        AverageWeight = pond.AverageWeight,
                        FeedConsumed = pondFeed,
                        Mortality = pondMortality,
                        Status = pond.Status
                    });
                }

                _logger.LogInformation("تم إنشاء تقرير الإنتاج اليومي لتاريخ {Date}", date);
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير الإنتاج اليومي لتاريخ {Date}", date);
                throw;
            }
        }

        public async Task<MonthlyProductionReport> GetMonthlyProductionReportAsync(int month, int year)
        {
            try
            {
                var startDate = new DateTime(year, month, 1);
                var endDate = startDate.AddMonths(1).AddDays(-1);

                var report = new MonthlyProductionReport
                {
                    Month = month,
                    Year = year,
                    DailyReports = new List<DailyProductionReport>()
                };

                // Generate daily reports for the month
                for (var date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    var dailyReport = await GetDailyProductionReportAsync(date);
                    report.DailyReports.Add(dailyReport);
                }

                // Calculate monthly totals
                report.TotalProduction = report.DailyReports.Sum(dr => dr.TotalBiomass);
                report.TotalRevenue = report.TotalProduction * 15; // Assuming 15 per kg
                
                // Calculate costs (feed + salaries + medications)
                var feedCosts = report.DailyReports.Sum(dr => dr.TotalFeedCost);
                var payrolls = await _unitOfWork.Payrolls.FindAsync(p => p.Month == month && p.Year == year);
                var salaryCosts = payrolls.Sum(p => p.NetSalary);
                
                var medications = await _unitOfWork.PondMedications.FindAsync(pm => 
                    pm.ApplicationDate >= startDate && pm.ApplicationDate <= endDate);
                var medicationCosts = medications.Sum(pm => pm.Quantity * pm.Medication.PricePerUnit);

                report.TotalCosts = feedCosts + salaryCosts + medicationCosts;
                report.NetProfit = report.TotalRevenue - report.TotalCosts;
                report.ProfitMargin = report.TotalRevenue > 0 ? 
                    (report.NetProfit / report.TotalRevenue) * 100 : 0;

                _logger.LogInformation("تم إنشاء تقرير الإنتاج الشهري للشهر {Month}/{Year}", month, year);
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير الإنتاج الشهري للشهر {Month}/{Year}", month, year);
                throw;
            }
        }

        public async Task<PondsReport> GetPondsReportAsync(int? cycleId = null)
        {
            try
            {
                var pondsQuery = cycleId.HasValue ? 
                    await _unitOfWork.Ponds.FindAsync(p => p.CycleId == cycleId.Value) :
                    await _unitOfWork.Ponds.GetAllAsync();

                var ponds = pondsQuery.ToList();

                var report = new PondsReport
                {
                    CycleId = cycleId,
                    TotalPonds = ponds.Count,
                    ActivePonds = ponds.Count(p => p.Status == "نشط"),
                    EmptyPonds = ponds.Count(p => p.Status == "فارغ"),
                    MaintenancePonds = ponds.Count(p => p.Status == "صيانة"),
                    PondSummaries = new List<PondSummary>()
                };

                if (cycleId.HasValue)
                {
                    var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId.Value);
                    report.CycleName = cycle?.CycleName;
                }

                foreach (var pond in ponds)
                {
                    var feedConsumptions = await _unitOfWork.FeedConsumptions
                        .FindAsync(fc => fc.PondId == pond.Id);
                    var mortalities = await _unitOfWork.FishMortalities
                        .FindAsync(fm => fm.PondId == pond.Id);

                    var totalFeedConsumed = feedConsumptions.Sum(fc => fc.Quantity);
                    var totalFeedCost = feedConsumptions.Sum(fc => fc.Quantity * fc.FeedType.PricePerKg);
                    var totalMortality = mortalities.Sum(m => m.DeadFishCount);
                    var totalBiomass = pond.FishCount * pond.AverageWeight;

                    report.PondSummaries.Add(new PondSummary
                    {
                        PondId = pond.Id,
                        PondNumber = pond.PondNumber,
                        Status = pond.Status,
                        FishCount = pond.FishCount,
                        AverageWeight = pond.AverageWeight,
                        TotalBiomass = totalBiomass,
                        StockingDate = pond.StockingDate,
                        ExpectedHarvestDate = pond.ExpectedHarvestDate,
                        DaysInProduction = (DateTime.Now - pond.StockingDate).Days,
                        TotalFeedConsumed = totalFeedConsumed,
                        TotalFeedCost = totalFeedCost,
                        TotalMortality = totalMortality,
                        MortalityRate = pond.FishCount > 0 ? 
                            (decimal)totalMortality / pond.FishCount * 100 : 0,
                        FCR = totalBiomass > 0 ? totalFeedConsumed / totalBiomass : 0
                    });
                }

                _logger.LogInformation("تم إنشاء تقرير الأحواض للدورة {CycleId}", cycleId);
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير الأحواض للدورة {CycleId}", cycleId);
                throw;
            }
        }

        public async Task<FeedConsumptionReport> GetFeedConsumptionReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var feedConsumptions = await _unitOfWork.FeedConsumptions
                    .FindAsync(fc => fc.FeedingDate >= startDate && fc.FeedingDate <= endDate);

                var report = new FeedConsumptionReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    TotalFeedConsumed = feedConsumptions.Sum(fc => fc.Quantity),
                    TotalFeedCost = feedConsumptions.Sum(fc => fc.Quantity * fc.FeedType.PricePerKg),
                    FeedTypeConsumptions = new List<FeedTypeConsumption>(),
                    PondConsumptions = new List<PondFeedConsumption>()
                };

                var days = (endDate - startDate).Days + 1;
                report.AverageDailyConsumption = days > 0 ? report.TotalFeedConsumed / days : 0;

                // Group by feed type
                var feedTypeGroups = feedConsumptions.GroupBy(fc => fc.FeedTypeId);
                foreach (var group in feedTypeGroups)
                {
                    var feedType = group.First().FeedType;
                    var totalQuantity = group.Sum(fc => fc.Quantity);
                    var totalCost = group.Sum(fc => fc.Quantity * fc.FeedType.PricePerKg);

                    report.FeedTypeConsumptions.Add(new FeedTypeConsumption
                    {
                        FeedTypeId = group.Key,
                        FeedName = feedType.FeedName,
                        TotalQuantity = totalQuantity,
                        TotalCost = totalCost,
                        AveragePrice = totalQuantity > 0 ? totalCost / totalQuantity : 0
                    });
                }

                // Group by pond
                var pondGroups = feedConsumptions.GroupBy(fc => fc.PondId);
                foreach (var group in pondGroups)
                {
                    var pond = group.First().Pond;
                    var totalFeedConsumed = group.Sum(fc => fc.Quantity);
                    var totalFeedCost = group.Sum(fc => fc.Quantity * fc.FeedType.PricePerKg);
                    var totalBiomass = pond.FishCount * pond.AverageWeight;

                    report.PondConsumptions.Add(new PondFeedConsumption
                    {
                        PondId = group.Key,
                        PondNumber = pond.PondNumber,
                        TotalFeedConsumed = totalFeedConsumed,
                        TotalFeedCost = totalFeedCost,
                        FCR = totalBiomass > 0 ? totalFeedConsumed / totalBiomass : 0
                    });
                }

                _logger.LogInformation("تم إنشاء تقرير استهلاك العلف للفترة {StartDate} - {EndDate}",
                    startDate, endDate);
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير استهلاك العلف للفترة {StartDate} - {EndDate}",
                    startDate, endDate);
                throw;
            }
        }

        public async Task<FishMortalityReport> GetFishMortalityReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var mortalities = await _unitOfWork.FishMortalities
                    .FindAsync(fm => fm.MortalityDate >= startDate && fm.MortalityDate <= endDate);

                var report = new FishMortalityReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    TotalMortality = mortalities.Sum(m => m.DeadFishCount),
                    TotalEstimatedLoss = mortalities.Sum(m => m.EstimatedLoss),
                    MortalityCauses = new List<MortalityCause>(),
                    PondMortalities = new List<PondMortality>()
                };

                // Calculate average mortality rate
                var totalFish = await _unitOfWork.Ponds.GetAllAsync();
                var totalFishCount = totalFish.Sum(p => p.FishCount);
                report.AverageMortalityRate = totalFishCount > 0 ?
                    (decimal)report.TotalMortality / totalFishCount * 100 : 0;

                // Group by cause
                var causeGroups = mortalities.GroupBy(m => m.Cause);
                foreach (var group in causeGroups)
                {
                    var count = group.Sum(m => m.DeadFishCount);
                    var loss = group.Sum(m => m.EstimatedLoss);

                    report.MortalityCauses.Add(new MortalityCause
                    {
                        Cause = group.Key,
                        Count = count,
                        Percentage = report.TotalMortality > 0 ?
                            (decimal)count / report.TotalMortality * 100 : 0,
                        EstimatedLoss = loss
                    });
                }

                // Group by pond
                var pondGroups = mortalities.GroupBy(m => m.PondId);
                foreach (var group in pondGroups)
                {
                    var pond = group.First().Pond;
                    var totalMortality = group.Sum(m => m.DeadFishCount);
                    var estimatedLoss = group.Sum(m => m.EstimatedLoss);

                    report.PondMortalities.Add(new PondMortality
                    {
                        PondId = group.Key,
                        PondNumber = pond.PondNumber,
                        TotalMortality = totalMortality,
                        MortalityRate = pond.FishCount > 0 ?
                            (decimal)totalMortality / pond.FishCount * 100 : 0,
                        EstimatedLoss = estimatedLoss
                    });
                }

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير نفوق الأسماك");
                throw;
            }
        }

        public async Task<PayrollReport> GetPayrollReportAsync(int month, int year)
        {
            try
            {
                var payrolls = await _unitOfWork.Payrolls.FindAsync(p => p.Month == month && p.Year == year);

                var report = new PayrollReport
                {
                    Month = month,
                    Year = year,
                    TotalEmployees = payrolls.Count(),
                    TotalBaseSalaries = payrolls.Sum(p => p.BaseSalary),
                    TotalAllowances = payrolls.Sum(p => p.Allowances),
                    TotalDeductions = payrolls.Sum(p => p.Deductions),
                    TotalNetSalaries = payrolls.Sum(p => p.NetSalary),
                    EmployeePayrolls = new List<EmployeePayrollSummary>()
                };

                foreach (var payroll in payrolls)
                {
                    report.EmployeePayrolls.Add(new EmployeePayrollSummary
                    {
                        EmployeeId = payroll.EmployeeId,
                        EmployeeName = payroll.Employee.FullName,
                        Position = payroll.Employee.Position,
                        BaseSalary = payroll.BaseSalary,
                        Allowances = payroll.Allowances,
                        Deductions = payroll.Deductions,
                        NetSalary = payroll.NetSalary,
                        WorkingDays = payroll.WorkingDays,
                        OvertimeHours = payroll.OvertimeHours,
                        PaymentStatus = payroll.PaymentStatus
                    });
                }

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير الرواتب");
                throw;
            }
        }

        public async Task<InventoryReport> GetInventoryReportAsync()
        {
            try
            {
                var items = await _unitOfWork.Inventories.FindAsync(i => i.Status == "نشط");

                var report = new InventoryReport
                {
                    ReportDate = DateTime.Now,
                    TotalItems = items.Count(),
                    LowStockItems = items.Count(i => i.Quantity <= i.MinimumStock),
                    ExpiredItems = items.Count(i => i.ExpiryDate.HasValue && i.ExpiryDate.Value < DateTime.Now),
                    TotalInventoryValue = items.Sum(i => i.Quantity * i.UnitPrice),
                    ItemSummaries = new List<InventoryItemSummary>()
                };

                foreach (var item in items)
                {
                    var status = "طبيعي";
                    if (item.ExpiryDate.HasValue && item.ExpiryDate.Value < DateTime.Now)
                        status = "منتهي الصلاحية";
                    else if (item.Quantity <= item.MinimumStock)
                        status = "مخزون منخفض";

                    report.ItemSummaries.Add(new InventoryItemSummary
                    {
                        ItemId = item.Id,
                        ItemName = item.ItemName,
                        ItemType = item.ItemType,
                        CurrentQuantity = item.Quantity,
                        UnitPrice = item.UnitPrice,
                        TotalValue = item.Quantity * item.UnitPrice,
                        MinimumStock = item.MinimumStock,
                        Status = status,
                        ExpiryDate = item.ExpiryDate
                    });
                }

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير المخزون");
                throw;
            }
        }

        public async Task<ProfitabilityReport> GetProfitabilityReportAsync(int cycleId)
        {
            try
            {
                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                if (cycle == null)
                {
                    throw new InvalidOperationException("الدورة غير موجودة");
                }

                var ponds = await _unitOfWork.Ponds.FindAsync(p => p.CycleId == cycleId);
                var totalProduction = ponds.Sum(p => p.FishCount * p.AverageWeight);
                var totalRevenue = totalProduction * 15; // Assuming 15 per kg

                // Calculate costs
                var feedCosts = await CalculateFeedCostsAsync(cycleId);
                var medicationCosts = await CalculateMedicationCostsAsync(cycleId);
                var salaryCosts = await CalculateSalaryCostsAsync(cycleId);

                var totalCosts = feedCosts + medicationCosts + salaryCosts;
                var netProfit = totalRevenue - totalCosts;

                var report = new ProfitabilityReport
                {
                    CycleId = cycleId,
                    CycleName = cycle.CycleName,
                    StartDate = cycle.StartDate,
                    EndDate = cycle.EndDate,
                    TotalRevenue = totalRevenue,
                    TotalCosts = totalCosts,
                    NetProfit = netProfit,
                    ProfitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0,
                    ROI = cycle.BudgetAmount > 0 ? (netProfit / cycle.BudgetAmount) * 100 : 0,
                    CostBreakdown = new CostBreakdown
                    {
                        FeedCosts = feedCosts,
                        MedicationCosts = medicationCosts,
                        SalaryCosts = salaryCosts,
                        UtilityCosts = 0, // Would need separate tracking
                        MaintenanceCosts = 0, // Would need separate tracking
                        OtherCosts = 0
                    }
                };

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير الربحية");
                throw;
            }
        }

        private async Task<decimal> CalculateFeedCostsAsync(int cycleId)
        {
            var feedConsumptions = await _unitOfWork.FeedConsumptions
                .FindAsync(fc => fc.Pond.CycleId == cycleId);
            return feedConsumptions.Sum(fc => fc.Quantity * fc.FeedType.PricePerKg);
        }

        private async Task<decimal> CalculateMedicationCostsAsync(int cycleId)
        {
            var medications = await _unitOfWork.PondMedications
                .FindAsync(pm => pm.Pond.CycleId == cycleId);
            return medications.Sum(pm => pm.Quantity * pm.Medication.PricePerUnit);
        }

        private async Task<decimal> CalculateSalaryCostsAsync(int cycleId)
        {
            var payrolls = await _unitOfWork.Payrolls.FindAsync(p => p.CycleId == cycleId);
            return payrolls.Sum(p => p.NetSalary);
        }

        public async Task<CyclesComparisonReport> GetCyclesComparisonReportAsync(List<int> cycleIds)
        {
            try
            {
                var report = new CyclesComparisonReport
                {
                    CycleComparisons = new List<CycleComparison>()
                };

                foreach (var cycleId in cycleIds)
                {
                    var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                    if (cycle != null)
                    {
                        var ponds = await _unitOfWork.Ponds.FindAsync(p => p.CycleId == cycleId);
                        var totalProduction = ponds.Sum(p => p.FishCount * p.AverageWeight);
                        var totalCosts = await CalculateFeedCostsAsync(cycleId) +
                                       await CalculateMedicationCostsAsync(cycleId) +
                                       await CalculateSalaryCostsAsync(cycleId);
                        var totalRevenue = totalProduction * 15;
                        var netProfit = totalRevenue - totalCosts;

                        var duration = cycle.EndDate.HasValue ?
                            (cycle.EndDate.Value - cycle.StartDate).Days :
                            (DateTime.Now - cycle.StartDate).Days;

                        report.CycleComparisons.Add(new CycleComparison
                        {
                            CycleId = cycleId,
                            CycleName = cycle.CycleName,
                            Duration = duration,
                            TotalProduction = totalProduction,
                            TotalCosts = totalCosts,
                            NetProfit = netProfit,
                            ProfitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0,
                            ProductivityPerPond = ponds.Count() > 0 ? totalProduction / ponds.Count() : 0
                        });
                    }
                }

                // Calculate averages and best performance
                if (report.CycleComparisons.Any())
                {
                    report.AveragePerformance = new CycleComparison
                    {
                        CycleName = "المتوسط",
                        Duration = (int)report.CycleComparisons.Average(c => c.Duration),
                        TotalProduction = report.CycleComparisons.Average(c => c.TotalProduction),
                        TotalCosts = report.CycleComparisons.Average(c => c.TotalCosts),
                        NetProfit = report.CycleComparisons.Average(c => c.NetProfit),
                        ProfitMargin = report.CycleComparisons.Average(c => c.ProfitMargin),
                        ProductivityPerPond = report.CycleComparisons.Average(c => c.ProductivityPerPond)
                    };

                    var bestCycle = report.CycleComparisons.OrderByDescending(c => c.ProfitMargin).First();
                    report.BestPerformance = bestCycle;
                }

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير مقارنة الدورات");
                throw;
            }
        }

        public async Task<CashFlowReport> GetCashFlowReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var report = new CashFlowReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    Inflows = new List<CashFlowItem>(),
                    Outflows = new List<CashFlowItem>()
                };

                // This would typically come from a separate financial transactions table
                // For now, we'll calculate based on available data

                // Outflows - Salaries
                var payrolls = await _unitOfWork.Payrolls.FindAsync(p =>
                    new DateTime(p.Year, p.Month, 1) >= startDate &&
                    new DateTime(p.Year, p.Month, 1) <= endDate);

                foreach (var payroll in payrolls)
                {
                    report.Outflows.Add(new CashFlowItem
                    {
                        Date = payroll.PaymentDate ?? new DateTime(payroll.Year, payroll.Month, 1),
                        Description = $"راتب {payroll.Employee.FullName}",
                        Category = "رواتب",
                        Amount = payroll.NetSalary,
                        Reference = $"PAY-{payroll.Id}"
                    });
                }

                // Outflows - Feed costs (estimated based on consumption)
                var feedConsumptions = await _unitOfWork.FeedConsumptions
                    .FindAsync(fc => fc.FeedingDate >= startDate && fc.FeedingDate <= endDate);

                var feedCostsByDate = feedConsumptions
                    .GroupBy(fc => fc.FeedingDate.Date)
                    .Select(g => new
                    {
                        Date = g.Key,
                        TotalCost = g.Sum(fc => fc.Quantity * fc.FeedType.PricePerKg)
                    });

                foreach (var feedCost in feedCostsByDate)
                {
                    report.Outflows.Add(new CashFlowItem
                    {
                        Date = feedCost.Date,
                        Description = "شراء علف",
                        Category = "علف",
                        Amount = feedCost.TotalCost,
                        Reference = $"FEED-{feedCost.Date:yyyyMMdd}"
                    });
                }

                // Calculate totals
                report.TotalInflows = report.Inflows.Sum(i => i.Amount);
                report.TotalOutflows = report.Outflows.Sum(o => o.Amount);
                report.NetCashFlow = report.TotalInflows - report.TotalOutflows;
                report.ClosingBalance = report.OpeningBalance + report.NetCashFlow;

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير التدفق النقدي");
                throw;
            }
        }

        public async Task<byte[]> ExportReportToPdfAsync<T>(T report, string reportTitle) where T : class
        {
            try
            {
                // This would typically use a PDF generation library like iTextSharp or similar
                // For now, returning empty byte array as placeholder
                _logger.LogInformation("تصدير تقرير {ReportTitle} إلى PDF", reportTitle);
                return new byte[0];
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير التقرير إلى PDF");
                throw;
            }
        }

        public async Task<byte[]> ExportReportToExcelAsync<T>(T report, string reportTitle) where T : class
        {
            try
            {
                // This would typically use a library like EPPlus or ClosedXML
                // For now, returning empty byte array as placeholder
                _logger.LogInformation("تصدير تقرير {ReportTitle} إلى Excel", reportTitle);
                return new byte[0];
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير التقرير إلى Excel");
                throw;
            }
        }
    }
}
