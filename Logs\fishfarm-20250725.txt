2025-07-25 00:23:23.197 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
System.IO.FileNotFoundException: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'D:\account pro\fish accounting & management\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.HandleException(ExceptionDispatchInfo info)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load()
   at Microsoft.Extensions.Configuration.ConfigurationRoot..ctor(IList`1 providers)
   at Microsoft.Extensions.Configuration.ConfigurationBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 37
2025-07-25 00:28:04.414 +03:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-25 00:28:04.541 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-25 00:28:04.562 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:28:04.567 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:28:04.587 +03:00 [INF] Applying migration '20250724205802_InitialCreate'.
2025-07-25 00:28:04.691 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:28:04.691 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:28:04.692 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:28:04.692 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:28:04.692 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:28:04.692 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:28:04.692 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:28:04.692 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:28:04.692 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE CASCADE
);
2025-07-25 00:28:04.693 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:28:04.693 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:28:04.693 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:28:04.693 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:28:04.693 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-25 00:28:04.693 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-25 00:28:04.693 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-25 00:28:04.694 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-25 00:28:04.694 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-24 23:58:00.3543764', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-24 23:58:00.3543765', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-24 23:58:00.3543765', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-24 23:58:00.3543766', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-24 23:58:00.3543766', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-25 00:28:04.694 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-24 23:58:00.3543917', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-25 00:28:04.694 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-24 23:58:00.3543858', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-24 23:58:00.3543858', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-24 23:58:00.3543859', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-24 23:58:00.3543859', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-24 23:58:00.3543861', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-24 23:58:00.3543861', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-24 23:58:00.3543863', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-25 00:28:04.694 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-25 00:28:04.695 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-25 00:28:04.695 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-25 00:28:04.695 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-25 00:28:04.695 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-25 00:28:04.695 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-25 00:28:04.695 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-25 00:28:04.695 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-25 00:28:04.695 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-25 00:28:04.695 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-25 00:28:04.696 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId" ON "InventoryMovements" ("ItemId");
2025-07-25 00:28:04.696 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-25 00:28:04.696 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-25 00:28:04.696 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-25 00:28:04.696 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-25 00:28:04.696 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-25 00:28:04.696 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-25 00:28:04.696 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-25 00:28:04.697 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-25 00:28:04.697 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-25 00:28:04.697 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-25 00:28:04.697 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-25 00:28:04.697 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724205802_InitialCreate', '8.0.0');
2025-07-25 00:28:04.709 +03:00 [INF] Applying migration '20250724211144_AddUserManagement'.
2025-07-25 00:28:04.762 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP INDEX "IX_InventoryMovements_ItemId";
2025-07-25 00:28:04.763 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:28:04.763 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:28:04.763 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-25 00:28:04.763 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100123'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 00:28:04.763 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100129'
WHERE "Id" = 2;
SELECT changes();
2025-07-25 00:28:04.763 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.810013'
WHERE "Id" = 3;
SELECT changes();
2025-07-25 00:28:04.763 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.810013'
WHERE "Id" = 4;
SELECT changes();
2025-07-25 00:28:04.763 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100131'
WHERE "Id" = 5;
SELECT changes();
2025-07-25 00:28:04.763 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100494'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 00:28:04.763 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100496'
WHERE "Id" = 2;
SELECT changes();
2025-07-25 00:28:04.763 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100506'
WHERE "Id" = 3;
SELECT changes();
2025-07-25 00:28:04.764 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100544'
WHERE "Id" = 4;
SELECT changes();
2025-07-25 00:28:04.764 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100545'
WHERE "Id" = 5;
SELECT changes();
2025-07-25 00:28:04.764 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100546'
WHERE "Id" = 6;
SELECT changes();
2025-07-25 00:28:04.764 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100547'
WHERE "Id" = 7;
SELECT changes();
2025-07-25 00:28:04.764 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100547'
WHERE "Id" = 8;
SELECT changes();
2025-07-25 00:28:04.764 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100548'
WHERE "Id" = 9;
SELECT changes();
2025-07-25 00:28:04.764 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100549'
WHERE "Id" = 10;
SELECT changes();
2025-07-25 00:28:04.764 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100549'
WHERE "Id" = 11;
SELECT changes();
2025-07-25 00:28:04.764 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.810055'
WHERE "Id" = 12;
SELECT changes();
2025-07-25 00:28:04.764 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100551'
WHERE "Id" = 13;
SELECT changes();
2025-07-25 00:28:04.764 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "FarmInfos" SET "CreatedDate" = '2025-07-25 00:11:43.8100734'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 00:28:04.764 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-25 00:11:43.8102126', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-25 00:11:43.8102335', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-25 00:11:43.8102376', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-25 00:11:43.810241', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-25 00:11:43.8102437', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-25 00:28:04.765 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-25 00:11:43.8102634', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, NULL, '$2a$11$xajPljvN0VMOVwgwxmR0vetvCHYroT46IZNhyYQDBHYuv1GqGxzKS', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-25 00:28:04.765 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-25 00:11:43.960912', NULL, 'System', '2025-07-25 00:11:43.9609117', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-25 00:28:04.765 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-25 00:28:04.765 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-25 00:28:04.765 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-25 00:28:04.765 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-25 00:28:04.765 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-25 00:28:04.765 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-25 00:28:04.765 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-25 00:28:04.766 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ef_temp_InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "BatchNumber" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "Reference" TEXT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:28:04.766 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "ef_temp_InventoryMovements" ("Id", "BatchNumber", "CreatedBy", "CreatedDate", "ItemId", "MovementDate", "MovementType", "Notes", "Quantity", "QuantityAfter", "QuantityBefore", "Reason", "Reference", "TotalValue", "UnitPrice", "UpdatedDate")
SELECT "Id", "BatchNumber", "CreatedBy", "CreatedDate", "ItemId", "MovementDate", "MovementType", "Notes", "Quantity", "QuantityAfter", "QuantityBefore", "Reason", "Reference", "TotalValue", "UnitPrice", "UpdatedDate"
FROM "InventoryMovements";
2025-07-25 00:28:04.772 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA foreign_keys = 0;
2025-07-25 00:28:04.773 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP TABLE "InventoryMovements";
2025-07-25 00:28:04.774 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "ef_temp_InventoryMovements" RENAME TO "InventoryMovements";
2025-07-25 00:28:04.778 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA foreign_keys = 1;
2025-07-25 00:28:04.778 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-25 00:28:04.779 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-25 00:28:04.779 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724211144_AddUserManagement', '8.0.0');
2025-07-25 00:28:04.786 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-25 00:28:04.786 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-25 00:28:26.144 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:28:26.412 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:28:26.435 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:28:40.825 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:28:40.954 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:28:40.959 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:32:14.318 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:32:14.360 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:32:14.372 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:32:14.389 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-25 00:32:14.392 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-25 00:32:14.392 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-25 00:32:31.810 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:32:32.066 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:32:32.088 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:36:11.707 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:36:11.745 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:36:11.758 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:36:11.776 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-25 00:36:11.780 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-25 00:36:11.986 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-25 00:36:12.064 +03:00 [INF] المستخدم الافتراضي موجود بالفعل
2025-07-25 00:36:12.064 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-25 00:36:35.494 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:36:35.694 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:36:35.721 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:36:51.011 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:36:51.142 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:36:51.147 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:36:55.369 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:36:55.502 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:36:55.507 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:40:32.308 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:40:32.344 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:40:32.357 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:40:32.374 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-25 00:40:32.376 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-25 00:40:32.569 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-25 00:40:32.634 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-25 00:40:32.661 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-25 00:40:32.661 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-25 00:40:32.661 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-25 00:40:46.525 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:40:46.792 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:40:46.815 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:41:02.525 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:41:02.670 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:41:02.676 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:41:44.396 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:41:44.440 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:41:44.453 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:41:44.469 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-25 00:41:44.470 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-25 00:41:44.710 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-25 00:41:44.795 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-25 00:41:44.819 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-25 00:41:44.820 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-25 00:41:44.820 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-25 00:41:53.401 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:41:53.809 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:41:53.836 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:42:05.852 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:42:06.123 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:42:06.128 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:44:39.124 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:44:39.182 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:44:39.204 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:44:39.221 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-25 00:44:39.223 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-25 00:44:39.424 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-25 00:44:39.504 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-25 00:44:39.534 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-25 00:44:39.535 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-25 00:44:39.535 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-25 00:44:51.595 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:44:51.827 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:44:51.858 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:45:28.974 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:45:29.113 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:45:29.119 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:46:01.936 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:46:02.066 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:46:02.070 +03:00 [WRN] محاولة دخول فاشلة - كلمة مرور خاطئة: admin
2025-07-25 00:46:26.014 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:46:26.144 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:46:26.148 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:50:25.875 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:50:25.911 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:50:25.924 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:50:25.943 +03:00 [INF] Applying migration '20250724214950_InitialCreate'.
2025-07-25 00:50:26.093 +03:00 [ERR] Failed executing DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:50:26.098 +03:00 [ERR] خطأ في تهيئة قاعدة البيانات
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'table "AccountTypes" already exists'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 149
2025-07-25 00:50:26.113 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'table "AccountTypes" already exists'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 149
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 47
2025-07-25 00:50:49.495 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:50:49.529 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:50:49.543 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:50:49.562 +03:00 [INF] Applying migration '20250724214950_InitialCreate'.
2025-07-25 00:50:49.700 +03:00 [ERR] Failed executing DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:50:49.706 +03:00 [ERR] خطأ في تهيئة قاعدة البيانات
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'table "AccountTypes" already exists'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 149
2025-07-25 00:50:49.721 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'table "AccountTypes" already exists'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 149
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 47
2025-07-25 03:06:02.055 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 03:06:02.095 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 03:06:02.111 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 03:06:02.130 +03:00 [INF] Applying migration '20250724214950_InitialCreate'.
2025-07-25 03:06:02.391 +03:00 [ERR] Failed executing DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:06:02.401 +03:00 [ERR] خطأ في تهيئة قاعدة البيانات
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'table "AccountTypes" already exists'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 165
2025-07-25 03:06:02.542 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'table "AccountTypes" already exists'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 165
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 48
2025-07-25 03:07:16.200 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 03:07:16.240 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 03:07:16.253 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 03:07:16.274 +03:00 [INF] Applying migration '20250724214950_InitialCreate'.
2025-07-25 03:07:16.398 +03:00 [ERR] Failed executing DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:07:16.402 +03:00 [ERR] خطأ في تهيئة قاعدة البيانات
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'table "AccountTypes" already exists'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 165
2025-07-25 03:07:16.417 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'table "AccountTypes" already exists'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 165
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 48
2025-07-25 03:24:46.034 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 03:24:46.076 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 03:24:46.094 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 03:24:46.132 +03:00 [INF] Applying migration '20250725002348_InitialCreate'.
2025-07-25 03:24:46.343 +03:00 [ERR] Failed executing DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:24:46.349 +03:00 [ERR] خطأ في تهيئة قاعدة البيانات
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'table "AccountTypes" already exists'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 165
2025-07-25 03:24:46.369 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'table "AccountTypes" already exists'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 165
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 48
2025-07-25 03:26:00.778 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 03:26:00.863 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 03:26:00.900 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 03:26:00.966 +03:00 [INF] Applying migration '20250725002348_InitialCreate'.
2025-07-25 03:26:01.304 +03:00 [ERR] Failed executing DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:26:01.321 +03:00 [ERR] خطأ في تهيئة قاعدة البيانات
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'table "AccountTypes" already exists'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 165
2025-07-25 03:26:01.358 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'table "AccountTypes" already exists'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 165
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 48
