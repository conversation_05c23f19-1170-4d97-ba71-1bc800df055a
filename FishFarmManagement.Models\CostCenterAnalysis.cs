using System.ComponentModel.DataAnnotations;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// تحليل مركز التكلفة
    /// Cost center analysis
    /// </summary>
    public class CostCenterAnalysis
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// اسم مركز التكلفة
        /// Cost center name
        /// </summary>
        public string CostCenterName { get; set; } = string.Empty;

        /// <summary>
        /// كود مركز التكلفة
        /// Cost center code
        /// </summary>
        public string CostCenterCode { get; set; } = string.Empty;

        /// <summary>
        /// التكلفة الفعلية
        /// Actual cost
        /// </summary>
        public decimal ActualCost { get; set; }

        /// <summary>
        /// التكلفة المخططة
        /// Planned cost
        /// </summary>
        public decimal PlannedCost { get; set; }

        /// <summary>
        /// الانحراف
        /// Variance
        /// </summary>
        public decimal Variance => ActualCost - PlannedCost;

        /// <summary>
        /// نسبة الانحراف
        /// Variance percentage
        /// </summary>
        public decimal VariancePercentage => PlannedCost > 0 ? (Variance / PlannedCost) * 100 : 0;

        /// <summary>
        /// تاريخ البداية
        /// Start date
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// تاريخ النهاية
        /// End date
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// المعاملات المرتبطة
        /// Related transactions
        /// </summary>
        public List<TransactionDetail> Transactions { get; set; } = new List<TransactionDetail>();

        /// <summary>
        /// تفاصيل التكلفة
        /// Cost details
        /// </summary>
        public List<CostCenterDetail> CostDetails { get; set; } = new List<CostCenterDetail>();

        /// <summary>
        /// الحالة
        /// Status
        /// </summary>
        public string Status { get; set; } = "نشط";

        /// <summary>
        /// الوصف
        /// Description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// Creation date
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// المستخدم المنشئ
        /// Created by user
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// تفاصيل مركز التكلفة
    /// Cost center detail
    /// </summary>
    public class CostCenterDetail
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// معرف تحليل مركز التكلفة
        /// Cost center analysis ID
        /// </summary>
        public int CostCenterAnalysisId { get; set; }

        /// <summary>
        /// تحليل مركز التكلفة
        /// Cost center analysis
        /// </summary>
        public CostCenterAnalysis CostCenterAnalysis { get; set; } = null!;

        /// <summary>
        /// نوع التكلفة
        /// Cost type
        /// </summary>
        public string CostType { get; set; } = string.Empty;

        /// <summary>
        /// المبلغ
        /// Amount
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// النسبة من إجمالي التكلفة
        /// Percentage of total cost
        /// </summary>
        public decimal Percentage { get; set; }

        /// <summary>
        /// الوصف
        /// Description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// تاريخ التسجيل
        /// Record date
        /// </summary>
        public DateTime RecordDate { get; set; } = DateTime.Now;
    }
}
