@echo off
echo ========================================
echo Fish Farm Management System v1.0.1
echo ========================================
echo.

echo [1/5] Checking .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not installed
    echo Please install .NET 8.0 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

echo .NET SDK found
for /f "tokens=*" %%i in ('dotnet --version') do echo Version: %%i
echo.

echo [2/5] Restoring packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages
    pause
    exit /b 1
)

echo [3/5] Building project...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ERROR: Failed to build project
    pause
    exit /b 1
)

echo [4/5] Checking executable...
if exist "FishFarmManagement\bin\Release\net8.0-windows\FishFarmManagement.exe" (
    echo Executable found
) else (
    echo ERROR: Executable not created
    pause
    exit /b 1
)

echo [5/5] Running program...
echo.
echo ========================================
echo          Starting Program...
echo ========================================
echo.
echo Default login credentials:
echo Username: admin
echo Password: Admin123!
echo.
echo A 30-day trial license will be created automatically
echo.

cd "FishFarmManagement\bin\Release\net8.0-windows"
start FishFarmManagement.exe

echo Program started successfully!
echo If the program doesn't appear, check Windows Defender or antivirus software
echo.
pause
