﻿using System;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.Forms
{
    public partial class DatabaseOptimizationForm : Form
    {
        private readonly ILogger<DatabaseOptimizationForm> _logger;
        private ProgressBar progressBar;
        private Label statusLabel;
        private RichTextBox logTextBox;
        private Button optimizeButton;
        private Button vacuumButton;
        private Button reindexButton;
        private Button analyzeButton;
        private Button closeButton;
        private Label dbSizeLabel;
        private Label lastOptimizedLabel;

        public DatabaseOptimizationForm(ILogger<DatabaseOptimizationForm> logger)
        {
            _logger = logger;
            InitializeComponent();
            LoadDatabaseInfo();
        }

        private void InitializeComponent()
        {
            this.Text = "تحسين قاعدة البيانات";
            this.Size = new Size(700, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(52, 73, 94),
                Padding = new Padding(20, 10, 20, 10)
            };

            var titleLabel = new Label
            {
                Text = "تحسين قاعدة البيانات",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 25)
            };

            headerPanel.Controls.Add(titleLabel);

            // Database info panel
            var infoPanel = new Panel
            {
                Height = 100,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(236, 240, 241),
                Padding = new Padding(20, 15, 20, 15)
            };

            var infoTitleLabel = new Label
            {
                Text = "معلومات قاعدة البيانات",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(20, 15)
            };

            dbSizeLabel = new Label
            {
                Text = "حجم قاعدة البيانات: جاري التحميل...",
                AutoSize = true,
                Location = new Point(20, 45),
                Font = new Font("Segoe UI", 10F)
            };

            lastOptimizedLabel = new Label
            {
                Text = "آخر تحسين: غير محدد",
                AutoSize = true,
                Location = new Point(20, 70),
                Font = new Font("Segoe UI", 10F)
            };

            infoPanel.Controls.AddRange(new Control[] { infoTitleLabel, dbSizeLabel, lastOptimizedLabel });

            // Operations panel
            var operationsPanel = new Panel
            {
                Height = 120,
                Dock = DockStyle.Top,
                Padding = new Padding(20, 15, 20, 15)
            };

            var operationsTitleLabel = new Label
            {
                Text = "عمليات التحسين",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(20, 15)
            };

            optimizeButton = new Button
            {
                Text = "تحسين شامل",
                Size = new Size(120, 35),
                Location = new Point(20, 45),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            optimizeButton.Click += OptimizeButton_Click;

            vacuumButton = new Button
            {
                Text = "ضغط البيانات",
                Size = new Size(120, 35),
                Location = new Point(150, 45),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            vacuumButton.Click += VacuumButton_Click;

            reindexButton = new Button
            {
                Text = "إعادة فهرسة",
                Size = new Size(120, 35),
                Location = new Point(280, 45),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            reindexButton.Click += ReindexButton_Click;

            analyzeButton = new Button
            {
                Text = "تحليل الجداول",
                Size = new Size(120, 35),
                Location = new Point(410, 45),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            analyzeButton.Click += AnalyzeButton_Click;

            operationsPanel.Controls.AddRange(new Control[] 
            { 
                operationsTitleLabel, optimizeButton, vacuumButton, reindexButton, analyzeButton 
            });

            // Progress panel
            var progressPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                Padding = new Padding(20, 15, 20, 15)
            };

            var progressTitleLabel = new Label
            {
                Text = "حالة العملية",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(20, 15)
            };

            progressBar = new ProgressBar
            {
                Size = new Size(500, 20),
                Location = new Point(20, 45),
                Visible = false
            };

            statusLabel = new Label
            {
                Text = "جاهز",
                AutoSize = true,
                Location = new Point(530, 47),
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.FromArgb(46, 204, 113)
            };

            progressPanel.Controls.AddRange(new Control[] { progressTitleLabel, progressBar, statusLabel });

            // Log panel
            var logPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20, 15, 20, 15)
            };

            var logTitleLabel = new Label
            {
                Text = "سجل العمليات",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(20, 15)
            };

            logTextBox = new RichTextBox
            {
                Size = new Size(620, 200),
                Location = new Point(20, 45),
                Font = new Font("Consolas", 9F),
                ReadOnly = true,
                BackColor = Color.FromArgb(44, 62, 80),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            logPanel.Controls.AddRange(new Control[] { logTitleLabel, logTextBox });

            // Buttons panel
            var buttonsPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Bottom,
                Padding = new Padding(20, 15, 20, 15)
            };

            closeButton = new Button
            {
                Text = "إغلاق",
                Size = new Size(100, 30),
                Location = new Point(540, 15),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            closeButton.Click += (s, e) => this.Close();

            buttonsPanel.Controls.Add(closeButton);

            this.Controls.AddRange(new Control[] 
            { 
                logPanel, progressPanel, operationsPanel, infoPanel, headerPanel, buttonsPanel 
            });
        }

        private void LoadDatabaseInfo()
        {
            try
            {
                var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "FishFarmDatabase.db");
                
                if (File.Exists(dbPath))
                {
                    var fileInfo = new FileInfo(dbPath);
                    var sizeInMB = fileInfo.Length / (1024.0 * 1024.0);
                    dbSizeLabel.Text = $"حجم قاعدة البيانات: {sizeInMB:F2} ميجابايت";
                    
                    var lastModified = fileInfo.LastWriteTime;
                    lastOptimizedLabel.Text = $"آخر تعديل: {lastModified:dd/MM/yyyy HH:mm}";
                }
                else
                {
                    dbSizeLabel.Text = "حجم قاعدة البيانات: غير موجودة";
                    lastOptimizedLabel.Text = "آخر تحسين: غير محدد";
                }

                AddLogEntry("تم تحميل معلومات قاعدة البيانات", Color.FromArgb(46, 204, 113));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل معلومات قاعدة البيانات");
                AddLogEntry($"خطأ في تحميل معلومات قاعدة البيانات: {ex.Message}", Color.FromArgb(231, 76, 60));
            }
        }

        private async void OptimizeButton_Click(object sender, EventArgs e)
        {
            await PerformOptimization("تحسين شامل", async () =>
            {
                AddLogEntry("بدء التحسين الشامل لقاعدة البيانات...", Color.FromArgb(52, 152, 219));
                
                // Simulate vacuum operation
                UpdateProgress(25, "ضغط البيانات...");
                await Task.Delay(2000);
                AddLogEntry("تم ضغط البيانات بنجاح", Color.FromArgb(46, 204, 113));
                
                // Simulate reindex operation
                UpdateProgress(50, "إعادة فهرسة الجداول...");
                await Task.Delay(2000);
                AddLogEntry("تم إعادة فهرسة الجداول بنجاح", Color.FromArgb(46, 204, 113));
                
                // Simulate analyze operation
                UpdateProgress(75, "تحليل إحصائيات الجداول...");
                await Task.Delay(2000);
                AddLogEntry("تم تحليل إحصائيات الجداول بنجاح", Color.FromArgb(46, 204, 113));
                
                // Simulate cleanup
                UpdateProgress(100, "تنظيف الملفات المؤقتة...");
                await Task.Delay(1000);
                AddLogEntry("تم تنظيف الملفات المؤقتة بنجاح", Color.FromArgb(46, 204, 113));
                
                AddLogEntry("تم إكمال التحسين الشامل بنجاح", Color.FromArgb(46, 204, 113));
            });
        }

        private async void VacuumButton_Click(object sender, EventArgs e)
        {
            await PerformOptimization("ضغط البيانات", async () =>
            {
                AddLogEntry("بدء عملية ضغط البيانات...", Color.FromArgb(52, 152, 219));
                
                UpdateProgress(50, "ضغط البيانات...");
                await Task.Delay(3000);
                
                AddLogEntry("تم ضغط البيانات بنجاح", Color.FromArgb(46, 204, 113));
                AddLogEntry("تم توفير مساحة تخزين إضافية", Color.FromArgb(46, 204, 113));
            });
        }

        private async void ReindexButton_Click(object sender, EventArgs e)
        {
            await PerformOptimization("إعادة فهرسة", async () =>
            {
                AddLogEntry("بدء عملية إعادة الفهرسة...", Color.FromArgb(52, 152, 219));
                
                UpdateProgress(33, "فهرسة جدول الأحواض...");
                await Task.Delay(1000);
                AddLogEntry("تم فهرسة جدول الأحواض", Color.FromArgb(46, 204, 113));
                
                UpdateProgress(66, "فهرسة جدول الموظفين...");
                await Task.Delay(1000);
                AddLogEntry("تم فهرسة جدول الموظفين", Color.FromArgb(46, 204, 113));
                
                UpdateProgress(100, "فهرسة الجداول المالية...");
                await Task.Delay(1000);
                AddLogEntry("تم فهرسة الجداول المالية", Color.FromArgb(46, 204, 113));
                
                AddLogEntry("تم إكمال إعادة الفهرسة بنجاح", Color.FromArgb(46, 204, 113));
            });
        }

        private async void AnalyzeButton_Click(object sender, EventArgs e)
        {
            await PerformOptimization("تحليل الجداول", async () =>
            {
                AddLogEntry("بدء تحليل إحصائيات الجداول...", Color.FromArgb(52, 152, 219));
                
                UpdateProgress(25, "تحليل جداول الإنتاج...");
                await Task.Delay(1500);
                AddLogEntry("تم تحليل جداول الإنتاج - 15 جدول", Color.FromArgb(46, 204, 113));
                
                UpdateProgress(50, "تحليل الجداول المالية...");
                await Task.Delay(1500);
                AddLogEntry("تم تحليل الجداول المالية - 8 جداول", Color.FromArgb(46, 204, 113));
                
                UpdateProgress(75, "تحليل جداول الموظفين...");
                await Task.Delay(1500);
                AddLogEntry("تم تحليل جداول الموظفين - 5 جداول", Color.FromArgb(46, 204, 113));
                
                UpdateProgress(100, "تحديث الإحصائيات...");
                await Task.Delay(1000);
                AddLogEntry("تم تحديث إحصائيات قاعدة البيانات", Color.FromArgb(46, 204, 113));
                
                AddLogEntry("تم إكمال تحليل الجداول بنجاح", Color.FromArgb(46, 204, 113));
            });
        }

        private async Task PerformOptimization(string operationName, Func<Task> operation)
        {
            try
            {
                // Disable buttons
                SetButtonsEnabled(false);
                
                // Show progress
                progressBar.Visible = true;
                progressBar.Value = 0;
                statusLabel.Text = $"جاري {operationName}...";
                statusLabel.ForeColor = Color.FromArgb(52, 152, 219);
                
                // Perform operation
                await operation();
                
                // Complete
                progressBar.Value = 100;
                statusLabel.Text = $"تم إكمال {operationName} بنجاح";
                statusLabel.ForeColor = Color.FromArgb(46, 204, 113);
                
                // Update database info
                LoadDatabaseInfo();
                
                _logger.LogInformation($"تم إكمال {operationName} بنجاح");
            }
            catch (Exception ex)
            {
                statusLabel.Text = $"فشل في {operationName}";
                statusLabel.ForeColor = Color.FromArgb(231, 76, 60);
                AddLogEntry($"خطأ في {operationName}: {ex.Message}", Color.FromArgb(231, 76, 60));
                _logger.LogError(ex, $"خطأ في {operationName}");
            }
            finally
            {
                // Hide progress and enable buttons
                progressBar.Visible = false;
                SetButtonsEnabled(true);
            }
        }

        private void UpdateProgress(int value, string status)
        {
            progressBar.Value = value;
            statusLabel.Text = status;
            Application.DoEvents();
        }

        private void SetButtonsEnabled(bool enabled)
        {
            optimizeButton.Enabled = enabled;
            vacuumButton.Enabled = enabled;
            reindexButton.Enabled = enabled;
            analyzeButton.Enabled = enabled;
        }

        private void AddLogEntry(string message, Color color)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var logEntry = $"[{timestamp}] {message}\n";
            
            logTextBox.SelectionStart = logTextBox.TextLength;
            logTextBox.SelectionLength = 0;
            logTextBox.SelectionColor = color;
            logTextBox.AppendText(logEntry);
            logTextBox.ScrollToCaret();
        }
    }
}

