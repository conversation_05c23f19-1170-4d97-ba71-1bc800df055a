using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة إدارة الأعلاف
    /// Feed management service
    /// </summary>
    public class FeedService : IFeedService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<FeedService> _logger;

        public FeedService(IUnitOfWork unitOfWork, ILogger<FeedService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }
    }
}