using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// المستخدم
    /// User
    /// </summary>
    public class User : BaseEntity
    {
        /// <summary>
        /// اسم المستخدم
        /// Username
        /// </summary>
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 50 حرف")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// البريد الإلكتروني
        /// Email
        /// </summary>
        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// كلمة المرور المشفرة
        /// Encrypted password
        /// </summary>
        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// الاسم الكامل
        /// Full name
        /// </summary>
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم الكامل يجب أن يكون أقل من 100 حرف")]
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// رقم الهاتف
        /// Phone number
        /// </summary>
        [StringLength(20)]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// حالة المستخدم
        /// User status
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = UserStatus.Active;

        /// <summary>
        /// هل المستخدم مدير نظام
        /// Is system administrator
        /// </summary>
        public bool IsSystemAdmin { get; set; } = false;

        /// <summary>
        /// هل يجب على المستخدم تغيير كلمة المرور
        /// Must change password on next login
        /// </summary>
        public bool MustChangePassword { get; set; } = false;

        /// <summary>
        /// آخر تسجيل دخول
        /// Last login
        /// </summary>
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        /// عدد محاولات تسجيل الدخول الفاشلة
        /// Failed login attempts count
        /// </summary>
        public int FailedLoginAttempts { get; set; } = 0;

        /// <summary>
        /// تاريخ قفل الحساب
        /// Account locked until
        /// </summary>
        public DateTime? LockedUntil { get; set; }

        /// <summary>
        /// ملاحظات
        /// Notes
        /// </summary>
        [StringLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// معرف الدور
        /// Role ID
        /// </summary>
        public int? RoleId { get; set; }

        /// <summary>
        /// الدور
        /// Role
        /// </summary>
        [ForeignKey("RoleId")]
        public virtual Role? Role { get; set; }

        /// <summary>
        /// التحقق من صحة كلمة المرور
        /// Verify password
        /// </summary>
        public bool VerifyPassword(string password)
        {
            return BCrypt.Net.BCrypt.Verify(password, PasswordHash);
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// Hash password
        /// </summary>
        public void SetPassword(string password)
        {
            // تحسين: زيادة قوة التشفير من 10 إلى 12
            PasswordHash = BCrypt.Net.BCrypt.HashPassword(password, 12);
        }

        /// <summary>
        /// التحقق من أن الحساب غير مقفل
        /// Check if account is not locked
        /// </summary>
        public bool IsAccountLocked()
        {
            return LockedUntil.HasValue && LockedUntil.Value > DateTime.Now;
        }

        /// <summary>
        /// التحقق من قوة كلمة المرور
        /// Check password strength
        /// </summary>
        public static bool IsStrongPassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password) || password.Length < 8)
                return false;

            bool hasUpper = password.Any(char.IsUpper);
            bool hasLower = password.Any(char.IsLower);
            bool hasDigit = password.Any(char.IsDigit);
            bool hasSpecial = password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c));

            return hasUpper && hasLower && hasDigit && hasSpecial;
        }

        /// <summary>
        /// قفل الحساب
        /// Lock account
        /// </summary>
        public void LockAccount(int lockoutMinutes = 30)
        {
            LockedUntil = DateTime.Now.AddMinutes(lockoutMinutes);
        }

        /// <summary>
        /// إلغاء قفل الحساب
        /// Unlock account
        /// </summary>
        public void UnlockAccount()
        {
            LockedUntil = null;
            FailedLoginAttempts = 0;
        }

        /// <summary>
        /// تسجيل محاولة دخول فاشلة
        /// Record failed login attempt
        /// </summary>
        public void RecordFailedLogin()
        {
            FailedLoginAttempts++;
            if (FailedLoginAttempts >= 3)
            {
                LockAccount();
            }
        }

        /// <summary>
        /// تسجيل دخول ناجح
        /// Record successful login
        /// </summary>
        public void RecordSuccessfulLogin()
        {
            LastLoginDate = DateTime.Now;
            FailedLoginAttempts = 0;
            LockedUntil = null;
        }
    }

    /// <summary>
    /// ثوابت حالات المستخدم
    /// User status constants
    /// </summary>
    public static class UserStatus
    {
        public const string Active = "نشط";
        public const string Inactive = "غير نشط";
        public const string Suspended = "موقوف";
        public const string Deleted = "محذوف";
    }
}
