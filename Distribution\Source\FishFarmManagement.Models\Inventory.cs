using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// المخزون
    /// Inventory
    /// </summary>
    public class Inventory : BaseEntity
    {
        [Required(ErrorMessage = "اسم الصنف مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الصنف يجب أن يكون أقل من 100 حرف")]
        public string ItemName { get; set; } = string.Empty;

        [Required(ErrorMessage = "نوع الصنف مطلوب")]
        [StringLength(50, ErrorMessage = "نوع الصنف يجب أن يكون أقل من 50 حرف")]
        public string ItemType { get; set; } = string.Empty; // علف، دواء، معدات، إلخ

        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من أو تساوي الصفر")]
        [Column(TypeName = "decimal(10,3)")]
        public decimal Quantity { get; set; }

        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(15,2)")]
        public decimal TotalValue { get; set; }

        [Required(ErrorMessage = "وحدة القياس مطلوبة")]
        [StringLength(20, ErrorMessage = "وحدة القياس يجب أن تكون أقل من 20 حرف")]
        public string Unit { get; set; } = string.Empty; // كيلو، لتر، قطعة، إلخ

        /// <summary>
        /// الحد الأدنى للمخزون
        /// Minimum stock level
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الحد الأدنى يجب أن يكون أكبر من أو يساوي الصفر")]
        [Column(TypeName = "decimal(10,3)")]
        public decimal MinimumStock { get; set; }

        /// <summary>
        /// الحد الأقصى للمخزون
        /// Maximum stock level
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الحد الأقصى يجب أن يكون أكبر من أو يساوي الصفر")]
        [Column(TypeName = "decimal(10,3)")]
        public decimal MaximumStock { get; set; }

        /// <summary>
        /// نقطة إعادة الطلب
        /// Reorder point
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "نقطة إعادة الطلب يجب أن تكون أكبر من أو تساوي الصفر")]
        [Column(TypeName = "decimal(10,3)")]
        public decimal ReorderPoint { get; set; }

        /// <summary>
        /// الموقع في المخزن
        /// Storage location
        /// </summary>
        [StringLength(100)]
        public string StorageLocation { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ انتهاء الصلاحية
        /// Expiry date
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// رقم الدفعة
        /// Batch number
        /// </summary>
        [StringLength(50)]
        public string BatchNumber { get; set; } = string.Empty;

        /// <summary>
        /// المورد
        /// Supplier
        /// </summary>
        [StringLength(100)]
        public string Supplier { get; set; } = string.Empty;

        /// <summary>
        /// حالة الصنف
        /// Item status
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "نشط"; // نشط، متوقف، منتهي الصلاحية

        /// <summary>
        /// ملاحظات
        /// Notes
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// حساب القيمة الإجمالية
        /// Calculate total value
        /// </summary>
        public void CalculateTotalValue()
        {
            TotalValue = Quantity * UnitPrice;
        }

        /// <summary>
        /// التحقق من الحاجة لإعادة الطلب
        /// Check if reorder is needed
        /// </summary>
        public bool NeedsReorder()
        {
            return Quantity <= ReorderPoint;
        }

        /// <summary>
        /// التحقق من نقص المخزون
        /// Check if stock is low
        /// </summary>
        public bool IsLowStock()
        {
            return Quantity <= MinimumStock;
        }

        /// <summary>
        /// التحقق من زيادة المخزون
        /// Check if stock is high
        /// </summary>
        public bool IsOverStock()
        {
            return Quantity >= MaximumStock;
        }

        /// <summary>
        /// التحقق من انتهاء الصلاحية
        /// Check if item is expired
        /// </summary>
        public bool IsExpired()
        {
            return ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now;
        }

        /// <summary>
        /// التحقق من قرب انتهاء الصلاحية (خلال 30 يوم)
        /// Check if item expires soon (within 30 days)
        /// </summary>
        public bool ExpiresSoon()
        {
            return ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.Now.AddDays(30);
        }

        /// <summary>
        /// تحديث الكمية
        /// Update quantity
        /// </summary>
        public void UpdateQuantity(decimal newQuantity, string reason = "")
        {
            if (newQuantity < 0)
                throw new ArgumentException("الكمية لا يمكن أن تكون سالبة");

            Quantity = newQuantity;
            CalculateTotalValue();
            UpdatedDate = DateTime.Now;
        }

        /// <summary>
        /// إضافة كمية
        /// Add quantity
        /// </summary>
        public void AddQuantity(decimal quantityToAdd, decimal? newUnitPrice = null)
        {
            if (quantityToAdd <= 0)
                throw new ArgumentException("الكمية المضافة يجب أن تكون أكبر من الصفر");

            if (newUnitPrice.HasValue && newUnitPrice.Value > 0)
            {
                // حساب المتوسط المرجح للسعر
                var totalValue = (Quantity * UnitPrice) + (quantityToAdd * newUnitPrice.Value);
                var totalQuantity = Quantity + quantityToAdd;
                UnitPrice = totalValue / totalQuantity;
            }

            Quantity += quantityToAdd;
            CalculateTotalValue();
            UpdatedDate = DateTime.Now;
        }

        /// <summary>
        /// خصم كمية
        /// Deduct quantity
        /// </summary>
        public void DeductQuantity(decimal quantityToDeduct)
        {
            if (quantityToDeduct <= 0)
                throw new ArgumentException("الكمية المخصومة يجب أن تكون أكبر من الصفر");

            if (quantityToDeduct > Quantity)
                throw new InvalidOperationException("الكمية المخصومة أكبر من الكمية المتاحة");

            Quantity -= quantityToDeduct;
            CalculateTotalValue();
            UpdatedDate = DateTime.Now;
        }
    }
}
