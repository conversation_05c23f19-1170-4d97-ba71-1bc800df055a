﻿using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    public partial class CostCenterAddEditForm : Form
    {
        private readonly ILogger _logger;
        private CostCenter? _costCenter;
        private bool _isEditMode;

        // Controls
        private TextBox centerCodeTextBox = null!;
        private TextBox centerNameTextBox = null!;
        private ComboBox cycleComboBox = null!;
        private NumericUpDown allocatedBudgetNumericUpDown = null!;
        private NumericUpDown actualSpendingNumericUpDown = null!;
        private ComboBox statusComboBox = null!;
        private TextBox descriptionTextBox = null!;
        private Button saveButton = null!;
        private Button cancelButton = null!;

        public CostCenterAddEditForm(ILogger logger)
        {
            _logger = logger;
            InitializeComponent();
        }

        public CostCenterAddEditForm(ILogger logger, CostCenter costCenter) : this(logger)
        {
            _costCenter = costCenter;
            _isEditMode = true;
            LoadCostCenterData();
        }

        private void InitializeComponent()
        {
            this.Text = _isEditMode ? "تعديل مركز تكلفة" : "إضافة مركز تكلفة جديد";
            this.Size = new Size(600, 650);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(155, 89, 182),
                Padding = new Padding(20, 15, 20, 15)
            };

            var titleLabel = new Label
            {
                Text = _isEditMode ? "تعديل بيانات مركز التكلفة" : "إضافة مركز تكلفة جديد",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 18)
            };

            headerPanel.Controls.Add(titleLabel);

            // Main panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White
            };

            // Create form fields
            int yPos = 20;
            const int spacing = 45;

            // Center Code
            var centerCodeLabel = new Label
            {
                Text = "كود المركز:",
                Location = new Point(20, yPos),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 10F)
            };

            centerCodeTextBox = new TextBox
            {
                Location = new Point(150, yPos),
                Size = new Size(200, 23),
                Font = new Font("Segoe UI", 10F)
            };

            yPos += spacing;

            // Center Name
            var centerNameLabel = new Label
            {
                Text = "اسم المركز:",
                Location = new Point(20, yPos),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 10F)
            };

            centerNameTextBox = new TextBox
            {
                Location = new Point(150, yPos),
                Size = new Size(300, 23),
                Font = new Font("Segoe UI", 10F)
            };

            yPos += spacing;

            // Production Cycle
            var cycleLabel = new Label
            {
                Text = "الدورة الإنتاجية:",
                Location = new Point(20, yPos),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 10F)
            };

            cycleComboBox = new ComboBox
            {
                Location = new Point(150, yPos),
                Size = new Size(250, 23),
                Font = new Font("Segoe UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            // TODO: Load production cycles from database

            yPos += spacing;

            // Allocated Budget
            var allocatedBudgetLabel = new Label
            {
                Text = "الميزانية المخصصة:",
                Location = new Point(20, yPos),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 10F)
            };

            allocatedBudgetNumericUpDown = new NumericUpDown
            {
                Location = new Point(150, yPos),
                Size = new Size(200, 23),
                Font = new Font("Segoe UI", 10F),
                DecimalPlaces = 2,
                Maximum = 99999999,
                Minimum = 0,
                ThousandsSeparator = true
            };

            yPos += spacing;

            // Actual Spending
            var actualSpendingLabel = new Label
            {
                Text = "الإنفاق الفعلي:",
                Location = new Point(20, yPos),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 10F)
            };

            actualSpendingNumericUpDown = new NumericUpDown
            {
                Location = new Point(150, yPos),
                Size = new Size(200, 23),
                Font = new Font("Segoe UI", 10F),
                DecimalPlaces = 2,
                Maximum = 99999999,
                Minimum = 0,
                ThousandsSeparator = true,
                ReadOnly = !_isEditMode // Only allow editing in edit mode
            };

            yPos += spacing;

            // Status
            var statusLabel = new Label
            {
                Text = "الحالة:",
                Location = new Point(20, yPos),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 10F)
            };

            statusComboBox = new ComboBox
            {
                Location = new Point(150, yPos),
                Size = new Size(150, 23),
                Font = new Font("Segoe UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            statusComboBox.Items.AddRange(new[] { "نشط", "متوقف", "مغلق" });

            yPos += spacing;

            // Description
            var descriptionLabel = new Label
            {
                Text = "الوصف:",
                Location = new Point(20, yPos),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 10F)
            };

            descriptionTextBox = new TextBox
            {
                Location = new Point(150, yPos),
                Size = new Size(350, 60),
                Font = new Font("Segoe UI", 10F),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            // Buttons
            var buttonPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(236, 240, 241),
                Padding = new Padding(20, 10, 20, 10)
            };

            saveButton = new Button
            {
                Text = "حفظ",
                Size = new Size(100, 30),
                Location = new Point(20, 10),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "إلغاء",
                Size = new Size(100, 30),
                Location = new Point(130, 10),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            cancelButton.Click += (s, e) => this.Close();

            buttonPanel.Controls.AddRange(new Control[] { saveButton, cancelButton });

            // Add all controls to main panel
            mainPanel.Controls.AddRange(new Control[]
            {
                centerCodeLabel, centerCodeTextBox,
                centerNameLabel, centerNameTextBox,
                cycleLabel, cycleComboBox,
                allocatedBudgetLabel, allocatedBudgetNumericUpDown,
                actualSpendingLabel, actualSpendingNumericUpDown,
                statusLabel, statusComboBox,
                descriptionLabel, descriptionTextBox
            });

            this.Controls.AddRange(new Control[] { mainPanel, buttonPanel, headerPanel });
        }

        private void LoadCostCenterData()
        {
            if (_costCenter == null) return;

            try
            {
                centerCodeTextBox.Text = _costCenter.CenterCode;
                centerNameTextBox.Text = _costCenter.CenterName;
                // TODO: Set cycle combo box selection based on _costCenter.CycleId
                allocatedBudgetNumericUpDown.Value = _costCenter.AllocatedBudget;
                actualSpendingNumericUpDown.Value = _costCenter.ActualSpending;
                statusComboBox.Text = _costCenter.Status;
                descriptionTextBox.Text = _costCenter.Description ?? "";

                _logger.LogInformation($"تم تحميل بيانات مركز التكلفة: {_costCenter.CenterName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل بيانات مركز التكلفة");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                // Create or update cost center
                if (_costCenter == null)
                {
                    _costCenter = new CostCenter();
                }

                _costCenter.CenterCode = centerCodeTextBox.Text.Trim();
                _costCenter.CenterName = centerNameTextBox.Text.Trim();
                // TODO: Set CycleId based on selected cycle
                _costCenter.AllocatedBudget = allocatedBudgetNumericUpDown.Value;
                _costCenter.ActualSpending = actualSpendingNumericUpDown.Value;
                _costCenter.Status = statusComboBox.Text;
                _costCenter.Description = descriptionTextBox.Text.Trim();

                // TODO: Save to database using service
                
                MessageBox.Show(_isEditMode ? "تم تحديث مركز التكلفة بنجاح" : "تم إضافة مركز التكلفة بنجاح", 
                    "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();

                _logger.LogInformation($"تم حفظ مركز التكلفة: {_costCenter.CenterName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ مركز التكلفة");
                MessageBox.Show($"خطأ في الحفظ: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(centerCodeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال كود مركز التكلفة", "تحذير", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                centerCodeTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(centerNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم مركز التكلفة", "تحذير", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                centerNameTextBox.Focus();
                return false;
            }

            if (cycleComboBox.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار الدورة الإنتاجية", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cycleComboBox.Focus();
                return false;
            }

            if (statusComboBox.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار حالة مركز التكلفة", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                statusComboBox.Focus();
                return false;
            }

            return true;
        }
    }
}

