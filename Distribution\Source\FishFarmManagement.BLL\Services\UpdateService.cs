using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using System.Text.Json;
using System.IO.Compression;
using System.Diagnostics;
using System.Reflection;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة التحديث التلقائي
    /// Automatic update service
    /// </summary>
    public class UpdateService
    {
        private readonly ILogger<UpdateService> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly string _updateServerUrl;
        private readonly string _currentVersion;

        public UpdateService(ILogger<UpdateService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _httpClient = new HttpClient();
            _updateServerUrl = _configuration["UpdateService:ServerUrl"] ?? "https://updates.fishfarm.com/api";
            _currentVersion = GetCurrentVersion();
        }

        /// <summary>
        /// التحقق من وجود تحديثات
        /// Check for updates
        /// </summary>
        public async Task<UpdateInfo?> CheckForUpdatesAsync()
        {
            try
            {
                _logger.LogInformation("بدء التحقق من التحديثات");

                var response = await _httpClient.GetAsync($"{_updateServerUrl}/check-update?version={_currentVersion}");
                
                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var updateInfo = JsonSerializer.Deserialize<UpdateInfo>(jsonContent);
                    
                    if (updateInfo != null && IsNewerVersion(updateInfo.Version, _currentVersion))
                    {
                        _logger.LogInformation("تحديث جديد متاح: {Version}", updateInfo.Version);
                        return updateInfo;
                    }
                    else
                    {
                        _logger.LogInformation("لا توجد تحديثات جديدة");
                        return null;
                    }
                }
                else
                {
                    _logger.LogWarning("فشل في التحقق من التحديثات: {StatusCode}", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من التحديثات");
                return null;
            }
        }

        /// <summary>
        /// تحميل وتثبيت التحديث
        /// Download and install update
        /// </summary>
        public async Task<bool> DownloadAndInstallUpdateAsync(UpdateInfo updateInfo, IProgress<int>? progress = null)
        {
            try
            {
                _logger.LogInformation("بدء تحميل التحديث: {Version}", updateInfo.Version);

                // إنشاء مجلد التحديثات
                var updateFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Updates");
                Directory.CreateDirectory(updateFolder);

                var updateFilePath = Path.Combine(updateFolder, $"update-{updateInfo.Version}.zip");

                // تحميل ملف التحديث
                using var response = await _httpClient.GetAsync(updateInfo.DownloadUrl, HttpCompletionOption.ResponseHeadersRead);
                response.EnsureSuccessStatusCode();

                var totalBytes = response.Content.Headers.ContentLength ?? 0;
                var downloadedBytes = 0L;

                using var contentStream = await response.Content.ReadAsStreamAsync();
                using var fileStream = new FileStream(updateFilePath, FileMode.Create, FileAccess.Write, FileShare.None);

                var buffer = new byte[8192];
                int bytesRead;

                while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    await fileStream.WriteAsync(buffer, 0, bytesRead);
                    downloadedBytes += bytesRead;

                    if (totalBytes > 0)
                    {
                        var progressPercentage = (int)((downloadedBytes * 100) / totalBytes);
                        progress?.Report(progressPercentage);
                    }
                }

                _logger.LogInformation("تم تحميل التحديث بنجاح");

                // التحقق من سلامة الملف
                if (await VerifyUpdateFileAsync(updateFilePath, updateInfo.Checksum))
                {
                    // تطبيق التحديث
                    return await ApplyUpdateAsync(updateFilePath, updateInfo);
                }
                else
                {
                    _logger.LogError("فشل في التحقق من سلامة ملف التحديث");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل وتثبيت التحديث");
                return false;
            }
        }

        /// <summary>
        /// تطبيق التحديث
        /// Apply update
        /// </summary>
        private async Task<bool> ApplyUpdateAsync(string updateFilePath, UpdateInfo updateInfo)
        {
            try
            {
                _logger.LogInformation("بدء تطبيق التحديث");

                var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
                var backupDirectory = Path.Combine(appDirectory, "Backup", DateTime.Now.ToString("yyyyMMdd-HHmmss"));
                
                // إنشاء نسخة احتياطية من الملفات الحالية
                Directory.CreateDirectory(backupDirectory);
                await CreateBackupAsync(appDirectory, backupDirectory);

                // استخراج ملفات التحديث
                using var archive = ZipFile.OpenRead(updateFilePath);
                
                foreach (var entry in archive.Entries)
                {
                    if (entry.FullName.EndsWith("/"))
                        continue;

                    var destinationPath = Path.Combine(appDirectory, entry.FullName);
                    var destinationDirectory = Path.GetDirectoryName(destinationPath);
                    
                    if (!string.IsNullOrEmpty(destinationDirectory))
                    {
                        Directory.CreateDirectory(destinationDirectory);
                    }

                    entry.ExtractToFile(destinationPath, true);
                }

                // إنشاء ملف معلومات التحديث
                var updateInfoPath = Path.Combine(appDirectory, "update-info.json");
                var updateInfoJson = JsonSerializer.Serialize(updateInfo, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(updateInfoPath, updateInfoJson);

                _logger.LogInformation("تم تطبيق التحديث بنجاح");

                // حذف ملف التحديث
                File.Delete(updateFilePath);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تطبيق التحديث");
                return false;
            }
        }

        /// <summary>
        /// التحقق من سلامة ملف التحديث
        /// Verify update file integrity
        /// </summary>
        private async Task<bool> VerifyUpdateFileAsync(string filePath, string expectedChecksum)
        {
            try
            {
                using var sha256 = System.Security.Cryptography.SHA256.Create();
                using var stream = File.OpenRead(filePath);
                var hash = await sha256.ComputeHashAsync(stream);
                var actualChecksum = Convert.ToHexString(hash).ToLowerInvariant();
                
                return actualChecksum.Equals(expectedChecksum, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من سلامة ملف التحديث");
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من الملفات الحالية
        /// Create backup of current files
        /// </summary>
        private async Task CreateBackupAsync(string sourceDirectory, string backupDirectory)
        {
            try
            {
                var filesToBackup = new[] { "*.exe", "*.dll", "*.config", "*.json" };
                
                foreach (var pattern in filesToBackup)
                {
                    var files = Directory.GetFiles(sourceDirectory, pattern, SearchOption.AllDirectories);
                    
                    foreach (var file in files)
                    {
                        var relativePath = Path.GetRelativePath(sourceDirectory, file);
                        var backupPath = Path.Combine(backupDirectory, relativePath);
                        var backupDir = Path.GetDirectoryName(backupPath);
                        
                        if (!string.IsNullOrEmpty(backupDir))
                        {
                            Directory.CreateDirectory(backupDir);
                        }
                        
                        File.Copy(file, backupPath, true);
                    }
                }
                
                _logger.LogInformation("تم إنشاء نسخة احتياطية في: {BackupDirectory}", backupDirectory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية");
            }
        }

        /// <summary>
        /// الحصول على الإصدار الحالي
        /// Get current version
        /// </summary>
        private string GetCurrentVersion()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "1.0.0.0";
            }
            catch
            {
                return "1.0.0.0";
            }
        }

        /// <summary>
        /// مقارنة الإصدارات
        /// Compare versions
        /// </summary>
        private bool IsNewerVersion(string newVersion, string currentVersion)
        {
            try
            {
                var newVer = new Version(newVersion);
                var currentVer = new Version(currentVersion);
                return newVer > currentVer;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إعادة تشغيل التطبيق
        /// Restart application
        /// </summary>
        public void RestartApplication()
        {
            try
            {
                var currentProcess = Process.GetCurrentProcess();
                var exePath = currentProcess.MainModule?.FileName;
                
                if (!string.IsNullOrEmpty(exePath))
                {
                    Process.Start(exePath);
                    Environment.Exit(0);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إعادة تشغيل التطبيق");
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// معلومات التحديث
    /// Update information
    /// </summary>
    public class UpdateInfo
    {
        public string Version { get; set; } = string.Empty;
        public string DownloadUrl { get; set; } = string.Empty;
        public string Checksum { get; set; } = string.Empty;
        public string ReleaseNotes { get; set; } = string.Empty;
        public DateTime ReleaseDate { get; set; }
        public bool IsRequired { get; set; }
        public long FileSize { get; set; }
    }
}
