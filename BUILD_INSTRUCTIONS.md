# تعليمات البناء وحل المشاكل
## Build Instructions and Troubleshooting

## 📋 متطلبات النظام

- .NET 8.0 SDK
- Windows 10/11
- **لا حاجة لتثبيت قاعدة بيانات منفصلة** - يستخدم النظام SQLite

## 🔧 حل مشاكل البناء السريع

### الطريقة الأولى: استخدام ملفات البناء التلقائي

1. **تشغيل ملف البناء:**
```cmd
build.bat
```

2. **تشغيل التطبيق:**
```cmd
run.bat
```

### الطريقة الثانية: البناء اليدوي

1. **تنظيف المشروع:**
```cmd
dotnet clean
```

2. **استعادة الحزم:**
```cmd
dotnet restore
```

3. **بناء المشاريع بالترتيب:**
```cmd
dotnet build FishFarmManagement.Models\FishFarmManagement.Models.csproj
dotnet build FishFarmManagement.DAL\FishFarmManagement.DAL.csproj
dotnet build FishFarmManagement.BLL\FishFarmManagement.BLL.csproj
dotnet build FishFarmManagement\FishFarmManagement.csproj
```

4. **تشغيل التطبيق:**
```cmd
dotnet run --project FishFarmManagement
```

## 🚨 حل المشاكل الشائعة

### مشكلة: NETSDK1136 - Target Platform Error

**الحل:**
تم إصلاح هذه المشكلة بإزالة `UseWindowsForms` من مشروع Models.

### مشكلة: CS0006 - Metadata file not found

**الحل:**
1. احذف مجلدات `bin` و `obj` من جميع المشاريع:
```cmd
rmdir /s /q FishFarmManagement.Models\bin
rmdir /s /q FishFarmManagement.Models\obj
rmdir /s /q FishFarmManagement.DAL\bin
rmdir /s /q FishFarmManagement.DAL\obj
rmdir /s /q FishFarmManagement.BLL\bin
rmdir /s /q FishFarmManagement.BLL\obj
rmdir /s /q FishFarmManagement\bin
rmdir /s /q FishFarmManagement\obj
```

2. أعد البناء:
```cmd
dotnet restore
dotnet build
```

### مشكلة: إصدار .NET غير متوافق

**الحل:**
1. تحقق من إصدار .NET المثبت:
```cmd
dotnet --version
```

2. إذا كان الإصدار أقل من 8.0، قم بتحديثه من:
https://dotnet.microsoft.com/download/dotnet/8.0

### مشكلة: مشاكل في Visual Studio

**الحل:**
1. أغلق Visual Studio
2. احذف مجلدات `bin` و `obj`
3. أعد فتح Visual Studio
4. اختر **Build** > **Rebuild Solution**

## 📋 متطلبات النظام

- Windows 10/11
- .NET 8.0 SDK أو أحدث
- Visual Studio 2022 (اختياري)

## 🔍 التحقق من البناء الناجح

بعد البناء الناجح، يجب أن ترى:
```
Build succeeded.
    0 Warning(s)
    0 Error(s)
```

## 🚀 تشغيل التطبيق

بعد البناء الناجح:
```cmd
dotnet run --project FishFarmManagement
```

يجب أن يظهر النموذج الرئيسي للتطبيق.

## 📞 الدعم

إذا استمرت المشاكل:
- تحقق من ملفات السجل في مجلد `Logs`
- راسل المطور: <EMAIL>
- أرفق رسائل الخطأ الكاملة
