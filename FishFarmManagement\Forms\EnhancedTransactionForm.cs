﻿using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Controls;
using FishFarmManagement.Helpers;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج محسن لإدخال المعاملات المالية
    /// Enhanced transaction input form
    /// </summary>
    public partial class EnhancedTransactionForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ISearchService _searchService;
        private readonly IAccountingService _accountingService;
        private readonly Transaction? _existingTransaction;
        private readonly bool _isEditMode;

        // Enhanced Controls
        private AutoCompleteTextBox accountAutoComplete;
        private EnhancedDateTimePicker transactionDatePicker;
        private TextBox referenceNumberTextBox = null! = null!;
        private ComboBox transactionTypeComboBox = null! = null!;
        private NumericUpDown amountNumericUpDown = null! = null!;
        private TextBox descriptionTextBox = null! = null!;
        private TextBox notesTextBox = null! = null!;
        private Button saveButton = null! = null!;
        private Button cancelButton = null! = null!;
        private Label validationLabel = null! = null!;

        // Validation
        private bool isFormValid = false;

        public EnhancedTransactionForm(IServiceProvider serviceProvider, Transaction? existingTransaction = null)
        {
            _serviceProvider = serviceProvider;
            _searchService = serviceProvider.GetRequiredService<ISearchService>();
            _accountingService = serviceProvider.GetRequiredService<IAccountingService>();
            _existingTransaction = existingTransaction;
            _isEditMode = existingTransaction != null;

            InitializeComponent();
            SetupControls();
            SetupValidation();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = _isEditMode ? "تعديل معاملة مالية" : "إضافة معاملة مالية جديدة";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(245, 245, 245);
        }

        private void SetupControls()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            int y = 20;
            const int spacing = 40;
            const int labelWidth = 120;
            const int controlWidth = 300;

            // Title
            var titleLabel = new Label
            {
                Text = _isEditMode ? "تعديل معاملة مالية" : "إضافة معاملة مالية جديدة",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Size = new Size(400, 30),
                Location = new Point(100, y),
                TextAlign = ContentAlignment.MiddleCenter
            };
            y += 50;

            // Transaction Type
            var typeLabel = CreateLabel("نوع المعاملة:", new Point(450, y), labelWidth);
            transactionTypeComboBox = new ComboBox
            {
                Location = new Point(120, y),
                Size = new Size(controlWidth, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F)
            };
            transactionTypeComboBox.Items.AddRange(new[] { "شراء", "بيع", "راتب", "مصروف عام", "إيراد أخر" });
            transactionTypeComboBox.SelectedIndexChanged += TransactionTypeComboBox_SelectedIndexChanged;
            y += spacing;

            // Reference Number
            var refLabel = CreateLabel("الرقم المرجعي:", new Point(450, y), labelWidth);
            referenceNumberTextBox = new TextBox
            {
                Location = new Point(120, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F),
                ReadOnly = true,
                BackColor = Color.FromArgb(240, 240, 240)
            };
            y += spacing;

            // Transaction Date
            var dateLabel = CreateLabel("تاريخ المعاملة:", new Point(450, y), labelWidth);
            transactionDatePicker = new EnhancedDateTimePicker
            {
                Location = new Point(120, y),
                Size = new Size(controlWidth, 25),
                Value = DateTime.Now
            };
            transactionDatePicker.ValueChanged += ValidateForm;
            y += spacing;

            // Account Selection
            var accountLabel = CreateLabel("الحساب:", new Point(450, y), labelWidth);
            accountAutoComplete = new AutoCompleteTextBox
            {
                Location = new Point(120, y),
                Size = new Size(controlWidth, 25),
                MinSearchLength = 2,
                ShowDetailsInDropdown = true
            };
            AutoCompleteHelper.SetupAccountsAutoComplete(accountAutoComplete, _searchService);
            accountAutoComplete.ItemSelected += AccountAutoComplete_ItemSelected;
            y += spacing;

            // Amount
            var amountLabel = CreateLabel("المبلغ:", new Point(450, y), labelWidth);
            amountNumericUpDown = new NumericUpDown
            {
                Location = new Point(120, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F),
                DecimalPlaces = 2,
                Maximum = *********,
                Minimum = 0.01m,
                ThousandsSeparator = true
            };
            amountNumericUpDown.ValueChanged += ValidateForm;
            y += spacing;

            // Description
            var descLabel = CreateLabel("الوصف:", new Point(450, y), labelWidth);
            descriptionTextBox = new TextBox
            {
                Location = new Point(120, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F),
                MaxLength = 500
            };
            descriptionTextBox.TextChanged += ValidateForm;
            y += spacing;

            // Notes
            var notesLabel = CreateLabel("ملاحظات:", new Point(450, y), labelWidth);
            notesTextBox = new TextBox
            {
                Location = new Point(120, y),
                Size = new Size(controlWidth, 60),
                Font = new Font("Segoe UI", 9F),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                MaxLength = 1000
            };
            y += 80;

            // Validation Label
            validationLabel = new Label
            {
                Location = new Point(120, y),
                Size = new Size(controlWidth, 20),
                ForeColor = Color.Red,
                Font = new Font("Segoe UI", 8F),
                Text = ""
            };
            y += 30;

            // Buttons
            saveButton = new Button
            {
                Text = _isEditMode ? "حفظ التعديلات" : "حفظ المعاملة",
                Size = new Size(120, 35),
                Location = new Point(300, y),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Enabled = false
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "إلغاء",
                Size = new Size(120, 35),
                Location = new Point(170, y),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            cancelButton.Click += (s, e) => this.Close();

            panel.Controls.AddRange(new Control[]
            {
                titleLabel, typeLabel, transactionTypeComboBox,
                refLabel, referenceNumberTextBox,
                dateLabel, transactionDatePicker,
                accountLabel, accountAutoComplete,
                amountLabel, amountNumericUpDown,
                descLabel, descriptionTextBox,
                notesLabel, notesTextBox,
                validationLabel, saveButton, cancelButton
            });

            this.Controls.Add(panel);
        }

        private Label CreateLabel(string text, Point location, int width)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(width, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F)
            };
        }

        private void SetupValidation()
        {
            // Real-time validation
            transactionTypeComboBox.SelectedIndexChanged += ValidateForm;
            accountAutoComplete.ItemSelected += (s, e) => ValidateForm(s, EventArgs.Empty);
            amountNumericUpDown.ValueChanged += ValidateForm;
            descriptionTextBox.TextChanged += ValidateForm;
        }

        private void ValidateForm(object? sender, EventArgs e)
        {
            var errors = new List<string>();

            if (transactionTypeComboBox.SelectedIndex == -1)
                errors.Add("يجب اختيار نوع المعاملة");

            if (accountAutoComplete.SelectedValue == null)
                errors.Add("يجب اختيار الحساب");

            if (amountNumericUpDown.Value <= 0)
                errors.Add("يجب إدخال مبلغ أكبر من الصفر");

            if (string.IsNullOrWhiteSpace(descriptionTextBox.Text))
                errors.Add("يجب إدخال وصف المعاملة");

            isFormValid = errors.Count == 0;
            saveButton.Enabled = isFormValid;

            validationLabel.Text = errors.Count > 0 ? string.Join(" • ", errors) : "";
        }

        private void TransactionTypeComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (transactionTypeComboBox.SelectedItem != null)
            {
                var transactionType = transactionTypeComboBox.SelectedItem.ToString();
                referenceNumberTextBox.Text = Transaction.GenerateReferenceNumber(transactionType!);
            }
        }

        private void AccountAutoComplete_ItemSelected(object? sender, AutoCompleteItemSelectedEventArgs e)
        {
            ValidateForm(sender, EventArgs.Empty);
        }

        private void LoadData()
        {
            if (_existingTransaction != null)
            {
                transactionTypeComboBox.Text = _existingTransaction.TransactionType;
                referenceNumberTextBox.Text = _existingTransaction.ReferenceNumber;
                transactionDatePicker.Value = _existingTransaction.TransactionDate;
                amountNumericUpDown.Value = _existingTransaction.TotalAmount;
                descriptionTextBox.Text = _existingTransaction.Description;
                notesTextBox.Text = _existingTransaction.Notes;

                // Load account information if available
                // This would require additional logic to fetch account details
            }
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            if (!isFormValid) return;

            try
            {
                this.Cursor = Cursors.WaitCursor;
                saveButton.Enabled = false;

                var transaction = _existingTransaction ?? new Transaction();
                
                transaction.TransactionType = transactionTypeComboBox.SelectedItem!.ToString()!;
                transaction.ReferenceNumber = referenceNumberTextBox.Text;
                transaction.TransactionDate = transactionDatePicker.Value;
                transaction.TotalAmount = amountNumericUpDown.Value;
                transaction.Description = descriptionTextBox.Text.Trim();
                transaction.Notes = notesTextBox.Text.Trim();
                transaction.CycleId = 1; // This should be set based on current production cycle

                if (_isEditMode)
                {
                    // Update existing transaction
                    // var updatedTransaction = await _accountingService.UpdateTransactionAsync(transaction);
                    MessageBox.Show("تم تحديث المعاملة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Create new transaction
                    // var newTransaction = await _accountingService.CreateTransactionAsync(transaction);
                    MessageBox.Show("تم إنشاء المعاملة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المعاملة:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                saveButton.Enabled = true;
            }
        }
    }
}
