﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.Forms
{
    public partial class ChartOfAccountsForm : Form
    {
        private readonly ILogger<ChartOfAccountsForm> _logger;
        private TreeView accountsTreeView;
        private DataGridView accountsGrid;
        private Button addButton;
        private Button editButton;
        private Button deleteButton;
        private Button refreshButton;
        private TextBox searchTextBox;
        private ComboBox accountTypeComboBox;
        private Label totalAccountsLabel;

        public ChartOfAccountsForm(ILogger<ChartOfAccountsForm> logger)
        {
            _logger = logger;
            InitializeComponent();
            LoadAccountsData();
        }

        private void InitializeComponent()
        {
            this.Text = "دليل الحسابات";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
            SetupLayout();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(52, 152, 219),
                Padding = new Padding(20, 10, 20, 10)
            };

            var titleLabel = new Label
            {
                Text = "دليل الحسابات",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 25)
            };

            headerPanel.Controls.Add(titleLabel);

            // Toolbar panel
            var toolbarPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(236, 240, 241),
                Padding = new Padding(20, 10, 20, 10)
            };

            // Search controls
            var searchLabel = new Label
            {
                Text = "البحث:",
                AutoSize = true,
                Location = new Point(20, 18),
                Font = new Font("Segoe UI", 10F)
            };

            searchTextBox = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(70, 15),
                Font = new Font("Segoe UI", 10F)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            var typeLabel = new Label
            {
                Text = "نوع الحساب:",
                AutoSize = true,
                Location = new Point(290, 18),
                Font = new Font("Segoe UI", 10F)
            };

            accountTypeComboBox = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(370, 15),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 10F)
            };
            accountTypeComboBox.Items.AddRange(new[] { "الكل", "الأصول", "الخصوم", "حقوق الملكية", "الإيرادات", "المصروفات" });
            accountTypeComboBox.SelectedIndex = 0;
            accountTypeComboBox.SelectedIndexChanged += AccountTypeComboBox_SelectedIndexChanged;

            // Action buttons
            addButton = new Button
            {
                Text = "إضافة حساب",
                Size = new Size(100, 30),
                Location = new Point(540, 12),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };
            addButton.Click += AddButton_Click;

            editButton = new Button
            {
                Text = "تعديل",
                Size = new Size(80, 30),
                Location = new Point(650, 12),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F),
                Enabled = false
            };
            editButton.Click += EditButton_Click;

            deleteButton = new Button
            {
                Text = "حذف",
                Size = new Size(80, 30),
                Location = new Point(740, 12),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F),
                Enabled = false
            };
            deleteButton.Click += DeleteButton_Click;

            refreshButton = new Button
            {
                Text = "تحديث",
                Size = new Size(80, 30),
                Location = new Point(830, 12),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };
            refreshButton.Click += RefreshButton_Click;

            toolbarPanel.Controls.AddRange(new Control[] 
            { 
                searchLabel, searchTextBox, typeLabel, accountTypeComboBox,
                addButton, editButton, deleteButton, refreshButton 
            });

            // Status panel
            var statusPanel = new Panel
            {
                Height = 40,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(236, 240, 241),
                Padding = new Padding(20, 10, 20, 10)
            };

            totalAccountsLabel = new Label
            {
                Text = "إجمالي الحسابات: 0",
                AutoSize = true,
                Location = new Point(20, 12),
                Font = new Font("Segoe UI", 9F)
            };

            statusPanel.Controls.Add(totalAccountsLabel);

            // Main content panel
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill
            };

            // Split container for tree view and grid
            var splitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                SplitterDistance = 300,
                Orientation = Orientation.Vertical
            };

            // Tree view for account hierarchy
            accountsTreeView = new TreeView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F),
                ShowLines = true,
                ShowPlusMinus = true,
                ShowRootLines = true,
                HideSelection = false
            };
            accountsTreeView.AfterSelect += AccountsTreeView_AfterSelect;

            // Data grid for account details
            accountsGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                Font = new Font("Segoe UI", 9F)
            };

            SetupGridColumns();
            accountsGrid.SelectionChanged += AccountsGrid_SelectionChanged;
            accountsGrid.CellDoubleClick += AccountsGrid_CellDoubleClick;

            splitContainer.Panel1.Controls.Add(accountsTreeView);
            splitContainer.Panel2.Controls.Add(accountsGrid);
            contentPanel.Controls.Add(splitContainer);

            this.Controls.AddRange(new Control[] { contentPanel, statusPanel, toolbarPanel, headerPanel });
        }

        private void SetupGridColumns()
        {
            accountsGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "الرقم", Width = 60, Visible = false },
                new DataGridViewTextBoxColumn { Name = "AccountCode", HeaderText = "رقم الحساب", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "AccountName", HeaderText = "اسم الحساب", Width = 250 },
                new DataGridViewTextBoxColumn { Name = "AccountType", HeaderText = "نوع الحساب", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "ParentAccount", HeaderText = "الحساب الأب", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "Balance", HeaderText = "الرصيد", Width = 120 },
                new DataGridViewCheckBoxColumn { Name = "IsActive", HeaderText = "نشط", Width = 60 },
                new DataGridViewTextBoxColumn { Name = "Description", HeaderText = "الوصف", Width = 200 }
            });

            // Format balance column
            accountsGrid.Columns["Balance"].DefaultCellStyle.Format = "C2";
            accountsGrid.Columns["Balance"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        }

        private void SetupLayout()
        {
            // Apply RTL layout
            foreach (Control control in this.Controls)
            {
                control.RightToLeft = RightToLeft.Yes;
            }
        }

        private void LoadAccountsData()
        {
            try
            {
                LoadTreeViewData();
                LoadGridData();
                UpdateStatusLabel();

                _logger.LogInformation("تم تحميل بيانات دليل الحسابات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل بيانات دليل الحسابات");
                MessageBox.Show($"خطأ في تحميل بيانات دليل الحسابات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadTreeViewData()
        {
            accountsTreeView.Nodes.Clear();

            // Sample hierarchical account data
            var assetsNode = new TreeNode("الأصول") { Tag = new { Type = "Assets", Code = "1000" } };
            assetsNode.Nodes.Add(new TreeNode("الأصول المتداولة") { Tag = new { Type = "CurrentAssets", Code = "1100" } });
            assetsNode.Nodes.Add(new TreeNode("الأصول الثابتة") { Tag = new { Type = "FixedAssets", Code = "1200" } });

            var liabilitiesNode = new TreeNode("الخصوم") { Tag = new { Type = "Liabilities", Code = "2000" } };
            liabilitiesNode.Nodes.Add(new TreeNode("الخصوم المتداولة") { Tag = new { Type = "CurrentLiabilities", Code = "2100" } });
            liabilitiesNode.Nodes.Add(new TreeNode("الخصوم طويلة الأجل") { Tag = new { Type = "LongTermLiabilities", Code = "2200" } });

            var equityNode = new TreeNode("حقوق الملكية") { Tag = new { Type = "Equity", Code = "3000" } };
            equityNode.Nodes.Add(new TreeNode("رأس المال") { Tag = new { Type = "Capital", Code = "3100" } });
            equityNode.Nodes.Add(new TreeNode("الأرباح المحتجزة") { Tag = new { Type = "RetainedEarnings", Code = "3200" } });

            var revenueNode = new TreeNode("الإيرادات") { Tag = new { Type = "Revenue", Code = "4000" } };
            revenueNode.Nodes.Add(new TreeNode("إيرادات المبيعات") { Tag = new { Type = "SalesRevenue", Code = "4100" } });
            revenueNode.Nodes.Add(new TreeNode("إيرادات أخرى") { Tag = new { Type = "OtherRevenue", Code = "4200" } });

            var expensesNode = new TreeNode("المصروفات") { Tag = new { Type = "Expenses", Code = "5000" } };
            expensesNode.Nodes.Add(new TreeNode("مصروفات التشغيل") { Tag = new { Type = "OperatingExpenses", Code = "5100" } });
            expensesNode.Nodes.Add(new TreeNode("مصروفات إدارية") { Tag = new { Type = "AdministrativeExpenses", Code = "5200" } });

            accountsTreeView.Nodes.AddRange(new TreeNode[] 
            { 
                assetsNode, liabilitiesNode, equityNode, revenueNode, expensesNode 
            });

            accountsTreeView.ExpandAll();
        }

        private void LoadGridData()
        {
            // Sample account data
            var sampleData = new List<dynamic>
            {
                new { Id = 1, AccountCode = "1001", AccountName = "النقدية", AccountType = "الأصول", ParentAccount = "الأصول المتداولة", Balance = 50000.00m, IsActive = true, Description = "النقدية في الصندوق" },
                new { Id = 2, AccountCode = "1002", AccountName = "البنك", AccountType = "الأصول", ParentAccount = "الأصول المتداولة", Balance = 150000.00m, IsActive = true, Description = "الحساب الجاري في البنك" },
                new { Id = 3, AccountCode = "1101", AccountName = "المخزون", AccountType = "الأصول", ParentAccount = "الأصول المتداولة", Balance = 75000.00m, IsActive = true, Description = "مخزون الأعلاف والمواد" },
                new { Id = 4, AccountCode = "1201", AccountName = "المعدات", AccountType = "الأصول", ParentAccount = "الأصول الثابتة", Balance = 200000.00m, IsActive = true, Description = "معدات المزرعة" },
                new { Id = 5, AccountCode = "2001", AccountName = "الموردون", AccountType = "الخصوم", ParentAccount = "الخصوم المتداولة", Balance = 25000.00m, IsActive = true, Description = "مستحقات الموردين" },
                new { Id = 6, AccountCode = "3001", AccountName = "رأس المال", AccountType = "حقوق الملكية", ParentAccount = "رأس المال", Balance = 300000.00m, IsActive = true, Description = "رأس مال المالك" },
                new { Id = 7, AccountCode = "4001", AccountName = "مبيعات الأسماك", AccountType = "الإيرادات", ParentAccount = "إيرادات المبيعات", Balance = 180000.00m, IsActive = true, Description = "إيرادات بيع الأسماك" },
                new { Id = 8, AccountCode = "5001", AccountName = "مصروفات الأعلاف", AccountType = "المصروفات", ParentAccount = "مصروفات التشغيل", Balance = 45000.00m, IsActive = true, Description = "تكلفة الأعلاف" }
            };

            accountsGrid.DataSource = sampleData;
        }

        private void UpdateStatusLabel()
        {
            var totalAccounts = accountsGrid.Rows.Count;
            totalAccountsLabel.Text = $"إجمالي الحسابات: {totalAccounts}";
        }

        // Event handlers
        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            FilterData();
        }

        private void AccountTypeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterData();
        }

        private void FilterData()
        {
            // TODO: Implement filtering logic
        }

        private void AccountsTreeView_AfterSelect(object sender, TreeViewEventArgs e)
        {
            // TODO: Filter grid based on selected tree node
        }

        private void AccountsGrid_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSelection = accountsGrid.SelectedRows.Count > 0;
            editButton.Enabled = hasSelection;
            deleteButton.Enabled = hasSelection;
        }

        private void AccountsGrid_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditSelectedAccount();
            }
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نموذج إضافة حساب جديد", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            EditSelectedAccount();
        }

        private void EditSelectedAccount()
        {
            if (accountsGrid.SelectedRows.Count > 0)
            {
                var selectedRow = accountsGrid.SelectedRows[0];
                var accountName = selectedRow.Cells["AccountName"].Value?.ToString();
                MessageBox.Show($"سيتم فتح نموذج تعديل الحساب: {accountName}", "معلومات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (accountsGrid.SelectedRows.Count > 0)
            {
                var selectedRow = accountsGrid.SelectedRows[0];
                var accountName = selectedRow.Cells["AccountName"].Value?.ToString();
                
                var result = MessageBox.Show($"هل أنت متأكد من حذف الحساب: {accountName}؟", 
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    MessageBox.Show("تم حذف الحساب بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadAccountsData();
                }
            }
        }

        private void RefreshButton_Click(object sender, EventArgs e)
        {
            LoadAccountsData();
        }
    }
}

