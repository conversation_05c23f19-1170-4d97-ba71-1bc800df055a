using System.Data;
using System.Text;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.Helpers
{
    /// <summary>
    /// مساعد تصدير التقارير
    /// Report exporter helper
    /// </summary>
    public class ReportExporter
    {
        private readonly ILogger<ReportExporter> _logger;

        public ReportExporter(ILogger<ReportExporter> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// تصدير البيانات إلى Excel
        /// Export data to Excel
        /// </summary>
        public async Task<bool> ExportToExcelAsync(DataTable data, string filePath, string sheetName = "Sheet1")
        {
            try
            {
                _logger.LogInformation("بدء تصدير البيانات إلى Excel: {FilePath}", filePath);

                // Create a simple CSV file (Excel can open CSV files)
                var csv = new StringBuilder();
                
                // Add headers
                var headers = data.Columns.Cast<DataColumn>().Select(column => column.ColumnName);
                csv.AppendLine(string.Join(",", headers.Select(h => $"\"{h}\"")));

                // Add data rows
                foreach (DataRow row in data.Rows)
                {
                    var fields = row.ItemArray.Select(field => $"\"{field?.ToString()?.Replace("\"", "\"\"") ?? ""}\"");
                    csv.AppendLine(string.Join(",", fields));
                }

                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                
                _logger.LogInformation("تم تصدير البيانات إلى Excel بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير البيانات إلى Excel");
                return false;
            }
        }

        /// <summary>
        /// تصدير البيانات إلى PDF
        /// Export data to PDF
        /// </summary>
        public async Task<bool> ExportToPdfAsync(DataTable data, string filePath, string title = "تقرير")
        {
            try
            {
                _logger.LogInformation("بدء تصدير البيانات إلى PDF: {FilePath}", filePath);

                // Create a simple HTML file that can be converted to PDF
                var html = new StringBuilder();
                html.AppendLine("<!DOCTYPE html>");
                html.AppendLine("<html dir='rtl' lang='ar'>");
                html.AppendLine("<head>");
                html.AppendLine("<meta charset='UTF-8'>");
                html.AppendLine("<title>" + title + "</title>");
                html.AppendLine("<style>");
                html.AppendLine("body { font-family: Arial, sans-serif; direction: rtl; }");
                html.AppendLine("table { border-collapse: collapse; width: 100%; margin: 20px 0; }");
                html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }");
                html.AppendLine("th { background-color: #f2f2f2; font-weight: bold; }");
                html.AppendLine("h1 { text-align: center; color: #333; }");
                html.AppendLine(".header { text-align: center; margin-bottom: 30px; }");
                html.AppendLine(".date { text-align: left; font-size: 12px; color: #666; }");
                html.AppendLine("</style>");
                html.AppendLine("</head>");
                html.AppendLine("<body>");
                
                html.AppendLine("<div class='header'>");
                html.AppendLine($"<h1>{title}</h1>");
                html.AppendLine($"<div class='date'>تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm}</div>");
                html.AppendLine("</div>");

                html.AppendLine("<table>");
                
                // Add headers
                html.AppendLine("<thead><tr>");
                foreach (DataColumn column in data.Columns)
                {
                    html.AppendLine($"<th>{column.ColumnName}</th>");
                }
                html.AppendLine("</tr></thead>");

                // Add data rows
                html.AppendLine("<tbody>");
                foreach (DataRow row in data.Rows)
                {
                    html.AppendLine("<tr>");
                    foreach (var item in row.ItemArray)
                    {
                        html.AppendLine($"<td>{item?.ToString() ?? string.Empty}</td>");
                    }
                    html.AppendLine("</tr>");
                }
                html.AppendLine("</tbody>");
                html.AppendLine("</table>");
                html.AppendLine("</body>");
                html.AppendLine("</html>");

                // Save as HTML file (can be opened in browser and printed to PDF)
                var htmlPath = filePath.Replace(".pdf", ".html");
                await File.WriteAllTextAsync(htmlPath, html.ToString(), Encoding.UTF8);
                
                _logger.LogInformation("تم إنشاء ملف HTML للتقرير: {HtmlPath}", htmlPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير البيانات إلى PDF");
                return false;
            }
        }

        /// <summary>
        /// تصدير البيانات إلى CSV
        /// Export data to CSV
        /// </summary>
        public async Task<bool> ExportToCsvAsync(DataTable data, string filePath)
        {
            try
            {
                _logger.LogInformation("بدء تصدير البيانات إلى CSV: {FilePath}", filePath);

                var csv = new StringBuilder();
                
                // Add headers
                var headers = data.Columns.Cast<DataColumn>().Select(column => column.ColumnName);
                csv.AppendLine(string.Join(",", headers.Select(h => $"\"{h}\"")));

                // Add data rows
                foreach (DataRow row in data.Rows)
                {
                    var fields = row.ItemArray.Select(field => $"\"{field?.ToString()?.Replace("\"", "\"\"") ?? ""}\"");
                    csv.AppendLine(string.Join(",", fields));
                }

                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                
                _logger.LogInformation("تم تصدير البيانات إلى CSV بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير البيانات إلى CSV");
                return false;
            }
        }

        /// <summary>
        /// إنشاء تقرير مخصص بتنسيق HTML
        /// Create custom HTML report
        /// </summary>
        public async Task<bool> CreateCustomHtmlReportAsync(
            string filePath, 
            string title, 
            Dictionary<string, object> summary, 
            DataTable data,
            string[]? chartData = null)
        {
            try
            {
                _logger.LogInformation("بدء إنشاء تقرير HTML مخصص: {FilePath}", filePath);

                var html = new StringBuilder();
                html.AppendLine("<!DOCTYPE html>");
                html.AppendLine("<html dir='rtl' lang='ar'>");
                html.AppendLine("<head>");
                html.AppendLine("<meta charset='UTF-8'>");
                html.AppendLine("<title>" + title + "</title>");
                html.AppendLine(GetCustomCss());
                html.AppendLine("</head>");
                html.AppendLine("<body>");

                // Header
                html.AppendLine("<div class='header'>");
                html.AppendLine("<div class='logo'>نظام إدارة مزرعة الأسماك</div>");
                html.AppendLine($"<h1>{title}</h1>");
                html.AppendLine($"<div class='date'>تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm}</div>");
                html.AppendLine("</div>");

                // Summary section
                if (summary != null && summary.Any())
                {
                    html.AppendLine("<div class='summary'>");
                    html.AppendLine("<h2>ملخص التقرير</h2>");
                    html.AppendLine("<div class='summary-grid'>");
                    
                    foreach (var item in summary)
                    {
                        html.AppendLine("<div class='summary-item'>");
                        html.AppendLine($"<div class='summary-label'>{item.Key}</div>");
                        html.AppendLine($"<div class='summary-value'>{item.Value}</div>");
                        html.AppendLine("</div>");
                    }
                    
                    html.AppendLine("</div>");
                    html.AppendLine("</div>");
                }

                // Data table
                if (data != null && data.Rows.Count > 0)
                {
                    html.AppendLine("<div class='data-section'>");
                    html.AppendLine("<h2>البيانات التفصيلية</h2>");
                    html.AppendLine("<table class='data-table'>");
                    
                    // Headers
                    html.AppendLine("<thead><tr>");
                    foreach (DataColumn column in data.Columns)
                    {
                        html.AppendLine($"<th>{column.ColumnName}</th>");
                    }
                    html.AppendLine("</tr></thead>");

                    // Data rows
                    html.AppendLine("<tbody>");
                    foreach (DataRow row in data.Rows)
                    {
                        html.AppendLine("<tr>");
                        foreach (var item in row.ItemArray)
                        {
                            html.AppendLine($"<td>{item?.ToString() ?? ""}</td>");
                        }
                        html.AppendLine("</tr>");
                    }
                    html.AppendLine("</tbody>");
                    html.AppendLine("</table>");
                    html.AppendLine("</div>");
                }

                // Footer
                html.AppendLine("<div class='footer'>");
                html.AppendLine("<p>تم إنشاء هذا التقرير بواسطة نظام إدارة مزرعة الأسماك</p>");
                html.AppendLine($"<p>© {DateTime.Now.Year} جميع الحقوق محفوظة</p>");
                html.AppendLine("</div>");

                html.AppendLine("</body>");
                html.AppendLine("</html>");

                await File.WriteAllTextAsync(filePath, html.ToString(), Encoding.UTF8);
                
                _logger.LogInformation("تم إنشاء التقرير المخصص بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء التقرير المخصص");
                return false;
            }
        }

        private string GetCustomCss()
        {
            return @"
<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        direction: rtl;
        margin: 0;
        padding: 20px;
        background-color: #f8f9fa;
        color: #333;
    }
    
    .header {
        text-align: center;
        margin-bottom: 40px;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    
    .logo {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    h1 {
        margin: 15px 0;
        font-size: 28px;
        font-weight: bold;
    }
    
    .date {
        font-size: 14px;
        opacity: 0.9;
    }
    
    .summary {
        background: white;
        padding: 25px;
        margin-bottom: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .summary h2 {
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }
    
    .summary-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }
    
    .summary-label {
        font-weight: bold;
        color: #6c757d;
        margin-bottom: 5px;
    }
    
    .summary-value {
        font-size: 18px;
        font-weight: bold;
        color: #495057;
    }
    
    .data-section {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .data-section h2 {
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }
    
    .data-table th,
    .data-table td {
        padding: 12px;
        text-align: right;
        border-bottom: 1px solid #dee2e6;
    }
    
    .data-table th {
        background-color: #f8f9fa;
        font-weight: bold;
        color: #495057;
        border-top: 2px solid #007bff;
    }
    
    .data-table tbody tr:hover {
        background-color: #f8f9fa;
    }
    
    .footer {
        text-align: center;
        margin-top: 40px;
        padding: 20px;
        background: #6c757d;
        color: white;
        border-radius: 10px;
        font-size: 14px;
    }
    
    @media print {
        body { background-color: white; }
        .header, .summary, .data-section { box-shadow: none; }
    }
</style>";
        }
    }
}
