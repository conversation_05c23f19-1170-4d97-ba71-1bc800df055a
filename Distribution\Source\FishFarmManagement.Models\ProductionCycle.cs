using System.ComponentModel.DataAnnotations;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// الدورة الإنتاجية
    /// Production cycle
    /// </summary>
    public class ProductionCycle : BaseEntity
    {
        [Required(ErrorMessage = "اسم الدورة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الدورة يجب أن يكون أقل من 100 حرف")]
        public string CycleName { get; set; } = string.Empty;

        [Required(ErrorMessage = "تاريخ البداية مطلوب")]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        [Required(ErrorMessage = "تاريخ النهاية المتوقع مطلوب")]
        public DateTime ExpectedEndDate { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "نشط"; // نشط، مكتمل، متوقف، ملغي

        [Range(0, double.MaxValue, ErrorMessage = "مبلغ الميزانية يجب أن يكون أكبر من الصفر")]
        public decimal BudgetAmount { get; set; }

        [StringLength(1000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 1000 حرف")]
        public string Notes { get; set; } = string.Empty;

        // Navigation Properties
        public virtual ICollection<Pond> Ponds { get; set; } = new List<Pond>();
        public virtual ICollection<Payroll> Payrolls { get; set; } = new List<Payroll>();
        public virtual ICollection<Transaction> Transactions { get; set; } = new List<Transaction>();
        public virtual ICollection<CostCenter> CostCenters { get; set; } = new List<CostCenter>();

        /// <summary>
        /// حساب مدة الدورة بالأيام
        /// Calculate cycle duration in days
        /// </summary>
        public int GetCycleDurationInDays()
        {
            var endDate = EndDate ?? ExpectedEndDate;
            return (endDate - StartDate).Days;
        }

        /// <summary>
        /// التحقق من انتهاء الدورة
        /// Check if cycle is completed
        /// </summary>
        public bool IsCompleted => Status == "مكتمل" && EndDate.HasValue;

        /// <summary>
        /// التحقق من تأخر الدورة
        /// Check if cycle is overdue
        /// </summary>
        public bool IsOverdue => !IsCompleted && DateTime.Now > ExpectedEndDate;
    }
}
