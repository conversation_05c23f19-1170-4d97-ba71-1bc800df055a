﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.DAL.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج التقارير
    /// Reports form
    /// </summary>
    public partial class ReportsForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ReportsForm> _logger;

        public ReportsForm(IUnitOfWork unitOfWork, ILogger<ReportsForm> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "التقارير";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateReportsMenu();
        }

        private void CreateReportsMenu()
        {
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 3,
                Padding = new Padding(20)
            };

            // Set column and row styles
            for (int i = 0; i < 3; i++)
            {
                mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
                mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33F));
            }

            // Create report buttons
            var reports = new[]
            {
                new { Name = "تقرير الأحواض", Description = "تقرير شامل عن حالة الأحواض", Action = (Action)(() => ShowPondsReport()) },
                new { Name = "تقرير الإنتاج", Description = "تقرير الدورات الإنتاجية", Action = (Action)(() => ShowProductionReport()) },
                new { Name = "تقرير الموظفين", Description = "تقرير شؤون الموظفين", Action = (Action)(() => ShowEmployeesReport()) },
                new { Name = "تقرير الرواتب", Description = "تقرير الرواتب والمستحقات", Action = (Action)(() => ShowPayrollReport()) },
                new { Name = "التقارير المالية", Description = "التقارير المحاسبية والمالية", Action = (Action)(() => ShowFinancialReports()) },
                new { Name = "تقرير المخزون", Description = "تقرير حالة المخزون", Action = (Action)(() => ShowInventoryReport()) },
                new { Name = "تقرير التكاليف", Description = "تقرير تحليل التكاليف", Action = (Action)(() => ShowCostAnalysisReport()) },
                new { Name = "تقرير الأرباح", Description = "تقرير الأرباح والخسائر", Action = (Action)(() => ShowProfitLossReport()) },
                new { Name = "تقارير مخصصة", Description = "إنشاء تقارير مخصصة", Action = (Action)(() => ShowCustomReports()) }
            };

            for (int i = 0; i < reports.Length; i++)
            {
                var report = reports[i];
                var button = CreateReportButton(report.Name, report.Description, report.Action);
                mainPanel.Controls.Add(button, i % 3, i / 3);
            }

            this.Controls.Add(mainPanel);
        }

        private Button CreateReportButton(string title, string description, Action clickAction)
        {
            var button = new Button
            {
                Text = $"{title}\n\n{description}",
                Font = new Font("Segoe UI", 10F),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Margin = new Padding(10),
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            button.Click += (s, e) => clickAction?.Invoke();

            // Add hover effect
            button.MouseEnter += (s, e) => button.BackColor = Color.FromArgb(41, 128, 185);
            button.MouseLeave += (s, e) => button.BackColor = Color.FromArgb(52, 152, 219);

            return button;
        }

        private void ShowPondsReport()
        {
            try
            {
                var reportForm = new PondsReportForm(_unitOfWork);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح تقرير الأحواض");
                MessageBox.Show($"خطأ في فتح التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowProductionReport()
        {
            try
            {
                var reportForm = new ProductionReportForm(_unitOfWork);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح تقرير الإنتاج");
                MessageBox.Show($"خطأ في فتح التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowEmployeesReport()
        {
            MessageBox.Show("تقرير الموظفين - قيد التطوير", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowPayrollReport()
        {
            try
            {
                var reportForm = new PayrollReportForm(_unitOfWork);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح تقرير الرواتب");
                MessageBox.Show($"خطأ في فتح التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowFinancialReports()
        {
            try
            {
                var accountingLogger = Microsoft.Extensions.Logging.LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<AccountingForm>();
                var accountingForm = new AccountingForm(_unitOfWork, accountingLogger);
                accountingForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح التقارير المالية");
                MessageBox.Show($"خطأ في فتح التقارير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowInventoryReport()
        {
            MessageBox.Show("تقرير المخزون - قيد التطوير", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCostAnalysisReport()
        {
            MessageBox.Show("تقرير التكاليف - قيد التطوير", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowProfitLossReport()
        {
            MessageBox.Show("تقرير الأرباح والخسائر - قيد التطوير", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCustomReports()
        {
            MessageBox.Show("التقارير المخصصة - قيد التطوير", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}

