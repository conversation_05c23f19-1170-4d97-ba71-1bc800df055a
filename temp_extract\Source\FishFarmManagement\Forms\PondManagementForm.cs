﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ø£Ø­ÙˆØ§Ø¶
    /// Pond management form
    /// </summary>
    public partial class PondManagementForm : Form
    {
        private readonly IPondService _pondService;
        private readonly ILogger<PondManagementForm> _logger;

        // UI Controls
        private DataGridView pondsDataGridView;
        private ToolStripButton addButton;
        private ToolStripButton editButton;
        private ToolStripButton deleteButton;
        private ToolStripButton refreshButton;
        private ToolStripButton viewDetailsButton;
        private TextBox searchTextBox;
        private ComboBox statusFilterComboBox;
        private Label statusLabel;
        private ToolStrip toolStrip;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusStripLabel;

        public PondManagementForm(IPondService pondService, ILogger<PondManagementForm> logger)
        {
            _pondService = pondService ?? throw new ArgumentNullException(nameof(pondService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            LoadPondsAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ø£Ø­ÙˆØ§Ø¶";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            CreateToolStrip();
            CreateMainPanel();
            CreateStatusStrip();
        }

        private void CreateToolStrip()
        {
            toolStrip = new ToolStrip
            {
                ImageScalingSize = new Size(24, 24),
                Font = new Font("Segoe UI", 9F)
            };

            addButton = new ToolStripButton("Ø¥Ø¶Ø§ÙØ© Ø­ÙˆØ¶ Ø¬Ø¯ÙŠØ¯", null, AddPond_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };

            editButton = new ToolStripButton("ØªØ¹Ø¯ÙŠÙ„", null, EditPond_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            deleteButton = new ToolStripButton("Ø­Ø°Ù", null, DeletePond_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            viewDetailsButton = new ToolStripButton("Ø¹Ø±Ø¶ Ø§Ù„ØªÙØ§ØµÙŠÙ„", null, ViewDetails_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            refreshButton = new ToolStripButton("ØªØ­Ø¯ÙŠØ«", null, RefreshData_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };

            toolStrip.Items.AddRange(new ToolStripItem[]
            {
                addButton,
                new ToolStripSeparator(),
                editButton,
                deleteButton,
                new ToolStripSeparator(),
                viewDetailsButton,
                new ToolStripSeparator(),
                refreshButton
            });

            this.Controls.Add(toolStrip);
        }

        private void CreateMainPanel()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            // Create filter panel
            var filterPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Top
            };

            // Search textbox
            var searchLabel = new Label
            {
                Text = "Ø§Ù„Ø¨Ø­Ø«:",
                Location = new Point(10, 15),
                Size = new Size(50, 20)
            };

            searchTextBox = new TextBox
            {
                Location = new Point(70, 12),
                Size = new Size(200, 25)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            // Status filter
            statusLabel = new Label
            {
                Text = "Ø§Ù„Ø­Ø§Ù„Ø©:",
                Location = new Point(290, 15),
                Size = new Size(50, 20)
            };

            statusFilterComboBox = new ComboBox
            {
                Location = new Point(350, 12),
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            statusFilterComboBox.Items.AddRange(new[] { "Ø§Ù„ÙƒÙ„", "Ù†Ø´Ø·", "Ù…Ø­ØµÙˆØ¯", "ÙØ§Ø±Øº", "ØµÙŠØ§Ù†Ø©" });
            statusFilterComboBox.SelectedIndex = 0;
            statusFilterComboBox.SelectedIndexChanged += StatusFilter_SelectedIndexChanged;

            filterPanel.Controls.AddRange(new Control[] { searchLabel, searchTextBox, statusLabel, statusFilterComboBox });

            // Create data grid view
            pondsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            SetupDataGridViewColumns();
            pondsDataGridView.SelectionChanged += PondsDataGridView_SelectionChanged;
            pondsDataGridView.CellDoubleClick += PondsDataGridView_CellDoubleClick;

            mainPanel.Controls.Add(pondsDataGridView);
            mainPanel.Controls.Add(filterPanel);
            this.Controls.Add(mainPanel);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();
            statusStripLabel = new ToolStripStatusLabel("Ø¬Ø§Ù‡Ø²")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };
            statusStrip.Items.Add(statusStripLabel);
            this.Controls.Add(statusStrip);
        }

        private void SetupDataGridViewColumns()
        {
            pondsDataGridView.Columns.Clear();

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "Ø§Ù„Ù…Ø¹Ø±Ù",
                DataPropertyName = "Id",
                Visible = false
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PondNumber",
                HeaderText = "Ø±Ù‚Ù… Ø§Ù„Ø­ÙˆØ¶",
                DataPropertyName = "PondNumber",
                Width = 100
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CycleId",
                HeaderText = "Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©",
                DataPropertyName = "CycleId",
                Width = 120
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FishCount",
                HeaderText = "Ø¹Ø¯Ø¯ Ø§Ù„Ø£Ø³Ù…Ø§Ùƒ",
                DataPropertyName = "FishCount",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N0" }
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AverageWeight",
                HeaderText = "Ù…ØªÙˆØ³Ø· Ø§Ù„ÙˆØ²Ù† (ÙƒØ¬Ù…)",
                DataPropertyName = "AverageWeight",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N3" }
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "StockingDate",
                HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„ØªØ®Ø²ÙŠÙ†",
                DataPropertyName = "StockingDate",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ExpectedHarvestDate",
                HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø­ØµØ§Ø¯ Ø§Ù„Ù…ØªÙˆÙ‚Ø¹",
                DataPropertyName = "ExpectedHarvestDate",
                Width = 140,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "Ø§Ù„Ø­Ø§Ù„Ø©",
                DataPropertyName = "Status",
                Width = 80
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Notes",
                HeaderText = "Ø§Ù„Ù…Ù„Ø§Ø­Ø¸Ø§Øª",
                DataPropertyName = "Notes",
                Width = 200
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø¥Ù†Ø´Ø§Ø¡",
                DataPropertyName = "CreatedDate",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd HH:mm" }
            });
        }

        private async void LoadPondsAsync()
        {
            try
            {
                statusStripLabel.Text = "Ø¬Ø§Ø±ÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª...";
                this.Cursor = Cursors.WaitCursor;

                var ponds = await _pondService.GetAllAsync();
                pondsDataGridView.DataSource = ponds.ToList();

                statusStripLabel.Text = $"ØªÙ… ØªØ­Ù…ÙŠÙ„ {ponds.Count()} Ø­ÙˆØ¶";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø£Ø­ÙˆØ§Ø¶");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusStripLabel.Text = "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª";
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void PondsDataGridView_SelectionChanged(object? sender, EventArgs e)
        {
            bool hasSelection = pondsDataGridView.SelectedRows.Count > 0;
            editButton.Enabled = hasSelection;
            deleteButton.Enabled = hasSelection;
            viewDetailsButton.Enabled = hasSelection;
        }

        private void PondsDataGridView_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                ViewDetails_Click(sender, e);
            }
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void StatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            try
            {
                var bindingSource = pondsDataGridView.DataSource as List<Pond>;
                if (bindingSource == null) return;

                var filteredData = bindingSource.AsEnumerable();

                // Apply search filter
                if (!string.IsNullOrWhiteSpace(searchTextBox.Text))
                {
                    var searchTerm = searchTextBox.Text.ToLower();
                    filteredData = filteredData.Where(p =>
                        p.PondNumber.ToLower().Contains(searchTerm) ||
                        p.Notes.ToLower().Contains(searchTerm));
                }

                // Apply status filter
                if (statusFilterComboBox.SelectedIndex > 0)
                {
                    var selectedStatus = statusFilterComboBox.SelectedItem.ToString();
                    filteredData = filteredData.Where(p => p.Status == selectedStatus);
                }

                pondsDataGridView.DataSource = filteredData.ToList();
                statusStripLabel.Text = $"Ø¹Ø±Ø¶ {filteredData.Count()} Ù…Ù† Ø£ØµÙ„ {bindingSource.Count} Ø­ÙˆØ¶";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ·Ø¨ÙŠÙ‚ Ø§Ù„Ù…Ø±Ø´Ø­Ø§Øª");
            }
        }

        private async void private async void private async void AddPond_Click(object? sender, EventArgs e)
        {
            try
            {
                var addForm = new PondAddEditForm(_pondService, _logger);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadPondsAsync();
                    statusStripLabel.Text = "ØªÙ… Ø¥Ø¶Ø§ÙØ© Ø­ÙˆØ¶ Ø¬Ø¯ÙŠØ¯ Ø¨Ù†Ø¬Ø§Ø­";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¶Ø§ÙØ© Ø­ÙˆØ¶");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ø§Ù„Ù†Ù…ÙˆØ°Ø¬: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void private async void private async void EditPond_Click(object? sender, EventArgs e)
        {
            try
            {
                if (pondsDataGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ø­ÙˆØ¶ Ù„Ù„ØªØ¹Ø¯ÙŠÙ„", "ØªÙ†Ø¨ÙŠÙ‡",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedPond = pondsDataGridView.SelectedRows[0].DataBoundItem as Pond;
                if (selectedPond == null) return;

                var editForm = new PondAddEditForm(_pondService, _logger, selectedPond);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadPondsAsync();
                    statusStripLabel.Text = $"ØªÙ… ØªØ­Ø¯ÙŠØ« Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø­ÙˆØ¶ {selectedPond.PondNumber} Ø¨Ù†Ø¬Ø§Ø­";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ ØªØ¹Ø¯ÙŠÙ„ Ø­ÙˆØ¶");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ø§Ù„Ù†Ù…ÙˆØ°Ø¬: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeletePond_Click(object? sender, EventArgs e)
        {
            try
            {
                if (pondsDataGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ø­ÙˆØ¶ Ù„Ù„Ø­Ø°Ù", "ØªÙ†Ø¨ÙŠÙ‡",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedPond = pondsDataGridView.SelectedRows[0].DataBoundItem as Pond;
                if (selectedPond == null) return;

                // Check if pond has active data
                var hasActiveData = await CheckPondHasActiveData(selectedPond.Id);
                var warningMessage = hasActiveData
                    ? $"ØªØ­Ø°ÙŠØ±: Ø§Ù„Ø­ÙˆØ¶ '{selectedPond.PondNumber}' ÙŠØ­ØªÙˆÙŠ Ø¹Ù„Ù‰ Ø¨ÙŠØ§Ù†Ø§Øª Ù†Ø´Ø·Ø© (ØªØºØ°ÙŠØ©ØŒ Ù†ÙÙˆÙ‚ØŒ Ø£Ø¯ÙˆÙŠØ©).\n\nÙ‡Ù„ Ø£Ù†Øª Ù…ØªØ£ÙƒØ¯ Ù…Ù† Ø­Ø°Ù Ø§Ù„Ø­ÙˆØ¶ ÙˆØ¬Ù…ÙŠØ¹ Ø¨ÙŠØ§Ù†Ø§ØªÙ‡ØŸ\n\nÙ‡Ø°Ø§ Ø§Ù„Ø¥Ø¬Ø±Ø§Ø¡ Ù„Ø§ ÙŠÙ…ÙƒÙ† Ø§Ù„ØªØ±Ø§Ø¬Ø¹ Ø¹Ù†Ù‡."
                    : $"Ù‡Ù„ Ø£Ù†Øª Ù…ØªØ£ÙƒØ¯ Ù…Ù† Ø­Ø°Ù Ø§Ù„Ø­ÙˆØ¶ '{selectedPond.PondNumber}'ØŸ\n\nÙ‡Ø°Ø§ Ø§Ù„Ø¥Ø¬Ø±Ø§Ø¡ Ù„Ø§ ÙŠÙ…ÙƒÙ† Ø§Ù„ØªØ±Ø§Ø¬Ø¹ Ø¹Ù†Ù‡.";

                var result = MessageBox.Show(warningMessage, "ØªØ£ÙƒÙŠØ¯ Ø§Ù„Ø­Ø°Ù",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    statusStripLabel.Text = "Ø¬Ø§Ø±ÙŠ Ø­Ø°Ù Ø§Ù„Ø­ÙˆØ¶...";
                    this.Cursor = Cursors.WaitCursor;

                    bool deleted = await _pondService.DeleteAsync(selectedPond.Id);
                    if (deleted)
                    {
                        LoadPondsAsync();
                        statusStripLabel.Text = $"ØªÙ… Ø­Ø°Ù Ø§Ù„Ø­ÙˆØ¶ {selectedPond.PondNumber} Ø¨Ù†Ø¬Ø§Ø­";
                        MessageBox.Show("ØªÙ… Ø­Ø°Ù Ø§Ù„Ø­ÙˆØ¶ Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        statusStripLabel.Text = "ÙØ´Ù„ ÙÙŠ Ø­Ø°Ù Ø§Ù„Ø­ÙˆØ¶";
                        MessageBox.Show("ÙØ´Ù„ ÙÙŠ Ø­Ø°Ù Ø§Ù„Ø­ÙˆØ¶. Ù‚Ø¯ ÙŠÙƒÙˆÙ† Ø§Ù„Ø­ÙˆØ¶ Ù…Ø±ØªØ¨Ø· Ø¨Ø¨ÙŠØ§Ù†Ø§Øª Ø£Ø®Ø±Ù‰.", "Ø®Ø·Ø£",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„Ø­ÙˆØ¶");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„Ø­ÙˆØ¶: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                statusStripLabel.Text = "Ø¬Ø§Ù‡Ø²";
            }
        }

        private async void private async void private async void ViewDetails_Click(object? sender, EventArgs e)
        {
            try
            {
                if (pondsDataGridView.SelectedRows.Count == 0) return;

                var selectedPond = pondsDataGridView.SelectedRows[0].DataBoundItem as Pond;
                if (selectedPond == null) return;

                statusStripLabel.Text = "Ø¬Ø§Ø±ÙŠ ØªØ­Ù…ÙŠÙ„ ØªÙØ§ØµÙŠÙ„ Ø§Ù„Ø­ÙˆØ¶...";
                this.Cursor = Cursors.WaitCursor;

                var statusReport = await _pondService.GetPondStatusReportAsync(selectedPond.Id);
                var detailsForm = new PondDetailsForm(statusReport);
                detailsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¹Ø±Ø¶ ØªÙØ§ØµÙŠÙ„ Ø§Ù„Ø­ÙˆØ¶");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¹Ø±Ø¶ Ø§Ù„ØªÙØ§ØµÙŠÙ„: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                statusStripLabel.Text = "Ø¬Ø§Ù‡Ø²";
            }
        }

        private void RefreshData_Click(object? sender, EventArgs e)
        {
            LoadPondsAsync();
            statusStripLabel.Text = "ØªÙ… ØªØ­Ø¯ÙŠØ« Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª";
        }

        /// <summary>
        /// Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† ÙˆØ¬ÙˆØ¯ Ø¨ÙŠØ§Ù†Ø§Øª Ù†Ø´Ø·Ø© Ù„Ù„Ø­ÙˆØ¶
        /// Check if pond has active data
        /// </summary>
        private async Task<bool> CheckPondHasActiveData(int pondId)
        {
            try
            {
                var feedConsumptions = await _pondService.GetTotalFeedConsumptionAsync(pondId);
                return feedConsumptions > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ù†Ø´Ø·Ø© Ù„Ù„Ø­ÙˆØ¶ {PondId}", pondId);
                return false;
            }
        }

        /// <summary>
        /// ØªØ­Ø¯ÙŠØ« Ø´Ø±ÙŠØ· Ø§Ù„Ø­Ø§Ù„Ø©
        /// Update status bar
        /// </summary>
        private void UpdateStatusBar(string? message = null)
        {
            if (!string.IsNullOrEmpty(message))
            {
                statusStripLabel.Text = message;
            }
            else
            {
                var totalPonds = pondsDataGridView.Rows.Count;
                statusStripLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø£Ø­ÙˆØ§Ø¶: {totalPonds}";
            }
        }
    }
}



