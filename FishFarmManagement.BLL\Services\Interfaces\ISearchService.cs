using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة البحث الديناميكي
    /// Dynamic search service interface
    /// </summary>
    public interface ISearchService
    {
        /// <summary>
        /// البحث في الحسابات
        /// Search accounts
        /// </summary>
        Task<IEnumerable<AccountSearchResult>> SearchAccountsAsync(string searchTerm, int maxResults = 10);

        /// <summary>
        /// البحث في الموظفين
        /// Search employees
        /// </summary>
        Task<IEnumerable<EmployeeSearchResult>> SearchEmployeesAsync(string searchTerm, int maxResults = 10);

        /// <summary>
        /// البحث في أصناف المخزون
        /// Search inventory items
        /// </summary>
        Task<IEnumerable<InventorySearchResult>> SearchInventoryItemsAsync(string searchTerm, int maxResults = 10);

        /// <summary>
        /// البحث في الأدوية
        /// Search medications
        /// </summary>
        Task<IEnumerable<MedicationSearchResult>> SearchMedicationsAsync(string searchTerm, int maxResults = 10);

        /// <summary>
        /// البحث في الأحواض
        /// Search ponds
        /// </summary>
        Task<IEnumerable<PondSearchResult>> SearchPondsAsync(string searchTerm, int maxResults = 10);

        /// <summary>
        /// البحث في أنواع العلف
        /// Search feed types
        /// </summary>
        Task<IEnumerable<FeedTypeSearchResult>> SearchFeedTypesAsync(string searchTerm, int maxResults = 10);

        /// <summary>
        /// البحث العام في جميع الكيانات
        /// Global search across all entities
        /// </summary>
        Task<GlobalSearchResult> GlobalSearchAsync(string searchTerm, int maxResults = 5);
    }

    /// <summary>
    /// نتيجة البحث في الحسابات
    /// Account search result
    /// </summary>
    public class AccountSearchResult
    {
        public int Id { get; set; }
        public string AccountCode { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public string AccountType { get; set; } = string.Empty;
        public decimal Balance { get; set; }
        public string Status { get; set; } = string.Empty;
        public string DisplayText => $"{AccountCode} - {AccountName}";
        public string SecondaryText => $"{AccountType} | الرصيد: {Balance:C} | {Status}";
    }

    /// <summary>
    /// نتيجة البحث في الموظفين
    /// Employee search result
    /// </summary>
    public class EmployeeSearchResult
    {
        public int Id { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public string? NationalId { get; set; }
        public string? Phone { get; set; }
        public string Status { get; set; } = string.Empty;
        public string DisplayText => FullName;
        public string SecondaryText => $"{Position} | {NationalId} | {Phone} | {Status}";
    }

    /// <summary>
    /// نتيجة البحث في المخزون
    /// Inventory search result
    /// </summary>
    public class InventorySearchResult
    {
        public int Id { get; set; }
        public string ItemCode { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public decimal CurrentQuantity { get; set; }
        public string Unit { get; set; } = string.Empty;
        public decimal UnitCost { get; set; }
        public string Status { get; set; } = string.Empty;
        public string DisplayText => $"{ItemCode} - {ItemName}";
        public string SecondaryText => $"{Category} | الكمية: {CurrentQuantity} {Unit} | {UnitCost:C} | {Status}";
    }

    /// <summary>
    /// نتيجة البحث في الأدوية
    /// Medication search result
    /// </summary>
    public class MedicationSearchResult
    {
        public int Id { get; set; }
        public string MedicationName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Dosage { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
        public DateTime? ExpiryDate { get; set; }
        public string DisplayText => MedicationName;
        public string SecondaryText => $"{Type} | {Dosage} {Unit} | انتهاء: {ExpiryDate?.ToString("dd/MM/yyyy") ?? "غير محدد"}";
    }

    /// <summary>
    /// نتيجة البحث في الأحواض
    /// Pond search result
    /// </summary>
    public class PondSearchResult
    {
        public int Id { get; set; }
        public string PondName { get; set; } = string.Empty;
        public decimal Capacity { get; set; }
        public decimal CurrentStock { get; set; }
        public string Status { get; set; } = string.Empty;
        public string DisplayText => PondName;
        public string SecondaryText => $"السعة: {Capacity} | المخزون: {CurrentStock} | {Status}";
    }

    /// <summary>
    /// نتيجة البحث في أنواع العلف
    /// Feed type search result
    /// </summary>
    public class FeedTypeSearchResult
    {
        public int Id { get; set; }
        public string FeedName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public decimal ProteinPercentage { get; set; }
        public decimal PricePerKg { get; set; }
        public string DisplayText => FeedName;
        public string SecondaryText => $"{Type} | بروتين: {ProteinPercentage}% | {PricePerKg:C}/كجم";
    }

    /// <summary>
    /// نتيجة البحث العام
    /// Global search result
    /// </summary>
    public class GlobalSearchResult
    {
        public IEnumerable<AccountSearchResult> Accounts { get; set; } = new List<AccountSearchResult>();
        public IEnumerable<EmployeeSearchResult> Employees { get; set; } = new List<EmployeeSearchResult>();
        public IEnumerable<InventorySearchResult> InventoryItems { get; set; } = new List<InventorySearchResult>();
        public IEnumerable<MedicationSearchResult> Medications { get; set; } = new List<MedicationSearchResult>();
        public IEnumerable<PondSearchResult> Ponds { get; set; } = new List<PondSearchResult>();
        public IEnumerable<FeedTypeSearchResult> FeedTypes { get; set; } = new List<FeedTypeSearchResult>();
        
        public int TotalResults => 
            Accounts.Count() + Employees.Count() + InventoryItems.Count() + 
            Medications.Count() + Ponds.Count() + FeedTypes.Count();
    }
}
