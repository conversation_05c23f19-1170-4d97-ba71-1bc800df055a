using System;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;

namespace FishFarmManagement.Helpers
{
    /// <summary>
    /// مساعد للتعامل مع النصوص العربية وضمان الترميز الصحيح
    /// Helper class for Arabic text handling and proper encoding
    /// </summary>
    public static class ArabicTextHelper
    {
        /// <summary>
        /// تحقق من أن النص يحتوي على أحرف عربية
        /// Check if text contains Arabic characters
        /// </summary>
        public static bool ContainsArabic(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            return Regex.IsMatch(text, @"[\u0600-\u06FF\u0750-\u077F]");
        }

        /// <summary>
        /// تنظيف النص العربي وإزالة الأحرف غير المرغوب فيها
        /// Clean Arabic text and remove unwanted characters
        /// </summary>
        public static string CleanArabicText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // Remove control characters and normalize whitespace
            text = Regex.Replace(text, @"[\u0000-\u001F\u007F-\u009F]", "");
            text = Regex.Replace(text, @"\s+", " ");
            
            return text.Trim();
        }

        /// <summary>
        /// تحويل النص إلى UTF-8 بشكل صحيح
        /// Convert text to proper UTF-8 encoding
        /// </summary>
        public static string EnsureUtf8Encoding(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // Ensure the text is properly encoded as UTF-8
            byte[] utf8Bytes = Encoding.UTF8.GetBytes(text);
            return Encoding.UTF8.GetString(utf8Bytes);
        }

        /// <summary>
        /// تنسيق النص العربي للعرض في واجهة المستخدم
        /// Format Arabic text for UI display
        /// </summary>
        public static string FormatForDisplay(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            text = CleanArabicText(text);
            text = EnsureUtf8Encoding(text);
            
            return text;
        }

        /// <summary>
        /// تحقق من صحة النص العربي
        /// Validate Arabic text
        /// </summary>
        public static bool IsValidArabicText(string text, bool allowEnglish = true, bool allowNumbers = true)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            // Build regex pattern based on allowed characters
            string pattern = @"^[\u0600-\u06FF\u0750-\u077F\s";
            
            if (allowEnglish)
                pattern += @"a-zA-Z";
            
            if (allowNumbers)
                pattern += @"0-9";
            
            pattern += @".,!?()-]+$";

            return Regex.IsMatch(text, pattern);
        }

        /// <summary>
        /// تحويل الأرقام الإنجليزية إلى عربية
        /// Convert English numbers to Arabic numbers
        /// </summary>
        public static string ConvertToArabicNumbers(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            var arabicNumbers = new string[] { "٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩" };
            
            for (int i = 0; i < 10; i++)
            {
                text = text.Replace(i.ToString(), arabicNumbers[i]);
            }
            
            return text;
        }

        /// <summary>
        /// تحويل الأرقام العربية إلى إنجليزية
        /// Convert Arabic numbers to English numbers
        /// </summary>
        public static string ConvertToEnglishNumbers(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            var arabicNumbers = new string[] { "٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩" };
            
            for (int i = 0; i < 10; i++)
            {
                text = text.Replace(arabicNumbers[i], i.ToString());
            }
            
            return text;
        }

        /// <summary>
        /// تنسيق التاريخ بالتقويم الهجري
        /// Format date with Hijri calendar
        /// </summary>
        public static string FormatHijriDate(DateTime date)
        {
            var hijriCalendar = new HijriCalendar();
            var arabicCulture = new CultureInfo("ar-SA");
            arabicCulture.DateTimeFormat.Calendar = hijriCalendar;
            
            return date.ToString("dd/MM/yyyy", arabicCulture);
        }

        /// <summary>
        /// تنسيق العملة بالريال السعودي
        /// Format currency in Saudi Riyal
        /// </summary>
        public static string FormatCurrency(decimal amount)
        {
            var arabicCulture = new CultureInfo("ar-SA");
            return amount.ToString("C", arabicCulture);
        }

        /// <summary>
        /// تحقق من اتجاه النص (من اليمين إلى اليسار)
        /// Check if text direction is Right-to-Left
        /// </summary>
        public static bool IsRightToLeft(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            return ContainsArabic(text);
        }

        /// <summary>
        /// تطبيق إعدادات الثقافة العربية على النص
        /// Apply Arabic culture settings to text
        /// </summary>
        public static string ApplyArabicCulture(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // Ensure proper text direction and formatting
            text = FormatForDisplay(text);
            
            // Apply Arabic culture formatting if needed
            var arabicCulture = new CultureInfo("ar-SA");
            System.Threading.Thread.CurrentThread.CurrentCulture = arabicCulture;
            System.Threading.Thread.CurrentThread.CurrentUICulture = arabicCulture;
            
            return text;
        }

        /// <summary>
        /// تحضير النص للحفظ في قاعدة البيانات
        /// Prepare text for database storage
        /// </summary>
        public static string PrepareForDatabase(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // Clean and ensure proper UTF-8 encoding
            text = CleanArabicText(text);
            text = EnsureUtf8Encoding(text);
            
            // Normalize Unicode characters
            text = text.Normalize(NormalizationForm.FormC);
            
            return text;
        }

        /// <summary>
        /// استرجاع النص من قاعدة البيانات وتنسيقه للعرض
        /// Retrieve text from database and format for display
        /// </summary>
        public static string RetrieveFromDatabase(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // Ensure proper UTF-8 decoding and formatting
            text = EnsureUtf8Encoding(text);
            text = FormatForDisplay(text);
            
            return text;
        }
    }
}
