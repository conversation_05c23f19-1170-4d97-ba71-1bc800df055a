﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.Models;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.BLL.Services;
using System.Data;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج محسن للتقارير
    /// Enhanced reports form
    /// </summary>
    public partial class EnhancedReportsForm : Form
    {
        private readonly IReportService _reportService;
        private readonly EnhancedReportService _enhancedReportService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<EnhancedReportsForm> _logger;

        // UI Controls
        private TabControl tabControl;
        private TabPage productionReportsTab;
        private TabPage financialReportsTab;
        private TabPage employeeReportsTab;
        private TabPage inventoryReportsTab;
        private TabPage customReportsTab;

        // Common Controls
        private DateTimePicker fromDatePicker;
        private DateTimePicker toDatePicker;
        // private ComboBox reportFormatComboBox; // غير مستخدم
        private Button generateButton;
        private Button exportButton;
        private Button printButton;
        private DataGridView reportDataGridView;
        private RichTextBox reportSummaryTextBox;

        public EnhancedReportsForm(IReportService reportService, EnhancedReportService enhancedReportService, IUnitOfWork unitOfWork, ILogger<EnhancedReportsForm> logger)
        {
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _enhancedReportService = enhancedReportService ?? throw new ArgumentNullException(nameof(enhancedReportService));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "التقارير المحسنة";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            CreateTabControl();
            CreateProductionReportsTab();
            CreateFinancialReportsTab();
            CreateEmployeeReportsTab();
            CreateInventoryReportsTab();
            CreateCustomReportsTab();
        }

        private void CreateTabControl()
        {
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            productionReportsTab = new TabPage("تقارير الإنتاج");
            financialReportsTab = new TabPage("التقارير المالية");
            employeeReportsTab = new TabPage("تقارير الموظفين");
            inventoryReportsTab = new TabPage("تقارير المخزون");
            customReportsTab = new TabPage("التقارير المخصصة");

            tabControl.TabPages.AddRange(new TabPage[] {
                productionReportsTab, financialReportsTab, employeeReportsTab,
                inventoryReportsTab, customReportsTab
            });

            this.Controls.Add(tabControl);
        }

        private void CreateProductionReportsTab()
        {
            var controlsPanel = new Panel
            {
                Height = 120,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(245, 245, 245),
                Padding = new Padding(20)
            };

            // Report type selection
            var reportTypeLabel = new Label
            {
                Text = "نوع التقرير:",
                Size = new Size(80, 23),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleRight
            };

            var reportTypeComboBox = new ComboBox
            {
                Size = new Size(200, 23),
                Location = new Point(110, 17),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            reportTypeComboBox.Items.AddRange(new[] {
                "تقرير الإنتاج اليومي",
                "تقرير الإنتاج الشهري",
                "تقرير أداء الأحواض",
                "تقرير استهلاك العلف",
                "تقرير نفوق الأسماك",
                "تقرير الأدوية المستخدمة",
                "تقرير مقارنة الدورات"
            });
            reportTypeComboBox.SelectedIndex = 0;

            // Date range
            var fromLabel = new Label
            {
                Text = "من تاريخ:",
                Size = new Size(60, 23),
                Location = new Point(330, 20),
                TextAlign = ContentAlignment.MiddleRight
            };

            fromDatePicker = new DateTimePicker
            {
                Size = new Size(150, 23),
                Location = new Point(400, 17),
                Value = DateTime.Now.AddMonths(-1)
            };

            var toLabel = new Label
            {
                Text = "إلى تاريخ:",
                Size = new Size(60, 23),
                Location = new Point(570, 20),
                TextAlign = ContentAlignment.MiddleRight
            };

            toDatePicker = new DateTimePicker
            {
                Size = new Size(150, 23),
                Location = new Point(640, 17),
                Value = DateTime.Now
            };

            // Cycle selection
            var cycleLabel = new Label
            {
                Text = "الدورة الإنتاجية:",
                Size = new Size(100, 23),
                Location = new Point(20, 60),
                TextAlign = ContentAlignment.MiddleRight
            };

            var cycleComboBox = new ComboBox
            {
                Size = new Size(200, 23),
                Location = new Point(130, 57),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cycleComboBox.Items.Add("جميع الدورات");
            cycleComboBox.SelectedIndex = 0;

            // Pond selection
            var pondLabel = new Label
            {
                Text = "الحوض:",
                Size = new Size(60, 23),
                Location = new Point(350, 60),
                TextAlign = ContentAlignment.MiddleRight
            };

            var pondComboBox = new ComboBox
            {
                Size = new Size(150, 23),
                Location = new Point(420, 57),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            pondComboBox.Items.Add("جميع الأحواض");
            pondComboBox.SelectedIndex = 0;

            // Action buttons
            generateButton = new Button
            {
                Text = "إنشاء التقرير",
                Size = new Size(100, 30),
                Location = new Point(590, 55),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            generateButton.Click += GenerateProductionReport_Click;

            exportButton = new Button
            {
                Text = "تصدير",
                Size = new Size(80, 30),
                Location = new Point(700, 55),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            exportButton.Click += ExportReport_Click;

            printButton = new Button
            {
                Text = "طباعة",
                Size = new Size(80, 30),
                Location = new Point(790, 55),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            printButton.Click += PrintReport_Click;

            controlsPanel.Controls.AddRange(new Control[] {
                reportTypeLabel, reportTypeComboBox, fromLabel, fromDatePicker,
                toLabel, toDatePicker, cycleLabel, cycleComboBox,
                pondLabel, pondComboBox, generateButton, exportButton, printButton
            });

            // Report display area
            var reportPanel = new Panel
            {
                Dock = DockStyle.Fill
            };

            // Summary panel
            var summaryPanel = new Panel
            {
                Height = 150,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var summaryLabel = new Label
            {
                Text = "ملخص التقرير:",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Size = new Size(120, 25),
                Location = new Point(10, 10)
            };

            reportSummaryTextBox = new RichTextBox
            {
                Size = new Size(summaryPanel.Width - 20, 110),
                Location = new Point(10, 35),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right,
                ReadOnly = true,
                BackColor = Color.FromArgb(250, 250, 250),
                Font = new Font("Segoe UI", 10F)
            };

            summaryPanel.Controls.AddRange(new Control[] { summaryLabel, reportSummaryTextBox });

            // Data grid
            reportDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F)
            };

            reportPanel.Controls.Add(reportDataGridView);
            reportPanel.Controls.Add(summaryPanel);

            productionReportsTab.Controls.Add(reportPanel);
            productionReportsTab.Controls.Add(controlsPanel);

            // Load initial data
            LoadCyclesAndPonds(cycleComboBox, pondComboBox);
        }

        private async void LoadCyclesAndPonds(ComboBox cycleComboBox, ComboBox pondComboBox)
        {
            try
            {
                // Load production cycles
                var cycles = await _unitOfWork.ProductionCycles.GetAllAsync();
                foreach (var cycle in cycles)
                {
                    cycleComboBox.Items.Add($"دورة {cycle.CycleName} - {cycle.StartDate:yyyy/MM/dd}");
                }

                // Load ponds
                var ponds = await _unitOfWork.Ponds.GetAllAsync();
                foreach (var pond in ponds)
                {
                    pondComboBox.Items.Add($"حوض {pond.PondNumber}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الدورات والأحواض");
            }
        }

        private void CreateFinancialReportsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            var titleLabel = new Label
            {
                Text = "التقارير المالية",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };

            // Financial report buttons
            var incomeStatementButton = new Button
            {
                Text = "قائمة الدخل",
                Size = new Size(200, 50),
                Location = new Point(20, 70),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F)
            };
            incomeStatementButton.Click += GenerateIncomeStatement_Click;

            var balanceSheetButton = new Button
            {
                Text = "الميزانية العمومية",
                Size = new Size(200, 50),
                Location = new Point(240, 70),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F)
            };
            balanceSheetButton.Click += GenerateBalanceSheet_Click;

            var cashFlowButton = new Button
            {
                Text = "قائمة التدفقات النقدية",
                Size = new Size(200, 50),
                Location = new Point(460, 70),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F)
            };
            cashFlowButton.Click += GenerateCashFlow_Click;

            var profitabilityButton = new Button
            {
                Text = "تحليل الربحية",
                Size = new Size(200, 50),
                Location = new Point(20, 140),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F)
            };
            profitabilityButton.Click += GenerateProfitabilityAnalysis_Click;

            var costAnalysisButton = new Button
            {
                Text = "تحليل التكاليف",
                Size = new Size(200, 50),
                Location = new Point(240, 140),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F)
            };
            costAnalysisButton.Click += GenerateCostAnalysis_Click;

            panel.Controls.AddRange(new Control[] {
                titleLabel, incomeStatementButton, balanceSheetButton,
                cashFlowButton, profitabilityButton, costAnalysisButton
            });

            financialReportsTab.Controls.Add(panel);
        }

        private void CreateEmployeeReportsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            var titleLabel = new Label
            {
                Text = "تقارير الموظفين",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };

            var employeeListButton = new Button
            {
                Text = "قائمة الموظفين",
                Size = new Size(200, 50),
                Location = new Point(20, 70),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F)
            };
            employeeListButton.Click += GenerateEmployeeList_Click;

            var payrollReportButton = new Button
            {
                Text = "تقرير الرواتب",
                Size = new Size(200, 50),
                Location = new Point(240, 70),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F)
            };
            payrollReportButton.Click += GeneratePayrollReport_Click;

            var attendanceButton = new Button
            {
                Text = "تقرير الحضور",
                Size = new Size(200, 50),
                Location = new Point(460, 70),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F)
            };
            attendanceButton.Click += GenerateAttendanceReport_Click;

            panel.Controls.AddRange(new Control[] {
                titleLabel, employeeListButton, payrollReportButton, attendanceButton
            });

            employeeReportsTab.Controls.Add(panel);
        }

        private void CreateInventoryReportsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            var titleLabel = new Label
            {
                Text = "تقارير المخزون",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };

            var inventoryStatusButton = new Button
            {
                Text = "حالة المخزون",
                Size = new Size(200, 50),
                Location = new Point(20, 70),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F)
            };
            inventoryStatusButton.Click += GenerateInventoryStatus_Click;

            var movementsReportButton = new Button
            {
                Text = "حركات المخزون",
                Size = new Size(200, 50),
                Location = new Point(240, 70),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F)
            };
            movementsReportButton.Click += GenerateInventoryMovements_Click;

            var valuationButton = new Button
            {
                Text = "تقييم المخزون",
                Size = new Size(200, 50),
                Location = new Point(460, 70),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F)
            };
            valuationButton.Click += GenerateInventoryValuation_Click;

            var alertsButton = new Button
            {
                Text = "تنبيهات المخزون",
                Size = new Size(200, 50),
                Location = new Point(20, 140),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F)
            };
            alertsButton.Click += GenerateInventoryAlerts_Click;

            panel.Controls.AddRange(new Control[] {
                titleLabel, inventoryStatusButton, movementsReportButton,
                valuationButton, alertsButton
            });

            inventoryReportsTab.Controls.Add(panel);
        }

        private void CreateCustomReportsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            var titleLabel = new Label
            {
                Text = "التقارير المخصصة",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };

            var queryLabel = new Label
            {
                Text = "استعلام مخصص:",
                Size = new Size(120, 23),
                Location = new Point(20, 70),
                TextAlign = ContentAlignment.MiddleRight
            };

            var queryTextBox = new TextBox
            {
                Size = new Size(600, 100),
                Location = new Point(20, 100),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Font = new Font("Consolas", 10F)
            };

            var executeButton = new Button
            {
                Text = "تنفيذ الاستعلام",
                Size = new Size(120, 30),
                Location = new Point(640, 100),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            executeButton.Click += ExecuteCustomQuery_Click;

            var resultGridView = new DataGridView
            {
                Size = new Size(740, 300),
                Location = new Point(20, 220),
                AutoGenerateColumns = true,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            panel.Controls.AddRange(new Control[] {
                titleLabel, queryLabel, queryTextBox, executeButton, resultGridView
            });

            customReportsTab.Controls.Add(panel);
        }

        // Event Handlers
        private async void GenerateProductionReport_Click(object? sender, EventArgs e)
        {
            try
            {
                reportSummaryTextBox.Text = "جاري إنشاء تقرير الإنتاج...";
                this.Cursor = Cursors.WaitCursor;

                var fromDate = fromDatePicker.Value.Date;
                var toDate = toDatePicker.Value.Date;

                // Generate production report using enhanced service
                var productionReport = await _enhancedReportService.GenerateProductionReportAsync(fromDate, toDate);

                // Display pond details in grid
                var displayData = productionReport.PondDetails.Select(p => new
                {
                    رقم_الحوض = p.PondNumber,
                    عدد_الأسماك = p.FishCount,
                    متوسط_الوزن = $"{p.AverageWeight:F2} كجم",
                    الإنتاج_المتوقع = $"{p.EstimatedProduction:F2} كجم",
                    استهلاك_العلف = $"{p.FeedConsumption:F2} كجم",
                    النفوق = p.Mortality,
                    الحالة = p.Status
                }).ToArray();

                reportDataGridView.DataSource = displayData;

                // Display summary
                reportSummaryTextBox.Text = $@"ملخص تقرير الإنتاج ({fromDate:yyyy/MM/dd} - {toDate:yyyy/MM/dd}):

• إجمالي الأحواض: {productionReport.TotalPonds}
• الأحواض النشطة: {productionReport.ActivePonds}
• إجمالي عدد الأسماك: {productionReport.TotalFishCount:N0}
• متوسط الوزن العام: {productionReport.AverageWeight:F2} كجم
• الإنتاج المتوقع: {productionReport.EstimatedProduction:F2} كجم
• إجمالي استهلاك العلف: {productionReport.TotalFeedConsumption:F2} كجم
• تكلفة العلف: {productionReport.TotalFeedCost:C2}
• إجمالي النفوق: {productionReport.TotalMortality:N0}
• معدل النفوق: {productionReport.MortalityRate:F2}%

التوصيات:
{(productionReport.MortalityRate > 5 ? "• معدل النفوق مرتفع - يُنصح بفحص جودة المياه والأعلاف" : "• معدل النفوق ضمن المعدل الطبيعي")}
{(productionReport.AverageWeight < 0.3m ? "• متوسط الوزن منخفض - يُنصح بزيادة كمية العلف" : "• معدل النمو جيد")}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير الإنتاج");
                reportSummaryTextBox.Text = "حدث خطأ في إنشاء التقرير";
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        // Financial Reports Event Handlers
        private void GenerateIncomeStatement_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء قائمة الدخل", "تقرير مالي",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for income statement
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء قائمة الدخل");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateBalanceSheet_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء الميزانية العمومية", "تقرير مالي",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for balance sheet
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الميزانية العمومية");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateCashFlow_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء قائمة التدفقات النقدية", "تقرير مالي",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for cash flow statement
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء قائمة التدفقات النقدية");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateProfitabilityAnalysis_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء تحليل الربحية", "تقرير مالي",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for profitability analysis
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تحليل الربحية");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateCostAnalysis_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء تحليل التكاليف", "تقرير مالي",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for cost analysis
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تحليل التكاليف");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Employee Reports Event Handlers
        private void GenerateEmployeeList_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء قائمة الموظفين", "تقرير الموظفين",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for employee list
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء قائمة الموظفين");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GeneratePayrollReport_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء تقرير الرواتب", "تقرير الموظفين",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for payroll report
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير الرواتب");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateAttendanceReport_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء تقرير الحضور", "تقرير الموظفين",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for attendance report
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير الحضور");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Inventory Reports Event Handlers
        private void GenerateInventoryStatus_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء تقرير حالة المخزون", "تقرير المخزون",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for inventory status
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير حالة المخزون");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateInventoryMovements_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء تقرير حركات المخزون", "تقرير المخزون",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for inventory movements
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير حركات المخزون");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateInventoryValuation_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء تقرير تقييم المخزون", "تقرير المخزون",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for inventory valuation
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير تقييم المخزون");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateInventoryAlerts_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء تقرير تنبيهات المخزون", "تقرير المخزون",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for inventory alerts
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير تنبيهات المخزون");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Common Event Handlers
        private async void ExportReport_Click(object? sender, EventArgs e)
        {
            try
            {
                if (reportDataGridView.DataSource == null)
                {
                    MessageBox.Show("لا توجد بيانات للتصدير. يرجى إنشاء تقرير أولاً.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var saveDialog = new SaveFileDialog
                {
                    Filter = "HTML Files|*.html|Excel Files|*.csv|PDF Files|*.html|All Files|*.*",
                    DefaultExt = "html",
                    AddExtension = true,
                    FileName = $"تقرير-{DateTime.Now:yyyyMMdd-HHmmss}"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    this.Cursor = Cursors.WaitCursor;

                    // Determine format based on extension
                    var extension = Path.GetExtension(saveDialog.FileName).ToLower();
                    var format = extension switch
                    {
                        ".html" => BLL.Services.ReportFormat.HTML,
                        ".csv" => BLL.Services.ReportFormat.CSV,
                        ".xlsx" => BLL.Services.ReportFormat.Excel,
                        ".pdf" => BLL.Services.ReportFormat.PDF,
                        _ => BLL.Services.ReportFormat.HTML
                    };

                    // Get current report data (this is simplified - in real implementation you'd store the report data)
                    var fromDate = fromDatePicker.Value.Date;
                    var toDate = toDatePicker.Value.Date;
                    var productionReport = await _enhancedReportService.GenerateProductionReportAsync(fromDate, toDate);

                    var success = await _enhancedReportService.ExportReportAsync(
                        productionReport,
                        saveDialog.FileName,
                        format,
                        "تقرير الإنتاج");

                    if (success)
                    {
                        MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{saveDialog.FileName}", "نجح التصدير",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // Ask if user wants to open the file
                        var result = MessageBox.Show("هل تريد فتح الملف المُصدَّر؟", "فتح الملف",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                        if (result == DialogResult.Yes)
                        {
                            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                            {
                                FileName = saveDialog.FileName,
                                UseShellExecute = true
                            });
                        }
                    }
                    else
                    {
                        MessageBox.Show("فشل في تصدير التقرير", "خطأ في التصدير",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير التقرير");
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void PrintReport_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم طباعة التقرير", "طباعة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for print functionality
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في طباعة التقرير");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExecuteCustomQuery_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم تنفيذ الاستعلام المخصص", "استعلام مخصص",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // Implementation for custom query execution
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ الاستعلام المخصص");
                MessageBox.Show($"خطأ في تنفيذ الاستعلام: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}

