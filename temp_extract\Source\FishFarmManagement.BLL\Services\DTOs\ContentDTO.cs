namespace FishFarmManagement.BLL.Services.DTOs
{
    /// <summary>
    /// DTO للمحتوى
    /// Content DTO
    /// </summary>
    public class ContentDTO
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Language { get; set; } = "ar";
        public string Category { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// DTO لنتيجة البحث
    /// Search result DTO
    /// </summary>
    public class SearchResultDTO
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Excerpt { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public double Relevance { get; set; }
    }
} 