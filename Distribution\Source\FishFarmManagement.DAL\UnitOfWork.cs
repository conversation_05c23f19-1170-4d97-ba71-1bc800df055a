using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.DAL.Repositories;
using FishFarmManagement.Models;

namespace FishFarmManagement.DAL
{
    /// <summary>
    /// تنفيذ وحدة العمل
    /// Unit of Work implementation
    /// </summary>
    public class UnitOfWork : IUnitOfWork
    {
        private readonly FishFarmDbContext _context;
        private IDbContextTransaction? _transaction;
        private bool _disposed = false;

        // Repository instances
        private IRepository<FarmInfo>? _farmInfos;
        private IRepository<ProductionCycle>? _productionCycles;
        private IRepository<Pond>? _ponds;
        private IRepository<FeedType>? _feedTypes;
        private IRepository<FeedConsumption>? _feedConsumptions;
        private IRepository<FishMortality>? _fishMortalities;
        private IRepository<Medication>? _medications;
        private IRepository<PondMedication>? _pondMedications;
        private IRepository<Employee>? _employees;
        private IRepository<Payroll>? _payrolls;
        private IRepository<AccountType>? _accountTypes;
        private IRepository<Account>? _accounts;
        private IRepository<Transaction>? _transactions;
        private IRepository<TransactionDetail>? _transactionDetails;
        private IRepository<CostCenter>? _costCenters;
        private IRepository<Inventory>? _inventories;
        private IRepository<InventoryMovement>? _inventoryMovements;
        private IRepository<User>? _users;
        private IRepository<Role>? _roles;
        private IRepository<UserRole>? _userRoles;

        public UnitOfWork(FishFarmDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        // Repository properties with lazy initialization
        public IRepository<FarmInfo> FarmInfos => 
            _farmInfos ??= new Repository<FarmInfo>(_context);

        public IRepository<ProductionCycle> ProductionCycles => 
            _productionCycles ??= new Repository<ProductionCycle>(_context);

        public IRepository<Pond> Ponds => 
            _ponds ??= new Repository<Pond>(_context);

        public IRepository<FeedType> FeedTypes => 
            _feedTypes ??= new Repository<FeedType>(_context);

        public IRepository<FeedConsumption> FeedConsumptions => 
            _feedConsumptions ??= new Repository<FeedConsumption>(_context);

        public IRepository<FishMortality> FishMortalities => 
            _fishMortalities ??= new Repository<FishMortality>(_context);

        public IRepository<Medication> Medications => 
            _medications ??= new Repository<Medication>(_context);

        public IRepository<PondMedication> PondMedications => 
            _pondMedications ??= new Repository<PondMedication>(_context);

        public IRepository<Employee> Employees => 
            _employees ??= new Repository<Employee>(_context);

        public IRepository<Payroll> Payrolls => 
            _payrolls ??= new Repository<Payroll>(_context);

        public IRepository<AccountType> AccountTypes => 
            _accountTypes ??= new Repository<AccountType>(_context);

        public IRepository<Account> Accounts => 
            _accounts ??= new Repository<Account>(_context);

        public IRepository<Transaction> Transactions => 
            _transactions ??= new Repository<Transaction>(_context);

        public IRepository<TransactionDetail> TransactionDetails => 
            _transactionDetails ??= new Repository<TransactionDetail>(_context);

        public IRepository<CostCenter> CostCenters => 
            _costCenters ??= new Repository<CostCenter>(_context);

        public IRepository<Inventory> Inventories =>
            _inventories ??= new Repository<Inventory>(_context);

        public IRepository<InventoryMovement> InventoryMovements =>
            _inventoryMovements ??= new Repository<InventoryMovement>(_context);

        public IRepository<User> Users =>
            _users ??= new Repository<User>(_context);

        public IRepository<Role> Roles =>
            _roles ??= new Repository<Role>(_context);

        public IRepository<UserRole> UserRoles =>
            _userRoles ??= new Repository<UserRole>(_context);

        public async Task<int> SaveChangesAsync()
        {
            try
            {
                return await _context.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                // Log the exception
                throw new InvalidOperationException("خطأ في حفظ البيانات", ex);
            }
        }

        public async Task BeginTransactionAsync()
        {
            if (_transaction != null)
            {
                throw new InvalidOperationException("المعاملة قيد التنفيذ بالفعل");
            }

            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("لا توجد معاملة نشطة للتأكيد");
            }

            try
            {
                await _transaction.CommitAsync();
            }
            catch
            {
                await RollbackTransactionAsync();
                throw;
            }
            finally
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("لا توجد معاملة نشطة للإلغاء");
            }

            try
            {
                await _transaction.RollbackAsync();
            }
            finally
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task<int> ExecuteSqlRawAsync(string sql, params object[] parameters)
        {
            return await _context.Database.ExecuteSqlRawAsync(sql, parameters);
        }

        public async Task<bool> CreateBackupAsync(string backupPath)
        {
            try
            {
                var connectionString = _context.Database.GetConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                    return false;

                // For SQLite, we can simply copy the database file
                var dbPath = connectionString.Replace("Data Source=", "").Split(';')[0];
                
                if (File.Exists(dbPath))
                {
                    await Task.Run(() => File.Copy(dbPath, backupPath, true));
                    return true;
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RestoreBackupAsync(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                    return false;

                var connectionString = _context.Database.GetConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                    return false;

                var dbPath = connectionString.Replace("Data Source=", "").Split(';')[0];
                
                // Close all connections
                await _context.Database.CloseConnectionAsync();
                
                // Copy backup file to database location
                await Task.Run(() => File.Copy(backupPath, dbPath, true));
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task OptimizeDatabaseAsync()
        {
            await _context.Database.ExecuteSqlRawAsync("VACUUM;");
            await _context.Database.ExecuteSqlRawAsync("ANALYZE;");
        }

        public async Task<bool> CheckDatabaseIntegrityAsync()
        {
            try
            {
                var result = await _context.Database.ExecuteSqlRawAsync("PRAGMA integrity_check;");
                return result >= 0;
            }
            catch
            {
                return false;
            }
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _transaction?.Dispose();
                _context.Dispose();
                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
