@echo off
chcp 65001 >nul
echo ========================================
echo    نظام إدارة مزرعة الأسماك - البناء التجاري
echo    Fish Farm Management - Commercial Build
echo ========================================
echo.

:: التحقق من وجود .NET SDK
echo [1/8] التحقق من .NET SDK...
dotnet --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo خطأ: .NET SDK غير مثبت
    echo يرجى تثبيت .NET 8.0 SDK من: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)
echo ✓ .NET SDK متوفر

:: التحقق من وجود ملف الحل
echo.
echo [2/8] التحقق من ملفات المشروع...
if not exist "FishFarmManagement.sln" (
    echo خطأ: ملف الحل FishFarmManagement.sln غير موجود
    echo تأكد من تشغيل السكريپت من المجلد الصحيح
    pause
    exit /b 1
)
echo ✓ ملف الحل موجود

:: تنظيف المشروع
echo.
echo [3/8] تنظيف المشروع...
echo   - تنظيف الحل...
dotnet clean FishFarmManagement.sln --verbosity quiet >nul 2>&1

echo   - حذف مجلدات bin و obj...
for /d %%i in (bin obj) do if exist "%%i" rmdir /s /q "%%i" >nul 2>&1
for /d %%i in (FishFarmManagement*) do (
    if exist "%%i\bin" rmdir /s /q "%%i\bin" >nul 2>&1
    if exist "%%i\obj" rmdir /s /q "%%i\obj" >nul 2>&1
)
echo ✓ تم تنظيف المشروع

:: استعادة الحزم
echo.
echo [4/8] استعادة الحزم...
dotnet restore FishFarmManagement.sln --verbosity quiet
if %ERRORLEVEL% neq 0 (
    echo خطأ: فشل في استعادة الحزم
    echo محاولة استعادة الحزم مع تفاصيل أكثر...
    dotnet restore FishFarmManagement.sln
    pause
    exit /b 1
)
echo ✓ تم استعادة الحزم

:: بناء المشاريع
echo.
echo [5/8] بناء المشاريع...
dotnet build FishFarmManagement.sln --configuration Release --verbosity quiet
if %ERRORLEVEL% neq 0 (
    echo خطأ: فشل في بناء الحل
    echo محاولة البناء مع تفاصيل أكثر...
    dotnet build FishFarmManagement.sln --configuration Release
    pause
    exit /b 1
)
echo ✓ تم بناء جميع المشاريع

:: تشغيل الاختبارات
echo.
echo [6/8] تشغيل الاختبارات...
if exist "FishFarmManagement.Tests\FishFarmManagement.Tests.csproj" (
    dotnet test FishFarmManagement.Tests\FishFarmManagement.Tests.csproj --configuration Release --verbosity quiet --no-build --logger "console;verbosity=minimal" >nul 2>&1
    if %ERRORLEVEL% neq 0 (
        echo تحذير: فشلت بعض الاختبارات، لكن سيستمر البناء
    ) else (
        echo ✓ تم تشغيل الاختبارات بنجاح
    )
) else (
    echo تحذير: مشروع الاختبارات غير موجود، تخطي الاختبارات
)

:: إنشاء مجلدات التوزيع
echo.
echo [7/8] إنشاء مجلدات التوزيع...
if not exist "Distribution" mkdir "Distribution"
if not exist "Distribution\Setup" mkdir "Distribution\Setup"
if not exist "Distribution\Portable" mkdir "Distribution\Portable"
if not exist "Distribution\Documentation" mkdir "Distribution\Documentation"
if not exist "Distribution\Samples" mkdir "Distribution\Samples"
echo ✓ تم إنشاء مجلدات التوزيع

:: نسخ الملفات المبنية
echo.
echo [8/8] نسخ الملفات المبنية...

:: التحقق من وجود الملفات المبنية
if not exist "FishFarmManagement\bin\Release\net8.0-windows" (
    echo خطأ: مجلد الملفات المبنية غير موجود
    echo تأكد من نجاح عملية البناء
    pause
    exit /b 1
)

:: نسخ التطبيق المحمول
echo   - إنشاء النسخة المحمولة...
xcopy "FishFarmManagement\bin\Release\net8.0-windows\*" "Distribution\Portable\" /E /I /Y /Q >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo خطأ: فشل في نسخ الملفات للنسخة المحمولة
    pause
    exit /b 1
)

:: إضافة مجلدات إضافية للنسخة المحمولة
echo   - إضافة مجلدات إضافية...
for %%i in (Backups Reports Logs Updates) do (
    if not exist "Distribution\Portable\%%i" mkdir "Distribution\Portable\%%i"
)

:: إنشاء ملف README للنسخة المحمولة
echo   - إنشاء ملف README...
(
echo # نظام إدارة مزرعة الأسماك - النسخة المحمولة v1.0.1
echo.
echo هذه النسخة المحمولة من نظام إدارة مزرعة الأسماك.
echo.
echo ## للتشغيل:
echo 1. انقر مرتين على FishFarmManagement.exe
echo 2. سيتم إنشاء ترخيص تجريبي تلقائياً لمدة 30 يوم
echo 3. استخدم للدخول:
echo    - اسم المستخدم: admin
echo    - كلمة المرور: Admin123!
echo.
echo ## الميزات الجديدة في v1.0.1:
echo - نظام التحديث التلقائي
echo - مراقب الأداء المتقدم
echo - الإشعارات الذكية
echo - تصدير متعدد الصيغ
echo - ضغط النسخ الاحتياطية
echo - تحسينات الأمان
echo.
echo ## للدعم الفني:
echo البريد الإلكتروني: <EMAIL>
echo.
echo © 2024 طارق حسين صالح Ahmed. جميع الحقوق محفوظة.
) > "Distribution\Portable\README.txt"

echo ✓ تم إنشاء النسخة المحمولة

:: نسخ ملفات التوثيق
echo   - نسخ ملفات التوثيق...
for %%i in (README.md DOCUMENTATION.md INSTALLATION.md LICENSE CHANGELOG.md COMMERCIAL_README.md COMMERCIAL_USER_GUIDE.md) do (
    if exist "%%i" copy "%%i" "Distribution\Documentation\" >nul 2>&1
)
echo ✓ تم نسخ ملفات التوثيق

:: إنشاء ملفات العينة
echo   - إنشاء ملفات العينة...
(
echo # ملفات عينة لنظام إدارة مزرعة الأسماك v1.0.1
echo.
echo هذا المجلد يحتوي على ملفات عينة لمساعدتك في بدء استخدام النظام.
echo.
echo ## المحتويات:
echo - قاعدة بيانات عينة مع بيانات تجريبية
echo - تقارير عينة بصيغ مختلفة
echo - ملفات إعدادات عينة
echo - أمثلة على الاستخدام
echo.
echo ## للاستفادة من الملفات:
echo 1. انسخ الملفات إلى مجلد البرنامج
echo 2. شغل البرنامج واستكشف البيانات التجريبية
echo 3. استخدم التقارير كنماذج لتقاريرك
echo.
echo للمزيد من المساعدة: <EMAIL>
) > "Distribution\Samples\README.txt"
echo ✓ تم إنشاء ملفات العينة

:: إنشاء ملفات ZIP
echo   - إنشاء ملفات التوزيع المضغوطة...

:: النسخة المحمولة
powershell -Command "try { Compress-Archive -Path 'Distribution\Portable\*' -DestinationPath 'Distribution\FishFarmManagement-v1.0.1-Portable.zip' -Force; Write-Host 'تم إنشاء النسخة المحمولة' } catch { Write-Host 'خطأ في إنشاء النسخة المحمولة' }" >nul 2>&1

:: ملفات التوثيق
powershell -Command "try { Compress-Archive -Path 'Distribution\Documentation\*' -DestinationPath 'Distribution\FishFarmManagement-v1.0.1-Documentation.zip' -Force; Write-Host 'تم إنشاء ملف التوثيق' } catch { Write-Host 'خطأ في إنشاء ملف التوثيق' }" >nul 2>&1

:: ملفات العينة
powershell -Command "try { Compress-Archive -Path 'Distribution\Samples\*' -DestinationPath 'Distribution\FishFarmManagement-v1.0.1-Samples.zip' -Force; Write-Host 'تم إنشاء ملف العينات' } catch { Write-Host 'خطأ في إنشاء ملف العينات' }" >nul 2>&1

echo ✓ تم إنشاء ملفات التوزيع المضغوطة

echo.
echo ========================================
echo    تم البناء بنجاح! | Build Successful!
echo ========================================
echo.
echo الملفات المتاحة في مجلد Distribution:
echo.
echo 📦 FishFarmManagement-v1.0.1-Portable.zip
echo    - النسخة المحمولة (تشغيل مباشر)
echo    - لا تحتاج تثبيت
echo    - مناسبة للاستخدام الفوري
echo.
echo 📋 FishFarmManagement-v1.0.1-Documentation.zip
echo    - ملفات التوثيق الشاملة
echo    - دليل المستخدم والمطور
echo    - أمثلة الاستخدام
echo.
echo 🎯 FishFarmManagement-v1.0.1-Samples.zip
echo    - ملفات عينة للبدء السريع
echo    - قاعدة بيانات تجريبية
echo    - تقارير عينة
echo.
echo 📁 Portable\ (النسخة المحمولة غير مضغوطة)
echo    - جاهزة للتشغيل المباشر
echo    - FishFarmManagement.exe
echo.
echo للاستفسارات والدعم: <EMAIL>
echo.
echo ✅ البرنامج جاهز للتوزيع التجاري!
echo.
pause
