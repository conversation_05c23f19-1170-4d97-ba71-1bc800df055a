using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة المخزون
    /// Inventory management service interface
    /// </summary>
    public interface IInventoryService : IBusinessService<Inventory, int>
    {
        /// <summary>
        /// الحصول على الأصناف النشطة
        /// Get active items
        /// </summary>
        Task<IEnumerable<Inventory>> GetActiveItemsAsync();

        /// <summary>
        /// الحصول على الأصناف منخفضة المخزون
        /// Get low stock items
        /// </summary>
        Task<IEnumerable<Inventory>> GetLowStockItemsAsync();

        /// <summary>
        /// الحصول على الأصناف منتهية الصلاحية
        /// Get expired items
        /// </summary>
        Task<IEnumerable<Inventory>> GetExpiredItemsAsync();

        /// <summary>
        /// الحصول على الأصناف قريبة انتهاء الصلاحية
        /// Get items expiring soon
        /// </summary>
        Task<IEnumerable<Inventory>> GetItemsExpiringSoonAsync(int daysAhead = 30);

        /// <summary>
        /// البحث في المخزون
        /// Search inventory
        /// </summary>
        Task<IEnumerable<Inventory>> SearchInventoryAsync(string searchTerm);

        /// <summary>
        /// الحصول على الأصناف بالنوع
        /// Get items by type
        /// </summary>
        Task<IEnumerable<Inventory>> GetItemsByTypeAsync(string itemType);

        /// <summary>
        /// إضافة كمية للمخزون
        /// Add quantity to inventory
        /// </summary>
        Task<bool> AddQuantityAsync(int itemId, decimal quantity, decimal unitPrice, 
            string? batchNumber = null, DateTime? expiryDate = null, string? supplier = null);

        /// <summary>
        /// استهلاك من المخزون
        /// Consume from inventory
        /// </summary>
        Task<bool> ConsumeQuantityAsync(int itemId, decimal quantity, string reason);

        /// <summary>
        /// تحديث سعر الصنف
        /// Update item price
        /// </summary>
        Task<bool> UpdateItemPriceAsync(int itemId, decimal newPrice);

        /// <summary>
        /// حساب إجمالي قيمة المخزون
        /// Calculate total inventory value
        /// </summary>
        Task<decimal> CalculateTotalInventoryValueAsync();

        /// <summary>
        /// حساب قيمة المخزون بالنوع
        /// Calculate inventory value by type
        /// </summary>
        Task<decimal> CalculateInventoryValueByTypeAsync(string itemType);

        /// <summary>
        /// إنشاء طلب شراء تلقائي
        /// Create automatic purchase order
        /// </summary>
        Task<PurchaseOrder> CreateAutomaticPurchaseOrderAsync();

        /// <summary>
        /// الحصول على تقرير حركة المخزون
        /// Get inventory movement report
        /// </summary>
        Task<IEnumerable<Models.InventoryMovement>> GetInventoryMovementReportAsync(
            DateTime startDate, DateTime endDate, int? itemId = null);

        /// <summary>
        /// تحديث حدود المخزون
        /// Update inventory limits
        /// </summary>
        Task<bool> UpdateInventoryLimitsAsync(int itemId, decimal minimumStock, 
            decimal maximumStock, decimal reorderPoint);

        /// <summary>
        /// إجراء جرد للمخزون
        /// Perform inventory count
        /// </summary>
        Task<InventoryCountResult> PerformInventoryCountAsync(
            Dictionary<int, decimal> actualQuantities, string countedBy);
    }

    /// <summary>
    /// طلب شراء
    /// Purchase order
    /// </summary>
    public class PurchaseOrder
    {
        public int Id { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public DateTime OrderDate { get; set; }
        public string Status { get; set; } = "معلق";
        public decimal TotalAmount { get; set; }
        public List<PurchaseOrderItem> Items { get; set; } = new();
    }

    /// <summary>
    /// صنف في طلب الشراء
    /// Purchase order item
    /// </summary>
    public class PurchaseOrderItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
    }



    /// <summary>
    /// نتيجة جرد المخزون
    /// Inventory count result
    /// </summary>
    public class InventoryCountResult
    {
        public DateTime CountDate { get; set; }
        public string CountedBy { get; set; } = string.Empty;
        public int TotalItemsCounted { get; set; }
        public int ItemsWithDifferences { get; set; }
        public decimal TotalValueDifference { get; set; }
        public List<InventoryCountDifference> Differences { get; set; } = new();
    }

    /// <summary>
    /// فرق جرد المخزون
    /// Inventory count difference
    /// </summary>
    public class InventoryCountDifference
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public decimal SystemQuantity { get; set; }
        public decimal ActualQuantity { get; set; }
        public decimal Difference { get; set; }
        public decimal ValueDifference { get; set; }
    }
}
