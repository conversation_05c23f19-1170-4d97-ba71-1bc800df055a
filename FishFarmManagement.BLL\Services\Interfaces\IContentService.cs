using FishFarmManagement.BLL.Services.DTOs;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة المحتوى
    /// Content management service interface
    /// </summary>
    public interface IContentService
    {
        /// <summary>
        /// الحصول على فهرس المحتوى
        /// Get content index
        /// </summary>
        Task<ContentIndexDTO?> GetContentIndexAsync();

        /// <summary>
        /// الحصول على محتوى موضوع معين
        /// Get topic content
        /// </summary>
        Task<ContentDTO?> GetTopicContentAsync(string topicId);

        /// <summary>
        /// البحث في المحتوى
        /// Search content
        /// </summary>
        Task<IEnumerable<ContentSearchResultDTO>> SearchContentAsync(string searchTerm);
    }

    /// <summary>
    /// فهرس المحتوى
    /// Content index
    /// </summary>
    public class ContentIndexDTO
    {
        public List<ContentTopicDTO> Topics { get; set; } = new();
    }

    /// <summary>
    /// موضوع المحتوى
    /// Content topic
    /// </summary>
    public class ContentTopicDTO
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public int Order { get; set; }
        public List<ContentTopicDTO>? Children { get; set; }
    }

    /// <summary>
    /// نتيجة البحث في المحتوى
    /// Content search result
    /// </summary>
    public class ContentSearchResultDTO
    {
        public string TopicId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Excerpt { get; set; } = string.Empty;
    }
}