using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.ComponentModel.DataAnnotations;
using FishFarmManagement.Models.Configuration;
using FishFarmManagement.BLL.Services.Interfaces;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة إدارة الإعدادات
    /// Settings management service
    /// </summary>
    public class SettingsService : ISettingsService
    {
        private readonly IOptionsMonitor<ApplicationSettings> _applicationSettings;
        private readonly IOptionsMonitor<DatabaseSettings> _databaseSettings;
        private readonly IOptionsMonitor<SecuritySettings> _securitySettings;
        private readonly IOptionsMonitor<ReportsSettings> _reportsSettings;
        private readonly IOptionsMonitor<NotificationsSettings> _notificationsSettings;
        private readonly IOptionsMonitor<LoggingSettings> _loggingSettings;
        private readonly IConfiguration _configuration;
        private readonly ILogger<SettingsService> _logger;

        public SettingsService(
            IOptionsMonitor<ApplicationSettings> applicationSettings,
            IOptionsMonitor<DatabaseSettings> databaseSettings,
            IOptionsMonitor<SecuritySettings> securitySettings,
            IOptionsMonitor<ReportsSettings> reportsSettings,
            IOptionsMonitor<NotificationsSettings> notificationsSettings,
            IOptionsMonitor<LoggingSettings> loggingSettings,
            IConfiguration configuration,
            ILogger<SettingsService> logger)
        {
            _applicationSettings = applicationSettings ?? throw new ArgumentNullException(nameof(applicationSettings));
            _databaseSettings = databaseSettings ?? throw new ArgumentNullException(nameof(databaseSettings));
            _securitySettings = securitySettings ?? throw new ArgumentNullException(nameof(securitySettings));
            _reportsSettings = reportsSettings ?? throw new ArgumentNullException(nameof(reportsSettings));
            _notificationsSettings = notificationsSettings ?? throw new ArgumentNullException(nameof(notificationsSettings));
            _loggingSettings = loggingSettings ?? throw new ArgumentNullException(nameof(loggingSettings));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public ApplicationSettings GetApplicationSettings()
        {
            return _applicationSettings.CurrentValue;
        }

        public DatabaseSettings GetDatabaseSettings()
        {
            return _databaseSettings.CurrentValue;
        }

        public SecuritySettings GetSecuritySettings()
        {
            return _securitySettings.CurrentValue;
        }

        public ReportsSettings GetReportsSettings()
        {
            return _reportsSettings.CurrentValue;
        }

        public NotificationsSettings GetNotificationsSettings()
        {
            return _notificationsSettings.CurrentValue;
        }

        public LoggingSettings GetLoggingSettings()
        {
            return _loggingSettings.CurrentValue;
        }

        public async Task<bool> UpdateApplicationSettingsAsync(ApplicationSettings settings)
        {
            try
            {
                var validationResult = ValidateObject(settings);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("فشل في تحديث إعدادات التطبيق: {Errors}", string.Join(", ", validationResult.Errors));
                    return false;
                }

                // Update configuration (this would typically involve writing to appsettings.json)
                // For now, we'll just log the update
                _logger.LogInformation("تم تحديث إعدادات التطبيق بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعدادات التطبيق");
                return false;
            }
        }

        public async Task<bool> UpdateSecuritySettingsAsync(SecuritySettings settings)
        {
            try
            {
                var validationResult = ValidateObject(settings);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("فشل في تحديث إعدادات الأمان: {Errors}", string.Join(", ", validationResult.Errors));
                    return false;
                }

                _logger.LogInformation("تم تحديث إعدادات الأمان بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعدادات الأمان");
                return false;
            }
        }

        public async Task<bool> UpdateReportsSettingsAsync(ReportsSettings settings)
        {
            try
            {
                var validationResult = ValidateObject(settings);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("فشل في تحديث إعدادات التقارير: {Errors}", string.Join(", ", validationResult.Errors));
                    return false;
                }

                _logger.LogInformation("تم تحديث إعدادات التقارير بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعدادات التقارير");
                return false;
            }
        }

        public async Task<bool> UpdateNotificationsSettingsAsync(NotificationsSettings settings)
        {
            try
            {
                var validationResult = ValidateObject(settings);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("فشل في تحديث إعدادات الإشعارات: {Errors}", string.Join(", ", validationResult.Errors));
                    return false;
                }

                _logger.LogInformation("تم تحديث إعدادات الإشعارات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعدادات الإشعارات");
                return false;
            }
        }

        public async Task ReloadSettingsAsync()
        {
            try
            {
                // Force reload of configuration
                if (_configuration is IConfigurationRoot configRoot)
                {
                    configRoot.Reload();
                }

                _logger.LogInformation("تم إعادة تحميل الإعدادات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إعادة تحميل الإعدادات");
            }
        }

        public SettingsValidationResult ValidateSettings()
        {
            var result = new SettingsValidationResult { IsValid = true };

            try
            {
                // Validate all settings sections
                var appValidation = ValidateObject(_applicationSettings.CurrentValue);
                var dbValidation = ValidateObject(_databaseSettings.CurrentValue);
                var secValidation = ValidateObject(_securitySettings.CurrentValue);
                var reportsValidation = ValidateObject(_reportsSettings.CurrentValue);
                var notificationsValidation = ValidateObject(_notificationsSettings.CurrentValue);
                var loggingValidation = ValidateObject(_loggingSettings.CurrentValue);

                // Combine all validation results
                var allValidations = new[] { appValidation, dbValidation, secValidation, reportsValidation, notificationsValidation, loggingValidation };

                foreach (var validation in allValidations)
                {
                    if (!validation.IsValid)
                    {
                        result.IsValid = false;
                        result.Errors.AddRange(validation.Errors);
                    }
                    result.Warnings.AddRange(validation.Warnings);
                }

                // Additional business logic validations
                ValidateBusinessLogic(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من صحة الإعدادات");
                result.IsValid = false;
                result.Errors.Add("خطأ في التحقق من صحة الإعدادات");
            }

            return result;
        }

        private SettingsValidationResult ValidateObject<T>(T obj)
        {
            var result = new SettingsValidationResult { IsValid = true };
            var context = new ValidationContext(obj!);
            var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();

            if (!Validator.TryValidateObject(obj!, context, validationResults, true))
            {
                result.IsValid = false;
                result.Errors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "خطأ في التحقق"));
            }

            return result;
        }

        private void ValidateBusinessLogic(SettingsValidationResult result)
        {
            var appSettings = _applicationSettings.CurrentValue;
            var secSettings = _securitySettings.CurrentValue;

            // Check if backup path exists
            if (!Directory.Exists(appSettings.AutoBackup.BackupPath))
            {
                result.Warnings.Add($"مجلد النسخ الاحتياطي غير موجود: {appSettings.AutoBackup.BackupPath}");
            }

            // Check security settings consistency
            if (secSettings.SessionTimeoutMinutes < 5)
            {
                result.Warnings.Add("مدة انتهاء الجلسة قصيرة جداً (أقل من 5 دقائق)");
            }

            // Check reports output path
            var reportsSettings = _reportsSettings.CurrentValue;
            if (!Directory.Exists(reportsSettings.OutputPath))
            {
                result.Warnings.Add($"مجلد التقارير غير موجود: {reportsSettings.OutputPath}");
            }
        }
    }
}
