using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// تفاصيل المعاملة المحاسبية
    /// Transaction detail
    /// </summary>
    public class TransactionDetail : BaseEntity
    {
        [Required(ErrorMessage = "معرف المعاملة مطلوب")]
        public int TransactionId { get; set; }

        [Required(ErrorMessage = "معرف الحساب مطلوب")]
        public int AccountId { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "مبلغ المدين يجب أن يكون أكبر من أو يساوي الصفر")]
        [Column(TypeName = "decimal(15,2)")]
        public decimal DebitAmount { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "مبلغ الدائن يجب أن يكون أكبر من أو يساوي الصفر")]
        [Column(TypeName = "decimal(15,2)")]
        public decimal CreditAmount { get; set; }

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// رقم السطر في المعاملة
        /// Line number in transaction
        /// </summary>
        public int LineNumber { get; set; }

        /// <summary>
        /// معرف مركز التكلفة
        /// Cost center ID
        /// </summary>
        public int? CostCenterId { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// Additional notes
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        // Navigation Properties
        [ForeignKey("TransactionId")]
        public virtual Transaction Transaction { get; set; } = null!;

        [ForeignKey("AccountId")]
        public virtual Account Account { get; set; } = null!;

        [ForeignKey("CostCenterId")]
        public virtual CostCenter? CostCenter { get; set; }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data
        /// </summary>
        public bool IsValid()
        {
            // يجب أن يكون أحد المبلغين فقط أكبر من الصفر
            var hasDebit = DebitAmount > 0;
            var hasCredit = CreditAmount > 0;
            
            return hasDebit ^ hasCredit; // XOR - واحد فقط يجب أن يكون true
        }

        /// <summary>
        /// الحصول على المبلغ الصافي
        /// Get net amount
        /// </summary>
        public decimal GetNetAmount()
        {
            return DebitAmount - CreditAmount;
        }

        /// <summary>
        /// التحقق من أن السطر مدين
        /// Check if line is debit
        /// </summary>
        public bool IsDebit => DebitAmount > 0;

        /// <summary>
        /// التحقق من أن السطر دائن
        /// Check if line is credit
        /// </summary>
        public bool IsCredit => CreditAmount > 0;

        /// <summary>
        /// الحصول على المبلغ (بغض النظر عن النوع)
        /// Get amount (regardless of type)
        /// </summary>
        public decimal GetAmount()
        {
            return Math.Max(DebitAmount, CreditAmount);
        }

        /// <summary>
        /// الحصول على نوع الحركة
        /// Get movement type
        /// </summary>
        public string GetMovementType()
        {
            return IsDebit ? "مدين" : "دائن";
        }
    }
}
