using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة البحث الديناميكي
    /// Dynamic search service
    /// </summary>
    public class SearchService : ISearchService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<SearchService> _logger;

        public SearchService(IUnitOfWork unitOfWork, ILogger<SearchService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<AccountSearchResult>> SearchAccountsAsync(string searchTerm, int maxResults = 10)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return Enumerable.Empty<AccountSearchResult>();

                var accounts = await _unitOfWork.Accounts.FindAsync(a =>
                    a.AccountName.Contains(searchTerm) ||
                    a.AccountCode.Contains(searchTerm) ||
                    a.AccountNameEn.Contains(searchTerm));

                return accounts
                    .Where(a => a.Status == "نشط")
                    .Take(maxResults)
                    .Select(a => new AccountSearchResult
                    {
                        Id = a.Id,
                        AccountCode = a.AccountCode,
                        AccountName = a.AccountName,
                        AccountType = a.AccountType?.TypeName ?? "",
                        Balance = a.Balance,
                        Status = a.Status
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن الحسابات بالكلمة {SearchTerm}", searchTerm);
                return Enumerable.Empty<AccountSearchResult>();
            }
        }

        public async Task<IEnumerable<EmployeeSearchResult>> SearchEmployeesAsync(string searchTerm, int maxResults = 10)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return Enumerable.Empty<EmployeeSearchResult>();

                var employees = await _unitOfWork.Employees.FindAsync(e =>
                    e.FullName.Contains(searchTerm) ||
                    e.Position.Contains(searchTerm) ||
                    (e.NationalId != null && e.NationalId.Contains(searchTerm)) ||
                    (e.Phone != null && e.Phone.Contains(searchTerm)));

                return employees
                    .Where(e => e.Status == "نشط")
                    .Take(maxResults)
                    .Select(e => new EmployeeSearchResult
                    {
                        Id = e.Id,
                        FullName = e.FullName,
                        Position = e.Position,
                        NationalId = e.NationalId,
                        Phone = e.Phone,
                        Status = e.Status
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن الموظفين بالكلمة {SearchTerm}", searchTerm);
                return Enumerable.Empty<EmployeeSearchResult>();
            }
        }

        public async Task<IEnumerable<InventorySearchResult>> SearchInventoryItemsAsync(string searchTerm, int maxResults = 10)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return Enumerable.Empty<InventorySearchResult>();

                var items = await _unitOfWork.Inventories.FindAsync(i =>
                    i.ItemName.Contains(searchTerm) ||
                    i.ItemType.Contains(searchTerm) ||
                    (i.Supplier != null && i.Supplier.Contains(searchTerm)));

                return items
                    .Take(maxResults)
                    .Select(i => new InventorySearchResult
                    {
                        Id = i.Id,
                        ItemCode = i.ItemName, // Using ItemName as code since no ItemCode exists
                        ItemName = i.ItemName,
                        Category = i.ItemType,
                        CurrentQuantity = i.Quantity,
                        Unit = i.Unit,
                        UnitCost = i.UnitPrice,
                        Status = "نشط" // Default status since no Status field exists
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن أصناف المخزون بالكلمة {SearchTerm}", searchTerm);
                return Enumerable.Empty<InventorySearchResult>();
            }
        }

        public async Task<IEnumerable<MedicationSearchResult>> SearchMedicationsAsync(string searchTerm, int maxResults = 10)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return Enumerable.Empty<MedicationSearchResult>();

                var medications = await _unitOfWork.Medications.FindAsync(m =>
                    m.MedicationName.Contains(searchTerm) ||
                    m.Type.Contains(searchTerm) ||
                    (m.Description != null && m.Description.Contains(searchTerm)));

                return medications
                    .Take(maxResults)
                    .Select(m => new MedicationSearchResult
                    {
                        Id = m.Id,
                        MedicationName = m.MedicationName,
                        Type = m.Type,
                        Dosage = m.Dosage,
                        Unit = m.Unit,
                        ExpiryDate = m.ExpiryDate
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن الأدوية بالكلمة {SearchTerm}", searchTerm);
                return Enumerable.Empty<MedicationSearchResult>();
            }
        }

        public async Task<IEnumerable<PondSearchResult>> SearchPondsAsync(string searchTerm, int maxResults = 10)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return Enumerable.Empty<PondSearchResult>();

                var ponds = await _unitOfWork.Ponds.FindAsync(p =>
                    p.PondNumber.Contains(searchTerm) ||
                    p.Notes.Contains(searchTerm));

                return ponds
                    .Where(p => p.Status == "نشط")
                    .Take(maxResults)
                    .Select(p => new PondSearchResult
                    {
                        Id = p.Id,
                        PondName = p.PondNumber,
                        Capacity = p.FishCount, // Using FishCount as capacity
                        CurrentStock = p.FishCount,
                        Status = p.Status
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن الأحواض بالكلمة {SearchTerm}", searchTerm);
                return Enumerable.Empty<PondSearchResult>();
            }
        }

        public async Task<IEnumerable<FeedTypeSearchResult>> SearchFeedTypesAsync(string searchTerm, int maxResults = 10)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return Enumerable.Empty<FeedTypeSearchResult>();

                var feedTypes = await _unitOfWork.FeedTypes.FindAsync(f =>
                    f.FeedName.Contains(searchTerm) ||
                    f.Brand.Contains(searchTerm) ||
                    f.Specifications.Contains(searchTerm));

                return feedTypes
                    .Take(maxResults)
                    .Select(f => new FeedTypeSearchResult
                    {
                        Id = f.Id,
                        FeedName = f.FeedName,
                        Type = f.Brand,
                        ProteinPercentage = 0, // No protein percentage field in model
                        PricePerKg = f.PricePerKg
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن أنواع العلف بالكلمة {SearchTerm}", searchTerm);
                return Enumerable.Empty<FeedTypeSearchResult>();
            }
        }

        public async Task<GlobalSearchResult> GlobalSearchAsync(string searchTerm, int maxResults = 5)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return new GlobalSearchResult();

                var accountsTask = SearchAccountsAsync(searchTerm, maxResults);
                var employeesTask = SearchEmployeesAsync(searchTerm, maxResults);
                var inventoryTask = SearchInventoryItemsAsync(searchTerm, maxResults);
                var medicationsTask = SearchMedicationsAsync(searchTerm, maxResults);
                var pondsTask = SearchPondsAsync(searchTerm, maxResults);
                var feedTypesTask = SearchFeedTypesAsync(searchTerm, maxResults);

                await Task.WhenAll(accountsTask, employeesTask, inventoryTask, medicationsTask, pondsTask, feedTypesTask);

                return new GlobalSearchResult
                {
                    Accounts = await accountsTask,
                    Employees = await employeesTask,
                    InventoryItems = await inventoryTask,
                    Medications = await medicationsTask,
                    Ponds = await pondsTask,
                    FeedTypes = await feedTypesTask
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث العام بالكلمة {SearchTerm}", searchTerm);
                return new GlobalSearchResult();
            }
        }
    }
}
