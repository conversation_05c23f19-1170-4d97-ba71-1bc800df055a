using FishFarmManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace FishFarmManagement.DAL
{
    public class FishFarmDbContext : DbContext
    {
        public FishFarmDbContext(DbContextOptions<FishFarmDbContext> options) : base(options)
        {
        }

        // DbSets for all models
        public DbSet<FarmInfo> FarmInfos { get; set; }
        public DbSet<ProductionCycle> ProductionCycles { get; set; }
        public DbSet<Pond> Ponds { get; set; }
        public DbSet<FeedType> FeedTypes { get; set; }
        public DbSet<FeedConsumption> FeedConsumptions { get; set; }
        public DbSet<FishMortality> FishMortalities { get; set; }
        public DbSet<Medication> Medications { get; set; }
        public DbSet<PondMedication> PondMedications { get; set; }
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Payroll> Payrolls { get; set; }
        public DbSet<Account> Accounts { get; set; }
        public DbSet<Transaction> Transactions { get; set; }
        public DbSet<CostCenter> CostCenters { get; set; }
        public DbSet<InventoryItem> InventoryItems { get; set; }
        public DbSet<Inventory> Inventories { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<IncomeStatement> IncomeStatements { get; set; }
        public DbSet<IncomeStatementItem> IncomeStatementItems { get; set; }
        public DbSet<BalanceSheet> BalanceSheets { get; set; }
        public DbSet<BalanceSheetItem> BalanceSheetItems { get; set; }
        public DbSet<CycleProfitability> CycleProfitabilities { get; set; }
        public DbSet<CostCenterAnalysis> CostCenterAnalyses { get; set; }
        public DbSet<CostCenterDetail> CostCenterDetails { get; set; }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Add a composite index to the Payroll table for faster lookups
            // by employee and production cycle.
            modelBuilder.Entity<Payroll>()
                .HasIndex(p => new { p.EmployeeId, p.CycleId })
                .HasDatabaseName("IX_Payroll_Employee_Cycle");

            // Configure composite key for the RolePermission join table
            modelBuilder.Entity<RolePermission>()
                .HasKey(rp => new { rp.RoleId, rp.PermissionId });

            // Configure composite key for the PondMedication join table
            modelBuilder.Entity<PondMedication>()
                .HasKey(pm => new { pm.PondId, pm.MedicationId });

            // Configure IncomeStatement relationships
            modelBuilder.Entity<IncomeStatementItem>()
                .HasOne(i => i.IncomeStatement)
                .WithMany()
                .HasForeignKey(i => i.IncomeStatementId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure string properties for proper Arabic text handling
            ConfigureStringPropertiesForArabic(modelBuilder);
        }

        /// <summary>
        /// تكوين خصائص النصوص لدعم اللغة العربية بشكل صحيح
        /// Configure string properties for proper Arabic text support
        /// </summary>
        private void ConfigureStringPropertiesForArabic(ModelBuilder modelBuilder)
        {
            // Configure Employee entity for Arabic text
            modelBuilder.Entity<Employee>(entity =>
            {
                entity.Property(e => e.FullName).HasColumnType("TEXT COLLATE NOCASE");
                entity.Property(e => e.Position).HasColumnType("TEXT COLLATE NOCASE");
                entity.Property(e => e.Status).HasColumnType("TEXT COLLATE NOCASE");
                entity.Property(e => e.ContactInfo).HasColumnType("TEXT COLLATE NOCASE");
            });

            // Configure Account entity for Arabic text
            modelBuilder.Entity<Account>(entity =>
            {
                entity.Property(a => a.AccountName).HasColumnType("TEXT COLLATE NOCASE");
                entity.Property(a => a.Description).HasColumnType("TEXT COLLATE NOCASE");
            });

            // Configure InventoryItem entity for Arabic text
            modelBuilder.Entity<InventoryItem>(entity =>
            {
                entity.Property(i => i.ItemName).HasColumnType("TEXT COLLATE NOCASE");
                entity.Property(i => i.Description).HasColumnType("TEXT COLLATE NOCASE");
                entity.Property(i => i.Status).HasColumnType("TEXT COLLATE NOCASE");
                entity.Property(i => i.Supplier).HasColumnType("TEXT COLLATE NOCASE");
                entity.Property(i => i.Location).HasColumnType("TEXT COLLATE NOCASE");
            });

            // This ensures proper sorting and searching of Arabic text in SQLite
        }
    }
}