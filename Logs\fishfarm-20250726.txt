2025-07-26 06:55:52.055 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = غير موجودة، التهجير التلقائي = false، استخدام نص SQL = true
2025-07-26 06:55:52.093 +03:00 [INF] الاعتماد على إنشاء قاعدة البيانات اليدوي باستخدام نص SQL
2025-07-26 06:55:52.095 +03:00 [WRN] لم يتم العثور على قاعدة بيانات. يُرجى تشغيل النص البرمجي SQL يدويًا أولاً
2025-07-26 06:55:52.095 +03:00 [ERR] خطأ في تهيئة قاعدة البيانات: لم يتم العثور على قاعدة البيانات. يُرجى تشغيل النص البرمجي SQL أولاً.
System.InvalidOperationException: لم يتم العثور على قاعدة البيانات. يُرجى تشغيل النص البرمجي SQL أولاً.
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 185
2025-07-26 06:55:52.137 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
System.Exception: حدث خطأ أثناء تهيئة قاعدة البيانات: لم يتم العثور على قاعدة البيانات. يُرجى تشغيل النص البرمجي SQL أولاً.
 ---> System.InvalidOperationException: لم يتم العثور على قاعدة البيانات. يُرجى تشغيل النص البرمجي SQL أولاً.
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 185
   --- End of inner exception stack trace ---
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 220
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 48
2025-07-26 07:01:20.990 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = غير موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 07:01:21.028 +03:00 [INF] إنشاء قاعدة البيانات باستخدام التهجيرات
2025-07-26 07:01:21.087 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-26 07:01:21.185 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 07:01:21.199 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:01:21.204 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:01:21.226 +03:00 [INF] Applying migration '20250724205802_InitialCreate'.
2025-07-26 07:01:21.378 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:01:21.378 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:01:21.378 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:01:21.378 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:01:21.379 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:01:21.379 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:01:21.379 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:01:21.379 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:01:21.379 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE CASCADE
);
2025-07-26 07:01:21.380 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:01:21.380 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:01:21.380 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:01:21.380 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:01:21.380 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:01:21.381 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:01:21.381 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:01:21.381 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-26 07:01:21.381 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-24 23:58:00.3543764', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-24 23:58:00.3543765', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-24 23:58:00.3543765', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-24 23:58:00.3543766', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-24 23:58:00.3543766', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-26 07:01:21.382 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-24 23:58:00.3543917', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-26 07:01:21.382 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-24 23:58:00.3543858', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-24 23:58:00.3543858', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-24 23:58:00.3543859', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-24 23:58:00.3543859', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-24 23:58:00.3543861', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-24 23:58:00.3543861', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-24 23:58:00.3543863', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-26 07:01:21.383 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-26 07:01:21.383 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-26 07:01:21.383 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-26 07:01:21.383 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-26 07:01:21.384 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-26 07:01:21.384 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-26 07:01:21.384 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-26 07:01:21.384 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-26 07:01:21.384 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-26 07:01:21.384 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-26 07:01:21.385 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId" ON "InventoryMovements" ("ItemId");
2025-07-26 07:01:21.385 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-26 07:01:21.385 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-26 07:01:21.385 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-26 07:01:21.386 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-26 07:01:21.386 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-26 07:01:21.386 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-26 07:01:21.386 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-26 07:01:21.386 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-26 07:01:21.387 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-26 07:01:21.387 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-26 07:01:21.387 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-26 07:01:21.387 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724205802_InitialCreate', '8.0.0');
2025-07-26 07:01:21.400 +03:00 [INF] Applying migration '20250724211144_AddUserManagement'.
2025-07-26 07:01:21.485 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP INDEX "IX_InventoryMovements_ItemId";
2025-07-26 07:01:21.485 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:01:21.485 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:01:21.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-26 07:01:21.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100123'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:01:21.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100129'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:01:21.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.810013'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:01:21.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.810013'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:01:21.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100131'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:01:21.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100494'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:01:21.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100496'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:01:21.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100506'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:01:21.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100544'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:01:21.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100545'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:01:21.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100546'
WHERE "Id" = 6;
SELECT changes();
2025-07-26 07:01:21.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100547'
WHERE "Id" = 7;
SELECT changes();
2025-07-26 07:01:21.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100547'
WHERE "Id" = 8;
SELECT changes();
2025-07-26 07:01:21.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100548'
WHERE "Id" = 9;
SELECT changes();
2025-07-26 07:01:21.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100549'
WHERE "Id" = 10;
SELECT changes();
2025-07-26 07:01:21.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100549'
WHERE "Id" = 11;
SELECT changes();
2025-07-26 07:01:21.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.810055'
WHERE "Id" = 12;
SELECT changes();
2025-07-26 07:01:21.488 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100551'
WHERE "Id" = 13;
SELECT changes();
2025-07-26 07:01:21.488 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "FarmInfos" SET "CreatedDate" = '2025-07-25 00:11:43.8100734'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:01:21.488 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-25 00:11:43.8102126', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-25 00:11:43.8102335', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-25 00:11:43.8102376', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-25 00:11:43.810241', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-25 00:11:43.8102437', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-26 07:01:21.488 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-25 00:11:43.8102634', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, NULL, '$2a$11$xajPljvN0VMOVwgwxmR0vetvCHYroT46IZNhyYQDBHYuv1GqGxzKS', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-26 07:01:21.488 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-25 00:11:43.960912', NULL, 'System', '2025-07-25 00:11:43.9609117', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-26 07:01:21.488 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 07:01:21.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 07:01:21.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-26 07:01:21.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-26 07:01:21.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-26 07:01:21.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-26 07:01:21.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-26 07:01:21.490 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ef_temp_InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "BatchNumber" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "Reference" TEXT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:01:21.490 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "ef_temp_InventoryMovements" ("Id", "BatchNumber", "CreatedBy", "CreatedDate", "ItemId", "MovementDate", "MovementType", "Notes", "Quantity", "QuantityAfter", "QuantityBefore", "Reason", "Reference", "TotalValue", "UnitPrice", "UpdatedDate")
SELECT "Id", "BatchNumber", "CreatedBy", "CreatedDate", "ItemId", "MovementDate", "MovementType", "Notes", "Quantity", "QuantityAfter", "QuantityBefore", "Reason", "Reference", "TotalValue", "UnitPrice", "UpdatedDate"
FROM "InventoryMovements";
2025-07-26 07:01:21.495 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA foreign_keys = 0;
2025-07-26 07:01:21.495 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP TABLE "InventoryMovements";
2025-07-26 07:01:21.497 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "ef_temp_InventoryMovements" RENAME TO "InventoryMovements";
2025-07-26 07:01:21.501 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA foreign_keys = 1;
2025-07-26 07:01:21.501 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 07:01:21.501 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 07:01:21.501 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724211144_AddUserManagement', '8.0.0');
2025-07-26 07:01:21.505 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-26 07:01:21.752 +03:00 [ERR] Failed executing DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:01:21.771 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FishFarmManagement.DAL.FishFarmDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-26 07:01:21.773 +03:00 [ERR] خطأ في إنشاء المستخدم الافتراضي
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at FishFarmManagement.Program.EnsureDefaultUserAsync(FishFarmDbContext context, ILogger logger) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 229
2025-07-26 07:01:21.789 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 07:01:35.676 +03:00 [ERR] Failed executing DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:01:35.677 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FishFarmManagement.DAL.FishFarmDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-26 07:01:35.679 +03:00 [ERR] خطأ في تسجيل الدخول
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at FishFarmManagement.BLL.Services.UserManagementService.IsSystemInitializedAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement.BLL\Services\UserManagementService.cs:line 367
   at FishFarmManagement.Forms.LoginForm.PerformLoginAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Forms\LoginForm.cs:line 273
2025-07-26 07:01:43.940 +03:00 [ERR] Failed executing DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:01:43.941 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FishFarmManagement.DAL.FishFarmDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-26 07:01:43.942 +03:00 [ERR] خطأ في تسجيل الدخول
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at FishFarmManagement.BLL.Services.UserManagementService.IsSystemInitializedAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement.BLL\Services\UserManagementService.cs:line 367
   at FishFarmManagement.Forms.LoginForm.PerformLoginAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Forms\LoginForm.cs:line 273
2025-07-26 07:01:48.404 +03:00 [ERR] Failed executing DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:01:48.405 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FishFarmManagement.DAL.FishFarmDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-26 07:01:48.405 +03:00 [ERR] خطأ في تسجيل الدخول
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at FishFarmManagement.BLL.Services.UserManagementService.IsSystemInitializedAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement.BLL\Services\UserManagementService.cs:line 367
   at FishFarmManagement.Forms.LoginForm.PerformLoginAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Forms\LoginForm.cs:line 273
2025-07-26 07:06:01.288 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 07:06:01.326 +03:00 [INF] تم اكتشاف قاعدة بيانات موجودة
2025-07-26 07:06:01.617 +03:00 [ERR] Failed executing DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:06:01.644 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FishFarmManagement.DAL.FishFarmDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-26 07:06:01.645 +03:00 [ERR] خطأ في إنشاء المستخدم الافتراضي
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at FishFarmManagement.Program.EnsureDefaultUserAsync(FishFarmDbContext context, ILogger logger) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 229
2025-07-26 07:06:01.652 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 07:06:15.287 +03:00 [ERR] Failed executing DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:06:15.289 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FishFarmManagement.DAL.FishFarmDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-26 07:06:15.290 +03:00 [ERR] خطأ في تسجيل الدخول
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at FishFarmManagement.BLL.Services.UserManagementService.IsSystemInitializedAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement.BLL\Services\UserManagementService.cs:line 367
   at FishFarmManagement.Forms.LoginForm.PerformLoginAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Forms\LoginForm.cs:line 273
2025-07-26 07:06:18.250 +03:00 [ERR] Failed executing DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:06:18.251 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FishFarmManagement.DAL.FishFarmDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-26 07:06:18.252 +03:00 [ERR] خطأ في تسجيل الدخول
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at FishFarmManagement.BLL.Services.UserManagementService.IsSystemInitializedAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement.BLL\Services\UserManagementService.cs:line 367
   at FishFarmManagement.Forms.LoginForm.PerformLoginAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Forms\LoginForm.cs:line 273
2025-07-26 07:06:20.497 +03:00 [ERR] Failed executing DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:06:20.498 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FishFarmManagement.DAL.FishFarmDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-26 07:06:20.499 +03:00 [ERR] خطأ في تسجيل الدخول
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: u.MustChangePassword'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at FishFarmManagement.BLL.Services.UserManagementService.IsSystemInitializedAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement.BLL\Services\UserManagementService.cs:line 367
   at FishFarmManagement.Forms.LoginForm.PerformLoginAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Forms\LoginForm.cs:line 273
2025-07-26 07:07:30.497 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = غير موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 07:07:30.532 +03:00 [INF] إنشاء قاعدة البيانات باستخدام التهجيرات
2025-07-26 07:07:30.590 +03:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-26 07:07:30.691 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 07:07:30.705 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:07:30.710 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:07:30.732 +03:00 [INF] Applying migration '20250724205802_InitialCreate'.
2025-07-26 07:07:30.875 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:30.876 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:30.876 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:30.876 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:30.876 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:30.877 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:30.877 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:30.877 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:07:30.877 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE CASCADE
);
2025-07-26 07:07:30.877 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:07:30.878 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:07:30.878 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:07:30.878 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:07:30.878 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:07:30.878 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:07:30.879 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:07:30.879 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-26 07:07:30.879 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-24 23:58:00.3543764', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-24 23:58:00.3543765', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-24 23:58:00.3543765', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-24 23:58:00.3543766', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-24 23:58:00.3543766', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-26 07:07:30.879 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-24 23:58:00.3543917', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-26 07:07:30.880 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-24 23:58:00.3543858', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-24 23:58:00.3543858', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-24 23:58:00.3543859', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-24 23:58:00.3543859', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-24 23:58:00.3543861', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-24 23:58:00.3543861', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-24 23:58:00.3543863', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-26 07:07:30.880 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-26 07:07:30.880 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-26 07:07:30.880 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-26 07:07:30.881 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-26 07:07:30.881 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-26 07:07:30.881 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-26 07:07:30.881 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-26 07:07:30.881 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-26 07:07:30.881 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-26 07:07:30.882 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-26 07:07:30.882 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId" ON "InventoryMovements" ("ItemId");
2025-07-26 07:07:30.882 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-26 07:07:30.882 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-26 07:07:30.882 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-26 07:07:30.883 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-26 07:07:30.883 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-26 07:07:30.883 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-26 07:07:30.884 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-26 07:07:30.884 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-26 07:07:30.884 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-26 07:07:30.884 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-26 07:07:30.884 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-26 07:07:30.884 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724205802_InitialCreate', '8.0.0');
2025-07-26 07:07:30.897 +03:00 [INF] Applying migration '20250724211144_AddUserManagement'.
2025-07-26 07:07:30.977 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP INDEX "IX_InventoryMovements_ItemId";
2025-07-26 07:07:30.977 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:30.977 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:30.977 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-26 07:07:30.977 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100123'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:30.978 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100129'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:07:30.978 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.810013'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:07:30.978 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.810013'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:07:30.978 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100131'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:07:30.978 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100494'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:30.978 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100496'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:07:30.978 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100506'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:07:30.978 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100544'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:07:30.978 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100545'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:07:30.979 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100546'
WHERE "Id" = 6;
SELECT changes();
2025-07-26 07:07:30.979 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100547'
WHERE "Id" = 7;
SELECT changes();
2025-07-26 07:07:30.979 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100547'
WHERE "Id" = 8;
SELECT changes();
2025-07-26 07:07:30.979 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100548'
WHERE "Id" = 9;
SELECT changes();
2025-07-26 07:07:30.979 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100549'
WHERE "Id" = 10;
SELECT changes();
2025-07-26 07:07:30.979 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100549'
WHERE "Id" = 11;
SELECT changes();
2025-07-26 07:07:30.979 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.810055'
WHERE "Id" = 12;
SELECT changes();
2025-07-26 07:07:30.979 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100551'
WHERE "Id" = 13;
SELECT changes();
2025-07-26 07:07:30.979 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "FarmInfos" SET "CreatedDate" = '2025-07-25 00:11:43.8100734'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:30.980 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-25 00:11:43.8102126', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-25 00:11:43.8102335', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-25 00:11:43.8102376', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-25 00:11:43.810241', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-25 00:11:43.8102437', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-26 07:07:30.980 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-25 00:11:43.8102634', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, NULL, '$2a$11$xajPljvN0VMOVwgwxmR0vetvCHYroT46IZNhyYQDBHYuv1GqGxzKS', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-26 07:07:30.980 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-25 00:11:43.960912', NULL, 'System', '2025-07-25 00:11:43.9609117', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-26 07:07:30.980 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 07:07:30.980 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 07:07:30.980 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-26 07:07:30.981 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-26 07:07:30.981 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-26 07:07:30.981 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-26 07:07:30.981 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-26 07:07:30.981 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ef_temp_InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "BatchNumber" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "Reference" TEXT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:07:30.982 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "ef_temp_InventoryMovements" ("Id", "BatchNumber", "CreatedBy", "CreatedDate", "ItemId", "MovementDate", "MovementType", "Notes", "Quantity", "QuantityAfter", "QuantityBefore", "Reason", "Reference", "TotalValue", "UnitPrice", "UpdatedDate")
SELECT "Id", "BatchNumber", "CreatedBy", "CreatedDate", "ItemId", "MovementDate", "MovementType", "Notes", "Quantity", "QuantityAfter", "QuantityBefore", "Reason", "Reference", "TotalValue", "UnitPrice", "UpdatedDate"
FROM "InventoryMovements";
2025-07-26 07:07:30.987 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA foreign_keys = 0;
2025-07-26 07:07:30.987 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP TABLE "InventoryMovements";
2025-07-26 07:07:30.989 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "ef_temp_InventoryMovements" RENAME TO "InventoryMovements";
2025-07-26 07:07:30.993 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA foreign_keys = 1;
2025-07-26 07:07:30.993 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 07:07:30.993 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 07:07:30.993 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724211144_AddUserManagement', '8.0.0');
2025-07-26 07:07:30.998 +03:00 [INF] Applying migration '20250726040518_AddMustChangePasswordColumn'.
2025-07-26 07:07:31.067 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "Users" ADD "MustChangePassword" INTEGER NOT NULL DEFAULT 0;
2025-07-26 07:07:31.067 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Notifications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Notifications" PRIMARY KEY AUTOINCREMENT,
    "Title" TEXT NOT NULL,
    "Message" TEXT NOT NULL,
    "Type" INTEGER NOT NULL,
    "Priority" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL,
    "ReadAt" TEXT NULL,
    "ScheduledAt" TEXT NULL,
    "RelatedEntityType" TEXT NULL,
    "RelatedEntityId" INTEGER NULL,
    "ActionUrl" TEXT NULL,
    "IsAutoGenerated" INTEGER NOT NULL,
    "CreatedBy" TEXT NULL,
    "AdditionalData" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:31.067 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720449'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:31.067 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720451'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:07:31.067 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720451'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:07:31.068 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720452'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:07:31.068 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720453'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:07:31.068 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720597'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:31.068 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720598'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:07:31.068 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720602'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:07:31.068 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720623'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:07:31.068 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720623'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:07:31.068 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720624'
WHERE "Id" = 6;
SELECT changes();
2025-07-26 07:07:31.068 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720625'
WHERE "Id" = 7;
SELECT changes();
2025-07-26 07:07:31.069 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720625'
WHERE "Id" = 8;
SELECT changes();
2025-07-26 07:07:31.069 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720626'
WHERE "Id" = 9;
SELECT changes();
2025-07-26 07:07:31.069 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720627'
WHERE "Id" = 10;
SELECT changes();
2025-07-26 07:07:31.069 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720627'
WHERE "Id" = 11;
SELECT changes();
2025-07-26 07:07:31.069 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720628'
WHERE "Id" = 12;
SELECT changes();
2025-07-26 07:07:31.069 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720628'
WHERE "Id" = 13;
SELECT changes();
2025-07-26 07:07:31.069 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "FarmInfos" SET "CreatedDate" = '2025-07-26 07:05:16.9720737'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:31.069 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721427'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:31.069 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721568'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:07:31.070 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721627'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:07:31.070 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721665'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:07:31.070 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721691'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:07:31.070 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedDate" = '2025-07-26 07:05:17.2015304', "GrantedDate" = '2025-07-26 07:05:17.2015303'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:31.070 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = '2025-07-26 07:05:16.9721768', "MustChangePassword" = 0, "PasswordHash" = '$2a$11$Io9.UFRwXMZqmnl8.29pgujYImy4zqqbkrDpTzxaR5LyJHdqitpJy'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:31.070 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726040518_AddMustChangePasswordColumn', '8.0.0');
2025-07-26 07:07:31.074 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-26 07:07:31.307 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:07:31.489 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-26 07:07:31.564 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-26 07:07:31.564 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-26 07:07:31.564 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 07:07:41.070 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:07:41.169 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."CreatedDate", "t"."Email", "t"."FailedLoginAttempts", "t"."FullName", "t"."IsSystemAdmin", "t"."LastLoginDate", "t"."LockedUntil", "t"."MustChangePassword", "t"."Notes", "t"."PasswordHash", "t"."PhoneNumber", "t"."Status", "t"."UpdatedDate", "t"."Username", "t0"."Id", "t0"."CreatedDate", "t0"."ExpiryDate", "t0"."GrantedBy", "t0"."GrantedDate", "t0"."IsActive", "t0"."Notes", "t0"."RoleId", "t0"."UpdatedDate", "t0"."UserId", "t0"."Id0", "t0"."CreatedDate0", "t0"."Description", "t0"."IsActive0", "t0"."IsSystemRole", "t0"."Name", "t0"."Permissions", "t0"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "t"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "t0" ON "t"."Id" = "t0"."UserId"
ORDER BY "t"."Id", "t0"."Id"
2025-07-26 07:07:41.489 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "LastLoginDate" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-26 07:07:42.218 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-07-26 07:07:42.249 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."AverageWeight", "p"."CreatedDate", "p"."CycleId", "p"."ExpectedHarvestDate", "p"."FishCount", "p"."Notes", "p"."PondNumber", "p"."Status", "p"."StockingDate", "p"."UpdatedDate"
FROM "Ponds" AS "p"
2025-07-26 07:07:42.254 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."BudgetAmount", "p"."CreatedDate", "p"."CycleName", "p"."EndDate", "p"."ExpectedEndDate", "p"."Notes", "p"."StartDate", "p"."Status", "p"."UpdatedDate"
FROM "ProductionCycles" AS "p"
2025-07-26 07:07:42.258 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "e"."Id", "e"."Address", "e"."BaseSalary", "e"."BirthDate", "e"."ContactInfo", "e"."CreatedDate", "e"."Email", "e"."FullName", "e"."JoinDate", "e"."LeaveDate", "e"."MaritalStatus", "e"."NationalId", "e"."Nationality", "e"."NumberOfChildren", "e"."Phone", "e"."Position", "e"."ResidenceNumber", "e"."Status", "e"."UpdatedDate"
FROM "Employees" AS "e"
2025-07-26 07:07:42.263 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
2025-07-26 07:07:42.268 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "i"."Id", "i"."BatchNumber", "i"."CreatedDate", "i"."ExpiryDate", "i"."ItemName", "i"."ItemType", "i"."MaximumStock", "i"."MinimumStock", "i"."Notes", "i"."Quantity", "i"."ReorderPoint", "i"."Status", "i"."StorageLocation", "i"."Supplier", "i"."TotalValue", "i"."Unit", "i"."UnitPrice", "i"."UpdatedDate"
FROM "Inventories" AS "i"
2025-07-26 07:07:42.282 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__startOfMonth_0='?' (DbType = DateTime), @__AddMonths_1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
WHERE "t"."TransactionDate" >= @__startOfMonth_0 AND "t"."TransactionDate" < @__AddMonths_1
2025-07-26 07:07:42.286 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__startOfMonth_0='?' (DbType = DateTime), @__AddMonths_1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
WHERE "t"."TransactionDate" >= @__startOfMonth_0 AND "t"."TransactionDate" < @__AddMonths_1
2025-07-26 07:07:42.286 +03:00 [INF] تم تحميل بيانات لوحة المعلومات بنجاح
2025-07-26 07:07:42.287 +03:00 [WRN] لا يوجد مستخدم مسجل دخول
2025-07-26 07:07:46.662 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."AverageWeight", "p"."CreatedDate", "p"."CycleId", "p"."ExpectedHarvestDate", "p"."FishCount", "p"."Notes", "p"."PondNumber", "p"."Status", "p"."StockingDate", "p"."UpdatedDate"
FROM "Ponds" AS "p"
2025-07-26 07:08:22.506 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."BudgetAmount", "p"."CreatedDate", "p"."CycleName", "p"."EndDate", "p"."ExpectedEndDate", "p"."Notes", "p"."StartDate", "p"."Status", "p"."UpdatedDate"
FROM "ProductionCycles" AS "p"
WHERE "p"."Id" = @__p_0
LIMIT 1
2025-07-26 07:08:22.507 +03:00 [ERR] خطأ في إضافة الحوض 0001
System.ArgumentException: بيانات الحوض غير صحيحة: الدورة الإنتاجية المحددة غير موجودة
   at FishFarmManagement.BLL.Services.PondService.AddAsync(Pond pond) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement.BLL\Services\PondService.cs:line 56
2025-07-26 07:08:22.519 +03:00 [ERR] خطأ في حفظ بيانات الحوض
System.ArgumentException: بيانات الحوض غير صحيحة: الدورة الإنتاجية المحددة غير موجودة
   at FishFarmManagement.BLL.Services.PondService.AddAsync(Pond pond) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement.BLL\Services\PondService.cs:line 56
   at FishFarmManagement.Forms.PondAddEditForm.SaveButton_Click(Object sender, EventArgs e) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Forms\PondAddEditForm.cs:line 329
2025-07-26 07:08:24.278 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."AverageWeight", "p"."CreatedDate", "p"."CycleId", "p"."ExpectedHarvestDate", "p"."FishCount", "p"."Notes", "p"."PondNumber", "p"."Status", "p"."StockingDate", "p"."UpdatedDate"
FROM "Ponds" AS "p"
2025-07-26 07:08:32.376 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."BudgetAmount", "p"."CreatedDate", "p"."CycleName", "p"."EndDate", "p"."ExpectedEndDate", "p"."Notes", "p"."StartDate", "p"."Status", "p"."UpdatedDate"
FROM "ProductionCycles" AS "p"
2025-07-26 07:08:54.629 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 07:08:54.665 +03:00 [INF] تم اكتشاف قاعدة بيانات موجودة
2025-07-26 07:08:54.920 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:08:55.005 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-26 07:08:55.046 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-26 07:08:55.047 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-26 07:08:55.047 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 07:09:05.200 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:09:05.296 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."CreatedDate", "t"."Email", "t"."FailedLoginAttempts", "t"."FullName", "t"."IsSystemAdmin", "t"."LastLoginDate", "t"."LockedUntil", "t"."MustChangePassword", "t"."Notes", "t"."PasswordHash", "t"."PhoneNumber", "t"."Status", "t"."UpdatedDate", "t"."Username", "t0"."Id", "t0"."CreatedDate", "t0"."ExpiryDate", "t0"."GrantedBy", "t0"."GrantedDate", "t0"."IsActive", "t0"."Notes", "t0"."RoleId", "t0"."UpdatedDate", "t0"."UserId", "t0"."Id0", "t0"."CreatedDate0", "t0"."Description", "t0"."IsActive0", "t0"."IsSystemRole", "t0"."Name", "t0"."Permissions", "t0"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "t"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "t0" ON "t"."Id" = "t0"."UserId"
ORDER BY "t"."Id", "t0"."Id"
2025-07-26 07:09:05.620 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "LastLoginDate" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-26 07:09:06.322 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-07-26 07:09:06.349 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."AverageWeight", "p"."CreatedDate", "p"."CycleId", "p"."ExpectedHarvestDate", "p"."FishCount", "p"."Notes", "p"."PondNumber", "p"."Status", "p"."StockingDate", "p"."UpdatedDate"
FROM "Ponds" AS "p"
2025-07-26 07:09:06.357 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."BudgetAmount", "p"."CreatedDate", "p"."CycleName", "p"."EndDate", "p"."ExpectedEndDate", "p"."Notes", "p"."StartDate", "p"."Status", "p"."UpdatedDate"
FROM "ProductionCycles" AS "p"
2025-07-26 07:09:06.363 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "e"."Id", "e"."Address", "e"."BaseSalary", "e"."BirthDate", "e"."ContactInfo", "e"."CreatedDate", "e"."Email", "e"."FullName", "e"."JoinDate", "e"."LeaveDate", "e"."MaritalStatus", "e"."NationalId", "e"."Nationality", "e"."NumberOfChildren", "e"."Phone", "e"."Position", "e"."ResidenceNumber", "e"."Status", "e"."UpdatedDate"
FROM "Employees" AS "e"
2025-07-26 07:09:06.368 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
2025-07-26 07:09:06.374 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "i"."Id", "i"."BatchNumber", "i"."CreatedDate", "i"."ExpiryDate", "i"."ItemName", "i"."ItemType", "i"."MaximumStock", "i"."MinimumStock", "i"."Notes", "i"."Quantity", "i"."ReorderPoint", "i"."Status", "i"."StorageLocation", "i"."Supplier", "i"."TotalValue", "i"."Unit", "i"."UnitPrice", "i"."UpdatedDate"
FROM "Inventories" AS "i"
2025-07-26 07:09:06.390 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__startOfMonth_0='?' (DbType = DateTime), @__AddMonths_1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
WHERE "t"."TransactionDate" >= @__startOfMonth_0 AND "t"."TransactionDate" < @__AddMonths_1
2025-07-26 07:09:06.393 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__startOfMonth_0='?' (DbType = DateTime), @__AddMonths_1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
WHERE "t"."TransactionDate" >= @__startOfMonth_0 AND "t"."TransactionDate" < @__AddMonths_1
2025-07-26 07:09:06.394 +03:00 [INF] تم تحميل بيانات لوحة المعلومات بنجاح
2025-07-26 07:09:06.395 +03:00 [WRN] لا يوجد مستخدم مسجل دخول
2025-07-26 07:09:09.240 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."BudgetAmount", "p"."CreatedDate", "p"."CycleName", "p"."EndDate", "p"."ExpectedEndDate", "p"."Notes", "p"."StartDate", "p"."Status", "p"."UpdatedDate"
FROM "ProductionCycles" AS "p"
2025-07-26 07:09:37.222 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Decimal), @p1='?' (DbType = DateTime), @p2='?' (Size = 10), @p3='?' (DbType = DateTime), @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = DateTime), @p7='?' (Size = 3), @p8='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "ProductionCycles" ("BudgetAmount", "CreatedDate", "CycleName", "EndDate", "ExpectedEndDate", "Notes", "StartDate", "Status", "UpdatedDate")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
RETURNING "Id";
2025-07-26 07:09:37.249 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."BudgetAmount", "p"."CreatedDate", "p"."CycleName", "p"."EndDate", "p"."ExpectedEndDate", "p"."Notes", "p"."StartDate", "p"."Status", "p"."UpdatedDate"
FROM "ProductionCycles" AS "p"
2025-07-26 07:09:44.270 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."AverageWeight", "p"."CreatedDate", "p"."CycleId", "p"."ExpectedHarvestDate", "p"."FishCount", "p"."Notes", "p"."PondNumber", "p"."Status", "p"."StockingDate", "p"."UpdatedDate"
FROM "Ponds" AS "p"
2025-07-26 07:10:09.642 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__pond_PondNumber_0='?' (Size = 3)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."AverageWeight", "p"."CreatedDate", "p"."CycleId", "p"."ExpectedHarvestDate", "p"."FishCount", "p"."Notes", "p"."PondNumber", "p"."Status", "p"."StockingDate", "p"."UpdatedDate"
FROM "Ponds" AS "p"
WHERE "p"."PondNumber" = @__pond_PondNumber_0
LIMIT 1
2025-07-26 07:10:09.678 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Decimal), @p1='?' (DbType = DateTime), @p2='?' (DbType = Int32), @p3='?' (DbType = DateTime), @p4='?' (DbType = Int32), @p5='?', @p6='?' (Size = 3), @p7='?' (Size = 3), @p8='?' (DbType = DateTime), @p9='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Ponds" ("AverageWeight", "CreatedDate", "CycleId", "ExpectedHarvestDate", "FishCount", "Notes", "PondNumber", "Status", "StockingDate", "UpdatedDate")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9)
RETURNING "Id";
2025-07-26 07:10:09.685 +03:00 [INF] تم إضافة الحوض 001 بنجاح
2025-07-26 07:10:10.854 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."AverageWeight", "p"."CreatedDate", "p"."CycleId", "p"."ExpectedHarvestDate", "p"."FishCount", "p"."Notes", "p"."PondNumber", "p"."Status", "p"."StockingDate", "p"."UpdatedDate"
FROM "Ponds" AS "p"
2025-07-26 07:10:18.230 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."AccountCode", "a"."AccountName", "a"."AccountNameEn", "a"."AccountTypeId", "a"."Balance", "a"."CreatedDate", "a"."Description", "a"."IsPostable", "a"."Level", "a"."ParentAccountId", "a"."Status", "a"."UpdatedDate"
FROM "Accounts" AS "a"
2025-07-26 07:10:18.277 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__fromDatePicker_Value_Date_0='?' (DbType = DateTime), @__toDatePicker_Value_Date_1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
WHERE "t"."TransactionDate" >= @__fromDatePicker_Value_Date_0 AND "t"."TransactionDate" <= @__toDatePicker_Value_Date_1
2025-07-26 07:10:41.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedDate", "a"."Description", "a"."DisplayOrder", "a"."Nature", "a"."TypeCode", "a"."TypeName", "a"."UpdatedDate"
FROM "AccountTypes" AS "a"
2025-07-26 07:10:41.694 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."AccountCode", "a"."AccountName", "a"."AccountNameEn", "a"."AccountTypeId", "a"."Balance", "a"."CreatedDate", "a"."Description", "a"."IsPostable", "a"."Level", "a"."ParentAccountId", "a"."Status", "a"."UpdatedDate"
FROM "Accounts" AS "a"
2025-07-26 07:10:41.705 +03:00 [ERR] خطأ في تحميل البيانات
System.ArgumentException: Items collection cannot be modified when the DataSource property is set.
   at System.Windows.Forms.ComboBox.CheckNoDataSource()
   at System.Windows.Forms.ComboBox.ObjectCollection.Insert(Int32 index, Object item)
   at FishFarmManagement.Forms.AccountAddEditForm.LoadDataAsync() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Forms\AccountAddEditForm.cs:line 191
2025-07-26 07:19:27.331 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = غير موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 07:19:27.365 +03:00 [INF] إنشاء قاعدة البيانات باستخدام التهجيرات
2025-07-26 07:19:27.533 +03:00 [ERR] An error occurred using the connection to database 'FishFarmDatabase' on server 'tcp://localhost:5432'.
2025-07-26 07:19:27.535 +03:00 [ERR] خطأ في تهيئة قاعدة البيانات: 28P01: password authentication failed for user "postgres"
Npgsql.PostgresException (0x80004005): 28P01: password authentication failed for user "postgres"
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.AuthenticateSASL(List`1 mechanisms, String username, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Authenticate(String username, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.HistoryRepository.GetAppliedMigrationsAsync(CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlHistoryRepository.GetAppliedMigrationsAsync(CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlMigrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 192
  Exception data:
    Severity: FATAL
    SqlState: 28P01
    MessageText: password authentication failed for user "postgres"
    File: auth.c
    Line: 324
    Routine: auth_failed
2025-07-26 07:19:27.565 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
System.Exception: حدث خطأ أثناء تهيئة قاعدة البيانات: 28P01: password authentication failed for user "postgres"
 ---> Npgsql.PostgresException (0x80004005): 28P01: password authentication failed for user "postgres"
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.AuthenticateSASL(List`1 mechanisms, String username, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Authenticate(String username, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.HistoryRepository.GetAppliedMigrationsAsync(CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlHistoryRepository.GetAppliedMigrationsAsync(CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlMigrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 192
  Exception data:
    Severity: FATAL
    SqlState: 28P01
    MessageText: password authentication failed for user "postgres"
    File: auth.c
    Line: 324
    Routine: auth_failed
   --- End of inner exception stack trace ---
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 220
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 48
2025-07-26 07:20:57.067 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
System.TypeLoadException: Method 'get_LockReleaseBehavior' in type 'Microsoft.EntityFrameworkCore.Sqlite.Migrations.Internal.SqliteHistoryRepository' from assembly 'Microsoft.EntityFrameworkCore.Sqlite, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60' does not have an implementation.
   at Microsoft.Extensions.DependencyInjection.SqliteServiceCollectionExtensions.AddEntityFrameworkSqlite(IServiceCollection serviceCollection)
   at Microsoft.EntityFrameworkCore.Sqlite.Infrastructure.Internal.SqliteOptionsExtension.ApplyServices(IServiceCollection services)
   at Microsoft.EntityFrameworkCore.Internal.ServiceProviderCache.ApplyServices(IDbContextOptions options, ServiceCollection services)
   at Microsoft.EntityFrameworkCore.Internal.ServiceProviderCache.<GetOrAdd>g__BuildServiceProvider|4_1(IDbContextOptions _, ValueTuple`2 arguments)
   at Microsoft.EntityFrameworkCore.Internal.ServiceProviderCache.<>c.<GetOrAdd>b__4_0(IDbContextOptions contextOptions, ValueTuple`2 tuples)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd[TArg](TKey key, Func`3 valueFactory, TArg factoryArgument)
   at Microsoft.EntityFrameworkCore.Internal.ServiceProviderCache.GetOrAdd(IDbContextOptions options, Boolean providerRequired)
   at Microsoft.EntityFrameworkCore.DbContext..ctor(DbContextOptions options)
   at FishFarmManagement.DAL.FishFarmDbContext..ctor(DbContextOptions`1 options) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement.DAL\FishFarmDbContext.cs:line 13
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FishFarmManagement.Program.InitializeDatabaseAsync(IServiceProvider services) in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 166
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 48
2025-07-26 07:33:03.708 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = غير موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 07:33:03.743 +03:00 [INF] إنشاء قاعدة البيانات باستخدام التهجيرات
2025-07-26 07:33:04.082 +03:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-26 07:33:04.093 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-26 07:33:04.103 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-07-26 07:33:04.119 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsLock" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK___EFMigrationsLock" PRIMARY KEY,
    "Timestamp" TEXT NOT NULL
);
2025-07-26 07:33:04.125 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-07-26 04:33:04.1204754+00:00');
SELECT changes();
2025-07-26 07:33:04.198 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 07:33:04.217 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:33:04.222 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:33:04.235 +03:00 [INF] Applying migration '20250726043205_InitialSQLiteMigration'.
2025-07-26 07:33:04.366 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:33:04.366 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:33:04.366 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:33:04.367 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:33:04.367 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:33:04.367 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:33:04.367 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Notifications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Notifications" PRIMARY KEY AUTOINCREMENT,
    "Title" TEXT NOT NULL,
    "Message" TEXT NOT NULL,
    "Type" INTEGER NOT NULL,
    "Priority" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL,
    "ReadAt" TEXT NULL,
    "ScheduledAt" TEXT NULL,
    "RelatedEntityType" TEXT NULL,
    "RelatedEntityId" INTEGER NULL,
    "ActionUrl" TEXT NULL,
    "IsAutoGenerated" INTEGER NOT NULL,
    "CreatedBy" TEXT NULL,
    "AdditionalData" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:33:04.367 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:33:04.368 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:33:04.368 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "MustChangePassword" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:33:04.368 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:33:04.368 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:33:04.368 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:33:04.369 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:33:04.369 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:33:04.369 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:33:04.369 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-26 07:33:04.369 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:33:04.369 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:33:04.370 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:33:04.370 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-26 07:33:04.370 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-26 07:33:04.370 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-26 07:00:00', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-26 07:33:04.370 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-26 07:33:04.371 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "MustChangePassword", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-26 07:00:00', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, 0, NULL, '$2a$11$K8gHZ8W8W8W8W8W8W8W8WOeH8W8W8W8W8W8W8W8W8W8W8W8W8W8W8W8', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-26 07:33:04.371 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-26 07:33:04.371 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-26 07:00:00', NULL, 'System', '2025-07-26 07:00:00', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-26 07:33:04.372 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-26 07:33:04.372 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-26 07:33:04.372 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-26 07:33:04.372 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-26 07:33:04.372 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-26 07:33:04.372 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-26 07:33:04.373 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-26 07:33:04.373 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-26 07:33:04.373 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-26 07:33:04.373 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-26 07:33:04.373 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 07:33:04.373 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 07:33:04.374 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-26 07:33:04.374 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-26 07:33:04.374 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-26 07:33:04.374 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-26 07:33:04.374 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-26 07:33:04.374 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-26 07:33:04.374 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-26 07:33:04.375 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-26 07:33:04.375 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-26 07:33:04.375 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-26 07:33:04.375 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-26 07:33:04.375 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-26 07:33:04.375 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-26 07:33:04.375 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-26 07:33:04.376 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-26 07:33:04.376 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-26 07:33:04.376 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726043205_InitialSQLiteMigration', '9.0.1');
2025-07-26 07:33:04.388 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-07-26 07:33:04.388 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-26 07:33:04.689 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:33:04.766 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-26 07:33:04.791 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-26 07:33:04.791 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-26 07:33:04.791 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 07:33:47.543 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:33:47.650 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 07:33:48.264 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-26 07:34:04.724 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:34:04.725 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 07:34:05.074 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-26 07:34:46.786 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 07:34:46.825 +03:00 [INF] تم اكتشاف قاعدة بيانات موجودة
2025-07-26 07:34:47.348 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:34:47.436 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-26 07:34:47.460 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-26 07:34:47.461 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-26 07:34:47.461 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 07:35:15.167 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:35:15.271 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 07:35:15.911 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p2='?' (DbType = Int32), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0, "LockedUntil" = @p1
WHERE "Id" = @p2
RETURNING 1;
2025-07-26 07:36:10.641 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 07:36:10.678 +03:00 [INF] تم اكتشاف قاعدة بيانات موجودة
2025-07-26 07:36:11.061 +03:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:36:11.158 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-26 07:36:11.186 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-26 07:36:11.186 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-26 07:36:11.186 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 07:36:24.071 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:36:24.175 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 07:37:06.036 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 07:37:06.067 +03:00 [INF] تم اكتشاف قاعدة بيانات موجودة
2025-07-26 07:37:06.396 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:37:06.467 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-26 07:37:06.491 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-26 07:37:06.492 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-26 07:37:06.492 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 07:37:15.612 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:37:15.713 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 07:46:10.458 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 07:46:10.508 +03:00 [INF] تم اكتشاف قاعدة بيانات موجودة
2025-07-26 07:46:11.167 +03:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:46:11.296 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-26 07:46:11.328 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-26 07:46:11.329 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-26 07:46:11.329 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 07:46:30.340 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:46:30.466 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 07:48:43.468 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 07:48:43.565 +03:00 [INF] تم اكتشاف قاعدة بيانات موجودة
2025-07-26 07:48:44.399 +03:00 [INF] Executed DbCommand (88ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:48:44.511 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-26 07:48:44.551 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-26 07:48:44.551 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-26 07:48:44.551 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 07:48:54.257 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:48:54.378 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 07:49:03.833 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 07:49:03.834 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 15:58:36.150 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 15:58:36.184 +03:00 [INF] تم اكتشاف قاعدة بيانات موجودة
2025-07-26 15:58:36.184 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 15:58:49.800 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 15:58:49.979 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 15:58:50.604 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p2='?' (DbType = Int32), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0, "LockedUntil" = @p1
WHERE "Id" = @p2
RETURNING 1;
