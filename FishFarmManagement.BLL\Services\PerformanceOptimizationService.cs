using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using FishFarmManagement.DAL;
using System.Timers;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة تحسين الأداء التلقائي
    /// Automatic performance optimization service
    /// </summary>
    public class PerformanceOptimizationService
    {
        private readonly ILogger<PerformanceOptimizationService> _logger;
        private readonly IConfiguration _configuration;
        private readonly FishFarmDbContext _context;
        private System.Timers.Timer? _optimizationTimer;
        private readonly bool _isEnabled;

        public PerformanceOptimizationService(
            ILogger<PerformanceOptimizationService> logger,
            IConfiguration configuration,
            FishFarmDbContext context)
        {
            _logger = logger;
            _configuration = configuration;
            _context = context;
            _isEnabled = _configuration.GetSection("Application:Performance:OptimizeMemory").Value == "true";

            if (_isEnabled)
            {
                InitializeOptimization();
            }
        }

        /// <summary>
        /// تهيئة التحسين التلقائي
        /// Initialize automatic optimization
        /// </summary>
        private void InitializeOptimization()
        {
            try
            {
                // تشغيل التحسين كل 30 دقيقة
                _optimizationTimer = new System.Timers.Timer(1800000);
                _optimizationTimer.Elapsed += OnOptimizationTimerElapsed;
                _optimizationTimer.AutoReset = true;
                _optimizationTimer.Start();

                _logger.LogInformation("تم تهيئة خدمة تحسين الأداء التلقائي");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة خدمة تحسين الأداء");
            }
        }

        /// <summary>
        /// تحسين الذاكرة
        /// Optimize memory
        /// </summary>
        public void OptimizeMemory()
        {
            try
            {
                _logger.LogDebug("بدء تحسين الذاكرة");

                // تنظيف الذاكرة المؤقتة
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                // تحسين ذاكرة Entity Framework
                _context.ChangeTracker.Clear();

                _logger.LogDebug("تم تحسين الذاكرة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين الذاكرة");
            }
        }

        /// <summary>
        /// تحسين قاعدة البيانات
        /// Optimize database
        /// </summary>
        public async Task OptimizeDatabaseAsync()
        {
            try
            {
                _logger.LogInformation("بدء تحسين قاعدة البيانات");

                // تحسين قاعدة البيانات SQLite
                await _context.Database.ExecuteSqlRawAsync("PRAGMA optimize");
                await _context.Database.ExecuteSqlRawAsync("VACUUM");
                await _context.Database.ExecuteSqlRawAsync("ANALYZE");

                _logger.LogInformation("تم تحسين قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين قاعدة البيانات");
            }
        }

        /// <summary>
        /// تنظيف الملفات المؤقتة
        /// Clean temporary files
        /// </summary>
        public void CleanTemporaryFiles()
        {
            try
            {
                _logger.LogDebug("بدء تنظيف الملفات المؤقتة");

                var tempPaths = new[]
                {
                    Path.GetTempPath(),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Temp"),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Cache")
                };

                foreach (var tempPath in tempPaths)
                {
                    if (Directory.Exists(tempPath))
                    {
                        CleanDirectory(tempPath, TimeSpan.FromDays(7));
                    }
                }

                _logger.LogDebug("تم تنظيف الملفات المؤقتة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنظيف الملفات المؤقتة");
            }
        }

        /// <summary>
        /// تنظيف مجلد
        /// Clean directory
        /// </summary>
        private void CleanDirectory(string directoryPath, TimeSpan maxAge)
        {
            try
            {
                var directory = new DirectoryInfo(directoryPath);
                var cutoffDate = DateTime.Now - maxAge;

                foreach (var file in directory.GetFiles())
                {
                    if (file.LastWriteTime < cutoffDate)
                    {
                        try
                        {
                            file.Delete();
                        }
                        catch
                        {
                            // تجاهل الأخطاء في حذف الملفات المؤقتة
                        }
                    }
                }

                foreach (var subDirectory in directory.GetDirectories())
                {
                    if (subDirectory.LastWriteTime < cutoffDate && !subDirectory.GetFiles().Any())
                    {
                        try
                        {
                            subDirectory.Delete();
                        }
                        catch
                        {
                            // تجاهل الأخطاء في حذف المجلدات الفارغة
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "خطأ في تنظيف المجلد: {Directory}", directoryPath);
            }
        }

        /// <summary>
        /// تحسين ملفات السجل
        /// Optimize log files
        /// </summary>
        public void OptimizeLogFiles()
        {
            try
            {
                _logger.LogDebug("بدء تحسين ملفات السجل");

                var logsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
                if (Directory.Exists(logsPath))
                {
                    var logFiles = Directory.GetFiles(logsPath, "*.txt")
                        .Select(f => new FileInfo(f))
                        .Where(f => f.LastWriteTime < DateTime.Now.AddDays(-30))
                        .ToList();

                    foreach (var logFile in logFiles)
                    {
                        try
                        {
                            logFile.Delete();
                            _logger.LogDebug("تم حذف ملف السجل القديم: {FileName}", logFile.Name);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "فشل في حذف ملف السجل: {FileName}", logFile.Name);
                        }
                    }
                }

                _logger.LogDebug("تم تحسين ملفات السجل");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين ملفات السجل");
            }
        }

        /// <summary>
        /// تحسين النسخ الاحتياطية القديمة
        /// Optimize old backups
        /// </summary>
        public void OptimizeOldBackups()
        {
            try
            {
                _logger.LogDebug("بدء تحسين النسخ الاحتياطية القديمة");

                var backupsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
                if (Directory.Exists(backupsPath))
                {
                    var maxBackupFiles = int.TryParse(_configuration.GetSection("Application:AutoBackup:MaxBackupFiles").Value, out var result) ? result : 30;
                    var backupFiles = Directory.GetFiles(backupsPath, "*.db")
                        .Select(f => new FileInfo(f))
                        .OrderByDescending(f => f.CreationTime)
                        .ToList();

                    if (backupFiles.Count > maxBackupFiles)
                    {
                        var filesToDelete = backupFiles.Skip(maxBackupFiles);
                        foreach (var file in filesToDelete)
                        {
                            try
                            {
                                file.Delete();
                                _logger.LogDebug("تم حذف النسخة الاحتياطية القديمة: {FileName}", file.Name);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "فشل في حذف النسخة الاحتياطية: {FileName}", file.Name);
                            }
                        }
                    }
                }

                _logger.LogDebug("تم تحسين النسخ الاحتياطية القديمة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين النسخ الاحتياطية القديمة");
            }
        }

        /// <summary>
        /// تشغيل التحسين الشامل
        /// Run comprehensive optimization
        /// </summary>
        public async Task RunComprehensiveOptimizationAsync()
        {
            try
            {
                _logger.LogInformation("بدء التحسين الشامل للنظام");

                // تحسين الذاكرة
                OptimizeMemory();

                // تحسين قاعدة البيانات
                await OptimizeDatabaseAsync();

                // تنظيف الملفات المؤقتة
                CleanTemporaryFiles();

                // تحسين ملفات السجل
                OptimizeLogFiles();

                // تحسين النسخ الاحتياطية القديمة
                OptimizeOldBackups();

                _logger.LogInformation("تم إكمال التحسين الشامل للنظام بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحسين الشامل للنظام");
            }
        }

        /// <summary>
        /// معالج التحسين الدوري
        /// Periodic optimization handler
        /// </summary>
        private async void OnOptimizationTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            try
            {
                _logger.LogDebug("بدء التحسين الدوري");

                // تحسين الذاكرة
                OptimizeMemory();

                // تنظيف الملفات المؤقتة (كل ساعة)
                if (DateTime.Now.Minute == 0)
                {
                    CleanTemporaryFiles();
                }

                // تحسين قاعدة البيانات (يومياً في الساعة 2 صباحاً)
                if (DateTime.Now.Hour == 2 && DateTime.Now.Minute < 30)
                {
                    await OptimizeDatabaseAsync();
                    OptimizeLogFiles();
                    OptimizeOldBackups();
                }

                _logger.LogDebug("تم إكمال التحسين الدوري");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحسين الدوري");
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _optimizationTimer?.Stop();
            _optimizationTimer?.Dispose();
        }
    }
}
