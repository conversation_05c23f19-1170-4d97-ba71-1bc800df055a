@echo off
echo ========================================
echo    Fish Farm Management System Test
echo ========================================
echo.

echo Checking .NET version...
dotnet --version
if %ERRORLEVEL% neq 0 (
    echo ERROR: .NET is not installed or not in PATH
    echo Please install .NET 8.0 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo.
echo Checking project files...
if not exist "FishFarmManagement.sln" (
    echo ERROR: Solution file not found
    pause
    exit /b 1
)

echo Creating Resources directory if not exists...
if not exist "FishFarmManagement\Resources" (
    mkdir "FishFarmManagement\Resources"
    echo Resources directory created.
)

echo.
echo [TEST] Cleaning and building...
dotnet clean --verbosity quiet
dotnet restore --verbosity quiet

echo.
echo [TEST] Building Models...
dotnet build FishFarmManagement.Models\FishFarmManagement.Models.csproj --verbosity minimal
if %ERRORLEVEL% neq 0 (
    echo ERROR: Models project failed to build
    pause
    exit /b 1
)

echo [TEST] Building DAL...
dotnet build FishFarmManagement.DAL\FishFarmManagement.DAL.csproj --verbosity minimal
if %ERRORLEVEL% neq 0 (
    echo ERROR: DAL project failed to build
    pause
    exit /b 1
)

echo [TEST] Building BLL...
dotnet build FishFarmManagement.BLL\FishFarmManagement.BLL.csproj --verbosity minimal
if %ERRORLEVEL% neq 0 (
    echo ERROR: BLL project failed to build
    pause
    exit /b 1
)

echo [TEST] Building Main Application...
dotnet build FishFarmManagement\FishFarmManagement.csproj --verbosity minimal
if %ERRORLEVEL% neq 0 (
    echo ERROR: Main application failed to build
    pause
    exit /b 1
)

echo.
echo ========================================
echo    All tests passed successfully!
echo ========================================
echo.
echo The application is ready to run.
echo Execute: dotnet run --project FishFarmManagement
echo.
pause
