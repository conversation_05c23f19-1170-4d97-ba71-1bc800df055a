﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.Models;
using FishFarmManagement.DAL.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج النظام المحاسبي
    /// Accounting system form
    /// </summary>
    public partial class AccountingForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AccountingForm> _logger;

        // UI Controls
        private TabControl tabControl;
        private TabPage accountsTabPage;
        private TabPage transactionsTabPage;
        private TabPage reportsTabPage;

        // Accounts Tab Controls
        private TreeView accountsTreeView;
        private Button addAccountButton;
        private Button editAccountButton;
        private Button deleteAccountButton;
        private Button refreshAccountsButton;

        // Transactions Tab Controls
        private DataGridView transactionsDataGridView;
        private Button addTransactionButton;
        private Button editTransactionButton;
        private Button deleteTransactionButton;
        private Button refreshTransactionsButton;
        private DateTimePicker fromDatePicker;
        private DateTimePicker toDatePicker;
        private Button filterButton;

        public AccountingForm(IUnitOfWork unitOfWork, ILogger<AccountingForm> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            LoadDataAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "النظام المحاسبي";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            CreateTabControl();
            CreateAccountsTab();
            CreateTransactionsTab();
            CreateReportsTab();
        }

        private void CreateTabControl()
        {
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            accountsTabPage = new TabPage("دليل الحسابات");
            transactionsTabPage = new TabPage("القيود المحاسبية");
            reportsTabPage = new TabPage("التقارير المالية");

            tabControl.TabPages.AddRange(new TabPage[] { accountsTabPage, transactionsTabPage, reportsTabPage });
            this.Controls.Add(tabControl);
        }

        private void CreateAccountsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            // Buttons panel
            var buttonsPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Top
            };

            addAccountButton = new Button
            {
                Text = "إضافة حساب",
                Size = new Size(100, 30),
                Location = new Point(10, 10)
            };
            addAccountButton.Click += AddAccount_Click;

            editAccountButton = new Button
            {
                Text = "تعديل",
                Size = new Size(80, 30),
                Location = new Point(120, 10),
                Enabled = false
            };
            editAccountButton.Click += EditAccount_Click;

            deleteAccountButton = new Button
            {
                Text = "حذف",
                Size = new Size(80, 30),
                Location = new Point(210, 10),
                Enabled = false
            };
            deleteAccountButton.Click += DeleteAccount_Click;

            refreshAccountsButton = new Button
            {
                Text = "تحديث",
                Size = new Size(80, 30),
                Location = new Point(300, 10)
            };
            refreshAccountsButton.Click += RefreshAccounts_Click;

            buttonsPanel.Controls.AddRange(new Control[] {
                addAccountButton, editAccountButton, deleteAccountButton, refreshAccountsButton
            });

            // Accounts tree view
            accountsTreeView = new TreeView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F),
                ShowLines = true,
                ShowPlusMinus = true,
                ShowRootLines = true,
                FullRowSelect = true,
                HideSelection = false
            };
            accountsTreeView.AfterSelect += AccountsTreeView_AfterSelect;
            accountsTreeView.NodeMouseDoubleClick += AccountsTreeView_NodeMouseDoubleClick;

            panel.Controls.Add(accountsTreeView);
            panel.Controls.Add(buttonsPanel);
            accountsTabPage.Controls.Add(panel);
        }

        private void CreateTransactionsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            // Filter panel
            var filterPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top
            };

            var fromLabel = new Label
            {
                Text = "من تاريخ:",
                Location = new Point(10, 15),
                Size = new Size(60, 20)
            };

            fromDatePicker = new DateTimePicker
            {
                Location = new Point(80, 12),
                Size = new Size(120, 25),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now.AddMonths(-1)
            };

            var toLabel = new Label
            {
                Text = "إلى تاريخ:",
                Location = new Point(220, 15),
                Size = new Size(60, 20)
            };

            toDatePicker = new DateTimePicker
            {
                Location = new Point(290, 12),
                Size = new Size(120, 25),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            filterButton = new Button
            {
                Text = "تصفية",
                Location = new Point(430, 12),
                Size = new Size(80, 25)
            };
            filterButton.Click += FilterTransactions_Click;

            // Buttons
            addTransactionButton = new Button
            {
                Text = "إضافة قيد",
                Size = new Size(100, 30),
                Location = new Point(10, 45)
            };
            addTransactionButton.Click += AddTransaction_Click;

            editTransactionButton = new Button
            {
                Text = "تعديل",
                Size = new Size(80, 30),
                Location = new Point(120, 45),
                Enabled = false
            };
            editTransactionButton.Click += EditTransaction_Click;

            deleteTransactionButton = new Button
            {
                Text = "حذف",
                Size = new Size(80, 30),
                Location = new Point(210, 45),
                Enabled = false
            };
            deleteTransactionButton.Click += DeleteTransaction_Click;

            refreshTransactionsButton = new Button
            {
                Text = "تحديث",
                Size = new Size(80, 30),
                Location = new Point(300, 45)
            };
            refreshTransactionsButton.Click += RefreshTransactions_Click;

            filterPanel.Controls.AddRange(new Control[] {
                fromLabel, fromDatePicker, toLabel, toDatePicker, filterButton,
                addTransactionButton, editTransactionButton, deleteTransactionButton, refreshTransactionsButton
            });

            // Transactions data grid view
            transactionsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            SetupTransactionsDataGridViewColumns();
            transactionsDataGridView.SelectionChanged += TransactionsDataGridView_SelectionChanged;
            transactionsDataGridView.CellDoubleClick += TransactionsDataGridView_CellDoubleClick;

            panel.Controls.Add(transactionsDataGridView);
            panel.Controls.Add(filterPanel);
            transactionsTabPage.Controls.Add(panel);
        }

        private void CreateReportsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            var reportsLabel = new Label
            {
                Text = "التقارير المالية",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Location = new Point(20, 20),
                Size = new Size(200, 30)
            };

            var trialBalanceButton = new Button
            {
                Text = "ميزان المراجعة",
                Size = new Size(150, 40),
                Location = new Point(20, 70)
            };
            trialBalanceButton.Click += TrialBalance_Click;

            var incomeStatementButton = new Button
            {
                Text = "قائمة الدخل",
                Size = new Size(150, 40),
                Location = new Point(190, 70)
            };
            incomeStatementButton.Click += IncomeStatement_Click;

            var balanceSheetButton = new Button
            {
                Text = "الميزانية العمومية",
                Size = new Size(150, 40),
                Location = new Point(360, 70)
            };
            balanceSheetButton.Click += BalanceSheet_Click;

            panel.Controls.AddRange(new Control[] {
                reportsLabel, trialBalanceButton, incomeStatementButton, balanceSheetButton
            });

            reportsTabPage.Controls.Add(panel);
        }

        private void SetupTransactionsDataGridViewColumns()
        {
            transactionsDataGridView.Columns.Clear();

            transactionsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "المعرف",
                DataPropertyName = "Id",
                Visible = false
            });

            transactionsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ReferenceNumber",
                HeaderText = "رقم القيد",
                DataPropertyName = "ReferenceNumber",
                Width = 100
            });

            transactionsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TransactionDate",
                HeaderText = "التاريخ",
                DataPropertyName = "TransactionDate",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            transactionsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "البيان",
                DataPropertyName = "Description",
                Width = 300
            });

            transactionsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalAmount",
                HeaderText = "المبلغ الإجمالي",
                DataPropertyName = "TotalAmount",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            transactionsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                DataPropertyName = "Status",
                Width = 80
            });
        }

        private async void LoadDataAsync()
        {
            try
            {
                await LoadAccountsAsync();
                await LoadTransactionsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل البيانات المحاسبية");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadAccountsAsync()
        {
            try
            {
                var accounts = await _unitOfWork.Accounts.GetAllAsync();
                accountsTreeView.Nodes.Clear();

                // Build tree structure
                var rootAccounts = accounts.Where(a => a.ParentAccountId == null).OrderBy(a => a.AccountCode);
                foreach (var account in rootAccounts)
                {
                    var node = CreateAccountNode(account, accounts);
                    accountsTreeView.Nodes.Add(node);
                }

                accountsTreeView.ExpandAll();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الحسابات");
                throw;
            }
        }

        private TreeNode CreateAccountNode(Account account, IEnumerable<Account> allAccounts)
        {
            var node = new TreeNode($"{account.AccountCode} - {account.AccountName} ({account.Balance:C})")
            {
                Tag = account
            };

            var subAccounts = allAccounts.Where(a => a.ParentAccountId == account.Id).OrderBy(a => a.AccountCode);
            foreach (var subAccount in subAccounts)
            {
                var subNode = CreateAccountNode(subAccount, allAccounts);
                node.Nodes.Add(subNode);
            }

            return node;
        }

        private async Task LoadTransactionsAsync()
        {
            try
            {
                var transactions = await _unitOfWork.Transactions.FindAsync(t =>
                    t.TransactionDate >= fromDatePicker.Value.Date &&
                    t.TransactionDate <= toDatePicker.Value.Date);

                transactionsDataGridView.DataSource = transactions.OrderByDescending(t => t.TransactionDate).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل القيود المحاسبية");
                throw;
            }
        }

        // Event Handlers
        private void AccountsTreeView_AfterSelect(object sender, TreeViewEventArgs e)
        {
            bool hasSelection = e.Node != null && e.Node.Tag is Account;
            editAccountButton.Enabled = hasSelection;
            deleteAccountButton.Enabled = hasSelection;
        }

        private void AccountsTreeView_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Node?.Tag is Account account)
            {
                EditAccount_Click(sender, e);
            }
        }

        private void TransactionsDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSelection = transactionsDataGridView.SelectedRows.Count > 0;
            editTransactionButton.Enabled = hasSelection;
            deleteTransactionButton.Enabled = hasSelection;
        }

        private void TransactionsDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditTransaction_Click(sender, e);
            }
        }

        private void AddAccount_Click(object sender, EventArgs e)
        {
            try
            {
                Account? parentAccount = null;
                if (accountsTreeView.SelectedNode?.Tag is Account selectedAccount)
                {
                    parentAccount = selectedAccount;
                }

                var addForm = new AccountAddEditForm(_unitOfWork, _logger, null, parentAccount);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadAccountsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نموذج إضافة حساب");
                MessageBox.Show($"خطأ في فتح النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditAccount_Click(object sender, EventArgs e)
        {
            try
            {
                if (!(accountsTreeView.SelectedNode?.Tag is Account selectedAccount)) return;

                var editForm = new AccountAddEditForm(_unitOfWork, _logger, selectedAccount);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadAccountsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نموذج تعديل حساب");
                MessageBox.Show($"خطأ في فتح النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteAccount_Click(object sender, EventArgs e)
        {
            try
            {
                if (!(accountsTreeView.SelectedNode?.Tag is Account selectedAccount)) return;

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الحساب '{selectedAccount.AccountName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    await _unitOfWork.Accounts.DeleteAsync(selectedAccount);
                    await _unitOfWork.SaveChangesAsync();

                    MessageBox.Show("تم حذف الحساب بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadAccountsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الحساب");
                MessageBox.Show($"خطأ في حذف الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshAccounts_Click(object sender, EventArgs e)
        {
            LoadAccountsAsync();
        }

        private void AddTransaction_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new TransactionAddEditForm(_unitOfWork, _logger);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadTransactionsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نموذج إضافة قيد محاسبي");
                MessageBox.Show($"خطأ في فتح النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditTransaction_Click(object sender, EventArgs e)
        {
            try
            {
                if (transactionsDataGridView.SelectedRows.Count == 0) return;

                var selectedTransaction = transactionsDataGridView.SelectedRows[0].DataBoundItem as Transaction;
                if (selectedTransaction == null) return;

                var editForm = new TransactionAddEditForm(_unitOfWork, _logger, selectedTransaction);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadTransactionsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نموذج تعديل قيد محاسبي");
                MessageBox.Show($"خطأ في فتح النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteTransaction_Click(object sender, EventArgs e)
        {
            try
            {
                if (transactionsDataGridView.SelectedRows.Count == 0) return;

                var selectedTransaction = transactionsDataGridView.SelectedRows[0].DataBoundItem as Transaction;
                if (selectedTransaction == null) return;

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف القيد المحاسبي رقم '{selectedTransaction.ReferenceNumber}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    await _unitOfWork.Transactions.DeleteAsync(selectedTransaction);
                    await _unitOfWork.SaveChangesAsync();

                    MessageBox.Show("تم حذف القيد المحاسبي بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadTransactionsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف القيد المحاسبي");
                MessageBox.Show($"خطأ في حذف القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshTransactions_Click(object sender, EventArgs e)
        {
            LoadTransactionsAsync();
        }

        private void FilterTransactions_Click(object sender, EventArgs e)
        {
            LoadTransactionsAsync();
        }

        private void TrialBalance_Click(object sender, EventArgs e)
        {
            try
            {
                var reportForm = new TrialBalanceReportForm(_unitOfWork);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح تقرير ميزان المراجعة");
                MessageBox.Show($"خطأ في فتح التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void IncomeStatement_Click(object sender, EventArgs e)
        {
            try
            {
                var reportForm = new IncomeStatementReportForm(_unitOfWork);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح تقرير قائمة الدخل");
                MessageBox.Show($"خطأ في فتح التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BalanceSheet_Click(object sender, EventArgs e)
        {
            try
            {
                var reportForm = new BalanceSheetReportForm(_unitOfWork);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح تقرير الميزانية العمومية");
                MessageBox.Show($"خطأ في فتح التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}

