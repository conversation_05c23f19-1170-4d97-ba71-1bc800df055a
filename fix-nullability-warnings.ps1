# PowerShell script to fix nullability warnings in Fish Farm Management System
# This script addresses CS8618 and CS8622 warnings systematically

Write-Host "Starting nullability warnings fix..." -ForegroundColor Green

# Get all C# files in Forms directory
$formsPath = "FishFarmManagement\Forms"
$csharpFiles = Get-ChildItem -Path $formsPath -Filter "*.cs" -Recurse

Write-Host "Found $($csharpFiles.Count) C# files to process" -ForegroundColor Yellow

foreach ($file in $csharpFiles) {
    Write-Host "Processing: $($file.Name)" -ForegroundColor Cyan
    
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $modified = $false
    
    # Fix CS8618 warnings - Non-nullable field declarations
    # Pattern: private ControlType fieldName;
    $fieldPattern = '(\s+private\s+(?:TreeView|DataGridView|Button|TextBox|ComboBox|Label|NumericUpDown|DateTimePicker|CheckBox|Panel|GroupBox|TabControl|TabPage|ListView|ProgressBar|ToolStrip|StatusStrip|ToolStripLabel|ToolStripStatusLabel|Chart|SplitContainer)\s+\w+);'
    if ($content -match $fieldPattern) {
        $content = $content -replace $fieldPattern, '$1 = null!;'
        $modified = $true
        Write-Host "  - Fixed field initialization warnings" -ForegroundColor Green
    }
    
    # Fix CS8622 warnings - Event handler parameter nullability
    # Pattern: (object sender, EventArgs e) -> (object? sender, EventArgs e)
    $eventHandlerPatterns = @(
        '(\s+private\s+void\s+\w+)\(object\s+sender,\s+(EventArgs\s+e)\)',
        '(\s+private\s+void\s+\w+)\(object\s+sender,\s+(TreeViewEventArgs\s+e)\)',
        '(\s+private\s+void\s+\w+)\(object\s+sender,\s+(DataGridViewCellEventArgs\s+e)\)',
        '(\s+private\s+void\s+\w+)\(object\s+sender,\s+(FormClosingEventArgs\s+e)\)',
        '(\s+private\s+void\s+\w+)\(object\s+sender,\s+(KeyEventArgs\s+e)\)',
        '(\s+private\s+void\s+\w+)\(object\s+sender,\s+(MouseEventArgs\s+e)\)',
        '(\s+private\s+void\s+\w+)\(object\s+sender,\s+(PaintEventArgs\s+e)\)'
    )
    
    foreach ($pattern in $eventHandlerPatterns) {
        if ($content -match $pattern) {
            $content = $content -replace $pattern, '$1(object? sender, $2)'
            $modified = $true
        }
    }
    
    if ($modified) {
        Write-Host "  - Fixed event handler parameter warnings" -ForegroundColor Green
    }
    
    # Fix CS8600 and CS8602 warnings - Null reference issues
    # Pattern: = null; -> = null!; (for assignments where we know it's safe)
    $nullAssignmentPattern = '(\s+\w+\s*=\s*)null;'
    if ($content -match $nullAssignmentPattern) {
        # Only replace in specific safe contexts (like form initialization)
        $content = $content -replace '(\s+var\s+\w+\s*=\s*)null;', '$1null!;'
        $content = $content -replace '(\s+string\s+\w+\s*=\s*)null;', '$1null!;'
    }
    
    # Fix CS8625 warnings - Cannot convert null literal to non-nullable reference type
    $nullLiteralPattern = '(\w+\s*=\s*)null(\s*[;,)])'
    if ($content -match $nullLiteralPattern) {
        $content = $content -replace $nullLiteralPattern, '$1null!$2'
        $modified = $true
        Write-Host "  - Fixed null literal conversion warnings" -ForegroundColor Green
    }
    
    # Fix CS8605 warnings - Unboxing a possibly null value
    # Pattern: (Type)value -> (Type)value!
    $unboxingPattern = '\((\w+)\)(\w+\.Value)\b'
    if ($content -match $unboxingPattern) {
        $content = $content -replace $unboxingPattern, '($1)$2!'
        $modified = $true
        Write-Host "  - Fixed unboxing warnings" -ForegroundColor Green
    }
    
    # Fix unused variable warnings (CS0219, CS0169)
    # Comment out unused variables instead of removing them
    $unusedVarPattern = '(\s+)(var|int|string|bool|decimal|double|float)\s+(\w+)\s*=\s*[^;]+;\s*$'
    $lines = $content -split "`n"
    $newLines = @()
    $inMethod = $false
    
    for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i]
        
        # Check if we're entering a method
        if ($line -match '^\s*private\s+\w+\s+\w+\([^)]*\)\s*$' -or $line -match '^\s*public\s+\w+\s+\w+\([^)]*\)\s*$') {
            $inMethod = $true
        }
        
        # Check if we're leaving a method
        if ($line -match '^\s*}\s*$' -and $inMethod) {
            $inMethod = $false
        }
        
        # Check for unused variables in methods
        if ($inMethod -and $line -match $unusedVarPattern) {
            $varName = $matches[3]
            # Check if variable is used later in the method
            $remainingLines = $lines[($i+1)..($lines.Count-1)]
            $isUsed = $false
            
            foreach ($remainingLine in $remainingLines) {
                if ($remainingLine -match "^\s*}\s*$") { break }
                if ($remainingLine -match "\b$varName\b" -and $remainingLine -notmatch "^\s*//") {
                    $isUsed = $true
                    break
                }
            }
            
            if (-not $isUsed) {
                $line = $line -replace "^(\s*)", '$1// Unused variable commented out: '
                $modified = $true
            }
        }
        
        $newLines += $line
    }
    
    if ($modified) {
        $content = $newLines -join "`n"
        Write-Host "  - Fixed unused variable warnings" -ForegroundColor Green
    }
    
    # Save the file if modified
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        Write-Host "  ✓ Updated $($file.Name)" -ForegroundColor Green
    } else {
        Write-Host "  - No changes needed for $($file.Name)" -ForegroundColor Gray
    }
}

Write-Host "`nNullability warnings fix completed!" -ForegroundColor Green
Write-Host "Please build the project to verify the fixes." -ForegroundColor Yellow
