using System.ComponentModel.DataAnnotations;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// الكلاس الأساسي لجميع الكيانات في النظام
    /// Base class for all entities in the system
    /// </summary>
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        /// <summary>
        /// تحديث تاريخ التعديل عند حفظ التغييرات
        /// Update modification date when saving changes
        /// </summary>
        public virtual void UpdateModificationDate()
        {
            UpdatedDate = DateTime.Now;
        }
    }
}
