using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة معلومات المزرعة
    /// Farm information management service interface
    /// </summary>
    public interface IFarmInfoService : IBusinessService<FarmInfo, int>
    {
        /// <summary>
        /// الحصول على معلومات المزرعة الحالية
        /// Get current farm information
        /// </summary>
        Task<FarmInfo?> GetCurrentFarmInfoAsync();

        /// <summary>
        /// تحديث معلومات المزرعة
        /// Update farm information
        /// </summary>
        Task<FarmInfo> UpdateFarmInfoAsync(FarmInfo farmInfo);

        /// <summary>
        /// تحديث شعار المزرعة
        /// Update farm logo
        /// </summary>
        Task<bool> UpdateFarmLogoAsync(byte[] logoData);

        /// <summary>
        /// الحصول على شعار المزرعة
        /// Get farm logo
        /// </summary>
        Task<byte[]?> GetFarmLogoAsync();

        /// <summary>
        /// إنشاء معلومات مزرعة افتراضية
        /// Create default farm information
        /// </summary>
        Task<FarmInfo> CreateDefaultFarmInfoAsync();

        /// <summary>
        /// التحقق من وجود معلومات المزرعة
        /// Check if farm information exists
        /// </summary>
        Task<bool> FarmInfoExistsAsync();

        /// <summary>
        /// الحصول على إحصائيات المزرعة
        /// Get farm statistics
        /// </summary>
        Task<FarmStatistics> GetFarmStatisticsAsync();

        /// <summary>
        /// تصدير معلومات المزرعة
        /// Export farm information
        /// </summary>
        Task<byte[]> ExportFarmInfoAsync(string format = "PDF");
    }

    /// <summary>
    /// إحصائيات المزرعة
    /// Farm statistics
    /// </summary>
    public class FarmStatistics
    {
        public string FarmName { get; set; } = string.Empty;
        public DateTime EstablishedDate { get; set; }
        public int TotalCycles { get; set; }
        public int ActiveCycles { get; set; }
        public int CompletedCycles { get; set; }
        public int TotalPonds { get; set; }
        public int ActivePonds { get; set; }
        public int TotalEmployees { get; set; }
        public int ActiveEmployees { get; set; }
        public decimal TotalProduction { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal NetProfit { get; set; }
        public decimal AverageCycleDuration { get; set; }
        public decimal AverageProfitMargin { get; set; }
        public decimal AverageFCR { get; set; }
        public decimal AverageMortalityRate { get; set; }
        public DateTime LastUpdated { get; set; }
    }
}
