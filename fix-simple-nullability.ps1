# Simple PowerShell script to fix the most common nullability warnings
Write-Host "Fixing nullability warnings..." -ForegroundColor Green

$formsPath = "FishFarmManagement\Forms"
$files = Get-ChildItem -Path $formsPath -Filter "*.cs"

foreach ($file in $files) {
    Write-Host "Processing $($file.Name)..." -ForegroundColor Yellow
    
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $modified = $false
    
    # Fix field declarations - add = null!
    $patterns = @(
        'private TreeView (\w+);',
        'private DataGridView (\w+);',
        'private Button (\w+);',
        'private TextBox (\w+);',
        'private ComboBox (\w+);',
        'private Label (\w+);',
        'private NumericUpDown (\w+);',
        'private DateTimePicker (\w+);',
        'private CheckBox (\w+);',
        'private Panel (\w+);',
        'private GroupBox (\w+);',
        'private TabControl (\w+);',
        'private TabPage (\w+);',
        'private ListView (\w+);',
        'private ProgressBar (\w+);',
        'private ToolStrip (\w+);',
        'private StatusStrip (\w+);',
        'private ToolStripLabel (\w+);',
        'private ToolStripStatusLabel (\w+);'
    )
    
    foreach ($pattern in $patterns) {
        $replacement = $pattern -replace '\(\\\w\+\)', '$1 = null!'
        $replacement = $replacement -replace ';', ' = null!;'
        if ($content -match $pattern) {
            $content = $content -replace $pattern, $replacement
            $modified = $true
        }
    }
    
    # Fix event handlers - make sender nullable
    $eventPatterns = @(
        'private void (\w+)\(object sender, EventArgs e\)',
        'private void (\w+)\(object sender, TreeViewEventArgs e\)',
        'private void (\w+)\(object sender, DataGridViewCellEventArgs e\)',
        'private void (\w+)\(object sender, FormClosingEventArgs e\)'
    )
    
    foreach ($pattern in $eventPatterns) {
        $replacement = $pattern -replace 'object sender', 'object? sender'
        if ($content -match $pattern) {
            $content = $content -replace $pattern, $replacement
            $modified = $true
        }
    }
    
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        Write-Host "Updated $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "Done!" -ForegroundColor Green
