using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Controls;

namespace FishFarmManagement.Helpers
{
    /// <summary>
    /// مساعد ربط الاقتراح التلقائي مع خدمة البحث
    /// AutoComplete helper for connecting with search service
    /// </summary>
    public static class AutoCompleteHelper
    {
        /// <summary>
        /// ربط مكون الاقتراح التلقائي بالبحث في الحسابات
        /// Connect AutoComplete with accounts search
        /// </summary>
        public static void SetupAccountsAutoComplete(AutoCompleteTextBox autoComplete, ISearchService searchService)
        {
            autoComplete.SearchRequested += async (sender, searchTerm) =>
            {
                try
                {
                    var results = await searchService.SearchAccountsAsync(searchTerm);
                    var items = results.Select(r => new AutoCompleteItem(r.Id, r.<PERSON>, r.SecondaryText)).ToList();
                    
                    if (autoComplete.InvokeRequired)
                    {
                        autoComplete.Invoke(new Action(() => autoComplete.SetDataSource(items)));
                    }
                    else
                    {
                        autoComplete.SetDataSource(items);
                    }
                }
                catch (Exception ex)
                {
                    // Log error but don't show to user to avoid interrupting workflow
                    System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن الحسابات: {ex.Message}");
                }
            };
        }

        /// <summary>
        /// ربط مكون الاقتراح التلقائي بالبحث في الموظفين
        /// Connect AutoComplete with employees search
        /// </summary>
        public static void SetupEmployeesAutoComplete(AutoCompleteTextBox autoComplete, ISearchService searchService)
        {
            autoComplete.SearchRequested += async (sender, searchTerm) =>
            {
                try
                {
                    var results = await searchService.SearchEmployeesAsync(searchTerm);
                    var items = results.Select(r => new AutoCompleteItem(r.Id, r.DisplayText, r.SecondaryText)).ToList();
                    
                    if (autoComplete.InvokeRequired)
                    {
                        autoComplete.Invoke(new Action(() => autoComplete.SetDataSource(items)));
                    }
                    else
                    {
                        autoComplete.SetDataSource(items);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن الموظفين: {ex.Message}");
                }
            };
        }

        /// <summary>
        /// ربط مكون الاقتراح التلقائي بالبحث في المخزون
        /// Connect AutoComplete with inventory search
        /// </summary>
        public static void SetupInventoryAutoComplete(AutoCompleteTextBox autoComplete, ISearchService searchService)
        {
            autoComplete.SearchRequested += async (sender, searchTerm) =>
            {
                try
                {
                    var results = await searchService.SearchInventoryItemsAsync(searchTerm);
                    var items = results.Select(r => new AutoCompleteItem(r.Id, r.DisplayText, r.SecondaryText)).ToList();
                    
                    if (autoComplete.InvokeRequired)
                    {
                        autoComplete.Invoke(new Action(() => autoComplete.SetDataSource(items)));
                    }
                    else
                    {
                        autoComplete.SetDataSource(items);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن المخزون: {ex.Message}");
                }
            };
        }

        /// <summary>
        /// ربط مكون الاقتراح التلقائي بالبحث في الأدوية
        /// Connect AutoComplete with medications search
        /// </summary>
        public static void SetupMedicationsAutoComplete(AutoCompleteTextBox autoComplete, ISearchService searchService)
        {
            autoComplete.SearchRequested += async (sender, searchTerm) =>
            {
                try
                {
                    var results = await searchService.SearchMedicationsAsync(searchTerm);
                    var items = results.Select(r => new AutoCompleteItem(r.Id, r.DisplayText, r.SecondaryText)).ToList();
                    
                    if (autoComplete.InvokeRequired)
                    {
                        autoComplete.Invoke(new Action(() => autoComplete.SetDataSource(items)));
                    }
                    else
                    {
                        autoComplete.SetDataSource(items);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن الأدوية: {ex.Message}");
                }
            };
        }

        /// <summary>
        /// ربط مكون الاقتراح التلقائي بالبحث في الأحواض
        /// Connect AutoComplete with ponds search
        /// </summary>
        public static void SetupPondsAutoComplete(AutoCompleteTextBox autoComplete, ISearchService searchService)
        {
            autoComplete.SearchRequested += async (sender, searchTerm) =>
            {
                try
                {
                    var results = await searchService.SearchPondsAsync(searchTerm);
                    var items = results.Select(r => new AutoCompleteItem(r.Id, r.DisplayText, r.SecondaryText)).ToList();
                    
                    if (autoComplete.InvokeRequired)
                    {
                        autoComplete.Invoke(new Action(() => autoComplete.SetDataSource(items)));
                    }
                    else
                    {
                        autoComplete.SetDataSource(items);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن الأحواض: {ex.Message}");
                }
            };
        }

        /// <summary>
        /// ربط مكون الاقتراح التلقائي بالبحث في أنواع العلف
        /// Connect AutoComplete with feed types search
        /// </summary>
        public static void SetupFeedTypesAutoComplete(AutoCompleteTextBox autoComplete, ISearchService searchService)
        {
            autoComplete.SearchRequested += async (sender, searchTerm) =>
            {
                try
                {
                    var results = await searchService.SearchFeedTypesAsync(searchTerm);
                    var items = results.Select(r => new AutoCompleteItem(r.Id, r.DisplayText, r.SecondaryText)).ToList();
                    
                    if (autoComplete.InvokeRequired)
                    {
                        autoComplete.Invoke(new Action(() => autoComplete.SetDataSource(items)));
                    }
                    else
                    {
                        autoComplete.SetDataSource(items);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن أنواع العلف: {ex.Message}");
                }
            };
        }

        /// <summary>
        /// إعداد البحث العام
        /// Setup global search
        /// </summary>
        public static void SetupGlobalAutoComplete(AutoCompleteTextBox autoComplete, ISearchService searchService)
        {
            autoComplete.SearchRequested += async (sender, searchTerm) =>
            {
                try
                {
                    var results = await searchService.GlobalSearchAsync(searchTerm, 3);
                    var items = new List<AutoCompleteItem>();

                    // Add accounts
                    items.AddRange(results.Accounts.Select(r => 
                        new AutoCompleteItem(r.Id, r.DisplayText, $"حساب: {r.SecondaryText}") { Tag = "Account" }));

                    // Add employees
                    items.AddRange(results.Employees.Select(r => 
                        new AutoCompleteItem(r.Id, r.DisplayText, $"موظف: {r.SecondaryText}") { Tag = "Employee" }));

                    // Add inventory items
                    items.AddRange(results.InventoryItems.Select(r => 
                        new AutoCompleteItem(r.Id, r.DisplayText, $"مخزون: {r.SecondaryText}") { Tag = "Inventory" }));

                    // Add medications
                    items.AddRange(results.Medications.Select(r => 
                        new AutoCompleteItem(r.Id, r.DisplayText, $"دواء: {r.SecondaryText}") { Tag = "Medication" }));

                    // Add ponds
                    items.AddRange(results.Ponds.Select(r => 
                        new AutoCompleteItem(r.Id, r.DisplayText, $"حوض: {r.SecondaryText}") { Tag = "Pond" }));

                    // Add feed types
                    items.AddRange(results.FeedTypes.Select(r => 
                        new AutoCompleteItem(r.Id, r.DisplayText, $"علف: {r.SecondaryText}") { Tag = "FeedType" }));

                    if (autoComplete.InvokeRequired)
                    {
                        autoComplete.Invoke(new Action(() => autoComplete.SetDataSource(items)));
                    }
                    else
                    {
                        autoComplete.SetDataSource(items);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في البحث العام: {ex.Message}");
                }
            };
        }
    }
}
