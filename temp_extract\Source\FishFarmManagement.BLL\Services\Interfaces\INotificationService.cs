using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// خدمة إدارة التنبيهات والإشعارات
    /// Notification and alert management service interface
    /// </summary>
    public interface INotificationService
    {
        /// <summary>
        /// الحصول على جميع التنبيهات النشطة
        /// Get all active notifications
        /// </summary>
        Task<List<Notification>> GetActiveNotificationsAsync();

        /// <summary>
        /// الحصول على التنبيهات حسب النوع
        /// Get notifications by type
        /// </summary>
        Task<List<Notification>> GetNotificationsByTypeAsync(NotificationType type);

        /// <summary>
        /// الحصول على التنبيهات حسب الأولوية
        /// Get notifications by priority
        /// </summary>
        Task<List<Notification>> GetNotificationsByPriorityAsync(NotificationPriority priority);

        /// <summary>
        /// إنشاء تنبيه جديد
        /// Create new notification
        /// </summary>
        Task<bool> CreateNotificationAsync(Notification notification);

        /// <summary>
        /// تحديث حالة التنبيه
        /// Update notification status
        /// </summary>
        Task<bool> UpdateNotificationStatusAsync(int notificationId, NotificationStatus status);

        /// <summary>
        /// وضع علامة مقروء على التنبيه
        /// Mark notification as read
        /// </summary>
        Task<bool> MarkAsReadAsync(int notificationId);

        /// <summary>
        /// وضع علامة مقروء على جميع التنبيهات
        /// Mark all notifications as read
        /// </summary>
        Task<bool> MarkAllAsReadAsync();

        /// <summary>
        /// حذف التنبيه
        /// Delete notification
        /// </summary>
        Task<bool> DeleteNotificationAsync(int notificationId);

        /// <summary>
        /// حذف التنبيهات القديمة
        /// Delete old notifications
        /// </summary>
        Task<bool> DeleteOldNotificationsAsync(DateTime olderThan);

        /// <summary>
        /// فحص التنبيهات التلقائية
        /// Check automatic notifications
        /// </summary>
        Task CheckAutomaticNotificationsAsync();

        /// <summary>
        /// فحص تنبيهات جودة المياه
        /// Check water quality alerts
        /// </summary>
        Task CheckWaterQualityAlertsAsync();

        /// <summary>
        /// فحص تنبيهات التغذية
        /// Check feeding alerts
        /// </summary>
        Task CheckFeedingAlertsAsync();

        /// <summary>
        /// فحص تنبيهات الصحة والوفيات
        /// Check health and mortality alerts
        /// </summary>
        Task CheckHealthAlertsAsync();

        /// <summary>
        /// فحص تنبيهات المخزون
        /// Check inventory alerts
        /// </summary>
        Task CheckInventoryAlertsAsync();

        /// <summary>
        /// فحص تنبيهات الدورات الإنتاجية
        /// Check production cycle alerts
        /// </summary>
        Task CheckProductionCycleAlertsAsync();

        /// <summary>
        /// الحصول على عدد التنبيهات غير المقروءة
        /// Get unread notifications count
        /// </summary>
        Task<int> GetUnreadCountAsync();

        /// <summary>
        /// إرسال تنبيه فوري
        /// Send immediate notification
        /// </summary>
        Task<bool> SendImmediateNotificationAsync(string title, string message, NotificationType type, NotificationPriority priority);

        /// <summary>
        /// جدولة تنبيه مؤجل
        /// Schedule delayed notification
        /// </summary>
        Task<bool> ScheduleNotificationAsync(string title, string message, NotificationType type, NotificationPriority priority, DateTime scheduledTime);
    }


}
