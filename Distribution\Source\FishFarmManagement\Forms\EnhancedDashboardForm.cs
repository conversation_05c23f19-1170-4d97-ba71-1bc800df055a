﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.Models;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.BLL.Services;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù„ÙˆØ­Ø© Ø§Ù„ØªØ­ÙƒÙ… Ø§Ù„Ù…Ø­Ø³Ù†Ø©
    /// Enhanced dashboard form
    /// </summary>
    public partial class EnhancedDashboardForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly EnhancedNotificationService _notificationService;
        private readonly ProfitabilityCalculatorService _profitabilityService;
        private readonly EnhancedReportService _reportService;
        private readonly ILogger<EnhancedDashboardForm> _logger;

        // UI Controls
        private Panel summaryPanel;
        private Panel alertsPanel;
        private Panel chartsPanel;
        private Panel quickActionsPanel;

        // Summary Cards
        private Label totalPondsLabel;
        private Label activeCyclesLabel;
        private Label monthlyRevenueLabel;
        private Label monthlyProfitLabel;
        private Label lowStockItemsLabel;
        private Label criticalAlertsLabel;

        // Alerts
        private ListBox alertsListBox;
        private Button refreshAlertsButton;
        private Label alertsCountLabel;

        // Quick Actions
        private Button addTransactionButton;
        private Button recordFeedingButton;
        private Button checkWaterQualityButton;
        private Button viewReportsButton;
        private Button backupDataButton;

        // Timer for auto-refresh
        private System.Windows.Forms.Timer refreshTimer;

        public EnhancedDashboardForm(
            IUnitOfWork unitOfWork,
            EnhancedNotificationService notificationService,
            ProfitabilityCalculatorService profitabilityService,
            EnhancedReportService reportService,
            ILogger<EnhancedDashboardForm> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _profitabilityService = profitabilityService ?? throw new ArgumentNullException(nameof(profitabilityService));
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            InitializeTimer();
            LoadDashboardDataAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "Ù„ÙˆØ­Ø© Ø§Ù„ØªØ­ÙƒÙ… Ø§Ù„Ø±Ø¦ÙŠØ³ÙŠØ©";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            CreateSummaryPanel();
            CreateAlertsPanel();
            CreateChartsPanel();
            CreateQuickActionsPanel();

            ArrangeControls();
        }

        private void CreateSummaryPanel()
        {
            summaryPanel = new Panel
            {
                Size = new Size(1360, 150),
                Location = new Point(20, 20),
                BackColor = Color.FromArgb(245, 245, 245),
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "Ù…Ù„Ø®Øµ Ø§Ù„Ù†Ø¸Ø§Ù…",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Size = new Size(200, 30),
                Location = new Point(20, 10),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            // Summary cards
            var pondsCard = CreateSummaryCard("Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø£Ø­ÙˆØ§Ø¶", "0", Color.FromArgb(52, 152, 219), new Point(20, 50));
            totalPondsLabel = pondsCard.Controls.OfType<Label>().Last();

            var cyclesCard = CreateSummaryCard("Ø§Ù„Ø¯ÙˆØ±Ø§Øª Ø§Ù„Ù†Ø´Ø·Ø©", "0", Color.FromArgb(46, 204, 113), new Point(240, 50));
            activeCyclesLabel = cyclesCard.Controls.OfType<Label>().Last();

            var revenueCard = CreateSummaryCard("Ø¥ÙŠØ±Ø§Ø¯Ø§Øª Ø§Ù„Ø´Ù‡Ø±", "0 Ø±ÙŠØ§Ù„", Color.FromArgb(155, 89, 182), new Point(460, 50));
            monthlyRevenueLabel = revenueCard.Controls.OfType<Label>().Last();

            var profitCard = CreateSummaryCard("Ø±Ø¨Ø­ Ø§Ù„Ø´Ù‡Ø±", "0 Ø±ÙŠØ§Ù„", Color.FromArgb(230, 126, 34), new Point(680, 50));
            monthlyProfitLabel = profitCard.Controls.OfType<Label>().Last();

            var stockCard = CreateSummaryCard("Ù…Ø®Ø²ÙˆÙ† Ù…Ù†Ø®ÙØ¶", "0", Color.FromArgb(231, 76, 60), new Point(900, 50));
            lowStockItemsLabel = stockCard.Controls.OfType<Label>().Last();

            var alertsCard = CreateSummaryCard("ØªÙ†Ø¨ÙŠÙ‡Ø§Øª Ø­Ø±Ø¬Ø©", "0", Color.FromArgb(192, 57, 43), new Point(1120, 50));
            criticalAlertsLabel = alertsCard.Controls.OfType<Label>().Last();

            summaryPanel.Controls.AddRange(new Control[] {
                titleLabel, pondsCard, cyclesCard, revenueCard, profitCard, stockCard, alertsCard
            });
        }

        private Panel CreateSummaryCard(string title, string value, Color color, Point location)
        {
            var card = new Panel
            {
                Size = new Size(200, 80),
                Location = location,
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var colorBar = new Panel
            {
                Size = new Size(4, 80),
                Location = new Point(0, 0),
                BackColor = color
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Size = new Size(180, 20),
                Location = new Point(10, 10),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Size = new Size(180, 30),
                Location = new Point(10, 35),
                ForeColor = color
            };

            card.Controls.AddRange(new Control[] { colorBar, titleLabel, valueLabel });
            return card;
        }

        private void CreateAlertsPanel()
        {
            alertsPanel = new Panel
            {
                Size = new Size(450, 400),
                Location = new Point(20, 190),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª ÙˆØ§Ù„Ø¥Ø´Ø¹Ø§Ø±Ø§Øª",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Size = new Size(200, 25),
                Location = new Point(15, 15),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            alertsCountLabel = new Label
            {
                Text = "0 ØªÙ†Ø¨ÙŠÙ‡",
                Font = new Font("Segoe UI", 10F),
                Size = new Size(100, 25),
                Location = new Point(330, 15),
                ForeColor = Color.FromArgb(128, 128, 128),
                TextAlign = ContentAlignment.MiddleLeft
            };

            refreshAlertsButton = new Button
            {
                Text = "ØªØ­Ø¯ÙŠØ«",
                Size = new Size(70, 25),
                Location = new Point(360, 45),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };
            refreshAlertsButton.Click += RefreshAlerts_Click;

            alertsListBox = new ListBox
            {
                Size = new Size(420, 320),
                Location = new Point(15, 75),
                Font = new Font("Segoe UI", 9F),
                DrawMode = DrawMode.OwnerDrawFixed,
                ItemHeight = 60
            };
            alertsListBox.DrawItem += AlertsListBox_DrawItem;
            alertsListBox.DoubleClick += AlertsListBox_DoubleClick;

            alertsPanel.Controls.AddRange(new Control[] {
                titleLabel, alertsCountLabel, refreshAlertsButton, alertsListBox
            });
        }

        private void CreateChartsPanel()
        {
            chartsPanel = new Panel
            {
                Size = new Size(450, 400),
                Location = new Point(490, 190),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "Ø§Ù„Ø±Ø³ÙˆÙ… Ø§Ù„Ø¨ÙŠØ§Ù†ÙŠØ©",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Size = new Size(200, 25),
                Location = new Point(15, 15),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            // Placeholder for charts
            var chartPlaceholder = new Label
            {
                Text = "Ø§Ù„Ø±Ø³ÙˆÙ… Ø§Ù„Ø¨ÙŠØ§Ù†ÙŠØ© Ù„Ù„Ø¥ÙŠØ±Ø§Ø¯Ø§Øª ÙˆØ§Ù„Ù…ØµØ±ÙˆÙØ§Øª\n(Ø³ÙŠØªÙ… ØªØ·ÙˆÙŠØ±Ù‡Ø§ Ù„Ø§Ø­Ù‚Ø§Ù‹)",
                Font = new Font("Segoe UI", 11F),
                Size = new Size(300, 60),
                Location = new Point(75, 150),
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Color.Gray
            };

            var revenueChartButton = new Button
            {
                Text = "Ø±Ø³Ù… Ø§Ù„Ø¥ÙŠØ±Ø§Ø¯Ø§Øª",
                Size = new Size(120, 35),
                Location = new Point(50, 250),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            revenueChartButton.Click += ShowRevenueChart_Click;

            var expenseChartButton = new Button
            {
                Text = "Ø±Ø³Ù… Ø§Ù„Ù…ØµØ±ÙˆÙØ§Øª",
                Size = new Size(120, 35),
                Location = new Point(180, 250),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            expenseChartButton.Click += ShowExpenseChart_Click;

            var profitChartButton = new Button
            {
                Text = "Ø±Ø³Ù… Ø§Ù„Ø£Ø±Ø¨Ø§Ø­",
                Size = new Size(120, 35),
                Location = new Point(310, 250),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            profitChartButton.Click += ShowProfitChart_Click;

            chartsPanel.Controls.AddRange(new Control[] {
                titleLabel, chartPlaceholder, revenueChartButton, expenseChartButton, profitChartButton
            });
        }

        private void CreateQuickActionsPanel()
        {
            quickActionsPanel = new Panel
            {
                Size = new Size(420, 400),
                Location = new Point(960, 190),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "Ø§Ù„Ø¥Ø¬Ø±Ø§Ø¡Ø§Øª Ø§Ù„Ø³Ø±ÙŠØ¹Ø©",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Size = new Size(200, 25),
                Location = new Point(15, 15),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            addTransactionButton = new Button
            {
                Text = "Ø¥Ø¶Ø§ÙØ© Ù…Ø¹Ø§Ù…Ù„Ø© Ù…Ø§Ù„ÙŠØ©",
                Size = new Size(180, 40),
                Location = new Point(20, 60),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            addTransactionButton.Click += AddTransaction_Click;

            recordFeedingButton = new Button
            {
                Text = "ØªØ³Ø¬ÙŠÙ„ ØªØºØ°ÙŠØ©",
                Size = new Size(180, 40),
                Location = new Point(210, 60),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            recordFeedingButton.Click += RecordFeeding_Click;

            checkWaterQualityButton = new Button
            {
                Text = "ÙØ­Øµ Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡",
                Size = new Size(180, 40),
                Location = new Point(20, 120),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            checkWaterQualityButton.Click += CheckWaterQuality_Click;

            viewReportsButton = new Button
            {
                Text = "Ø¹Ø±Ø¶ Ø§Ù„ØªÙ‚Ø§Ø±ÙŠØ±",
                Size = new Size(180, 40),
                Location = new Point(210, 120),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            viewReportsButton.Click += ViewReports_Click;

            backupDataButton = new Button
            {
                Text = "Ù†Ø³Ø® Ø§Ø­ØªÙŠØ§Ø·ÙŠ Ù„Ù„Ø¨ÙŠØ§Ù†Ø§Øª",
                Size = new Size(180, 40),
                Location = new Point(20, 180),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            backupDataButton.Click += BackupData_Click;

            // Recent activities section
            var activitiesLabel = new Label
            {
                Text = "Ø§Ù„Ø£Ù†Ø´Ø·Ø© Ø§Ù„Ø£Ø®ÙŠØ±Ø©",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Size = new Size(150, 25),
                Location = new Point(15, 240),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            var activitiesListBox = new ListBox
            {
                Size = new Size(380, 130),
                Location = new Point(15, 270),
                Font = new Font("Segoe UI", 9F)
            };

            quickActionsPanel.Controls.AddRange(new Control[] {
                titleLabel, addTransactionButton, recordFeedingButton,
                checkWaterQualityButton, viewReportsButton, backupDataButton,
                activitiesLabel, activitiesListBox
            });
        }

        private void ArrangeControls()
        {
            this.Controls.AddRange(new Control[] {
                summaryPanel, alertsPanel, chartsPanel, quickActionsPanel
            });
        }

        private void InitializeTimer()
        {
            refreshTimer = new System.Windows.Forms.Timer
            {
                Interval = 300000 // 5 minutes
            };
            refreshTimer.Tick += RefreshTimer_Tick;
            refreshTimer.Start();
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;

                // Load summary data
                await LoadSummaryDataAsync();

                // Load alerts
                await LoadAlertsAsync();

                _logger.LogInformation("ØªÙ… ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ù„ÙˆØ­Ø© Ø§Ù„ØªØ­ÙƒÙ… Ø¨Ù†Ø¬Ø§Ø­");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ù„ÙˆØ­Ø© Ø§Ù„ØªØ­ÙƒÙ…");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private async Task LoadSummaryDataAsync()
        {
            try
            {
                // Total ponds
                var ponds = await _unitOfWork.Ponds.GetAllAsync();
                totalPondsLabel.Text = ponds.Count().ToString();

                // Active cycles
                var activeCycles = await _unitOfWork.ProductionCycles.FindAsync(c => c.Status == "Ù†Ø´Ø·");
                activeCyclesLabel.Text = activeCycles.Count().ToString();

                // Monthly revenue and profit
                var currentMonth = DateTime.Now;
                var monthStart = new DateTime(currentMonth.Year, currentMonth.Month, 1);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                var financialReport = await _reportService.GenerateFinancialReportAsync(monthStart, monthEnd);
                monthlyRevenueLabel.Text = $"{financialReport.TotalRevenue:C0}";
                monthlyProfitLabel.Text = $"{financialReport.NetProfit:C0}";

                // Low stock items
                var inventoryItems = await _unitOfWork.Inventories.GetAllAsync();
                var lowStockCount = inventoryItems.Count(i => i.Quantity <= i.MinimumStock);
                lowStockItemsLabel.Text = lowStockCount.ToString();

                // Update colors based on values
                UpdateSummaryCardColors(financialReport.NetProfit, lowStockCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ù…Ù„Ø®ØµØ©");
            }
        }

        private void UpdateSummaryCardColors(decimal profit, int lowStockCount)
        {
            // Update profit color
            monthlyProfitLabel.ForeColor = profit >= 0 ? Color.FromArgb(46, 204, 113) : Color.FromArgb(231, 76, 60);

            // Update low stock color
            lowStockItemsLabel.ForeColor = lowStockCount == 0 ? Color.FromArgb(46, 204, 113) : 
                                          lowStockCount <= 5 ? Color.FromArgb(230, 126, 34) : 
                                          Color.FromArgb(231, 76, 60);
        }

        private async Task LoadAlertsAsync()
        {
            try
            {
                var alerts = await _notificationService.CheckAllAlertsAsync();
                
                alertsListBox.Items.Clear();
                foreach (var alert in alerts.Take(10)) // Show only top 10 alerts
                {
                    alertsListBox.Items.Add(alert);
                }

                alertsCountLabel.Text = $"{alerts.Count} ØªÙ†Ø¨ÙŠÙ‡";
                
                var criticalCount = alerts.Count(a => a.Priority == AlertPriority.Critical);
                criticalAlertsLabel.Text = criticalCount.ToString();
                criticalAlertsLabel.ForeColor = criticalCount > 0 ? Color.FromArgb(231, 76, 60) : Color.FromArgb(46, 204, 113);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª");
            }
        }

        // Event Handlers
        private async void RefreshTimer_Tick(object? sender, EventArgs e)
        {
            await LoadDashboardDataAsync();
        }

        private async void private async void private async void RefreshAlerts_Click(object? sender, EventArgs e)
        {
            await LoadAlertsAsync();
        }

        private void AlertsListBox_DrawItem(object sender, DrawItemEventArgs e)
        {
            if (e.Index < 0) return;

            var alert = alertsListBox.Items[e.Index] as SystemAlert;
            if (alert == null) return;

            e.DrawBackground();

            // Determine colors based on priority
            var priorityColor = alert.Priority switch
            {
                AlertPriority.Critical => Color.FromArgb(231, 76, 60),
                AlertPriority.High => Color.FromArgb(230, 126, 34),
                AlertPriority.Medium => Color.FromArgb(52, 152, 219),
                _ => Color.FromArgb(149, 165, 166)
            };

            // Draw priority indicator
            using (var brush = new SolidBrush(priorityColor))
            {
                e.Graphics.FillRectangle(brush, e.Bounds.X, e.Bounds.Y, 4, e.Bounds.Height);
            }

            // Draw alert text
            var titleFont = new Font("Segoe UI", 9F, FontStyle.Bold);
            var messageFont = new Font("Segoe UI", 8F);

            var titleRect = new Rectangle(e.Bounds.X + 10, e.Bounds.Y + 5, e.Bounds.Width - 15, 20);
            var messageRect = new Rectangle(e.Bounds.X + 10, e.Bounds.Y + 25, e.Bounds.Width - 15, 30);

            e.Graphics.DrawString(alert.Title, titleFont, Brushes.Black, titleRect);
            e.Graphics.DrawString(alert.Message, messageFont, Brushes.Gray, messageRect);

            e.DrawFocusRectangle();
        }

        private void AlertsListBox_DoubleClick(object? sender, EventArgs e)
        {
            if (alertsListBox.SelectedItem is SystemAlert alert)
            {
                var detailMessage = $"Ù†ÙˆØ¹ Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡: {alert.Type}\n" +
                                   $"Ø§Ù„Ø£ÙˆÙ„ÙˆÙŠØ©: {alert.Priority}\n" +
                                   $"Ø§Ù„ØªØ§Ø±ÙŠØ®: {alert.CreatedDate:yyyy/MM/dd HH:mm}\n\n" +
                                   $"Ø§Ù„ØªÙØ§ØµÙŠÙ„:\n{alert.Message}";

                MessageBox.Show(detailMessage, alert.Title, MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ShowRevenueChart_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("Ø³ÙŠØªÙ… Ø¹Ø±Ø¶ Ø±Ø³Ù… Ø¨ÙŠØ§Ù†ÙŠ Ù„Ù„Ø¥ÙŠØ±Ø§Ø¯Ø§Øª", "Ø§Ù„Ø±Ø³ÙˆÙ… Ø§Ù„Ø¨ÙŠØ§Ù†ÙŠØ©",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowExpenseChart_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("Ø³ÙŠØªÙ… Ø¹Ø±Ø¶ Ø±Ø³Ù… Ø¨ÙŠØ§Ù†ÙŠ Ù„Ù„Ù…ØµØ±ÙˆÙØ§Øª", "Ø§Ù„Ø±Ø³ÙˆÙ… Ø§Ù„Ø¨ÙŠØ§Ù†ÙŠØ©",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowProfitChart_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("Ø³ÙŠØªÙ… Ø¹Ø±Ø¶ Ø±Ø³Ù… Ø¨ÙŠØ§Ù†ÙŠ Ù„Ù„Ø£Ø±Ø¨Ø§Ø­", "Ø§Ù„Ø±Ø³ÙˆÙ… Ø§Ù„Ø¨ÙŠØ§Ù†ÙŠØ©",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void AddTransaction_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("Ø³ÙŠØªÙ… ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¶Ø§ÙØ© Ù…Ø¹Ø§Ù…Ù„Ø© Ù…Ø§Ù„ÙŠØ©", "Ø¥Ø¶Ø§ÙØ© Ù…Ø¹Ø§Ù…Ù„Ø©",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¶Ø§ÙØ© Ù…Ø¹Ø§Ù…Ù„Ø©");
                MessageBox.Show($"Ø®Ø·Ø£: {ex.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RecordFeeding_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("Ø³ÙŠØªÙ… ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ ØªØ³Ø¬ÙŠÙ„ Ø§Ù„ØªØºØ°ÙŠØ©", "ØªØ³Ø¬ÙŠÙ„ Ø§Ù„ØªØºØ°ÙŠØ©",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ ØªØ³Ø¬ÙŠÙ„ Ø§Ù„ØªØºØ°ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£: {ex.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CheckWaterQuality_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("Ø³ÙŠØªÙ… ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ ÙØ­Øµ Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡", "ÙØ­Øµ Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ ÙØ­Øµ Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡");
                MessageBox.Show($"Ø®Ø·Ø£: {ex.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewReports_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("Ø³ÙŠØªÙ… ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø§Ù„ØªÙ‚Ø§Ø±ÙŠØ±", "Ø§Ù„ØªÙ‚Ø§Ø±ÙŠØ±",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø§Ù„ØªÙ‚Ø§Ø±ÙŠØ±");
                MessageBox.Show($"Ø®Ø·Ø£: {ex.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BackupData_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("Ø³ÙŠØªÙ… ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠ", "Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠ",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠ");
                MessageBox.Show($"Ø®Ø·Ø£: {ex.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                refreshTimer?.Stop();
                refreshTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}



