﻿using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ ØªØ³Ø¬ÙŠÙ„ Ø§Ù„Ø£ÙˆØ²Ø§Ù†
    /// Weight Record Form
    /// </summary>
    public partial class WeightRecordForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<WeightRecordForm> _logger;

        // UI Controls
        private ComboBox pondComboBox;
        private DateTimePicker weighingDatePicker;
        private NumericUpDown sampleSizeNumericUpDown;
        private NumericUpDown averageWeightNumericUpDown;
        private NumericUpDown minWeightNumericUpDown;
        private NumericUpDown maxWeightNumericUpDown;
        private Label totalBiomassLabel;
        private Label growthRateLabel;
        private Label fcRatioLabel;
        private ComboBox weighingMethodComboBox;
        private TextBox notesTextBox;
        private Button saveButton;
        private Button cancelButton;
        private DataGridView weightHistoryDataGridView;
        // private Panel weightGrowthChart; // ØºÙŠØ± Ù…Ø³ØªØ®Ø¯Ù…

        public WeightRecordForm(IUnitOfWork unitOfWork, ILogger<WeightRecordForm> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            InitializeComponent();
            LoadInitialData();
        }

        private void InitializeComponent()
        {
            this.Text = "ØªØ³Ø¬ÙŠÙ„ Ø§Ù„Ø£ÙˆØ²Ø§Ù†";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 1,
                ColumnCount = 2,
                Padding = new Padding(20)
            };

            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 500));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // Left Panel (Input and History)
            var leftPanel = CreateLeftPanel();
            mainPanel.Controls.Add(leftPanel, 0, 0);

            // Right Panel (Chart and Analysis)
            var rightPanel = CreateRightPanel();
            mainPanel.Controls.Add(rightPanel, 1, 0);

            this.Controls.Add(mainPanel);
        }

        private TableLayoutPanel CreateLeftPanel()
        {
            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 2,
                ColumnCount = 1
            };

            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 400));
            panel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            // Input Panel
            var inputPanel = CreateInputPanel();
            panel.Controls.Add(inputPanel, 0, 0);

            // History Panel
            var historyPanel = CreateHistoryPanel();
            panel.Controls.Add(historyPanel, 0, 1);

            return panel;
        }

        private GroupBox CreateInputPanel()
        {
            var panel = new GroupBox
            {
                Text = "ØªØ³Ø¬ÙŠÙ„ ÙˆØ²Ù† Ø¬Ø¯ÙŠØ¯",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Padding = new Padding(15)
            };

            int y = 30;
            int spacing = 35;

            // Pond selection
            var pondLabel = CreateLabel("Ø§Ù„Ø­ÙˆØ¶:", new Point(350, y));
            pondComboBox = new ComboBox
            {
                Location = new Point(150, y),
                Size = new Size(180, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            pondComboBox.SelectedIndexChanged += PondComboBox_SelectedIndexChanged;
            y += spacing;

            // Weighing date
            var dateLabel = CreateLabel("ØªØ§Ø±ÙŠØ® Ø§Ù„ÙˆØ²Ù†:", new Point(350, y));
            weighingDatePicker = new DateTimePicker
            {
                Location = new Point(150, y),
                Size = new Size(180, 23),
                Value = DateTime.Now
            };
            y += spacing;

            // Sample size
            var sampleLabel = CreateLabel("Ø­Ø¬Ù… Ø§Ù„Ø¹ÙŠÙ†Ø©:", new Point(350, y));
            sampleSizeNumericUpDown = new NumericUpDown
            {
                Location = new Point(150, y),
                Size = new Size(180, 23),
                Maximum = 1000,
                Minimum = 1,
                Value = 10
            };
            y += spacing;

            // Weighing method
            var methodLabel = CreateLabel("Ø·Ø±ÙŠÙ‚Ø© Ø§Ù„ÙˆØ²Ù†:", new Point(350, y));
            weighingMethodComboBox = new ComboBox
            {
                Location = new Point(150, y),
                Size = new Size(180, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            y += spacing;

            // Average weight
            var avgWeightLabel = CreateLabel("Ù…ØªÙˆØ³Ø· Ø§Ù„ÙˆØ²Ù† (Ø¬Ø±Ø§Ù…):", new Point(350, y));
            averageWeightNumericUpDown = new NumericUpDown
            {
                Location = new Point(150, y),
                Size = new Size(180, 23),
                DecimalPlaces = 2,
                Maximum = 10000,
                Minimum = 0.1m,
                Value = 100
            };
            averageWeightNumericUpDown.ValueChanged += CalculateMetrics;
            y += spacing;

            // Min weight
            var minWeightLabel = CreateLabel("Ø£Ù‚Ù„ ÙˆØ²Ù† (Ø¬Ø±Ø§Ù…):", new Point(350, y));
            minWeightNumericUpDown = new NumericUpDown
            {
                Location = new Point(150, y),
                Size = new Size(180, 23),
                DecimalPlaces = 2,
                Maximum = 10000,
                Minimum = 0.1m,
                Value = 80
            };
            y += spacing;

            // Max weight
            var maxWeightLabel = CreateLabel("Ø£Ø¹Ù„Ù‰ ÙˆØ²Ù† (Ø¬Ø±Ø§Ù…):", new Point(350, y));
            maxWeightNumericUpDown = new NumericUpDown
            {
                Location = new Point(150, y),
                Size = new Size(180, 23),
                DecimalPlaces = 2,
                Maximum = 10000,
                Minimum = 0.1m,
                Value = 120
            };
            y += spacing;

            // Total biomass
            var biomassLabel = CreateLabel("Ø§Ù„ÙƒØªÙ„Ø© Ø§Ù„Ø­ÙŠÙˆÙŠØ© (ÙƒØ¬Ù…):", new Point(350, y));
            totalBiomassLabel = new Label
            {
                Location = new Point(150, y),
                Size = new Size(180, 23),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(240, 240, 240),
                TextAlign = ContentAlignment.MiddleLeft,
                Text = "0.00"
            };
            y += spacing;

            // Growth rate
            var growthLabel = CreateLabel("Ù…Ø¹Ø¯Ù„ Ø§Ù„Ù†Ù…Ùˆ (%):", new Point(350, y));
            growthRateLabel = new Label
            {
                Location = new Point(150, y),
                Size = new Size(180, 23),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(240, 255, 240),
                TextAlign = ContentAlignment.MiddleLeft,
                Text = "0.00%",
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            y += spacing;

            // Notes
            var notesLabel = CreateLabel("Ù…Ù„Ø§Ø­Ø¸Ø§Øª:", new Point(350, y));
            notesTextBox = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(180, 40),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            y += 50;

            // Buttons
            saveButton = new Button
            {
                Text = "Ø­ÙØ¸",
                Location = new Point(250, y),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "Ø¥Ù„ØºØ§Ø¡",
                Location = new Point(150, y),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            cancelButton.Click += (s, e) => this.Close();

            panel.Controls.AddRange(new Control[]
            {
                pondLabel, pondComboBox,
                dateLabel, weighingDatePicker,
                sampleLabel, sampleSizeNumericUpDown,
                methodLabel, weighingMethodComboBox,
                avgWeightLabel, averageWeightNumericUpDown,
                minWeightLabel, minWeightNumericUpDown,
                maxWeightLabel, maxWeightNumericUpDown,
                biomassLabel, totalBiomassLabel,
                growthLabel, growthRateLabel,
                notesLabel, notesTextBox,
                saveButton, cancelButton
            });

            return panel;
        }

        private GroupBox CreateHistoryPanel()
        {
            var panel = new GroupBox
            {
                Text = "Ø³Ø¬Ù„ Ø§Ù„Ø£ÙˆØ²Ø§Ù†",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Padding = new Padding(15)
            };

            weightHistoryDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 8F)
            };

            SetupHistoryColumns();
            panel.Controls.Add(weightHistoryDataGridView);

            return panel;
        }

        private TableLayoutPanel CreateRightPanel()
        {
            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 2,
                ColumnCount = 1
            };

            panel.RowStyles.Add(new RowStyle(SizeType.Percent, 60));
            panel.RowStyles.Add(new RowStyle(SizeType.Percent, 40));

            // Chart Panel
            var chartPanel = CreateChartPanel();
            panel.Controls.Add(chartPanel, 0, 0);

            // Analysis Panel
            var analysisPanel = CreateAnalysisPanel();
            panel.Controls.Add(analysisPanel, 0, 1);

            return panel;
        }

        private GroupBox CreateChartPanel()
        {
            var panel = new GroupBox
            {
                Text = "Ù…Ù†Ø­Ù†Ù‰ Ø§Ù„Ù†Ù…Ùˆ",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Padding = new Padding(15)
            };

            // Placeholder for chart - you would use a charting library like OxyPlot or System.Windows.Forms.DataVisualization
            var chartPlaceholder = new Label
            {
                Text = "Ù…Ù†Ø­Ù†Ù‰ Ø§Ù„Ù†Ù…Ùˆ\n(ÙŠØªØ·Ù„Ø¨ Ù…ÙƒØªØ¨Ø© Ø±Ø³Ù… Ø¨ÙŠØ§Ù†ÙŠ)",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle,
                Font = new Font("Segoe UI", 12F)
            };

            panel.Controls.Add(chartPlaceholder);
            return panel;
        }

        private GroupBox CreateAnalysisPanel()
        {
            var panel = new GroupBox
            {
                Text = "ØªØ­Ù„ÙŠÙ„ Ø§Ù„Ù†Ù…Ùˆ",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Padding = new Padding(15)
            };

            int y = 30;
            int spacing = 25;

            // FCR
            var fcrTitleLabel = CreateLabel("Ù…Ø¹Ø§Ù…Ù„ ØªØ­ÙˆÙŠÙ„ Ø§Ù„Ø¹Ù„Ù:", new Point(400, y));
            fcRatioLabel = new Label
            {
                Location = new Point(200, y),
                Size = new Size(180, 20),
                Text = "Ø³ÙŠØªÙ… Ø­Ø³Ø§Ø¨Ù‡...",
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            y += spacing;

            // Growth performance
            var performanceLabel = CreateLabel("Ø£Ø¯Ø§Ø¡ Ø§Ù„Ù†Ù…Ùˆ:", new Point(400, y));
            var performanceValue = new Label
            {
                Location = new Point(200, y),
                Size = new Size(180, 20),
                Text = "Ø¬ÙŠØ¯",
                ForeColor = Color.Green,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            y += spacing;

            // Expected harvest date
            var harvestLabel = CreateLabel("ØªØ§Ø±ÙŠØ® Ø§Ù„Ø­ØµØ§Ø¯ Ø§Ù„Ù…ØªÙˆÙ‚Ø¹:", new Point(400, y));
            var harvestValue = new Label
            {
                Location = new Point(200, y),
                Size = new Size(180, 20),
                Text = "Ø³ÙŠØªÙ… Ø­Ø³Ø§Ø¨Ù‡...",
                Font = new Font("Segoe UI", 9F)
            };
            y += spacing;

            // Recommendations
            var recommendationsLabel = CreateLabel("Ø§Ù„ØªÙˆØµÙŠØ§Øª:", new Point(400, y));
            var recommendationsText = new TextBox
            {
                Location = new Point(50, y + 25),
                Size = new Size(500, 80),
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                Text = "â€¢ Ø§Ø³ØªÙ…Ø± ÙÙŠ Ù†ÙØ³ Ù†Ø¸Ø§Ù… Ø§Ù„ØªØºØ°ÙŠØ©\nâ€¢ Ø±Ø§Ù‚Ø¨ Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡\nâ€¢ Ù‚Ù… Ø¨ÙˆØ²Ù† Ø¯ÙˆØ±ÙŠ ÙƒÙ„ Ø£Ø³Ø¨ÙˆØ¹ÙŠÙ†",
                BackColor = Color.FromArgb(240, 248, 255)
            };

            panel.Controls.AddRange(new Control[]
            {
                fcrTitleLabel, fcRatioLabel,
                performanceLabel, performanceValue,
                harvestLabel, harvestValue,
                recommendationsLabel, recommendationsText
            });

            return panel;
        }

        private void SetupHistoryColumns()
        {
            weightHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "WeighingDate",
                HeaderText = "Ø§Ù„ØªØ§Ø±ÙŠØ®",
                DataPropertyName = "WeighingDate",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "MM/dd" }
            });

            weightHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AverageWeight",
                HeaderText = "Ù…ØªÙˆØ³Ø· Ø§Ù„ÙˆØ²Ù†",
                DataPropertyName = "AverageWeight",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N1" }
            });

            weightHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SampleSize",
                HeaderText = "Ø§Ù„Ø¹ÙŠÙ†Ø©",
                DataPropertyName = "SampleSize",
                Width = 50
            });

            weightHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "GrowthRate",
                HeaderText = "Ø§Ù„Ù†Ù…Ùˆ %",
                DataPropertyName = "GrowthRate",
                Width = 60,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "P1" }
            });

            weightHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalBiomass",
                HeaderText = "Ø§Ù„ÙƒØªÙ„Ø© Ø§Ù„Ø­ÙŠÙˆÙŠØ©",
                DataPropertyName = "TotalBiomass",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N1" }
            });
        }

        private Label CreateLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
        }

        private async void LoadInitialData()
        {
            try
            {
                // Load ponds
                var ponds = await _unitOfWork.Ponds.GetAllAsync();
                var pondList = ponds.Select(p => new { Value = p.Id, Text = $"Ø­ÙˆØ¶ {p.PondNumber}" }).ToList();
                
                pondComboBox.DataSource = pondList;
                pondComboBox.DisplayMember = "Text";
                pondComboBox.ValueMember = "Value";

                // Load weighing methods
                var methods = new[]
                {
                    "Ø¹ÙŠÙ†Ø© Ø¹Ø´ÙˆØ§Ø¦ÙŠØ©",
                    "Ø´Ø¨ÙƒØ© ØµØºÙŠØ±Ø©",
                    "ÙˆØ²Ù† Ø¬Ù…Ø§Ø¹ÙŠ",
                    "Ø¹ÙŠÙ†Ø© Ù…Ø³ØªÙ‡Ø¯ÙØ©"
                };
                weighingMethodComboBox.Items.AddRange(methods);
                weighingMethodComboBox.SelectedIndex = 0;

                // Load weight history
                await LoadWeightHistory();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø£ÙˆÙ„ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadWeightHistory()
        {
            try
            {
                // Placeholder for weight records
                var historyData = new List<object>();
                weightHistoryDataGridView.DataSource = historyData;

                await Task.CompletedTask; // Ù„ØªØ¬Ù†Ø¨ ØªØ­Ø°ÙŠØ± async
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø³Ø¬Ù„ Ø§Ù„Ø£ÙˆØ²Ø§Ù†");
            }
        }

        private void PondComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            CalculateMetrics(sender, e);
        }

        private void CalculateMetrics(object sender, EventArgs e)
        {
            try
            {
                if (pondComboBox.SelectedValue == null)
                    return;

                var pondId = (int)pondComboBox.SelectedValue;
                var pond = _unitOfWork.Ponds.GetByIdAsync(pondId).Result;
                
                if (pond != null)
                {
                    // Calculate total biomass
                    var avgWeight = averageWeightNumericUpDown.Value / 1000; // Convert to kg
                    var totalBiomass = pond.FishCount * avgWeight;
                    totalBiomassLabel.Text = totalBiomass.ToString("N2");

                    // Calculate growth rate (placeholder calculation)
                    var growthRate = 2.5; // This should be calculated based on previous weights
                    growthRateLabel.Text = $"{growthRate:F1}%";
                    
                    // Color coding for growth rate
                    if (growthRate >= 2.0)
                        growthRateLabel.ForeColor = Color.Green;
                    else if (growthRate >= 1.0)
                        growthRateLabel.ForeColor = Color.Orange;
                    else
                        growthRateLabel.ForeColor = Color.Red;

                    // Update FCR (placeholder)
                    fcRatioLabel.Text = "1.8:1";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­Ø³Ø§Ø¨ Ø§Ù„Ù…Ù‚Ø§ÙŠÙŠØ³");
            }
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                // Create weight record
                var weightRecord = new
                {
                    PondId = (int)pondComboBox.SelectedValue,
                    WeighingDate = weighingDatePicker.Value.Date,
                    SampleSize = (int)sampleSizeNumericUpDown.Value,
                    WeighingMethod = weighingMethodComboBox.SelectedItem.ToString(),
                    AverageWeight = averageWeightNumericUpDown.Value,
                    MinWeight = minWeightNumericUpDown.Value,
                    MaxWeight = maxWeightNumericUpDown.Value,
                    TotalBiomass = decimal.Parse(totalBiomassLabel.Text),
                    Notes = notesTextBox.Text,
                    RecordedDate = DateTime.Now
                };

                // Update pond average weight
                var pondId = (int)pondComboBox.SelectedValue;
                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                if (pond != null)
                {
                    pond.AverageWeight = averageWeightNumericUpDown.Value;
                    await _unitOfWork.Ponds.UpdateAsync(pond);
                    await _unitOfWork.SaveChangesAsync();
                }

                MessageBox.Show("ØªÙ… Ø­ÙØ¸ Ø³Ø¬Ù„ Ø§Ù„ÙˆØ²Ù† Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ClearForm();
                await LoadWeightHistory();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­ÙØ¸ Ø³Ø¬Ù„ Ø§Ù„ÙˆØ²Ù†");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø­ÙØ¸: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (pondComboBox.SelectedValue == null)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ø§Ù„Ø­ÙˆØ¶", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (sampleSizeNumericUpDown.Value <= 0)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø¥Ø¯Ø®Ø§Ù„ Ø­Ø¬Ù… Ø¹ÙŠÙ†Ø© ØµØ­ÙŠØ­", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (averageWeightNumericUpDown.Value <= 0)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø¥Ø¯Ø®Ø§Ù„ ÙˆØ²Ù† ØµØ­ÙŠØ­", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (minWeightNumericUpDown.Value > averageWeightNumericUpDown.Value ||
                maxWeightNumericUpDown.Value < averageWeightNumericUpDown.Value)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ù„ØªØ£ÙƒØ¯ Ù…Ù† ØµØ­Ø© Ù‚ÙŠÙ… Ø§Ù„Ø£ÙˆØ²Ø§Ù†", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            weighingDatePicker.Value = DateTime.Now;
            sampleSizeNumericUpDown.Value = 10;
            weighingMethodComboBox.SelectedIndex = 0;
            averageWeightNumericUpDown.Value = 100;
            minWeightNumericUpDown.Value = 80;
            maxWeightNumericUpDown.Value = 120;
            notesTextBox.Clear();
            totalBiomassLabel.Text = "0.00";
            growthRateLabel.Text = "0.00%";
        }
    }
}



