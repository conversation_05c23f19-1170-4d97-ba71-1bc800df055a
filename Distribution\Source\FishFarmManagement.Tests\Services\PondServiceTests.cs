using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;
using FishFarmManagement.BLL.Services;
using FishFarmManagement.DAL;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Tests.Services
{
    /// <summary>
    /// اختبارات خدمة الأحواض
    /// Pond service tests
    /// </summary>
    public class PondServiceTests : IDisposable
    {
        private readonly FishFarmDbContext _context;
        private readonly Mock<ILogger<PondService>> _mockLogger;
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly PondService _pondService;

        public PondServiceTests()
        {
            // Setup in-memory database
            var options = new DbContextOptionsBuilder<FishFarmDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new FishFarmDbContext(options);
            _mockLogger = new Mock<ILogger<PondService>>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();

            _pondService = new PondService(_mockUnitOfWork.Object, _mockLogger.Object);
        }

        [Fact]
        public void Pond_ShouldHaveCorrectProperties()
        {
            // Arrange & Act
            var pond = new Pond
            {
                PondNumber = "P001",
                CycleId = 1,
                FishCount = 1000,
                AverageWeight = 0.5m,
                StockingDate = DateTime.Now,
                Status = "نشط",
                Notes = "حوض اختبار"
            };

            // Assert
            pond.PondNumber.Should().Be("P001");
            pond.CycleId.Should().Be(1);
            pond.FishCount.Should().Be(1000);
            pond.AverageWeight.Should().Be(0.5m);
            pond.Status.Should().Be("نشط");
            pond.Notes.Should().Be("حوض اختبار");
        }

        [Fact]
        public void Pond_GetTotalExpectedWeight_ShouldCalculateCorrectly()
        {
            // Arrange
            var pond = new Pond
            {
                FishCount = 1000,
                AverageWeight = 0.5m
            };

            // Act
            var totalWeight = pond.GetTotalExpectedWeight();

            // Assert
            totalWeight.Should().Be(500m); // 1000 * 0.5
        }

        [Fact]
        public void Pond_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange & Act
            var pond = new Pond();

            // Assert
            pond.PondNumber.Should().Be(string.Empty);
            pond.FishCount.Should().Be(0);
            pond.AverageWeight.Should().Be(0);
            pond.Status.Should().Be("نشط");
            pond.Notes.Should().Be(string.Empty);
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
