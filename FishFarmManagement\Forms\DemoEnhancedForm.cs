﻿using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Controls;
using FishFarmManagement.Helpers;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج توضيحي للمكونات المحسنة
    /// Demo form for enhanced components
    /// </summary>
    public partial class DemoEnhancedForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ISearchService _searchService;

        // Enhanced Controls
        private AutoCompleteTextBox accountAutoComplete;
        private AutoCompleteTextBox employeeAutoComplete;
        private EnhancedDateTimePicker startDatePicker;
        private EnhancedDateTimePicker endDatePicker;
        private TextBox emailTextBox = null! = null!;
        private TextBox phoneTextBox = null! = null!;
        private TextBox nationalIdTextBox = null! = null!;
        private NumericUpDown amountNumericUpDown = null! = null!;
        private NotificationPanel notificationPanel;
        private EnhancedTooltip tooltip;

        public DemoEnhancedForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _searchService = serviceProvider.GetRequiredService<ISearchService>();

            InitializeComponent();
            SetupControls();
            SetupValidationAndEffects();
        }

        private void InitializeComponent()
        {
            this.Text = "نموذج توضيحي للمكونات المحسنة";
            this.Size = new Size(800, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
        }

        private void SetupControls()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true
            };

            int y = 20;
            const int spacing = 50;
            const int labelWidth = 150;
            const int controlWidth = 300;

            // Title
            var titleLabel = new Label
            {
                Text = "نموذج توضيحي للمكونات المحسنة",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Size = new Size(500, 30),
                Location = new Point(150, y),
                TextAlign = ContentAlignment.MiddleCenter
            };
            y += 60;

            // Notification Panel
            notificationPanel = new NotificationPanel
            {
                Location = new Point(250, y),
                Size = new Size(300, 50)
            };
            y += 70;

            // Account AutoComplete
            var accountLabel = CreateLabel("البحث في الحسابات:", new Point(500, y), labelWidth);
            accountAutoComplete = new AutoCompleteTextBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                MinSearchLength = 2,
                ShowDetailsInDropdown = true
            };
            AutoCompleteHelper.SetupAccountsAutoComplete(accountAutoComplete, _searchService);
            accountAutoComplete.ItemSelected += (s, e) => 
                notificationPanel.ShowSuccess($"تم اختيار الحساب: {e.SelectedItem.DisplayText}");
            y += spacing;

            // Employee AutoComplete
            var employeeLabel = CreateLabel("البحث في الموظفين:", new Point(500, y), labelWidth);
            employeeAutoComplete = new AutoCompleteTextBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                MinSearchLength = 2,
                ShowDetailsInDropdown = true
            };
            AutoCompleteHelper.SetupEmployeesAutoComplete(employeeAutoComplete, _searchService);
            employeeAutoComplete.ItemSelected += (s, e) => 
                notificationPanel.ShowInfo($"تم اختيار الموظف: {e.SelectedItem.DisplayText}");
            y += spacing;

            // Enhanced Date Pickers
            var startDateLabel = CreateLabel("تاريخ البداية:", new Point(500, y), labelWidth);
            startDatePicker = new EnhancedDateTimePicker
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Value = DateTime.Now.AddDays(-30)
            };
            y += spacing;

            var endDateLabel = CreateLabel("تاريخ النهاية:", new Point(500, y), labelWidth);
            endDatePicker = new EnhancedDateTimePicker
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Value = DateTime.Now
            };
            y += spacing;

            // Email with validation
            var emailLabel = CreateLabel("البريد الإلكتروني:", new Point(500, y), labelWidth);
            emailTextBox = new TextBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F)
            };
            y += spacing;

            // Phone with validation
            var phoneLabel = CreateLabel("رقم الهاتف:", new Point(500, y), labelWidth);
            phoneTextBox = new TextBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F)
            };
            y += spacing;

            // National ID with validation
            var nationalIdLabel = CreateLabel("رقم الهوية:", new Point(500, y), labelWidth);
            nationalIdTextBox = new TextBox
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F)
            };
            y += spacing;

            // Amount
            var amountLabel = CreateLabel("المبلغ:", new Point(500, y), labelWidth);
            amountNumericUpDown = new NumericUpDown
            {
                Location = new Point(170, y),
                Size = new Size(controlWidth, 25),
                Font = new Font("Segoe UI", 9F),
                DecimalPlaces = 2,
                Maximum = 999999999,
                Minimum = 0,
                ThousandsSeparator = true
            };
            y += spacing;

            // Action Buttons
            var testButton = new Button
            {
                Text = "اختبار الإشعارات",
                Size = new Size(120, 35),
                Location = new Point(350, y),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            testButton.Click += TestButton_Click;

            var clearButton = new Button
            {
                Text = "مسح الحقول",
                Size = new Size(120, 35),
                Location = new Point(220, y),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            clearButton.Click += ClearButton_Click;

            var validateButton = new Button
            {
                Text = "التحقق من البيانات",
                Size = new Size(120, 35),
                Location = new Point(90, y),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            validateButton.Click += ValidateButton_Click;

            panel.Controls.AddRange(new Control[]
            {
                titleLabel, notificationPanel,
                accountLabel, accountAutoComplete,
                employeeLabel, employeeAutoComplete,
                startDateLabel, startDatePicker,
                endDateLabel, endDatePicker,
                emailLabel, emailTextBox,
                phoneLabel, phoneTextBox,
                nationalIdLabel, nationalIdTextBox,
                amountLabel, amountNumericUpDown,
                testButton, clearButton, validateButton
            });

            this.Controls.Add(panel);
        }

        private Label CreateLabel(string text, Point location, int width)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(width, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
        }

        private void SetupValidationAndEffects()
        {
            tooltip = new EnhancedTooltip();

            // Add tooltips
            tooltip.SetInfoTooltip(accountAutoComplete, "ابدأ بكتابة اسم الحساب أو رقمه للبحث");
            tooltip.SetInfoTooltip(employeeAutoComplete, "ابدأ بكتابة اسم الموظف أو رقم هويته للبحث");
            tooltip.SetInfoTooltip(startDatePicker, "اضغط على التقويم أو استخدم الأسهم للتنقل");
            tooltip.SetInfoTooltip(endDatePicker, "يمكنك استخدام اختصارات لوحة المفاتيح للتنقل السريع");

            // Add real-time validation
            ValidationHelper.AddRealTimeValidation(emailTextBox, 
                ValidationHelper.IsValidEmail, 
                "البريد الإلكتروني غير صحيح");

            ValidationHelper.AddRealTimeValidation(phoneTextBox, 
                ValidationHelper.IsValidSaudiPhone, 
                "رقم الهاتف غير صحيح");

            ValidationHelper.AddRealTimeValidation(nationalIdTextBox, 
                ValidationHelper.IsValidSaudiNationalId, 
                "رقم الهوية غير صحيح");

            // Add focus effects
            ValidationHelper.AddFocusEffect(emailTextBox);
            ValidationHelper.AddFocusEffect(phoneTextBox);
            ValidationHelper.AddFocusEffect(nationalIdTextBox);

            // Add hover effects to buttons
            foreach (Control control in this.Controls[0].Controls)
            {
                if (control is Button button)
                {
                    ValidationHelper.AddHoverEffect(button);
                }
            }

            // Make numeric fields numeric only
            ValidationHelper.MakeNumericOnly(phoneTextBox);
            ValidationHelper.MakeNumericOnly(nationalIdTextBox);
        }

        private void TestButton_Click(object? sender, EventArgs e)
        {
            var random = new Random();
            var notifications = new[]
            {
                () => notificationPanel.ShowSuccess("تم الحفظ بنجاح!"),
                () => notificationPanel.ShowError("حدث خطأ في العملية"),
                () => notificationPanel.ShowWarning("تحذير: تحقق من البيانات"),
                () => notificationPanel.ShowInfo("معلومة: تم تحديث البيانات")
            };

            notifications[random.Next(notifications.Length)]();
        }

        private void ClearButton_Click(object? sender, EventArgs e)
        {
            accountAutoComplete.DisplayText = "";
            employeeAutoComplete.DisplayText = "";
            startDatePicker.Value = DateTime.Now.AddDays(-30);
            endDatePicker.Value = DateTime.Now;
            emailTextBox.Text = "";
            phoneTextBox.Text = "";
            nationalIdTextBox.Text = "";
            amountNumericUpDown.Value = 0;

            notificationPanel.ShowInfo("تم مسح جميع الحقول");
        }

        private void ValidateButton_Click(object? sender, EventArgs e)
        {
            var errors = new List<string>();

            if (!ValidationHelper.IsValidEmail(emailTextBox.Text) && !string.IsNullOrEmpty(emailTextBox.Text))
                errors.Add("البريد الإلكتروني غير صحيح");

            if (!ValidationHelper.IsValidSaudiPhone(phoneTextBox.Text) && !string.IsNullOrEmpty(phoneTextBox.Text))
                errors.Add("رقم الهاتف غير صحيح");

            if (!ValidationHelper.IsValidSaudiNationalId(nationalIdTextBox.Text) && !string.IsNullOrEmpty(nationalIdTextBox.Text))
                errors.Add("رقم الهوية غير صحيح");

            if (startDatePicker.Value >= endDatePicker.Value)
                errors.Add("تاريخ البداية يجب أن يكون قبل تاريخ النهاية");

            if (errors.Any())
            {
                notificationPanel.ShowError($"أخطاء في البيانات: {string.Join(", ", errors)}");
            }
            else
            {
                notificationPanel.ShowSuccess("جميع البيانات صحيحة!");
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                tooltip?.Dispose();
                notificationPanel?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
