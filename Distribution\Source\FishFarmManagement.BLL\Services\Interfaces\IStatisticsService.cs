using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة الإحصائيات
    /// Statistics service interface
    /// </summary>
    public interface IStatisticsService
    {
        /// <summary>
        /// الحصول على إحصائيات لوحة المعلومات
        /// Get dashboard statistics
        /// </summary>
        Task<DashboardStatistics> GetDashboardStatisticsAsync();

        /// <summary>
        /// الحصول على إحصائيات الأحواض
        /// Get pond statistics
        /// </summary>
        Task<PondStatistics> GetPondStatisticsAsync();

        /// <summary>
        /// الحصول على إحصائيات الموظفين
        /// Get employee statistics
        /// </summary>
        Task<EmployeeStatistics> GetEmployeeStatisticsAsync();

        /// <summary>
        /// الحصول على إحصائيات المخزون
        /// Get inventory statistics
        /// </summary>
        Task<InventoryStatistics> GetInventoryStatisticsAsync();

        /// <summary>
        /// الحصول على إحصائيات المالية
        /// Get financial statistics
        /// </summary>
        Task<FinancialStatistics> GetFinancialStatisticsAsync();
    }

    /// <summary>
    /// إحصائيات لوحة المعلومات
    /// Dashboard statistics
    /// </summary>
    public class DashboardStatistics
    {
        public int ActivePonds { get; set; }
        public int ActiveCycles { get; set; }
        public int TotalEmployees { get; set; }
        public int TodayTransactions { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public int LowStockItems { get; set; }
        public decimal MonthlyRevenue { get; set; }
        public decimal MonthlyExpenses { get; set; }
    }

    /// <summary>
    /// إحصائيات الأحواض
    /// Pond statistics
    /// </summary>
    public class PondStatistics
    {
        public int TotalPonds { get; set; }
        public int ActivePonds { get; set; }
        public int InactivePonds { get; set; }
        public int UnderMaintenancePonds { get; set; }
        public decimal AverageCapacity { get; set; }
        public decimal TotalCapacity { get; set; }
    }

    /// <summary>
    /// إحصائيات الموظفين
    /// Employee statistics
    /// </summary>
    public class EmployeeStatistics
    {
        public int TotalEmployees { get; set; }
        public int ActiveEmployees { get; set; }
        public int InactiveEmployees { get; set; }
        public decimal TotalMonthlySalaries { get; set; }
        public decimal AverageSalary { get; set; }
    }

    /// <summary>
    /// إحصائيات المخزون
    /// Inventory statistics
    /// </summary>
    public class InventoryStatistics
    {
        public int TotalItems { get; set; }
        public int ActiveItems { get; set; }
        public int LowStockItems { get; set; }
        public int ExpiredItems { get; set; }
        public decimal TotalValue { get; set; }
        public decimal AverageItemValue { get; set; }
    }

    /// <summary>
    /// إحصائيات المالية
    /// Financial statistics
    /// </summary>
    public class FinancialStatistics
    {
        public decimal MonthlyRevenue { get; set; }
        public decimal MonthlyExpenses { get; set; }
        public decimal MonthlyProfit { get; set; }
        public decimal YearlyRevenue { get; set; }
        public decimal YearlyExpenses { get; set; }
        public decimal YearlyProfit { get; set; }
        public decimal TotalAssets { get; set; }
        public decimal TotalLiabilities { get; set; }
    }
}
