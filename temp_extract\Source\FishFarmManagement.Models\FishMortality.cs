using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// نفوق الأسماك
    /// Fish mortality
    /// </summary>
    public class FishMortality : BaseEntity
    {
        [Required(ErrorMessage = "معرف الحوض مطلوب")]
        public int PondId { get; set; }

        [Required(ErrorMessage = "عدد الأسماك النافقة مطلوب")]
        [Range(1, int.MaxValue, ErrorMessage = "عدد الأسماك النافقة يجب أن يكون أكبر من الصفر")]
        public int DeadFishCount { get; set; }

        [Required(ErrorMessage = "تاريخ النفوق مطلوب")]
        public DateTime MortalityDate { get; set; }

        [Required(ErrorMessage = "سبب النفوق مطلوب")]
        [StringLength(200, ErrorMessage = "سبب النفوق يجب أن يكون أقل من 200 حرف")]
        public string Cause { get; set; } = string.Empty;

        [StringLength(1000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 1000 حرف")]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// الوزن المقدر للأسماك النافقة
        /// Estimated weight of dead fish
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الوزن المقدر يجب أن يكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,3)")]
        public decimal EstimatedWeight { get; set; }

        /// <summary>
        /// الخسارة المالية المقدرة
        /// Estimated financial loss
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الخسارة المالية يجب أن تكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal EstimatedLoss { get; set; }

        // Navigation Properties
        [ForeignKey("PondId")]
        public virtual Pond Pond { get; set; } = null!;

        /// <summary>
        /// حساب معدل النفوق كنسبة مئوية
        /// Calculate mortality rate as percentage
        /// </summary>
        public decimal GetMortalityRate()
        {
            if (Pond?.FishCount > 0)
            {
                return (decimal)DeadFishCount / Pond.FishCount * 100;
            }
            return 0;
        }

        /// <summary>
        /// حساب متوسط الوزن للسمكة النافقة
        /// Calculate average weight per dead fish
        /// </summary>
        public decimal GetAverageWeightPerFish()
        {
            return DeadFishCount > 0 ? EstimatedWeight / DeadFishCount : 0;
        }

        /// <summary>
        /// التحقق من خطورة معدل النفوق
        /// Check if mortality rate is critical
        /// </summary>
        public bool IsCriticalMortality()
        {
            return GetMortalityRate() > 5; // أكثر من 5% يعتبر خطير
        }
    }
}
