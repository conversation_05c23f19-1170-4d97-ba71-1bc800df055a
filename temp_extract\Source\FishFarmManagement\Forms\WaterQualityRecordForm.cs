﻿using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ ØªØ³Ø¬ÙŠÙ„ Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡
    /// Water Quality Record Form
    /// </summary>
    public partial class WaterQualityRecordForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<WaterQualityRecordForm> _logger;

        // UI Controls
        private ComboBox pondComboBox;
        private DateTimePicker testDatePicker;
        // private TimeSpan testTime; // ØºÙŠØ± Ù…Ø³ØªØ®Ø¯Ù…
        private NumericUpDown temperatureNumericUpDown;
        private NumericUpDown phNumericUpDown;
        private NumericUpDown dissolvedOxygenNumericUpDown;
        private NumericUpDown ammoniaNumericUpDown;
        private NumericUpDown nitriteNumericUpDown;
        private NumericUpDown nitrateNumericUpDown;
        private NumericUpDown alkalinityNumericUpDown;
        private NumericUpDown hardnessNumericUpDown;
        private ComboBox turbidityComboBox;
        private TextBox notesTextBox;
        private Button saveButton;
        private Button cancelButton;
        private DataGridView qualityHistoryDataGridView;
        private GroupBox alertsPanel;
        private Label overallStatusLabel;

        public WaterQualityRecordForm(IUnitOfWork unitOfWork, ILogger<WaterQualityRecordForm> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            InitializeComponent();
            LoadInitialData();
        }

        private void InitializeComponent()
        {
            this.Text = "ØªØ³Ø¬ÙŠÙ„ Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 1,
                ColumnCount = 2,
                Padding = new Padding(20)
            };

            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 600));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // Left Panel (Input)
            var leftPanel = CreateInputPanel();
            mainPanel.Controls.Add(leftPanel, 0, 0);

            // Right Panel (History and Alerts)
            var rightPanel = CreateRightPanel();
            mainPanel.Controls.Add(rightPanel, 1, 0);

            this.Controls.Add(mainPanel);
        }

        private GroupBox CreateInputPanel()
        {
            var panel = new GroupBox
            {
                Text = "Ù‚ÙŠØ§Ø³ Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Padding = new Padding(15)
            };

            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            int y = 10;
            int spacing = 35;
            int leftCol = 20;
            // int rightCol = 320; // ØºÙŠØ± Ù…Ø³ØªØ®Ø¯Ù…

            // Basic Info
            var basicInfoLabel = new Label
            {
                Text = "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø£Ø³Ø§Ø³ÙŠØ©",
                Location = new Point(leftCol, y),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Blue
            };
            y += 25;

            // Pond selection
            var pondLabel = CreateLabel("Ø§Ù„Ø­ÙˆØ¶:", new Point(leftCol + 200, y));
            pondComboBox = new ComboBox
            {
                Location = new Point(leftCol, y),
                Size = new Size(180, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            y += spacing;

            // Test date and time
            var dateLabel = CreateLabel("ØªØ§Ø±ÙŠØ® Ø§Ù„ÙØ­Øµ:", new Point(leftCol + 200, y));
            testDatePicker = new DateTimePicker
            {
                Location = new Point(leftCol, y),
                Size = new Size(180, 23),
                Value = DateTime.Now
            };
            y += spacing;

            // Physical Parameters
            var physicalLabel = new Label
            {
                Text = "Ø§Ù„Ù…Ø¹Ø§ÙŠÙŠØ± Ø§Ù„ÙÙŠØ²ÙŠØ§Ø¦ÙŠØ©",
                Location = new Point(leftCol, y),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Blue
            };
            y += 25;

            // Temperature
            var tempLabel = CreateLabel("Ø¯Ø±Ø¬Ø© Ø§Ù„Ø­Ø±Ø§Ø±Ø© (Â°Ù…):", new Point(leftCol + 200, y));
            temperatureNumericUpDown = new NumericUpDown
            {
                Location = new Point(leftCol, y),
                Size = new Size(180, 23),
                DecimalPlaces = 1,
                Maximum = 50,
                Minimum = 0,
                Value = 25
            };
            temperatureNumericUpDown.ValueChanged += ValidateParameters;
            y += spacing;

            // Turbidity
            var turbidityLabel = CreateLabel("Ø§Ù„Ø¹ÙƒØ§Ø±Ø©:", new Point(leftCol + 200, y));
            turbidityComboBox = new ComboBox
            {
                Location = new Point(leftCol, y),
                Size = new Size(180, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            y += spacing;

            // Chemical Parameters
            var chemicalLabel = new Label
            {
                Text = "Ø§Ù„Ù…Ø¹Ø§ÙŠÙŠØ± Ø§Ù„ÙƒÙŠÙ…ÙŠØ§Ø¦ÙŠØ©",
                Location = new Point(leftCol, y),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Blue
            };
            y += 25;

            // pH
            var phLabel = CreateLabel("Ø§Ù„Ø±Ù‚Ù… Ø§Ù„Ù‡ÙŠØ¯Ø±ÙˆØ¬ÙŠÙ†ÙŠ (pH):", new Point(leftCol + 200, y));
            phNumericUpDown = new NumericUpDown
            {
                Location = new Point(leftCol, y),
                Size = new Size(180, 23),
                DecimalPlaces = 2,
                Maximum = 14,
                Minimum = 0,
                Value = 7
            };
            phNumericUpDown.ValueChanged += ValidateParameters;
            y += spacing;

            // Dissolved Oxygen
            var doLabel = CreateLabel("Ø§Ù„Ø£ÙƒØ³Ø¬ÙŠÙ† Ø§Ù„Ù…Ø°Ø§Ø¨ (mg/L):", new Point(leftCol + 200, y));
            dissolvedOxygenNumericUpDown = new NumericUpDown
            {
                Location = new Point(leftCol, y),
                Size = new Size(180, 23),
                DecimalPlaces = 2,
                Maximum = 20,
                Minimum = 0,
                Value = 6
            };
            dissolvedOxygenNumericUpDown.ValueChanged += ValidateParameters;
            y += spacing;

            // Ammonia
            var ammoniaLabel = CreateLabel("Ø§Ù„Ø£Ù…ÙˆÙ†ÙŠØ§ (mg/L):", new Point(leftCol + 200, y));
            ammoniaNumericUpDown = new NumericUpDown
            {
                Location = new Point(leftCol, y),
                Size = new Size(180, 23),
                DecimalPlaces = 3,
                Maximum = 10,
                Minimum = 0,
                Value = 0
            };
            ammoniaNumericUpDown.ValueChanged += ValidateParameters;
            y += spacing;

            // Nitrite
            var nitriteLabel = CreateLabel("Ø§Ù„Ù†ØªØ±ÙŠØª (mg/L):", new Point(leftCol + 200, y));
            nitriteNumericUpDown = new NumericUpDown
            {
                Location = new Point(leftCol, y),
                Size = new Size(180, 23),
                DecimalPlaces = 3,
                Maximum = 5,
                Minimum = 0,
                Value = 0
            };
            nitriteNumericUpDown.ValueChanged += ValidateParameters;
            y += spacing;

            // Nitrate
            var nitrateLabel = CreateLabel("Ø§Ù„Ù†ØªØ±Ø§Øª (mg/L):", new Point(leftCol + 200, y));
            nitrateNumericUpDown = new NumericUpDown
            {
                Location = new Point(leftCol, y),
                Size = new Size(180, 23),
                DecimalPlaces = 2,
                Maximum = 100,
                Minimum = 0,
                Value = 0
            };
            y += spacing;

            // Alkalinity
            var alkalinityLabel = CreateLabel("Ø§Ù„Ù‚Ù„ÙˆÙŠØ© (mg/L):", new Point(leftCol + 200, y));
            alkalinityNumericUpDown = new NumericUpDown
            {
                Location = new Point(leftCol, y),
                Size = new Size(180, 23),
                DecimalPlaces = 1,
                Maximum = 500,
                Minimum = 0,
                Value = 100
            };
            y += spacing;

            // Hardness
            var hardnessLabel = CreateLabel("Ø§Ù„Ø¹Ø³Ø± (mg/L):", new Point(leftCol + 200, y));
            hardnessNumericUpDown = new NumericUpDown
            {
                Location = new Point(leftCol, y),
                Size = new Size(180, 23),
                DecimalPlaces = 1,
                Maximum = 1000,
                Minimum = 0,
                Value = 150
            };
            y += spacing;

            // Overall Status
            var statusLabel = CreateLabel("Ø§Ù„Ø­Ø§Ù„Ø© Ø§Ù„Ø¹Ø§Ù…Ø©:", new Point(leftCol + 200, y));
            overallStatusLabel = new Label
            {
                Location = new Point(leftCol, y),
                Size = new Size(180, 23),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(240, 255, 240),
                TextAlign = ContentAlignment.MiddleLeft,
                Text = "Ù…Ù…ØªØ§Ø²",
                ForeColor = Color.Green,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            y += spacing;

            // Notes
            var notesLabel = CreateLabel("Ù…Ù„Ø§Ø­Ø¸Ø§Øª:", new Point(leftCol + 200, y));
            notesTextBox = new TextBox
            {
                Location = new Point(leftCol, y),
                Size = new Size(400, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            y += 70;

            // Buttons
            saveButton = new Button
            {
                Text = "Ø­ÙØ¸",
                Location = new Point(leftCol + 100, y),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "Ø¥Ù„ØºØ§Ø¡",
                Location = new Point(leftCol, y),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            cancelButton.Click += (s, e) => this.Close();

            scrollPanel.Controls.AddRange(new Control[]
            {
                basicInfoLabel,
                pondLabel, pondComboBox,
                dateLabel, testDatePicker,
                physicalLabel,
                tempLabel, temperatureNumericUpDown,
                turbidityLabel, turbidityComboBox,
                chemicalLabel,
                phLabel, phNumericUpDown,
                doLabel, dissolvedOxygenNumericUpDown,
                ammoniaLabel, ammoniaNumericUpDown,
                nitriteLabel, nitriteNumericUpDown,
                nitrateLabel, nitrateNumericUpDown,
                alkalinityLabel, alkalinityNumericUpDown,
                hardnessLabel, hardnessNumericUpDown,
                statusLabel, overallStatusLabel,
                notesLabel, notesTextBox,
                saveButton, cancelButton
            });

            panel.Controls.Add(scrollPanel);
            return panel;
        }

        private Panel CreateRightPanel()
        {
            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 2,
                ColumnCount = 1
            };

            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 200));
            panel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            // Alerts Panel
            alertsPanel = CreateAlertsPanel();
            panel.Controls.Add(alertsPanel, 0, 0);

            // History Panel
            var historyPanel = CreateHistoryPanel();
            panel.Controls.Add(historyPanel, 0, 1);

            return panel;
        }

        private GroupBox CreateAlertsPanel()
        {
            var panel = new GroupBox
            {
                Text = "ØªÙ†Ø¨ÙŠÙ‡Ø§Øª Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Padding = new Padding(15)
            };

            var alertsListBox = new ListBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9F),
                BackColor = Color.FromArgb(255, 248, 220)
            };

            panel.Controls.Add(alertsListBox);
            panel.Tag = alertsListBox; // Store reference for updates

            return panel;
        }

        private GroupBox CreateHistoryPanel()
        {
            var panel = new GroupBox
            {
                Text = "Ø³Ø¬Ù„ Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Padding = new Padding(15)
            };

            qualityHistoryDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 8F)
            };

            SetupHistoryColumns();
            panel.Controls.Add(qualityHistoryDataGridView);

            return panel;
        }

        private void SetupHistoryColumns()
        {
            qualityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TestDate",
                HeaderText = "Ø§Ù„ØªØ§Ø±ÙŠØ®",
                DataPropertyName = "TestDate",
                Width = 70,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "MM/dd" }
            });

            qualityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Temperature",
                HeaderText = "Ø§Ù„Ø­Ø±Ø§Ø±Ø©",
                DataPropertyName = "Temperature",
                Width = 50,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N1" }
            });

            qualityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "pH",
                HeaderText = "pH",
                DataPropertyName = "pH",
                Width = 40,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            qualityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DissolvedOxygen",
                HeaderText = "Ø§Ù„Ø£ÙƒØ³Ø¬ÙŠÙ†",
                DataPropertyName = "DissolvedOxygen",
                Width = 60,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            qualityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Ammonia",
                HeaderText = "Ø§Ù„Ø£Ù…ÙˆÙ†ÙŠØ§",
                DataPropertyName = "Ammonia",
                Width = 60,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N3" }
            });

            qualityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "Ø§Ù„Ø­Ø§Ù„Ø©",
                DataPropertyName = "Status",
                Width = 60
            });
        }

        private Label CreateLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(150, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
        }

        private async void LoadInitialData()
        {
            try
            {
                // Load ponds
                var ponds = await _unitOfWork.Ponds.GetAllAsync();
                var pondList = ponds.Select(p => new { Value = p.Id, Text = $"Ø­ÙˆØ¶ {p.PondNumber}" }).ToList();
                
                pondComboBox.DataSource = pondList;
                pondComboBox.DisplayMember = "Text";
                pondComboBox.ValueMember = "Value";

                // Load turbidity levels
                var turbidityLevels = new[]
                {
                    "ØµØ§ÙÙŠ",
                    "ØµØ§ÙÙŠ Ù‚Ù„ÙŠÙ„Ø§Ù‹",
                    "Ù…Ø¹ÙƒØ± Ù‚Ù„ÙŠÙ„Ø§Ù‹",
                    "Ù…Ø¹ÙƒØ±",
                    "Ù…Ø¹ÙƒØ± Ø¬Ø¯Ø§Ù‹"
                };
                turbidityComboBox.Items.AddRange(turbidityLevels);
                turbidityComboBox.SelectedIndex = 0;

                // Load quality history
                await LoadQualityHistory();
                
                // Initial validation
                ValidateParameters(null, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø£ÙˆÙ„ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadQualityHistory()
        {
            try
            {
                // Placeholder for quality records
                var historyData = new List<object>();
                qualityHistoryDataGridView.DataSource = historyData;

                await Task.CompletedTask; // Ù„ØªØ¬Ù†Ø¨ ØªØ­Ø°ÙŠØ± async
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø³Ø¬Ù„ Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡");
            }
        }

        private void ValidateParameters(object sender, EventArgs e)
        {
            var alerts = new List<string>();
            var status = "Ù…Ù…ØªØ§Ø²";
            var statusColor = Color.Green;

            // Temperature validation
            var temp = temperatureNumericUpDown.Value;
            if (temp < 20 || temp > 30)
            {
                alerts.Add($"âš  Ø¯Ø±Ø¬Ø© Ø§Ù„Ø­Ø±Ø§Ø±Ø© ({temp}Â°Ù…) Ø®Ø§Ø±Ø¬ Ø§Ù„Ù†Ø·Ø§Ù‚ Ø§Ù„Ù…Ø«Ø§Ù„ÙŠ (20-30Â°Ù…)");
                status = "ÙŠØ­ØªØ§Ø¬ Ø§Ù†ØªØ¨Ø§Ù‡";
                statusColor = Color.Orange;
            }

            // pH validation
            var ph = phNumericUpDown.Value;
            if (ph < 6.5m || ph > 8.5m)
            {
                alerts.Add($"âš  Ø§Ù„Ø±Ù‚Ù… Ø§Ù„Ù‡ÙŠØ¯Ø±ÙˆØ¬ÙŠÙ†ÙŠ ({ph}) Ø®Ø§Ø±Ø¬ Ø§Ù„Ù†Ø·Ø§Ù‚ Ø§Ù„Ù…Ø«Ø§Ù„ÙŠ (6.5-8.5)");
                status = "ÙŠØ­ØªØ§Ø¬ Ø§Ù†ØªØ¨Ø§Ù‡";
                statusColor = Color.Orange;
            }

            // Dissolved Oxygen validation
            var do2 = dissolvedOxygenNumericUpDown.Value;
            if (do2 < 5)
            {
                alerts.Add($"ðŸš¨ Ø§Ù„Ø£ÙƒØ³Ø¬ÙŠÙ† Ø§Ù„Ù…Ø°Ø§Ø¨ ({do2} mg/L) Ù…Ù†Ø®ÙØ¶ Ø¬Ø¯Ø§Ù‹! (ÙŠØ¬Ø¨ Ø£Ù† ÙŠÙƒÙˆÙ† > 5 mg/L)");
                status = "Ø®Ø·Ø±";
                statusColor = Color.Red;
            }
            else if (do2 < 6)
            {
                alerts.Add($"âš  Ø§Ù„Ø£ÙƒØ³Ø¬ÙŠÙ† Ø§Ù„Ù…Ø°Ø§Ø¨ ({do2} mg/L) Ù…Ù†Ø®ÙØ¶ (Ø§Ù„Ù…Ø«Ø§Ù„ÙŠ > 6 mg/L)");
                status = "ÙŠØ­ØªØ§Ø¬ Ø§Ù†ØªØ¨Ø§Ù‡";
                statusColor = Color.Orange;
            }

            // Ammonia validation
            var ammonia = ammoniaNumericUpDown.Value;
            if (ammonia > 0.5m)
            {
                alerts.Add($"ðŸš¨ Ø§Ù„Ø£Ù…ÙˆÙ†ÙŠØ§ ({ammonia} mg/L) Ø¹Ø§Ù„ÙŠØ© Ø¬Ø¯Ø§Ù‹! (ÙŠØ¬Ø¨ Ø£Ù† ØªÙƒÙˆÙ† < 0.5 mg/L)");
                status = "Ø®Ø·Ø±";
                statusColor = Color.Red;
            }
            else if (ammonia > 0.25m)
            {
                alerts.Add($"âš  Ø§Ù„Ø£Ù…ÙˆÙ†ÙŠØ§ ({ammonia} mg/L) Ù…Ø±ØªÙØ¹Ø© (Ø§Ù„Ù…Ø«Ø§Ù„ÙŠ < 0.25 mg/L)");
                status = "ÙŠØ­ØªØ§Ø¬ Ø§Ù†ØªØ¨Ø§Ù‡";
                statusColor = Color.Orange;
            }

            // Nitrite validation
            var nitrite = nitriteNumericUpDown.Value;
            if (nitrite > 0.5m)
            {
                alerts.Add($"ðŸš¨ Ø§Ù„Ù†ØªØ±ÙŠØª ({nitrite} mg/L) Ø¹Ø§Ù„ÙŠ Ø¬Ø¯Ø§Ù‹! (ÙŠØ¬Ø¨ Ø£Ù† ÙŠÙƒÙˆÙ† < 0.5 mg/L)");
                status = "Ø®Ø·Ø±";
                statusColor = Color.Red;
            }
            else if (nitrite > 0.1m)
            {
                alerts.Add($"âš  Ø§Ù„Ù†ØªØ±ÙŠØª ({nitrite} mg/L) Ù…Ø±ØªÙØ¹ (Ø§Ù„Ù…Ø«Ø§Ù„ÙŠ < 0.1 mg/L)");
                status = "ÙŠØ­ØªØ§Ø¬ Ø§Ù†ØªØ¨Ø§Ù‡";
                statusColor = Color.Orange;
            }

            // Update status
            overallStatusLabel.Text = status;
            overallStatusLabel.ForeColor = statusColor;

            // Update alerts
            var alertsListBox = alertsPanel.Tag as ListBox;
            if (alertsListBox != null)
            {
                alertsListBox.Items.Clear();
                if (alerts.Count == 0)
                {
                    alertsListBox.Items.Add("âœ“ Ø¬Ù…ÙŠØ¹ Ø§Ù„Ù…Ø¹Ø§ÙŠÙŠØ± Ø¶Ù…Ù† Ø§Ù„Ù†Ø·Ø§Ù‚ Ø§Ù„Ù…Ø«Ø§Ù„ÙŠ");
                    alertsListBox.ForeColor = Color.Green;
                }
                else
                {
                    foreach (var alert in alerts)
                    {
                        alertsListBox.Items.Add(alert);
                    }
                    alertsListBox.ForeColor = statusColor;
                }
            }
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                // Create water quality record
                var qualityRecord = new
                {
                    PondId = (int)pondComboBox.SelectedValue,
                    TestDate = testDatePicker.Value.Date,
                    Temperature = temperatureNumericUpDown.Value,
                    pH = phNumericUpDown.Value,
                    DissolvedOxygen = dissolvedOxygenNumericUpDown.Value,
                    Ammonia = ammoniaNumericUpDown.Value,
                    Nitrite = nitriteNumericUpDown.Value,
                    Nitrate = nitrateNumericUpDown.Value,
                    Alkalinity = alkalinityNumericUpDown.Value,
                    Hardness = hardnessNumericUpDown.Value,
                    Turbidity = turbidityComboBox.SelectedItem.ToString(),
                    OverallStatus = overallStatusLabel.Text,
                    Notes = notesTextBox.Text,
                    RecordedDate = DateTime.Now
                };

                // Save to database (implement when WaterQualityRecord entity is created)
                // await _unitOfWork.WaterQualityRecords.AddAsync(qualityRecord);
                // await _unitOfWork.SaveChangesAsync();

                MessageBox.Show("ØªÙ… Ø­ÙØ¸ Ø³Ø¬Ù„ Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡ Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ClearForm();
                await LoadQualityHistory();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­ÙØ¸ Ø³Ø¬Ù„ Ø¬ÙˆØ¯Ø© Ø§Ù„Ù…ÙŠØ§Ù‡");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø­ÙØ¸: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (pondComboBox.SelectedValue == null)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ø§Ù„Ø­ÙˆØ¶", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (turbidityComboBox.SelectedItem == null)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ù…Ø³ØªÙˆÙ‰ Ø§Ù„Ø¹ÙƒØ§Ø±Ø©", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            testDatePicker.Value = DateTime.Now;
            temperatureNumericUpDown.Value = 25;
            phNumericUpDown.Value = 7;
            dissolvedOxygenNumericUpDown.Value = 6;
            ammoniaNumericUpDown.Value = 0;
            nitriteNumericUpDown.Value = 0;
            nitrateNumericUpDown.Value = 0;
            alkalinityNumericUpDown.Value = 100;
            hardnessNumericUpDown.Value = 150;
            turbidityComboBox.SelectedIndex = 0;
            notesTextBox.Clear();
            
            ValidateParameters(null, EventArgs.Empty);
        }
    }
}



