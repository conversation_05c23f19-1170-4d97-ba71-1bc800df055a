@echo off
chcp 65001 >nul
echo ========================================
echo    نظام إدارة مزرعة الأسماك v1.0.1
echo    Fish Farm Management System v1.0.1
echo ========================================
echo.

echo [1/5] التحقق من .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET SDK غير مثبت
    echo يرجى تثبيت .NET 8.0 SDK من: https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

echo تم العثور على .NET SDK
for /f "tokens=*" %%i in ('dotnet --version') do echo الإصدار: %%i
echo.

echo [2/5] استعادة الحزم...
dotnet restore
if %errorlevel% neq 0 (
    echo خطأ في استعادة الحزم
    pause
    exit /b 1
)

echo [3/5] بناء المشروع...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    pause
    exit /b 1
)

echo [4/5] التحقق من الملف التنفيذي...
if exist "FishFarmManagement\bin\Release\net8.0-windows\FishFarmManagement.exe" (
    echo تم العثور على الملف التنفيذي
) else (
    echo خطأ: لم يتم إنشاء الملف التنفيذي
    pause
    exit /b 1
)

echo [5/5] تشغيل البرنامج...
echo.
echo ========================================
echo          تشغيل البرنامج...
echo ========================================
echo.
echo معلومات تسجيل الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: Admin123!
echo.
echo سيتم إنشاء ترخيص تجريبي لمدة 30 يوم تلقائياً
echo.

cd "FishFarmManagement\bin\Release\net8.0-windows"
start FishFarmManagement.exe

echo تم تشغيل البرنامج بنجاح!
echo إذا لم يظهر البرنامج، تحقق من Windows Defender أو برامج مكافحة الفيروسات
echo.
pause
