using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// حوض الأسماك
    /// Fish pond
    /// </summary>
    public class Pond : BaseEntity
    {
        [Required(ErrorMessage = "رقم الحوض مطلوب")]
        [StringLength(50, ErrorMessage = "رقم الحوض يجب أن يكون أقل من 50 حرف")]
        public string PondNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "معرف الدورة الإنتاجية مطلوب")]
        public int CycleId { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "عدد الأسماك يجب أن يكون أكبر من الصفر")]
        public int FishCount { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "متوسط الوزن يجب أن يكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,3)")]
        public decimal AverageWeight { get; set; }

        [Required(ErrorMessage = "تاريخ التخزين مطلوب")]
        public DateTime StockingDate { get; set; }

        public DateTime? ExpectedHarvestDate { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "نشط"; // نشط، محصود، فارغ، صيانة

        [StringLength(1000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 1000 حرف")]
        public string Notes { get; set; } = string.Empty;

        // Navigation Properties
        [ForeignKey("CycleId")]
        public virtual ProductionCycle ProductionCycle { get; set; } = null!;

        public virtual ICollection<FeedConsumption> FeedConsumptions { get; set; } = new List<FeedConsumption>();
        public virtual ICollection<FishMortality> FishMortalities { get; set; } = new List<FishMortality>();
        public virtual ICollection<PondMedication> PondMedications { get; set; } = new List<PondMedication>();

        /// <summary>
        /// حساب إجمالي الوزن المتوقع
        /// Calculate total expected weight
        /// </summary>
        public decimal GetTotalExpectedWeight()
        {
            return FishCount * AverageWeight;
        }

        /// <summary>
        /// حساب عدد الأسماك النافقة الإجمالي
        /// Calculate total dead fish count
        /// </summary>
        public int GetTotalDeadFishCount()
        {
            return FishMortalities.Sum(m => m.DeadFishCount);
        }

        /// <summary>
        /// حساب عدد الأسماك الحية
        /// Calculate live fish count
        /// </summary>
        public int GetLiveFishCount()
        {
            return FishCount - GetTotalDeadFishCount();
        }

        /// <summary>
        /// حساب إجمالي استهلاك العلف
        /// Calculate total feed consumption
        /// </summary>
        public decimal GetTotalFeedConsumption()
        {
            return FeedConsumptions.Sum(f => f.Quantity);
        }

        /// <summary>
        /// حساب إجمالي تكلفة العلف
        /// Calculate total feed cost
        /// </summary>
        public decimal GetTotalFeedCost()
        {
            return FeedConsumptions.Sum(f => f.Cost);
        }
    }
}
