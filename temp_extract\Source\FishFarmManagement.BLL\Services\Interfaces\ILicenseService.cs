using FishFarmManagement.Models.License;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة التراخيص
    /// License management service interface
    /// </summary>
    public interface ILicenseService
    {
        /// <summary>
        /// التحقق من صلاحية الترخيص
        /// Validate license
        /// </summary>
        Task<LicenseValidationResult> ValidateLicenseAsync(string licenseKey);

        /// <summary>
        /// تفعيل الترخيص
        /// Activate license
        /// </summary>
        Task<LicenseActivationResult> ActivateLicenseAsync(string licenseKey);

        /// <summary>
        /// الحصول على معلومات الترخيص الحالي
        /// Get current license information
        /// </summary>
        Task<LicenseInfo?> GetCurrentLicenseAsync();

        /// <summary>
        /// التحقق من صلاحية الترخيص للمزرعة
        /// Check license validity for farm
        /// </summary>
        Task<bool> IsLicenseValidForFarmAsync(int pondCount, int userCount);

        /// <summary>
        /// الحصول على معلومات الترخيص المحلية
        /// Get local license information
        /// </summary>
        LicenseInfo? GetLocalLicense();

        /// <summary>
        /// حفظ معلومات الترخيص محلياً
        /// Save license information locally
        /// </summary>
        Task<bool> SaveLicenseLocallyAsync(LicenseInfo license);

        /// <summary>
        /// التحقق من انتهاء صلاحية الترخيص
        /// Check license expiration
        /// </summary>
        Task<LicenseExpirationResult> CheckExpirationAsync();

        /// <summary>
        /// إنشاء ترخيص تجريبي
        /// Create trial license
        /// </summary>
        Task<LicenseInfo> CreateTrialLicenseAsync();

        /// <summary>
        /// الحصول على معرف الجهاز
        /// Get hardware identifier
        /// </summary>
        string GetHardwareId();

        /// <summary>
        /// التحقق من اتصال الإنترنت للتفعيل
        /// Check internet connection for activation
        /// </summary>
        Task<bool> CheckInternetConnectionAsync();
    }

    /// <summary>
    /// نتيجة التحقق من الترخيص
    /// License validation result
    /// </summary>
    public class LicenseValidationResult
    {
        public bool IsValid { get; set; }
        public string Message { get; set; } = string.Empty;
        public LicenseInfo? License { get; set; }
        public LicenseValidationError? Error { get; set; }
    }

    /// <summary>
    /// نتيجة تفعيل الترخيص
    /// License activation result
    /// </summary>
    public class LicenseActivationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public LicenseInfo? License { get; set; }
        public LicenseActivationError? Error { get; set; }
    }

    /// <summary>
    /// نتيجة انتهاء صلاحية الترخيص
    /// License expiration result
    /// </summary>
    public class LicenseExpirationResult
    {
        public bool IsExpired { get; set; }
        public bool ExpiresSoon { get; set; }
        public int RemainingDays { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// أخطاء التحقق من الترخيص
    /// License validation errors
    /// </summary>
    public enum LicenseValidationError
    {
        None = 0,
        InvalidKey = 1,
        Expired = 2,
        MaxActivationsReached = 3,
        HardwareMismatch = 4,
        NetworkError = 5,
        ServerError = 6
    }

    /// <summary>
    /// أخطاء تفعيل الترخيص
    /// License activation errors
    /// </summary>
    public enum LicenseActivationError
    {
        None = 0,
        InvalidKey = 1,
        AlreadyActivated = 2,
        MaxActivationsReached = 3,
        NetworkError = 4,
        ServerError = 5,
        HardwareError = 6
    }
} 