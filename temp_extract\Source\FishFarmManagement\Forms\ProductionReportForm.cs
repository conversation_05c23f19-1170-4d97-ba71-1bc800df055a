﻿using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ ØªÙ‚Ø±ÙŠØ± Ø§Ù„Ø¥Ù†ØªØ§Ø¬
    /// Production Report Form
    /// </summary>
    public partial class ProductionReportForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;

        // UI Controls
        private ComboBox cycleFilterComboBox;
        private ComboBox statusFilterComboBox;
        private DateTimePicker fromDatePicker;
        private DateTimePicker toDatePicker;
        private Button generateReportButton;
        private DataGridView productionDataGridView;
        private Label totalCyclesLabel;
        private Label activeCyclesLabel;
        private Label completedCyclesLabel;
        private Label totalProductionLabel;
        private Label averageProductivityLabel;
        private Label totalRevenueLabel;

        public ProductionReportForm(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            InitializeComponent();
            LoadInitialData();
        }

        private void InitializeComponent()
        {
            this.Text = "ØªÙ‚Ø±ÙŠØ± Ø§Ù„Ø¥Ù†ØªØ§Ø¬";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
        }

        private void CreateControls()
        {
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 4,
                ColumnCount = 1,
                Padding = new Padding(20)
            };

            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 100));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 50));

            // Filter Panel
            var filterPanel = CreateFilterPanel();
            mainPanel.Controls.Add(filterPanel, 0, 0);

            // Data Grid
            productionDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F)
            };

            SetupDataGridColumns();
            mainPanel.Controls.Add(productionDataGridView, 0, 1);

            // Summary Panel
            var summaryPanel = CreateSummaryPanel();
            mainPanel.Controls.Add(summaryPanel, 0, 2);

            // Buttons Panel
            var buttonsPanel = CreateButtonsPanel();
            mainPanel.Controls.Add(buttonsPanel, 0, 3);

            this.Controls.Add(mainPanel);
        }

        private Panel CreateFilterPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            // First row
            var cycleLabel = new Label
            {
                Text = "Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©:",
                Location = new Point(900, 15),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            cycleFilterComboBox = new ComboBox
            {
                Location = new Point(750, 15),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            var statusLabel = new Label
            {
                Text = "Ø§Ù„Ø­Ø§Ù„Ø©:",
                Location = new Point(680, 15),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            statusFilterComboBox = new ComboBox
            {
                Location = new Point(530, 15),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            generateReportButton = new Button
            {
                Text = "Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±",
                Location = new Point(400, 15),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            generateReportButton.Click += GenerateReport_Click;

            // Second row
            var fromLabel = new Label
            {
                Text = "Ù…Ù† ØªØ§Ø±ÙŠØ®:",
                Location = new Point(900, 50),
                Size = new Size(60, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            fromDatePicker = new DateTimePicker
            {
                Location = new Point(750, 50),
                Size = new Size(120, 23),
                Value = DateTime.Now.AddMonths(-6)
            };

            var toLabel = new Label
            {
                Text = "Ø¥Ù„Ù‰ ØªØ§Ø±ÙŠØ®:",
                Location = new Point(680, 50),
                Size = new Size(60, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            toDatePicker = new DateTimePicker
            {
                Location = new Point(530, 50),
                Size = new Size(120, 23),
                Value = DateTime.Now
            };

            panel.Controls.AddRange(new Control[] 
            { 
                cycleLabel, cycleFilterComboBox,
                statusLabel, statusFilterComboBox,
                generateReportButton,
                fromLabel, fromDatePicker,
                toLabel, toDatePicker
            });

            return panel;
        }

        private void SetupDataGridColumns()
        {
            productionDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CycleName",
                HeaderText = "Ø§Ø³Ù… Ø§Ù„Ø¯ÙˆØ±Ø©",
                DataPropertyName = "CycleName",
                Width = 150
            });

            productionDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "StartDate",
                HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø¨Ø¯Ø§ÙŠØ©",
                DataPropertyName = "StartDate",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            productionDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "EndDate",
                HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ù†Ù‡Ø§ÙŠØ©",
                DataPropertyName = "EndDate",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            productionDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "Ø§Ù„Ø­Ø§Ù„Ø©",
                DataPropertyName = "Status",
                Width = 80
            });

            productionDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Duration",
                HeaderText = "Ø§Ù„Ù…Ø¯Ø© (Ø£ÙŠØ§Ù…)",
                DataPropertyName = "Duration",
                Width = 80
            });

            productionDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PondCount",
                HeaderText = "Ø¹Ø¯Ø¯ Ø§Ù„Ø£Ø­ÙˆØ§Ø¶",
                DataPropertyName = "PondCount",
                Width = 80
            });

            productionDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalFish",
                HeaderText = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø£Ø³Ù…Ø§Ùƒ",
                DataPropertyName = "TotalFish",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N0" }
            });

            productionDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalProduction",
                HeaderText = "Ø§Ù„Ø¥Ù†ØªØ§Ø¬ (ÙƒØ¬Ù…)",
                DataPropertyName = "TotalProduction",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            productionDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Productivity",
                HeaderText = "Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ© (ÙƒØ¬Ù…/ÙŠÙˆÙ…)",
                DataPropertyName = "Productivity",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N3" }
            });

            productionDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "EstimatedRevenue",
                HeaderText = "Ø§Ù„Ø¥ÙŠØ±Ø§Ø¯ Ø§Ù„Ù…ØªÙˆÙ‚Ø¹",
                DataPropertyName = "EstimatedRevenue",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            productionDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "BudgetUtilization",
                HeaderText = "Ø§Ø³ØªØ®Ø¯Ø§Ù… Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ© %",
                DataPropertyName = "BudgetUtilization",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "P1" }
            });
        }

        private Panel CreateSummaryPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            // First row
            totalCyclesLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø¯ÙˆØ±Ø§Øª: 0",
                Location = new Point(850, 10),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            activeCyclesLabel = new Label
            {
                Text = "Ø§Ù„Ø¯ÙˆØ±Ø§Øª Ø§Ù„Ù†Ø´Ø·Ø©: 0",
                Location = new Point(850, 35),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Green
            };

            completedCyclesLabel = new Label
            {
                Text = "Ø§Ù„Ø¯ÙˆØ±Ø§Øª Ø§Ù„Ù…ÙƒØªÙ…Ù„Ø©: 0",
                Location = new Point(850, 60),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Blue
            };

            // Second row
            totalProductionLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø¥Ù†ØªØ§Ø¬: 0 ÙƒØ¬Ù…",
                Location = new Point(600, 10),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            averageProductivityLabel = new Label
            {
                Text = "Ù…ØªÙˆØ³Ø· Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©: 0 ÙƒØ¬Ù…/ÙŠÙˆÙ…",
                Location = new Point(600, 35),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            totalRevenueLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø¥ÙŠØ±Ø§Ø¯ Ø§Ù„Ù…ØªÙˆÙ‚Ø¹: 0",
                Location = new Point(600, 60),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Green
            };

            // Performance indicators
            var performanceLabel = new Label
            {
                Text = "Ù…Ø¤Ø´Ø±Ø§Øª Ø§Ù„Ø£Ø¯Ø§Ø¡:",
                Location = new Point(400, 10),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Blue
            };

            var efficiencyLabel = new Label
            {
                Text = "Ø§Ù„ÙƒÙØ§Ø¡Ø©: Ù…Ù…ØªØ§Ø²",
                Location = new Point(400, 35),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.Green
            };

            var profitabilityLabel = new Label
            {
                Text = "Ø§Ù„Ø±Ø¨Ø­ÙŠØ©: Ø¬ÙŠØ¯",
                Location = new Point(400, 60),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.Orange
            };

            panel.Controls.AddRange(new Control[] 
            { 
                totalCyclesLabel, activeCyclesLabel, completedCyclesLabel,
                totalProductionLabel, averageProductivityLabel, totalRevenueLabel,
                performanceLabel, efficiencyLabel, profitabilityLabel
            });

            return panel;
        }

        private Panel CreateButtonsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill
            };

            var exportButton = new Button
            {
                Text = "ØªØµØ¯ÙŠØ± Ø¥Ù„Ù‰ Excel",
                Location = new Point(20, 10),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            exportButton.Click += ExportToExcel_Click;

            var printButton = new Button
            {
                Text = "Ø·Ø¨Ø§Ø¹Ø©",
                Location = new Point(160, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            printButton.Click += Print_Click;

            var detailsButton = new Button
            {
                Text = "ØªÙØ§ØµÙŠÙ„ Ø§Ù„Ø¯ÙˆØ±Ø©",
                Location = new Point(260, 10),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(241, 196, 15),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            detailsButton.Click += ShowCycleDetails_Click;

            var costAnalysisButton = new Button
            {
                Text = "ØªØ­Ù„ÙŠÙ„ Ø§Ù„ØªÙƒØ§Ù„ÙŠÙ",
                Location = new Point(400, 10),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            costAnalysisButton.Click += ShowCostAnalysis_Click;

            panel.Controls.AddRange(new Control[] { exportButton, printButton, detailsButton, costAnalysisButton });
            return panel;
        }

        private async void LoadInitialData()
        {
            try
            {
                // Load production cycles
                var cycles = await _unitOfWork.ProductionCycles.GetAllAsync();
                var cycleOptions = new List<object> { new { Value = 0, Text = "Ø¬Ù…ÙŠØ¹ Ø§Ù„Ø¯ÙˆØ±Ø§Øª" } };
                cycleOptions.AddRange(cycles.Select(c => new { Value = c.Id, Text = c.CycleName }));

                cycleFilterComboBox.DataSource = cycleOptions;
                cycleFilterComboBox.DisplayMember = "Text";
                cycleFilterComboBox.ValueMember = "Value";

                // Load status options
                var statusOptions = new[]
                {
                    new { Value = "", Text = "Ø¬Ù…ÙŠØ¹ Ø§Ù„Ø­Ø§Ù„Ø§Øª" },
                    new { Value = "Ù†Ø´Ø·", Text = "Ù†Ø´Ø·" },
                    new { Value = "Ù…ÙƒØªÙ…Ù„", Text = "Ù…ÙƒØªÙ…Ù„" },
                    new { Value = "Ù…ØªÙˆÙ‚Ù", Text = "Ù…ØªÙˆÙ‚Ù" },
                    new { Value = "Ù…Ù„ØºÙŠ", Text = "Ù…Ù„ØºÙŠ" }
                };

                statusFilterComboBox.DataSource = statusOptions;
                statusFilterComboBox.DisplayMember = "Text";
                statusFilterComboBox.ValueMember = "Value";

                // Generate initial report
                await GenerateReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void private async void private async void GenerateReport_Click(object? sender, EventArgs e)
        {
            await GenerateReport();
        }

        private async Task GenerateReport()
        {
            try
            {
                var selectedCycleId = (int)cycleFilterComboBox.SelectedValue;
                var selectedStatus = statusFilterComboBox.SelectedValue?.ToString();
                var fromDate = fromDatePicker.Value.Date;
                var toDate = toDatePicker.Value.Date.AddDays(1).AddTicks(-1);

                // Get production cycles data
                var cycles = await _unitOfWork.ProductionCycles.GetAllAsync();
                var ponds = await _unitOfWork.Ponds.GetAllAsync();

                // Apply filters
                cycles = cycles.Where(c => c.StartDate >= fromDate && c.StartDate <= toDate);

                if (selectedCycleId > 0)
                    cycles = cycles.Where(c => c.Id == selectedCycleId);

                if (!string.IsNullOrEmpty(selectedStatus))
                    cycles = cycles.Where(c => c.Status == selectedStatus);

                // Create report data
                var reportData = cycles.Select(c => new ProductionReportItem
                {
                    CycleName = c.CycleName,
                    StartDate = c.StartDate,
                    EndDate = c.EndDate,
                    Status = c.Status,
                    Duration = c.GetCycleDurationInDays(),
                    PondCount = ponds.Count(p => p.CycleId == c.Id),
                    TotalFish = ponds.Where(p => p.CycleId == c.Id).Sum(p => p.FishCount),
                    TotalProduction = CalculateTotalProduction(c, ponds),
                    Productivity = CalculateProductivity(c, ponds),
                    EstimatedRevenue = CalculateEstimatedRevenue(c, ponds),
                    BudgetUtilization = CalculateBudgetUtilization(c)
                }).OrderByDescending(r => r.StartDate).ToList();

                // Display data
                productionDataGridView.DataSource = reportData;

                // Update summary
                UpdateSummary(reportData);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private decimal CalculateTotalProduction(ProductionCycle cycle, IEnumerable<Pond> allPonds)
        {
            var cyclePonds = allPonds.Where(p => p.CycleId == cycle.Id);
            return cyclePonds.Sum(p => p.FishCount * p.AverageWeight / 1000); // Convert to kg
        }

        private decimal CalculateProductivity(ProductionCycle cycle, IEnumerable<Pond> allPonds)
        {
            var totalProduction = CalculateTotalProduction(cycle, allPonds);
            var duration = cycle.GetCycleDurationInDays();
            return duration > 0 ? totalProduction / duration : 0;
        }

        private decimal CalculateEstimatedRevenue(ProductionCycle cycle, IEnumerable<Pond> allPonds)
        {
            var totalProduction = CalculateTotalProduction(cycle, allPonds);
            var pricePerKg = 15m; // Estimated price per kg
            return totalProduction * pricePerKg;
        }

        private decimal CalculateBudgetUtilization(ProductionCycle cycle)
        {
            // This would calculate actual costs vs budget
            // For now, return a placeholder value
            return 0.75m; // 75% budget utilization
        }

        private void UpdateSummary(List<ProductionReportItem> reportData)
        {
            var totalCycles = reportData.Count;
            var activeCycles = reportData.Count(r => r.Status == "Ù†Ø´Ø·");
            var completedCycles = reportData.Count(r => r.Status == "Ù…ÙƒØªÙ…Ù„");
            var totalProduction = reportData.Sum(r => r.TotalProduction);
            var averageProductivity = reportData.Where(r => r.Productivity > 0)
                                                .Average(r => (double)r.Productivity);
            var totalRevenue = reportData.Sum(r => r.EstimatedRevenue);

            totalCyclesLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø¯ÙˆØ±Ø§Øª: {totalCycles}";
            activeCyclesLabel.Text = $"Ø§Ù„Ø¯ÙˆØ±Ø§Øª Ø§Ù„Ù†Ø´Ø·Ø©: {activeCycles}";
            completedCyclesLabel.Text = $"Ø§Ù„Ø¯ÙˆØ±Ø§Øª Ø§Ù„Ù…ÙƒØªÙ…Ù„Ø©: {completedCycles}";
            totalProductionLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø¥Ù†ØªØ§Ø¬: {totalProduction:N2} ÙƒØ¬Ù…";
            averageProductivityLabel.Text = $"Ù…ØªÙˆØ³Ø· Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©: {averageProductivity:N3} ÙƒØ¬Ù…/ÙŠÙˆÙ…";
            totalRevenueLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø¥ÙŠØ±Ø§Ø¯ Ø§Ù„Ù…ØªÙˆÙ‚Ø¹: {totalRevenue:C}";
        }

        private void ExportToExcel_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("ØªØµØ¯ÙŠØ± Ø¥Ù„Ù‰ Excel - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Print_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("Ø§Ù„Ø·Ø¨Ø§Ø¹Ø© - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCycleDetails_Click(object? sender, EventArgs e)
        {
            if (productionDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ø¯ÙˆØ±Ø© Ø¥Ù†ØªØ§Ø¬ÙŠØ© Ù…Ù† Ø§Ù„Ù‚Ø§Ø¦Ù…Ø©", "ØªÙ†Ø¨ÙŠÙ‡",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("ØªÙØ§ØµÙŠÙ„ Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ© - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCostAnalysis_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("ØªØ­Ù„ÙŠÙ„ Ø§Ù„ØªÙƒØ§Ù„ÙŠÙ - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    public class ProductionReportItem
    {
        public string CycleName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public int Duration { get; set; }
        public int PondCount { get; set; }
        public int TotalFish { get; set; }
        public decimal TotalProduction { get; set; }
        public decimal Productivity { get; set; }
        public decimal EstimatedRevenue { get; set; }
        public decimal BudgetUtilization { get; set; }
    }
}



