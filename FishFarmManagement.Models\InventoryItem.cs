using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    [Table("InventoryItems")]
    public class InventoryItem
    {
        [Key]
        public int ItemId { get; set; }

        [Required]
        [StringLength(50)]
        public string ItemCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        [StringLength(200)]
        public string? ItemNameEn { get; set; }

        [Required]
        [StringLength(100)]
        public string Category { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Unit { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,3)")]
        public decimal CurrentQuantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal ReorderLevel { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal MaxStockLevel { get; set; }

        [Required]
        [StringLength(50)]
        public string Status { get; set; } = "نشط";

        [StringLength(200)]
        public string? Supplier { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(100)]
        public string? Location { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime LastUpdated { get; set; } = DateTime.Now;

        [StringLength(100)]
        public string? CreatedBy { get; set; }

        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        // Calculated properties
        [NotMapped]
        public decimal TotalValue => CurrentQuantity * UnitCost;

        [NotMapped]
        public bool IsLowStock => CurrentQuantity <= ReorderLevel;

        [NotMapped]
        public bool IsOverStock => CurrentQuantity >= MaxStockLevel;

        [NotMapped]
        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now;

        [NotMapped]
        public bool IsExpiringSoon => ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.Now.AddDays(30);

        [NotMapped]
        public string StockStatus
        {
            get
            {
                if (IsExpired) return "منتهي الصلاحية";
                if (IsExpiringSoon) return "قريب الانتهاء";
                if (IsLowStock) return "مخزون منخفض";
                if (IsOverStock) return "مخزون زائد";
                return "طبيعي";
            }
        }

        [NotMapped]
        public int DaysToExpiry
        {
            get
            {
                if (!ExpiryDate.HasValue) return int.MaxValue;
                return (ExpiryDate.Value - DateTime.Now).Days;
            }
        }
    }
}
