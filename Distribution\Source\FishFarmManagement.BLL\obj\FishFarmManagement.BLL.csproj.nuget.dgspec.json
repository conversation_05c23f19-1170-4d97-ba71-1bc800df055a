{"format": 1, "restore": {"H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.BLL\\FishFarmManagement.BLL.csproj": {}}, "projects": {"H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.BLL\\FishFarmManagement.BLL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.BLL\\FishFarmManagement.BLL.csproj", "projectName": "FishFarmManagement.BLL", "projectPath": "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.BLL\\FishFarmManagement.BLL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.BLL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.DAL\\FishFarmManagement.DAL.csproj": {"projectPath": "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.DAL\\FishFarmManagement.DAL.csproj"}, "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj": {"projectPath": "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.303/PortableRuntimeIdentifierGraph.json"}}}, "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.DAL\\FishFarmManagement.DAL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.DAL\\FishFarmManagement.DAL.csproj", "projectName": "FishFarmManagement.DAL", "projectPath": "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.DAL\\FishFarmManagement.DAL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.DAL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj": {"projectPath": "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.1, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.303/PortableRuntimeIdentifierGraph.json"}}}, "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj", "projectName": "FishFarmManagement.Models", "projectPath": "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "H:\\account pro\\fish accounting & management\\fish accounting & management\\Distribution\\Source\\FishFarmManagement.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.303/PortableRuntimeIdentifierGraph.json"}}}}}