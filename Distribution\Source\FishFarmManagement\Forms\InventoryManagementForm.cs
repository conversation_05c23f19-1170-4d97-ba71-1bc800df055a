﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.Forms
{
    public partial class InventoryManagementForm : Form
    {
        private readonly ILogger<InventoryManagementForm> _logger;
        private DataGridView inventoryGrid;
        private ComboBox categoryComboBox;
        private TextBox searchTextBox;
        private Button addButton;
        private Button editButton;
        private Button deleteButton;
        private Button refreshButton;
        private Label totalItemsLabel;
        private Label lowStockLabel;

        public InventoryManagementForm(ILogger<InventoryManagementForm> logger)
        {
            _logger = logger;
            InitializeComponent();
            LoadInventoryData();
        }

        private void InitializeComponent()
        {
            this.Text = "Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ù…Ø®Ø²ÙˆÙ†";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
            SetupLayout();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(52, 152, 219),
                Padding = new Padding(20, 10, 20, 10)
            };

            var titleLabel = new Label
            {
                Text = "Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ù…Ø®Ø²ÙˆÙ†",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 25)
            };

            headerPanel.Controls.Add(titleLabel);

            // Toolbar panel
            var toolbarPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(236, 240, 241),
                Padding = new Padding(20, 10, 20, 10)
            };

            // Search controls
            var searchLabel = new Label
            {
                Text = "Ø§Ù„Ø¨Ø­Ø«:",
                AutoSize = true,
                Location = new Point(20, 18),
                Font = new Font("Segoe UI", 10F)
            };

            searchTextBox = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(70, 15),
                Font = new Font("Segoe UI", 10F)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            var categoryLabel = new Label
            {
                Text = "Ø§Ù„ÙØ¦Ø©:",
                AutoSize = true,
                Location = new Point(290, 18),
                Font = new Font("Segoe UI", 10F)
            };

            categoryComboBox = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(330, 15),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 10F)
            };
            categoryComboBox.Items.AddRange(new[] { "Ø§Ù„ÙƒÙ„", "Ø§Ù„Ø£Ø¹Ù„Ø§Ù", "Ø§Ù„Ø£Ø¯ÙˆÙŠØ©", "Ø§Ù„Ù…Ø¹Ø¯Ø§Øª", "Ø§Ù„Ù…ÙˆØ§Ø¯ Ø§Ù„ÙƒÙŠÙ…ÙŠØ§Ø¦ÙŠØ©", "Ø£Ø®Ø±Ù‰" });
            categoryComboBox.SelectedIndex = 0;
            categoryComboBox.SelectedIndexChanged += CategoryComboBox_SelectedIndexChanged;

            // Action buttons
            addButton = new Button
            {
                Text = "Ø¥Ø¶Ø§ÙØ© ØµÙ†Ù",
                Size = new Size(100, 30),
                Location = new Point(500, 12),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };
            addButton.Click += AddButton_Click;

            editButton = new Button
            {
                Text = "ØªØ¹Ø¯ÙŠÙ„",
                Size = new Size(80, 30),
                Location = new Point(610, 12),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F),
                Enabled = false
            };
            editButton.Click += EditButton_Click;

            deleteButton = new Button
            {
                Text = "Ø­Ø°Ù",
                Size = new Size(80, 30),
                Location = new Point(700, 12),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F),
                Enabled = false
            };
            deleteButton.Click += DeleteButton_Click;

            refreshButton = new Button
            {
                Text = "ØªØ­Ø¯ÙŠØ«",
                Size = new Size(80, 30),
                Location = new Point(790, 12),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };
            refreshButton.Click += RefreshButton_Click;

            toolbarPanel.Controls.AddRange(new Control[] 
            { 
                searchLabel, searchTextBox, categoryLabel, categoryComboBox,
                addButton, editButton, deleteButton, refreshButton 
            });

            // Status panel
            var statusPanel = new Panel
            {
                Height = 40,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(236, 240, 241),
                Padding = new Padding(20, 10, 20, 10)
            };

            totalItemsLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø£ØµÙ†Ø§Ù: 0",
                AutoSize = true,
                Location = new Point(20, 12),
                Font = new Font("Segoe UI", 9F)
            };

            lowStockLabel = new Label
            {
                Text = "Ø£ØµÙ†Ø§Ù Ù…Ù†Ø®ÙØ¶Ø© Ø§Ù„Ù…Ø®Ø²ÙˆÙ†: 0",
                AutoSize = true,
                Location = new Point(200, 12),
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.FromArgb(231, 76, 60)
            };

            statusPanel.Controls.AddRange(new Control[] { totalItemsLabel, lowStockLabel });

            // Data grid
            inventoryGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                Font = new Font("Segoe UI", 9F)
            };

            // Setup columns
            SetupGridColumns();

            inventoryGrid.SelectionChanged += InventoryGrid_SelectionChanged;
            inventoryGrid.CellDoubleClick += InventoryGrid_CellDoubleClick;

            this.Controls.AddRange(new Control[] { inventoryGrid, statusPanel, toolbarPanel, headerPanel });
        }

        private void SetupGridColumns()
        {
            inventoryGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "Ø§Ù„Ø±Ù‚Ù…", Width = 60, Visible = false },
                new DataGridViewTextBoxColumn { Name = "ItemCode", HeaderText = "ÙƒÙˆØ¯ Ø§Ù„ØµÙ†Ù", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "ItemName", HeaderText = "Ø§Ø³Ù… Ø§Ù„ØµÙ†Ù", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "Category", HeaderText = "Ø§Ù„ÙØ¦Ø©", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Unit", HeaderText = "Ø§Ù„ÙˆØ­Ø¯Ø©", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "CurrentStock", HeaderText = "Ø§Ù„Ù…Ø®Ø²ÙˆÙ† Ø§Ù„Ø­Ø§Ù„ÙŠ", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "MinimumStock", HeaderText = "Ø§Ù„Ø­Ø¯ Ø§Ù„Ø£Ø¯Ù†Ù‰", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "UnitPrice", HeaderText = "Ø³Ø¹Ø± Ø§Ù„ÙˆØ­Ø¯Ø©", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "TotalValue", HeaderText = "Ø§Ù„Ù‚ÙŠÙ…Ø© Ø§Ù„Ø¥Ø¬Ù…Ø§Ù„ÙŠØ©", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "LastUpdated", HeaderText = "Ø¢Ø®Ø± ØªØ­Ø¯ÙŠØ«", Width = 120 }
            });

            // Format columns
            inventoryGrid.Columns["CurrentStock"].DefaultCellStyle.Format = "N2";
            inventoryGrid.Columns["MinimumStock"].DefaultCellStyle.Format = "N2";
            inventoryGrid.Columns["UnitPrice"].DefaultCellStyle.Format = "C2";
            inventoryGrid.Columns["TotalValue"].DefaultCellStyle.Format = "C2";
            inventoryGrid.Columns["LastUpdated"].DefaultCellStyle.Format = "dd/MM/yyyy";

            // Set column alignments
            inventoryGrid.Columns["CurrentStock"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            inventoryGrid.Columns["MinimumStock"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            inventoryGrid.Columns["UnitPrice"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            inventoryGrid.Columns["TotalValue"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        }

        private void SetupLayout()
        {
            // Apply RTL layout
            foreach (Control control in this.Controls)
            {
                control.RightToLeft = RightToLeft.Yes;
            }
        }

        private void LoadInventoryData()
        {
            try
            {
                // Sample data - replace with actual database query
                var sampleData = new List<dynamic>
                {
                    new { Id = 1, ItemCode = "FEED001", ItemName = "Ø¹Ù„Ù Ù†Ù…Ùˆ", Category = "Ø§Ù„Ø£Ø¹Ù„Ø§Ù", Unit = "ÙƒÙŠØ³", CurrentStock = 50, MinimumStock = 10, UnitPrice = 150.00m, TotalValue = 7500.00m, LastUpdated = DateTime.Now.AddDays(-2) },
                    new { Id = 2, ItemCode = "MED001", ItemName = "Ù…Ø¶Ø§Ø¯ Ø­ÙŠÙˆÙŠ", Category = "Ø§Ù„Ø£Ø¯ÙˆÙŠØ©", Unit = "Ø²Ø¬Ø§Ø¬Ø©", CurrentStock = 5, MinimumStock = 3, UnitPrice = 75.00m, TotalValue = 375.00m, LastUpdated = DateTime.Now.AddDays(-1) },
                    new { Id = 3, ItemCode = "EQUIP001", ItemName = "Ù…Ø¶Ø®Ø© Ù‡ÙˆØ§Ø¡", Category = "Ø§Ù„Ù…Ø¹Ø¯Ø§Øª", Unit = "Ù‚Ø·Ø¹Ø©", CurrentStock = 2, MinimumStock = 1, UnitPrice = 500.00m, TotalValue = 1000.00m, LastUpdated = DateTime.Now.AddDays(-5) },
                    new { Id = 4, ItemCode = "CHEM001", ItemName = "ÙƒÙ„ÙˆØ±", Category = "Ø§Ù„Ù…ÙˆØ§Ø¯ Ø§Ù„ÙƒÙŠÙ…ÙŠØ§Ø¦ÙŠØ©", Unit = "ÙƒÙŠÙ„Ùˆ", CurrentStock = 20, MinimumStock = 5, UnitPrice = 25.00m, TotalValue = 500.00m, LastUpdated = DateTime.Now.AddDays(-3) }
                };

                inventoryGrid.DataSource = sampleData;
                UpdateStatusLabels();
                HighlightLowStockItems();

                _logger.LogInformation("ØªÙ… ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ù…Ø®Ø²ÙˆÙ† Ø¨Ù†Ø¬Ø§Ø­");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ù…Ø®Ø²ÙˆÙ†");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ù…Ø®Ø²ÙˆÙ†: {ex.Message}", "Ø®Ø·Ø£", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateStatusLabels()
        {
            var totalItems = inventoryGrid.Rows.Count;
            var lowStockItems = 0;

            foreach (DataGridViewRow row in inventoryGrid.Rows)
            {
                if (row.Cells["CurrentStock"].Value != null && row.Cells["MinimumStock"].Value != null)
                {
                    var currentStock = Convert.ToDecimal(row.Cells["CurrentStock"].Value);
                    var minimumStock = Convert.ToDecimal(row.Cells["MinimumStock"].Value);
                    
                    if (currentStock <= minimumStock)
                        lowStockItems++;
                }
            }

            totalItemsLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø£ØµÙ†Ø§Ù: {totalItems}";
            lowStockLabel.Text = $"Ø£ØµÙ†Ø§Ù Ù…Ù†Ø®ÙØ¶Ø© Ø§Ù„Ù…Ø®Ø²ÙˆÙ†: {lowStockItems}";
        }

        private void HighlightLowStockItems()
        {
            foreach (DataGridViewRow row in inventoryGrid.Rows)
            {
                if (row.Cells["CurrentStock"].Value != null && row.Cells["MinimumStock"].Value != null)
                {
                    var currentStock = Convert.ToDecimal(row.Cells["CurrentStock"].Value);
                    var minimumStock = Convert.ToDecimal(row.Cells["MinimumStock"].Value);
                    
                    if (currentStock <= minimumStock)
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(169, 68, 66);
                    }
                }
            }
        }

        // Event handlers
        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            // Implement search functionality
            FilterData();
        }

        private void CategoryComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            // Implement category filtering
            FilterData();
        }

        private void FilterData()
        {
            // TODO: Implement filtering logic
            // This would filter the data based on search text and selected category
        }

        private void InventoryGrid_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSelection = inventoryGrid.SelectedRows.Count > 0;
            editButton.Enabled = hasSelection;
            deleteButton.Enabled = hasSelection;
        }

        private void InventoryGrid_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditSelectedItem();
            }
        }

        private void AddButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var form = new InventoryItemAddEditForm(_logger);
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadInventoryData();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¶Ø§ÙØ© Ø§Ù„ØµÙ†Ù");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ø§Ù„Ù†Ù…ÙˆØ°Ø¬: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object? sender, EventArgs e)
        {
            EditSelectedItem();
        }

        private void EditSelectedItem()
        {
            if (inventoryGrid.SelectedRows.Count > 0)
            {
                // TODO: Open edit inventory item form
                var selectedRow = inventoryGrid.SelectedRows[0];
                var itemName = selectedRow.Cells["ItemName"].Value?.ToString();
                MessageBox.Show($"Ø³ÙŠØªÙ… ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„ØµÙ†Ù: {itemName}", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DeleteButton_Click(object? sender, EventArgs e)
        {
            if (inventoryGrid.SelectedRows.Count > 0)
            {
                var selectedRow = inventoryGrid.SelectedRows[0];
                var itemName = selectedRow.Cells["ItemName"].Value?.ToString();
                
                var result = MessageBox.Show($"Ù‡Ù„ Ø£Ù†Øª Ù…ØªØ£ÙƒØ¯ Ù…Ù† Ø­Ø°Ù Ø§Ù„ØµÙ†Ù: {itemName}ØŸ", 
                    "ØªØ£ÙƒÙŠØ¯ Ø§Ù„Ø­Ø°Ù", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    // TODO: Implement delete functionality
                    MessageBox.Show("ØªÙ… Ø­Ø°Ù Ø§Ù„ØµÙ†Ù Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadInventoryData();
                }
            }
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            LoadInventoryData();
        }
    }
}



