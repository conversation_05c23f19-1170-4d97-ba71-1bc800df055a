﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¶Ø§ÙØ©/ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„Ø£Ø­ÙˆØ§Ø¶
    /// Pond add/edit form
    /// </summary>
    public partial class PondAddEditForm : Form
    {
        private readonly IPondService _pondService;
        private readonly ILogger _logger;
        private readonly Pond? _existingPond;
        private readonly bool _isEditMode;

        // UI Controls
        private TextBox pondNumberTextBox;
        private NumericUpDown cycleIdNumericUpDown;
        private NumericUpDown fishCountNumericUpDown;
        private NumericUpDown averageWeightNumericUpDown;
        private DateTimePicker stockingDatePicker;
        private DateTimePicker expectedHarvestDatePicker;
        private ComboBox statusComboBox;
        private TextBox notesTextBox;
        private Button saveButton;
        private Button cancelButton;
        private CheckBox hasExpectedHarvestDateCheckBox;

        public PondAddEditForm(IPondService pondService, ILogger logger, Pond? existingPond = null)
        {
            _pondService = pondService ?? throw new ArgumentNullException(nameof(pondService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _existingPond = existingPond;
            _isEditMode = existingPond != null;

            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = _isEditMode ? "ØªØ¹Ø¯ÙŠÙ„ Ø­ÙˆØ¶" : "Ø¥Ø¶Ø§ÙØ© Ø­ÙˆØ¶ Ø¬Ø¯ÙŠØ¯";
            this.Size = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
            LayoutControls();
        }

        private void CreateControls()
        {
            // Pond Number
            var pondNumberLabel = new Label
            {
                Text = "Ø±Ù‚Ù… Ø§Ù„Ø­ÙˆØ¶:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            pondNumberTextBox = new TextBox
            {
                Size = new Size(200, 23),
                MaxLength = 50
            };

            // Cycle ID
            var cycleIdLabel = new Label
            {
                Text = "Ù…Ø¹Ø±Ù Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©:",
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            cycleIdNumericUpDown = new NumericUpDown
            {
                Size = new Size(200, 23),
                Minimum = 1,
                Maximum = int.MaxValue,
                Value = 1
            };

            // Fish Count
            var fishCountLabel = new Label
            {
                Text = "Ø¹Ø¯Ø¯ Ø§Ù„Ø£Ø³Ù…Ø§Ùƒ:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            fishCountNumericUpDown = new NumericUpDown
            {
                Size = new Size(200, 23),
                Minimum = 0,
                Maximum = int.MaxValue,
                ThousandsSeparator = true
            };

            // Average Weight
            var averageWeightLabel = new Label
            {
                Text = "Ù…ØªÙˆØ³Ø· Ø§Ù„ÙˆØ²Ù† (ÙƒØ¬Ù…):",
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            averageWeightNumericUpDown = new NumericUpDown
            {
                Size = new Size(200, 23),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 3,
                Increment = 0.001m
            };

            // Stocking Date
            var stockingDateLabel = new Label
            {
                Text = "ØªØ§Ø±ÙŠØ® Ø§Ù„ØªØ®Ø²ÙŠÙ†:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            stockingDatePicker = new DateTimePicker
            {
                Size = new Size(200, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // Expected Harvest Date
            var expectedHarvestDateLabel = new Label
            {
                Text = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø­ØµØ§Ø¯ Ø§Ù„Ù…ØªÙˆÙ‚Ø¹:",
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            hasExpectedHarvestDateCheckBox = new CheckBox
            {
                Text = "ØªØ­Ø¯ÙŠØ¯ ØªØ§Ø±ÙŠØ® Ø§Ù„Ø­ØµØ§Ø¯ Ø§Ù„Ù…ØªÙˆÙ‚Ø¹",
                Size = new Size(200, 23),
                CheckAlign = ContentAlignment.MiddleRight
            };
            hasExpectedHarvestDateCheckBox.CheckedChanged += HasExpectedHarvestDateCheckBox_CheckedChanged;

            expectedHarvestDatePicker = new DateTimePicker
            {
                Size = new Size(200, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now.AddMonths(6),
                Enabled = false
            };

            // Status
            var statusLabel = new Label
            {
                Text = "Ø§Ù„Ø­Ø§Ù„Ø©:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            statusComboBox = new ComboBox
            {
                Size = new Size(200, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            statusComboBox.Items.AddRange(new[] { "Ù†Ø´Ø·", "Ù…Ø­ØµÙˆØ¯", "ÙØ§Ø±Øº", "ØµÙŠØ§Ù†Ø©" });
            statusComboBox.SelectedIndex = 0;

            // Notes
            var notesLabel = new Label
            {
                Text = "Ø§Ù„Ù…Ù„Ø§Ø­Ø¸Ø§Øª:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            notesTextBox = new TextBox
            {
                Size = new Size(350, 80),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                MaxLength = 1000
            };

            // Buttons
            saveButton = new Button
            {
                Text = _isEditMode ? "Ø­ÙØ¸ Ø§Ù„ØªØ¹Ø¯ÙŠÙ„Ø§Øª" : "Ø¥Ø¶Ø§ÙØ©",
                Size = new Size(100, 30),
                DialogResult = DialogResult.OK
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "Ø¥Ù„ØºØ§Ø¡",
                Size = new Size(100, 30),
                DialogResult = DialogResult.Cancel
            };

            this.Controls.AddRange(new Control[]
            {
                pondNumberLabel, pondNumberTextBox,
                cycleIdLabel, cycleIdNumericUpDown,
                fishCountLabel, fishCountNumericUpDown,
                averageWeightLabel, averageWeightNumericUpDown,
                stockingDateLabel, stockingDatePicker,
                expectedHarvestDateLabel, hasExpectedHarvestDateCheckBox, expectedHarvestDatePicker,
                statusLabel, statusComboBox,
                notesLabel, notesTextBox,
                saveButton, cancelButton
            });
        }

        private void LayoutControls()
        {
            int x = 20;
            int y = 20;
            int spacing = 35;

            // Pond Number
            this.Controls[0].Location = new Point(x + 250, y);
            this.Controls[1].Location = new Point(x + 30, y);
            y += spacing;

            // Cycle ID
            this.Controls[2].Location = new Point(x + 230, y);
            this.Controls[3].Location = new Point(x + 30, y);
            y += spacing;

            // Fish Count
            this.Controls[4].Location = new Point(x + 250, y);
            this.Controls[5].Location = new Point(x + 30, y);
            y += spacing;

            // Average Weight
            this.Controls[6].Location = new Point(x + 230, y);
            this.Controls[7].Location = new Point(x + 30, y);
            y += spacing;

            // Stocking Date
            this.Controls[8].Location = new Point(x + 250, y);
            this.Controls[9].Location = new Point(x + 30, y);
            y += spacing;

            // Expected Harvest Date
            this.Controls[10].Location = new Point(x + 230, y);
            this.Controls[11].Location = new Point(x + 30, y);
            y += spacing;
            this.Controls[12].Location = new Point(x + 30, y);
            y += spacing;

            // Status
            this.Controls[13].Location = new Point(x + 250, y);
            this.Controls[14].Location = new Point(x + 30, y);
            y += spacing;

            // Notes
            this.Controls[15].Location = new Point(x + 250, y);
            this.Controls[16].Location = new Point(x + 30, y);
            y += 100;

            // Buttons
            this.Controls[17].Location = new Point(x + 130, y);
            this.Controls[18].Location = new Point(x + 250, y);
        }

        private void LoadData()
        {
            if (_existingPond != null)
            {
                pondNumberTextBox.Text = _existingPond.PondNumber;
                cycleIdNumericUpDown.Value = _existingPond.CycleId;
                fishCountNumericUpDown.Value = _existingPond.FishCount;
                averageWeightNumericUpDown.Value = _existingPond.AverageWeight;
                stockingDatePicker.Value = _existingPond.StockingDate;
                
                if (_existingPond.ExpectedHarvestDate.HasValue)
                {
                    hasExpectedHarvestDateCheckBox.Checked = true;
                    expectedHarvestDatePicker.Value = _existingPond.ExpectedHarvestDate.Value;
                }

                statusComboBox.SelectedItem = _existingPond.Status;
                notesTextBox.Text = _existingPond.Notes;
            }
        }

        private void HasExpectedHarvestDateCheckBox_CheckedChanged(object? sender, EventArgs e)
        {
            expectedHarvestDatePicker.Enabled = hasExpectedHarvestDateCheckBox.Checked;
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                this.Cursor = Cursors.WaitCursor;
                saveButton.Enabled = false;

                var pond = _existingPond ?? new Pond();
                
                pond.PondNumber = pondNumberTextBox.Text.Trim();
                pond.CycleId = (int)cycleIdNumericUpDown.Value;
                pond.FishCount = (int)fishCountNumericUpDown.Value;
                pond.AverageWeight = averageWeightNumericUpDown.Value;
                pond.StockingDate = stockingDatePicker.Value;
                pond.ExpectedHarvestDate = hasExpectedHarvestDateCheckBox.Checked ? expectedHarvestDatePicker.Value : null;
                pond.Status = statusComboBox.SelectedItem?.ToString() ?? "Ù†Ø´Ø·";
                pond.Notes = notesTextBox.Text.Trim();

                if (_isEditMode)
                {
                    await _pondService.UpdateAsync(pond);
                    MessageBox.Show("ØªÙ… ØªØ­Ø¯ÙŠØ« Ø§Ù„Ø­ÙˆØ¶ Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _pondService.AddAsync(pond);
                    MessageBox.Show("ØªÙ… Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ø­ÙˆØ¶ Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­ÙØ¸ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø­ÙˆØ¶");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø­ÙØ¸ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                saveButton.Enabled = true;
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(pondNumberTextBox.Text))
            {
                MessageBox.Show("Ø±Ù‚Ù… Ø§Ù„Ø­ÙˆØ¶ Ù…Ø·Ù„ÙˆØ¨", "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                pondNumberTextBox.Focus();
                return false;
            }

            if (cycleIdNumericUpDown.Value <= 0)
            {
                MessageBox.Show("Ù…Ø¹Ø±Ù Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ© Ù…Ø·Ù„ÙˆØ¨", "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cycleIdNumericUpDown.Focus();
                return false;
            }

            if (statusComboBox.SelectedIndex < 0)
            {
                MessageBox.Show("ÙŠØ¬Ø¨ Ø§Ø®ØªÙŠØ§Ø± Ø­Ø§Ù„Ø© Ø§Ù„Ø­ÙˆØ¶", "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                statusComboBox.Focus();
                return false;
            }

            return true;
        }
    }
}



