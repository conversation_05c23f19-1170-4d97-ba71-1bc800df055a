using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة الموظفين
    /// Employee management service interface
    /// </summary>
    public interface IEmployeeService : IBusinessService<Employee, int>
    {
        /// <summary>
        /// الحصول على الموظفين النشطين
        /// Get active employees
        /// </summary>
        Task<IEnumerable<Employee>> GetActiveEmployeesAsync();

        /// <summary>
        /// الحصول على موظف بالرقم الوطني
        /// Get employee by national ID
        /// </summary>
        Task<Employee?> GetByNationalIdAsync(string nationalId);

        /// <summary>
        /// البحث في الموظفين
        /// Search employees
        /// </summary>
        Task<IEnumerable<Employee>> SearchEmployeesAsync(string searchTerm);

        /// <summary>
        /// الحصول على موظفين بالمنصب
        /// Get employees by position
        /// </summary>
        Task<IEnumerable<Employee>> GetByPositionAsync(string position);

        /// <summary>
        /// حساب الراتب الصافي للموظف
        /// Calculate net salary for employee
        /// </summary>
        Task<decimal> CalculateNetSalaryAsync(int employeeId, decimal allowances = 0, decimal deductions = 0);

        /// <summary>
        /// إنشاء كشف راتب جديد
        /// Create new payroll record
        /// </summary>
        Task<Payroll> CreatePayrollAsync(int employeeId, int cycleId, int month, int year, 
            decimal allowances = 0, decimal deductions = 0, int workingDays = 30, 
            decimal overtimeHours = 0, decimal overtimeRate = 0);

        /// <summary>
        /// الحصول على كشوف رواتب الموظف
        /// Get employee payroll records
        /// </summary>
        Task<IEnumerable<Payroll>> GetEmployeePayrollsAsync(int employeeId);

        /// <summary>
        /// الحصول على كشوف رواتب شهر معين
        /// Get payroll records for specific month
        /// </summary>
        Task<IEnumerable<Payroll>> GetMonthlyPayrollsAsync(int month, int year);

        /// <summary>
        /// تحديث حالة الموظف
        /// Update employee status
        /// </summary>
        Task<bool> UpdateEmployeeStatusAsync(int employeeId, string status, DateTime? leaveDate = null);

        /// <summary>
        /// حساب إجمالي رواتب الشهر
        /// Calculate total monthly salaries
        /// </summary>
        Task<decimal> CalculateMonthlyTotalSalariesAsync(int month, int year);
    }
}
