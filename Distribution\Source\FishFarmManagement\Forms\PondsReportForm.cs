﻿using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;
using FishFarmManagement.BLL.Services.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ ØªÙ‚Ø±ÙŠØ± Ø§Ù„Ø£Ø­ÙˆØ§Ø¶
    /// Ponds report form
    /// </summary>
    public partial class PondsReportForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;

        // UI Controls
        private ComboBox statusFilterComboBox;
        private ComboBox cycleFilterComboBox;
        private Button generateReportButton;
        private DataGridView pondsDataGridView;
        private Label totalPondsLabel;
        private Label activePondsLabel;
        private Label totalFishLabel;
        private Label totalBiomassLabel;
        private Label averageProductivityLabel;

        public PondsReportForm(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            InitializeComponent();
            LoadInitialData();
        }

        private void InitializeComponent()
        {
            this.Text = "ØªÙ‚Ø±ÙŠØ± Ø§Ù„Ø£Ø­ÙˆØ§Ø¶";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
        }

        private void CreateControls()
        {
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 4,
                ColumnCount = 1,
                Padding = new Padding(20)
            };

            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 60));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 50));

            // Filter Panel
            var filterPanel = CreateFilterPanel();
            mainPanel.Controls.Add(filterPanel, 0, 0);

            // Data Grid
            pondsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F)
            };

            SetupDataGridColumns();
            mainPanel.Controls.Add(pondsDataGridView, 0, 1);

            // Summary Panel
            var summaryPanel = CreateSummaryPanel();
            mainPanel.Controls.Add(summaryPanel, 0, 2);

            // Buttons Panel
            var buttonsPanel = CreateButtonsPanel();
            mainPanel.Controls.Add(buttonsPanel, 0, 3);

            this.Controls.Add(mainPanel);
        }

        private Panel CreateFilterPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            var statusLabel = new Label
            {
                Text = "Ø­Ø§Ù„Ø© Ø§Ù„Ø­ÙˆØ¶:",
                Location = new Point(700, 15),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            statusFilterComboBox = new ComboBox
            {
                Location = new Point(550, 15),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            var cycleLabel = new Label
            {
                Text = "Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©:",
                Location = new Point(450, 15),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            cycleFilterComboBox = new ComboBox
            {
                Location = new Point(300, 15),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            generateReportButton = new Button
            {
                Text = "Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±",
                Location = new Point(180, 15),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            generateReportButton.Click += GenerateReport_Click;

            panel.Controls.AddRange(new Control[]
            {
                statusLabel, statusFilterComboBox,
                cycleLabel, cycleFilterComboBox,
                generateReportButton
            });

            return panel;
        }

        private void SetupDataGridColumns()
        {
            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PondNumber",
                HeaderText = "Ø±Ù‚Ù… Ø§Ù„Ø­ÙˆØ¶",
                DataPropertyName = "PondNumber",
                Width = 80
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "Ø§Ù„Ø­Ø§Ù„Ø©",
                DataPropertyName = "Status",
                Width = 80
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FishCount",
                HeaderText = "Ø¹Ø¯Ø¯ Ø§Ù„Ø£Ø³Ù…Ø§Ùƒ",
                DataPropertyName = "FishCount",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N0" }
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AverageWeight",
                HeaderText = "Ù…ØªÙˆØ³Ø· Ø§Ù„ÙˆØ²Ù† (Ø¬Ù…)",
                DataPropertyName = "AverageWeight",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalBiomass",
                HeaderText = "Ø§Ù„ÙƒØªÙ„Ø© Ø§Ù„Ø­ÙŠÙˆÙŠØ© (ÙƒØ¬Ù…)",
                DataPropertyName = "TotalBiomass",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "StockingDate",
                HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„ØªØ®Ø²ÙŠÙ†",
                DataPropertyName = "StockingDate",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DaysInProduction",
                HeaderText = "Ø£ÙŠØ§Ù… Ø§Ù„Ø¥Ù†ØªØ§Ø¬",
                DataPropertyName = "DaysInProduction",
                Width = 80
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Productivity",
                HeaderText = "Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ© (ÙƒØ¬Ù…/ÙŠÙˆÙ…)",
                DataPropertyName = "Productivity",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N3" }
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CycleName",
                HeaderText = "Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©",
                DataPropertyName = "CycleName",
                Width = 120
            });
        }

        private Panel CreateSummaryPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            totalPondsLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø£Ø­ÙˆØ§Ø¶: 0",
                Location = new Point(750, 10),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            activePondsLabel = new Label
            {
                Text = "Ø§Ù„Ø£Ø­ÙˆØ§Ø¶ Ø§Ù„Ù†Ø´Ø·Ø©: 0",
                Location = new Point(750, 35),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Green
            };

            totalFishLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø£Ø³Ù…Ø§Ùƒ: 0",
                Location = new Point(550, 10),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            totalBiomassLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„ÙƒØªÙ„Ø© Ø§Ù„Ø­ÙŠÙˆÙŠØ©: 0 ÙƒØ¬Ù…",
                Location = new Point(550, 35),
                Size = new Size(180, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            averageProductivityLabel = new Label
            {
                Text = "Ù…ØªÙˆØ³Ø· Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©: 0 ÙƒØ¬Ù…/ÙŠÙˆÙ…",
                Location = new Point(300, 22),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Blue
            };

            panel.Controls.AddRange(new Control[]
            {
                totalPondsLabel, activePondsLabel, totalFishLabel,
                totalBiomassLabel, averageProductivityLabel
            });

            return panel;
        }

        private Panel CreateButtonsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill
            };

            var exportButton = new Button
            {
                Text = "ØªØµØ¯ÙŠØ± Ø¥Ù„Ù‰ Excel",
                Location = new Point(20, 10),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            exportButton.Click += ExportToExcel_Click;

            var printButton = new Button
            {
                Text = "Ø·Ø¨Ø§Ø¹Ø©",
                Location = new Point(160, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            printButton.Click += Print_Click;

            var detailsButton = new Button
            {
                Text = "ØªÙØ§ØµÙŠÙ„ Ø§Ù„Ø­ÙˆØ¶",
                Location = new Point(260, 10),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(241, 196, 15),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            detailsButton.Click += ShowPondDetails_Click;

            panel.Controls.AddRange(new Control[] { exportButton, printButton, detailsButton });
            return panel;
        }

        private async void LoadInitialData()
        {
            try
            {
                // Load status options
                var statusOptions = new[]
                {
                    new { Value = "", Text = "Ø¬Ù…ÙŠØ¹ Ø§Ù„Ø­Ø§Ù„Ø§Øª" },
                    new { Value = "Ù†Ø´Ø·", Text = "Ù†Ø´Ø·" },
                    new { Value = "ÙØ§Ø±Øº", Text = "ÙØ§Ø±Øº" },
                    new { Value = "ØµÙŠØ§Ù†Ø©", Text = "ØµÙŠØ§Ù†Ø©" },
                    new { Value = "Ø¬Ø§Ù‡Ø² Ù„Ù„Ø­ØµØ§Ø¯", Text = "Ø¬Ø§Ù‡Ø² Ù„Ù„Ø­ØµØ§Ø¯" }
                };

                statusFilterComboBox.DataSource = statusOptions;
                statusFilterComboBox.DisplayMember = "Text";
                statusFilterComboBox.ValueMember = "Value";

                // Load production cycles
                var cycles = await _unitOfWork.ProductionCycles.GetAllAsync();
                var cycleOptions = new List<object> { new { Value = 0, Text = "Ø¬Ù…ÙŠØ¹ Ø§Ù„Ø¯ÙˆØ±Ø§Øª" } };
                cycleOptions.AddRange(cycles.Select(c => new { Value = c.Id, Text = c.CycleName }));

                cycleFilterComboBox.DataSource = cycleOptions;
                cycleFilterComboBox.DisplayMember = "Text";
                cycleFilterComboBox.ValueMember = "Value";

                // Generate initial report
                await GenerateReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void private async void private async void GenerateReport_Click(object? sender, EventArgs e)
        {
            await GenerateReport();
        }

        private async Task GenerateReport()
        {
            try
            {
                var selectedStatus = statusFilterComboBox.SelectedValue?.ToString();
                var selectedCycleId = (int)cycleFilterComboBox.SelectedValue;

                // Get ponds data
                var ponds = await _unitOfWork.Ponds.GetAllAsync();
                var cycles = await _unitOfWork.ProductionCycles.GetAllAsync();
                var cyclesDict = cycles.ToDictionary(c => c.Id, c => c);

                // Apply filters
                if (!string.IsNullOrEmpty(selectedStatus))
                    ponds = ponds.Where(p => p.Status == selectedStatus);

                if (selectedCycleId > 0)
                    ponds = ponds.Where(p => p.CycleId == selectedCycleId);

                // Create report data
                var reportData = ponds.Select(p => new PondReportItem
                {
                    PondNumber = p.PondNumber,
                    Status = p.Status,
                    FishCount = p.FishCount,
                    AverageWeight = p.AverageWeight,
                    TotalBiomass = p.FishCount * p.AverageWeight / 1000, // Convert to kg
                    StockingDate = p.StockingDate,
                    DaysInProduction = (DateTime.Now - p.StockingDate).Days,
                    Productivity = CalculateProductivity(p),
                    CycleName = cyclesDict.TryGetValue(p.CycleId, out var cycle) ?
                        cycle.CycleName : "ØºÙŠØ± Ù…Ø­Ø¯Ø¯"
                }).OrderBy(p => p.PondNumber).ToList();

                // Display data
                pondsDataGridView.DataSource = reportData;

                // Update summary
                UpdateSummary(reportData);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private decimal CalculateProductivity(Pond pond)
        {
            if (pond.FishCount == 0)
                return 0;

            var daysInProduction = (DateTime.Now - pond.StockingDate).Days;
            if (daysInProduction <= 0)
                return 0;

            var totalBiomass = pond.FishCount * pond.AverageWeight / 1000; // kg
            return totalBiomass / daysInProduction;
        }

        private void UpdateSummary(List<PondReportItem> reportData)
        {
            var totalPonds = reportData.Count;
            var activePonds = reportData.Count(p => p.Status == "Ù†Ø´Ø·");
            var totalFish = reportData.Sum(p => p.FishCount);
            var totalBiomass = reportData.Sum(p => p.TotalBiomass);
            var averageProductivity = reportData.Where(p => p.Productivity > 0)
                                                .Average(p => (double)p.Productivity);

            totalPondsLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø£Ø­ÙˆØ§Ø¶: {totalPonds}";
            activePondsLabel.Text = $"Ø§Ù„Ø£Ø­ÙˆØ§Ø¶ Ø§Ù„Ù†Ø´Ø·Ø©: {activePonds}";
            totalFishLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø£Ø³Ù…Ø§Ùƒ: {totalFish:N0}";
            totalBiomassLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„ÙƒØªÙ„Ø© Ø§Ù„Ø­ÙŠÙˆÙŠØ©: {totalBiomass:N2} ÙƒØ¬Ù…";
            averageProductivityLabel.Text = $"Ù…ØªÙˆØ³Ø· Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©: {averageProductivity:N3} ÙƒØ¬Ù…/ÙŠÙˆÙ…";
        }

        private void ExportToExcel_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("ØªØµØ¯ÙŠØ± Ø¥Ù„Ù‰ Excel - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Print_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("Ø§Ù„Ø·Ø¨Ø§Ø¹Ø© - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowPondDetails_Click(object? sender, EventArgs e)
        {
            if (pondsDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ø­ÙˆØ¶ Ù…Ù† Ø§Ù„Ù‚Ø§Ø¦Ù…Ø©", "ØªÙ†Ø¨ÙŠÙ‡",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("ØªÙØ§ØµÙŠÙ„ Ø§Ù„Ø­ÙˆØ¶ - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    public class PondReportItem
    {
        public string PondNumber { get; set; }
        public string Status { get; set; }
        public int FishCount { get; set; }
        public decimal AverageWeight { get; set; }
        public decimal TotalBiomass { get; set; }
        public DateTime StockingDate { get; set; }
        public int DaysInProduction { get; set; }
        public decimal Productivity { get; set; }
        public string CycleName { get; set; }
    }
}



