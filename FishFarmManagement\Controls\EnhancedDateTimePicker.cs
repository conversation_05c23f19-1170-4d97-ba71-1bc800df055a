using System;
using System.Drawing;
using System.Globalization;
using System.Windows.Forms;

namespace FishFarmManagement.Controls
{
    /// <summary>
    /// مكون تقويم محسن مع واجهة مستخدم أفضل
    /// Enhanced DateTimePicker with better UI
    /// </summary>
    public partial class EnhancedDateTimePicker : UserControl
    {
        private TextBox dateTextBox;
        private Button calendarButton;
        private MonthCalendar monthCalendar;
        private Form? calendarForm;
        private bool isCalendarVisible = false;
        private DateTime _value = DateTime.Now;
        private bool _showTime = false;
        private DateTimePickerFormat _format = DateTimePickerFormat.Short;

        // Events
        public event EventHandler? ValueChanged;

        // Properties
        public DateTime Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    _value = value;
                    UpdateDisplayText();
                    ValueChanged?.Invoke(this, EventArgs.Empty);
                }
            }
        }

        public bool ShowTime
        {
            get => _showTime;
            set
            {
                if (_showTime != value)
                {
                    _showTime = value;
                    UpdateDisplayText();
                }
            }
        }

        public DateTimePickerFormat Format
        {
            get => _format;
            set
            {
                if (_format != value)
                {
                    _format = value;
                    UpdateDisplayText();
                }
            }
        }

        public DateTime MinDate { get; set; } = DateTime.MinValue;
        public DateTime MaxDate { get; set; } = DateTime.MaxValue;

        public EnhancedDateTimePicker()
        {
            InitializeComponent();
            SetupControls();
            SetupEvents();
            UpdateDisplayText();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(200, 25);
            this.BackColor = Color.White;
            this.BorderStyle = BorderStyle.FixedSingle;
        }

        private void SetupControls()
        {
            // Date TextBox
            dateTextBox = new TextBox
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.None,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes,
                TextAlign = HorizontalAlignment.Center,
                ReadOnly = true,
                BackColor = Color.White,
                Cursor = Cursors.Hand
            };

            // Calendar Button
            calendarButton = new Button
            {
                Text = "📅",
                Width = 25,
                Dock = DockStyle.Right,
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.FromArgb(240, 240, 240),
                Font = new Font("Segoe UI", 8F),
                Cursor = Cursors.Hand
            };
            calendarButton.FlatAppearance.BorderSize = 0;

            // Month Calendar
            monthCalendar = new MonthCalendar
            {
                MaxSelectionCount = 1,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes
            };

            this.Controls.Add(dateTextBox);
            this.Controls.Add(calendarButton);
        }

        private void SetupEvents()
        {
            calendarButton.Click += CalendarButton_Click;
            dateTextBox.Click += DateTextBox_Click;
            dateTextBox.KeyDown += DateTextBox_KeyDown;
            monthCalendar.DateSelected += MonthCalendar_DateSelected;
            
            // Handle focus events
            dateTextBox.Enter += (s, e) => this.BackColor = Color.FromArgb(173, 216, 230);
            dateTextBox.Leave += (s, e) => this.BackColor = Color.White;
        }

        private void UpdateDisplayText()
        {
            var culture = new CultureInfo("ar-SA");
            
            string dateText = _format switch
            {
                DateTimePickerFormat.Long => _value.ToString("dddd، dd MMMM yyyy", culture),
                DateTimePickerFormat.Short => _value.ToString("dd/MM/yyyy", culture),
                DateTimePickerFormat.Time => _value.ToString("HH:mm:ss", culture),
                DateTimePickerFormat.Custom => _value.ToString("dd/MM/yyyy", culture),
                _ => _value.ToString("dd/MM/yyyy", culture)
            };

            if (_showTime && _format != DateTimePickerFormat.Time)
            {
                dateText += " " + _value.ToString("HH:mm", culture);
            }

            dateTextBox.Text = dateText;
        }

        private void CalendarButton_Click(object? sender, EventArgs e)
        {
            ShowCalendar();
        }

        private void DateTextBox_Click(object? sender, EventArgs e)
        {
            ShowCalendar();
        }

        private void DateTextBox_KeyDown(object? sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Space:
                case Keys.Enter:
                    ShowCalendar();
                    e.Handled = true;
                    break;
                    
                case Keys.Up:
                    Value = Value.AddDays(1);
                    e.Handled = true;
                    break;
                    
                case Keys.Down:
                    Value = Value.AddDays(-1);
                    e.Handled = true;
                    break;
                    
                case Keys.Right:
                    Value = Value.AddMonths(1);
                    e.Handled = true;
                    break;
                    
                case Keys.Left:
                    Value = Value.AddMonths(-1);
                    e.Handled = true;
                    break;
            }
        }

        private void ShowCalendar()
        {
            if (isCalendarVisible) return;

            // Create calendar form
            calendarForm = new Form
            {
                FormBorderStyle = FormBorderStyle.None,
                StartPosition = FormStartPosition.Manual,
                ShowInTaskbar = false,
                TopMost = true,
                Size = monthCalendar.Size,
                BackColor = Color.White
            };

            monthCalendar.SetDate(Value);
            monthCalendar.Dock = DockStyle.Fill;
            calendarForm.Controls.Add(monthCalendar);

            // Position the calendar
            var screenLocation = this.PointToScreen(new Point(0, this.Height));
            calendarForm.Location = screenLocation;

            // Show calendar
            calendarForm.Show();
            calendarForm.Deactivate += CalendarForm_Deactivate;
            
            isCalendarVisible = true;
        }

        private void HideCalendar()
        {
            if (!isCalendarVisible) return;

            calendarForm?.Hide();
            calendarForm?.Dispose();
            calendarForm = null;
            isCalendarVisible = false;
        }

        private void CalendarForm_Deactivate(object? sender, EventArgs e)
        {
            HideCalendar();
        }

        private void MonthCalendar_DateSelected(object? sender, DateRangeEventArgs e)
        {
            var selectedDate = e.Start;
            
            // Preserve time if showing time
            if (_showTime)
            {
                selectedDate = new DateTime(selectedDate.Year, selectedDate.Month, selectedDate.Day,
                    _value.Hour, _value.Minute, _value.Second);
            }

            Value = selectedDate;
            HideCalendar();
        }

        // Quick date selection methods
        public void SetToday()
        {
            Value = DateTime.Now;
        }

        public void SetYesterday()
        {
            Value = DateTime.Now.AddDays(-1);
        }

        public void SetTomorrow()
        {
            Value = DateTime.Now.AddDays(1);
        }

        public void SetStartOfMonth()
        {
            Value = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
        }

        public void SetEndOfMonth()
        {
            var now = DateTime.Now;
            Value = new DateTime(now.Year, now.Month, DateTime.DaysInMonth(now.Year, now.Month));
        }

        // Validation
        public bool IsValidDate()
        {
            return Value >= MinDate && Value <= MaxDate;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                HideCalendar();
                monthCalendar?.Dispose();
            }
            base.Dispose(disposing);
        }

        // Override focus methods to handle the composite control
        public new bool Focus()
        {
            return dateTextBox.Focus();
        }

        protected override void OnGotFocus(EventArgs e)
        {
            dateTextBox.Focus();
            base.OnGotFocus(e);
        }
    }

    /// <summary>
    /// تنسيق عرض التاريخ
    /// Date display format
    /// </summary>
    public enum DateTimePickerFormat
    {
        Long,
        Short,
        Time,
        Custom
    }
}
