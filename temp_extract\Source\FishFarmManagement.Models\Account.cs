using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// الحساب المحاسبي
    /// Accounting account
    /// </summary>
    public class Account : BaseEntity
    {
        [Required(ErrorMessage = "معرف نوع الحساب مطلوب")]
        public int AccountTypeId { get; set; }

        [Required(ErrorMessage = "رمز الحساب مطلوب")]
        [StringLength(20, ErrorMessage = "رمز الحساب يجب أن يكون أقل من 20 حرف")]
        public string AccountCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم الحساب مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الحساب يجب أن يكون أقل من 200 حرف")]
        public string AccountName { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "اسم الحساب بالإنجليزية يجب أن يكون أقل من 200 حرف")]
        public string AccountNameEn { get; set; } = string.Empty;

        [Column(TypeName = "decimal(15,2)")]
        public decimal Balance { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "نشط"; // نشط، متوقف، مغلق

        /// <summary>
        /// الحساب الأب (للحسابات الفرعية)
        /// Parent account (for sub-accounts)
        /// </summary>
        public int? ParentAccountId { get; set; }

        /// <summary>
        /// مستوى الحساب في الشجرة
        /// Account level in tree
        /// </summary>
        public int Level { get; set; } = 1;

        /// <summary>
        /// هل الحساب قابل للترحيل
        /// Is account postable
        /// </summary>
        public bool IsPostable { get; set; } = true;

        /// <summary>
        /// وصف الحساب
        /// Account description
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        // Navigation Properties
        [ForeignKey("AccountTypeId")]
        public virtual AccountType AccountType { get; set; } = null!;

        [ForeignKey("ParentAccountId")]
        public virtual Account? ParentAccount { get; set; }

        public virtual ICollection<Account> SubAccounts { get; set; } = new List<Account>();
        public virtual ICollection<TransactionDetail> TransactionDetails { get; set; } = new List<TransactionDetail>();

        /// <summary>
        /// حساب الرصيد الإجمالي (شامل الحسابات الفرعية)
        /// Calculate total balance (including sub-accounts)
        /// </summary>
        public decimal GetTotalBalance()
        {
            var totalBalance = Balance;
            foreach (var subAccount in SubAccounts)
            {
                totalBalance += subAccount.GetTotalBalance();
            }
            return totalBalance;
        }

        /// <summary>
        /// الحصول على المسار الكامل للحساب
        /// Get full account path
        /// </summary>
        public string GetFullPath()
        {
            if (ParentAccount != null)
            {
                return $"{ParentAccount.GetFullPath()} > {AccountName}";
            }
            return AccountName;
        }

        /// <summary>
        /// التحقق من أن الحساب قابل للترحيل
        /// Check if account is postable
        /// </summary>
        public bool CanPost()
        {
            return IsPostable && Status == "نشط" && SubAccounts.Count == 0;
        }

        /// <summary>
        /// تحديث الرصيد
        /// Update balance
        /// </summary>
        public void UpdateBalance(decimal debitAmount, decimal creditAmount)
        {
            if (AccountType.Nature == "مدين")
            {
                Balance += debitAmount - creditAmount;
            }
            else
            {
                Balance += creditAmount - debitAmount;
            }
        }

        /// <summary>
        /// الحصول على الحسابات الافتراضية
        /// Get default accounts
        /// </summary>
        public static List<Account> GetDefaultAccounts()
        {
            return new List<Account>
            {
                // الأصول
                new Account { AccountCode = "1001", AccountName = "النقدية", AccountNameEn = "Cash", AccountTypeId = 1, Level = 1 },
                new Account { AccountCode = "1002", AccountName = "البنك", AccountNameEn = "Bank", AccountTypeId = 1, Level = 1 },
                new Account { AccountCode = "1003", AccountName = "المخزون", AccountNameEn = "Inventory", AccountTypeId = 1, Level = 1 },
                new Account { AccountCode = "1004", AccountName = "الأصول الثابتة", AccountNameEn = "Fixed Assets", AccountTypeId = 1, Level = 1 },
                
                // الخصوم
                new Account { AccountCode = "2001", AccountName = "الموردون", AccountNameEn = "Suppliers", AccountTypeId = 2, Level = 1 },
                new Account { AccountCode = "2002", AccountName = "رواتب مستحقة", AccountNameEn = "Accrued Salaries", AccountTypeId = 2, Level = 1 },
                
                // حقوق الملكية
                new Account { AccountCode = "3001", AccountName = "رأس المال", AccountNameEn = "Capital", AccountTypeId = 3, Level = 1 },
                new Account { AccountCode = "3002", AccountName = "الأرباح المحتجزة", AccountNameEn = "Retained Earnings", AccountTypeId = 3, Level = 1 },
                
                // الإيرادات
                new Account { AccountCode = "4001", AccountName = "مبيعات الأسماك", AccountNameEn = "Fish Sales", AccountTypeId = 4, Level = 1 },
                
                // المصروفات
                new Account { AccountCode = "5001", AccountName = "مصروفات العلف", AccountNameEn = "Feed Expenses", AccountTypeId = 5, Level = 1 },
                new Account { AccountCode = "5002", AccountName = "مصروفات الرواتب", AccountNameEn = "Salary Expenses", AccountTypeId = 5, Level = 1 },
                new Account { AccountCode = "5003", AccountName = "مصروفات الأدوية", AccountNameEn = "Medicine Expenses", AccountTypeId = 5, Level = 1 },
                new Account { AccountCode = "5004", AccountName = "مصروفات عامة", AccountNameEn = "General Expenses", AccountTypeId = 5, Level = 1 }
            };
        }
    }
}
