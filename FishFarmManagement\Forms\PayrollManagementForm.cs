﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ø±ÙˆØ§ØªØ¨
    /// Payroll management form
    /// </summary>
    public partial class PayrollManagementForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;
        private readonly Employee _employee;

        // UI Controls
        private DataGridView payrollDataGridView;
        private Button addPayrollButton;
        private Button editPayrollButton;
        private Button deletePayrollButton;
        private Button refreshButton;
        private Label employeeInfoLabel;

        public PayrollManagementForm(IUnitOfWork unitOfWork, ILogger logger, Employee employee)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _employee = employee ?? throw new ArgumentNullException(nameof(employee));

            InitializeComponent();
            LoadPayrollDataAsync();
        }

        private void InitializeComponent()
        {
            this.Text = $"Ø¥Ø¯Ø§Ø±Ø© Ø±ÙˆØ§ØªØ¨ - {_employee.FullName}";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
        }

        private void CreateControls()
        {
            // Employee info panel
            var infoPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            employeeInfoLabel = new Label
            {
                Text = $"Ø§Ù„Ù…ÙˆØ¸Ù: {_employee.FullName} | Ø§Ù„Ù…Ù†ØµØ¨: {_employee.Position} | Ø§Ù„Ø±Ø§ØªØ¨ Ø§Ù„Ø£Ø³Ø§Ø³ÙŠ: {_employee.BaseSalary:C}",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter
            };

            infoPanel.Controls.Add(employeeInfoLabel);

            // Buttons panel
            var buttonsPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Top,
                Padding = new Padding(10)
            };

            addPayrollButton = new Button
            {
                Text = "Ø¥Ø¶Ø§ÙØ© Ø±Ø§ØªØ¨",
                Size = new Size(100, 30),
                Location = new Point(10, 10)
            };
            addPayrollButton.Click += AddPayroll_Click;

            editPayrollButton = new Button
            {
                Text = "ØªØ¹Ø¯ÙŠÙ„",
                Size = new Size(80, 30),
                Location = new Point(120, 10),
                Enabled = false
            };
            editPayrollButton.Click += EditPayroll_Click;

            deletePayrollButton = new Button
            {
                Text = "Ø­Ø°Ù",
                Size = new Size(80, 30),
                Location = new Point(210, 10),
                Enabled = false
            };
            deletePayrollButton.Click += DeletePayroll_Click;

            refreshButton = new Button
            {
                Text = "ØªØ­Ø¯ÙŠØ«",
                Size = new Size(80, 30),
                Location = new Point(300, 10)
            };
            refreshButton.Click += Refresh_Click;

            buttonsPanel.Controls.AddRange(new Control[] { addPayrollButton, editPayrollButton, deletePayrollButton, refreshButton });

            // Data grid view
            payrollDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            SetupDataGridViewColumns();
            payrollDataGridView.SelectionChanged += PayrollDataGridView_SelectionChanged;

            this.Controls.Add(payrollDataGridView);
            this.Controls.Add(buttonsPanel);
            this.Controls.Add(infoPanel);
        }

        private void SetupDataGridViewColumns()
        {
            payrollDataGridView.Columns.Clear();

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "Ø§Ù„Ù…Ø¹Ø±Ù",
                DataPropertyName = "Id",
                Visible = false
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "MonthYear",
                HeaderText = "Ø§Ù„Ø´Ù‡Ø±/Ø§Ù„Ø³Ù†Ø©",
                DataPropertyName = "MonthYear",
                Width = 120
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "BaseSalary",
                HeaderText = "Ø§Ù„Ø±Ø§ØªØ¨ Ø§Ù„Ø£Ø³Ø§Ø³ÙŠ",
                DataPropertyName = "BaseSalary",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Allowances",
                HeaderText = "Ø§Ù„Ø¨Ø¯Ù„Ø§Øª",
                DataPropertyName = "Allowances",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Deductions",
                HeaderText = "Ø§Ù„Ø®ØµÙˆÙ…Ø§Øª",
                DataPropertyName = "Deductions",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "NetSalary",
                HeaderText = "ØµØ§ÙÙŠ Ø§Ù„Ø±Ø§ØªØ¨",
                DataPropertyName = "NetSalary",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            payrollDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Notes",
                HeaderText = "Ø§Ù„Ù…Ù„Ø§Ø­Ø¸Ø§Øª",
                DataPropertyName = "Notes",
                Width = 200
            });
        }

        private async void LoadPayrollDataAsync()
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;
                var payrolls = await _unitOfWork.Payrolls.FindAsync(p => p.EmployeeId == _employee.Id);
                payrollDataGridView.DataSource = payrolls.OrderByDescending(p => p.Year).ThenByDescending(p => p.Month).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø±ÙˆØ§ØªØ¨");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void PayrollDataGridView_SelectionChanged(object? sender, EventArgs e)
        {
            bool hasSelection = payrollDataGridView.SelectedRows.Count > 0;
            editPayrollButton.Enabled = hasSelection;
            deletePayrollButton.Enabled = hasSelection;
        }

        private void AddPayroll_Click(object? sender, EventArgs e)
        {
            try
            {
                var addForm = new PayrollAddEditForm(_unitOfWork, _logger, _employee);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadPayrollDataAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¶Ø§ÙØ© Ø±Ø§ØªØ¨");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ø§Ù„Ù†Ù…ÙˆØ°Ø¬: {ex.Message}", "Ø®Ø·Ø£", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditPayroll_Click(object? sender, EventArgs e)
        {
            try
            {
                if (payrollDataGridView.SelectedRows.Count == 0) return;

                var selectedPayroll = payrollDataGridView.SelectedRows[0].DataBoundItem as Payroll;
                if (selectedPayroll == null) return;

                var editForm = new PayrollAddEditForm(_unitOfWork, _logger, _employee, selectedPayroll);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadPayrollDataAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ ØªØ¹Ø¯ÙŠÙ„ Ø±Ø§ØªØ¨");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ø§Ù„Ù†Ù…ÙˆØ°Ø¬: {ex.Message}", "Ø®Ø·Ø£", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeletePayroll_Click(object? sender, EventArgs e)
        {
            try
            {
                if (payrollDataGridView.SelectedRows.Count == 0) return;

                var selectedPayroll = payrollDataGridView.SelectedRows[0].DataBoundItem as Payroll;
                if (selectedPayroll == null) return;

                var result = MessageBox.Show(
                    $"Ù‡Ù„ Ø£Ù†Øª Ù…ØªØ£ÙƒØ¯ Ù…Ù† Ø­Ø°Ù Ø±Ø§ØªØ¨ {selectedPayroll.GetMonthName()} {selectedPayroll.Year}ØŸ",
                    "ØªØ£ÙƒÙŠØ¯ Ø§Ù„Ø­Ø°Ù",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    this.Cursor = Cursors.WaitCursor;
                    await _unitOfWork.Payrolls.DeleteAsync(selectedPayroll);
                    await _unitOfWork.SaveChangesAsync();
                    
                    MessageBox.Show("ØªÙ… Ø­Ø°Ù Ø§Ù„Ø±Ø§ØªØ¨ Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadPayrollDataAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„Ø±Ø§ØªØ¨");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„Ø±Ø§ØªØ¨: {ex.Message}", "Ø®Ø·Ø£", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void Refresh_Click(object? sender, EventArgs e)
        {
            LoadPayrollDataAsync();
        }
    }
}



