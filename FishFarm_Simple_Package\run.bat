@echo off
chcp 65001 >nul
title نظام إدارة مزرعة الأسماك - Fish Farm Management System

echo ========================================
echo    نظام إدارة مزرعة الأسماك
echo    Fish Farm Management System
echo    النسخة المبسطة - Simplified Version
echo ========================================
echo.

:: التحقق من وجود الملف التنفيذي
if not exist "FishFarmManagement.exe" (
    echo خطأ: لم يتم العثور على الملف التنفيذي
    echo Error: Executable file not found
    echo.
    echo يرجى التأكد من وجود ملف FishFarmManagement.exe في نفس المجلد
    echo Please ensure FishFarmManagement.exe exists in the same folder
    pause
    exit /b 1
)

:: إنشاء المجلدات الضرورية
echo إنشاء المجلدات الضرورية...
if not exist "Logs" mkdir "Logs"
if not exist "Backups" mkdir "Backups"
if not exist "Reports" mkdir "Reports"
if not exist "Temp" mkdir "Temp"

:: التحقق من .NET Runtime
echo التحقق من .NET Runtime...
dotnet --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo تحذير: .NET Runtime غير مثبت أو غير متاح
    echo Warning: .NET Runtime not installed or not available
    echo.
    echo سيتم محاولة تشغيل البرنامج مباشرة...
    echo Attempting to run the program directly...
    echo.
)

:: تشغيل البرنامج
echo تشغيل نظام إدارة مزرعة الأسماك...
echo Starting Fish Farm Management System...
echo.

start "" "FishFarmManagement.exe"

if %ERRORLEVEL% neq 0 (
    echo خطأ: فشل في تشغيل البرنامج
    echo Error: Failed to start the program
    echo.
    echo الحلول المقترحة:
    echo Suggested solutions:
    echo 1. تشغيل البرنامج كمدير - Run as Administrator
    echo 2. تثبيت .NET 8.0 Runtime
    echo 3. فحص ملفات السجل في مجلد Logs
    echo.
    pause
    exit /b 1
)

echo تم تشغيل البرنامج بنجاح!
echo Program started successfully!
echo.
echo إذا لم يظهر البرنامج، تحقق من:
echo If the program doesn't appear, check:
echo - شريط المهام - Taskbar
echo - ملفات السجل - Log files
echo - إعدادات مكافح الفيروسات - Antivirus settings
echo.

timeout /t 3 >nul
exit /b 0
