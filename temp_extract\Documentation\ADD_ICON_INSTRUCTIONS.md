# إضافة أيقونة للتطبيق
## Adding Application Icon

## 🎨 كيفية إضافة أيقونة مخصصة

### الخطوة 1: إنشاء ملف الأيقونة
1. أنشئ أو احصل على ملف أيقونة بصيغة `.ico`
2. تأكد من أن الأيقونة بأحجام متعددة (16x16, 32x32, 48x48, 256x256)
3. اسم الملف المقترح: `fish-farm-icon.ico`

### الخطوة 2: وضع الأيقونة في المكان الصحيح
```
FishFarmManagement/
└── Resources/
    └── fish-farm-icon.ico  ← ضع الملف هنا
```

### الخطوة 3: تحديث ملف المشروع
أضف السطر التالي في ملف `FishFarmManagement.csproj` داخل `<PropertyGroup>`:

```xml
<ApplicationIcon>Resources\fish-farm-icon.ico</ApplicationIcon>
```

المثال الكامل:
```xml
<PropertyGroup>
  <OutputType>WinExe</OutputType>
  <TargetFramework>net8.0-windows</TargetFramework>
  <Nullable>enable</Nullable>
  <UseWindowsForms>true</UseWindowsForms>
  <ImplicitUsings>enable</ImplicitUsings>
  <ApplicationIcon>Resources\fish-farm-icon.ico</ApplicationIcon>
  <!-- باقي الإعدادات... -->
</PropertyGroup>
```

### الخطوة 4: إعادة البناء
```cmd
dotnet clean
dotnet build
```

## 🛠️ إنشاء أيقونة بسيطة

إذا لم تكن لديك أيقونة، يمكنك:

1. **استخدام أدوات مجانية:**
   - GIMP (مجاني)
   - Paint.NET (مجاني)
   - Online ICO converters

2. **تحويل صورة PNG إلى ICO:**
   - اذهب إلى: https://convertio.co/png-ico/
   - ارفع صورة PNG
   - حمل ملف ICO

3. **استخدام أيقونة Windows افتراضية:**
   - انسخ أيقونة من `C:\Windows\System32\shell32.dll`
   - استخدم أداة مثل IconsExtract

## 📝 ملاحظات مهمة

- **الأحجام المطلوبة:** 16x16, 32x32, 48x48, 256x256 بكسل
- **التنسيق:** ICO فقط (ليس PNG أو JPG)
- **المسار:** يجب أن يكون نسبي من مجلد المشروع
- **البناء:** أعد بناء المشروع بعد إضافة الأيقونة

## 🎯 أيقونة مقترحة للمشروع

للحصول على أيقونة مناسبة لمزرعة الأسماك:
- ابحث عن "fish farm icon" أو "aquaculture icon"
- استخدم ألوان زرقاء وخضراء
- تأكد من وضوح الأيقونة في الأحجام الصغيرة

## ⚠️ استكشاف الأخطاء

**خطأ: "Could not find icon file"**
- تأكد من وجود الملف في المسار الصحيح
- تأكد من أن اسم الملف صحيح
- تأكد من أن الملف بصيغة ICO

**خطأ: "Invalid icon format"**
- تأكد من أن الملف بصيغة ICO صحيحة
- جرب تحويل الصورة مرة أخرى
- استخدم أداة تحويل مختلفة

---

**ملاحظة:** تم إزالة مرجع الأيقونة مؤقتاً من المشروع لتجنب أخطاء البناء. يمكنك إضافتها لاحقاً باتباع التعليمات أعلاه.
