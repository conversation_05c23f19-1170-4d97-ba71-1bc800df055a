using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// الأدوية والإضافات
    /// Medications and supplements
    /// </summary>
    public class Medication : BaseEntity
    {
        [Required(ErrorMessage = "اسم الدواء مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الدواء يجب أن يكون أقل من 100 حرف")]
        public string MedicationName { get; set; } = string.Empty;

        [Required(ErrorMessage = "نوع الدواء مطلوب")]
        [StringLength(50, ErrorMessage = "نوع الدواء يجب أن يكون أقل من 50 حرف")]
        public string Type { get; set; } = string.Empty; // مضاد حيوي، فيتامين، مطهر، إلخ

        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal PricePerUnit { get; set; }

        [Required(ErrorMessage = "الجرعة مطلوبة")]
        [StringLength(200, ErrorMessage = "الجرعة يجب أن تكون أقل من 200 حرف")]
        public string Dosage { get; set; } = string.Empty;

        [StringLength(50)]
        public string Unit { get; set; } = "مل"; // مل، جرام، قرص، إلخ

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;

        [StringLength(20)]
        public string Status { get; set; } = "نشط"; // نشط، متوقف، منتهي الصلاحية

        /// <summary>
        /// تاريخ انتهاء الصلاحية
        /// Expiry date
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// الشركة المصنعة
        /// Manufacturer
        /// </summary>
        [StringLength(100)]
        public string Manufacturer { get; set; } = string.Empty;

        // Navigation Properties
        public virtual ICollection<PondMedication> PondMedications { get; set; } = new List<PondMedication>();

        /// <summary>
        /// التحقق من انتهاء صلاحية الدواء
        /// Check if medication is expired
        /// </summary>
        public bool IsExpired()
        {
            return ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now;
        }

        /// <summary>
        /// التحقق من قرب انتهاء الصلاحية (خلال 30 يوم)
        /// Check if medication expires soon (within 30 days)
        /// </summary>
        public bool ExpiresSoon()
        {
            return ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.Now.AddDays(30);
        }

        /// <summary>
        /// حساب إجمالي الكمية المستخدمة
        /// Calculate total used quantity
        /// </summary>
        public decimal GetTotalUsedQuantity()
        {
            return PondMedications.Sum(pm => pm.Quantity);
        }

        /// <summary>
        /// حساب إجمالي التكلفة
        /// Calculate total cost
        /// </summary>
        public decimal GetTotalCost()
        {
            return PondMedications.Sum(pm => pm.Cost);
        }
    }
}
