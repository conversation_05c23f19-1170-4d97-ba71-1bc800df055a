using FishFarmManagement.Models;
using System.Security.Claims;
using System.Threading.Tasks;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    public interface IAuthenticationService
    {
        Task<User?> AuthenticateAsync(string username, string password);
        ClaimsPrincipal CreateClaimsPrincipalForUser(User user);
        User? GetCurrentUser();
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
    }
}