using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services;
using System.Windows.Forms.DataVisualization.Charting;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج مراقبة الأداء
    /// Performance monitor form
    /// </summary>
    public partial class PerformanceMonitorForm : Form
    {
        private readonly PerformanceMonitorService _performanceService;
        private readonly ILogger<PerformanceMonitorForm> _logger;
        private readonly System.Windows.Forms.Timer _refreshTimer;

        // Controls
        private Chart _performanceChart;
        private ListView _metricsListView;
        private Label _systemHealthLabel;
        private Button _optimizeButton;
        private Button _refreshButton;
        private Button _closeButton;
        private GroupBox _currentMetricsGroup;
        private GroupBox _chartGroup;
        private GroupBox _systemHealthGroup;

        public PerformanceMonitorForm(PerformanceMonitorService performanceService, ILogger<PerformanceMonitorForm> logger)
        {
            _performanceService = performanceService;
            _logger = logger;
            
            InitializeComponent();
            InitializeChart();
            
            // تهيئة التحديث التلقائي
            _refreshTimer = new System.Windows.Forms.Timer();
            _refreshTimer.Interval = 5000; // كل 5 ثوان
            _refreshTimer.Tick += RefreshTimer_Tick;
            _refreshTimer.Start();
            
            LoadInitialData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "مراقب الأداء - نظام إدارة مزرعة الأسماك";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Current metrics group
            _currentMetricsGroup = new GroupBox
            {
                Text = "المقاييس الحالية",
                Location = new Point(20, 20),
                Size = new Size(400, 300),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            // Metrics ListView
            _metricsListView = new ListView
            {
                Location = new Point(10, 25),
                Size = new Size(380, 260),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Font = new Font("Segoe UI", 9F)
            };
            _metricsListView.Columns.Add("المقياس", 200);
            _metricsListView.Columns.Add("القيمة", 100);
            _metricsListView.Columns.Add("الوحدة", 80);

            _currentMetricsGroup.Controls.Add(_metricsListView);

            // Chart group
            _chartGroup = new GroupBox
            {
                Text = "رسم بياني للأداء",
                Location = new Point(440, 20),
                Size = new Size(420, 300),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            // Performance chart
            _performanceChart = new Chart
            {
                Location = new Point(10, 25),
                Size = new Size(400, 260)
            };

            _chartGroup.Controls.Add(_performanceChart);

            // System health group
            _systemHealthGroup = new GroupBox
            {
                Text = "حالة النظام",
                Location = new Point(20, 340),
                Size = new Size(840, 200),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            _systemHealthLabel = new Label
            {
                Location = new Point(20, 30),
                Size = new Size(800, 150),
                Font = new Font("Segoe UI", 10F),
                Text = "جاري تحميل معلومات النظام...",
                AutoSize = false
            };

            _systemHealthGroup.Controls.Add(_systemHealthLabel);

            // Buttons
            _optimizeButton = new Button
            {
                Text = "تحسين الأداء",
                Location = new Point(20, 560),
                Size = new Size(120, 35),
                Font = new Font("Segoe UI", 9F),
                UseVisualStyleBackColor = true
            };
            _optimizeButton.Click += OptimizeButton_Click;

            _refreshButton = new Button
            {
                Text = "تحديث",
                Location = new Point(150, 560),
                Size = new Size(120, 35),
                Font = new Font("Segoe UI", 9F),
                UseVisualStyleBackColor = true
            };
            _refreshButton.Click += RefreshButton_Click;

            _closeButton = new Button
            {
                Text = "إغلاق",
                Location = new Point(740, 560),
                Size = new Size(120, 35),
                Font = new Font("Segoe UI", 9F),
                UseVisualStyleBackColor = true,
                DialogResult = DialogResult.OK
            };

            // Add controls to form
            this.Controls.AddRange(new Control[]
            {
                _currentMetricsGroup,
                _chartGroup,
                _systemHealthGroup,
                _optimizeButton,
                _refreshButton,
                _closeButton
            });

            this.ResumeLayout(false);
        }

        private void InitializeChart()
        {
            try
            {
                // إعداد الرسم البياني
                var chartArea = new ChartArea("MainArea");
                chartArea.AxisX.Title = "الوقت";
                chartArea.AxisY.Title = "النسبة المئوية";
                chartArea.AxisY.Maximum = 100;
                chartArea.AxisY.Minimum = 0;
                _performanceChart.ChartAreas.Add(chartArea);

                // إضافة سلاسل البيانات
                var cpuSeries = new Series("المعالج")
                {
                    ChartType = SeriesChartType.Line,
                    Color = Color.Red,
                    BorderWidth = 2
                };
                _performanceChart.Series.Add(cpuSeries);

                var memorySeries = new Series("الذاكرة")
                {
                    ChartType = SeriesChartType.Line,
                    Color = Color.Blue,
                    BorderWidth = 2
                };
                _performanceChart.Series.Add(memorySeries);

                // إضافة وسيلة الإيضاح
                var legend = new Legend("MainLegend");
                _performanceChart.Legends.Add(legend);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة الرسم البياني");
            }
        }

        private void LoadInitialData()
        {
            try
            {
                RefreshData();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل البيانات الأولية");
            }
        }

        private void RefreshData()
        {
            try
            {
                var performance = _performanceService.GetCurrentPerformance();
                var health = _performanceService.CheckSystemHealth();

                UpdateMetricsList(performance);
                UpdateChart(performance);
                UpdateSystemHealth(health);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث البيانات");
            }
        }

        private void UpdateMetricsList(PerformanceInfo performance)
        {
            try
            {
                _metricsListView.Items.Clear();

                var metrics = new[]
                {
                    new { Name = "استخدام المعالج", Value = $"{performance.CpuUsagePercent:F1}", Unit = "%" },
                    new { Name = "استخدام الذاكرة", Value = performance.ProcessMemoryMB.ToString(), Unit = "MB" },
                    new { Name = "الذاكرة المتاحة", Value = $"{performance.AvailableMemoryMB:F0}", Unit = "MB" },
                    new { Name = "عدد الخيوط", Value = performance.ThreadCount.ToString(), Unit = "خيط" },
                    new { Name = "عدد المقابض", Value = performance.HandleCount.ToString(), Unit = "مقبض" },
                    new { Name = "وقت الاستجابة", Value = performance.ResponseTimeMs.ToString(), Unit = "ms" }
                };

                foreach (var metric in metrics)
                {
                    var item = new ListViewItem(metric.Name);
                    item.SubItems.Add(metric.Value);
                    item.SubItems.Add(metric.Unit);
                    _metricsListView.Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث قائمة المقاييس");
            }
        }

        private void UpdateChart(PerformanceInfo performance)
        {
            try
            {
                var currentTime = DateTime.Now;

                // إضافة نقاط جديدة
                _performanceChart.Series["المعالج"].Points.AddXY(currentTime, performance.CpuUsagePercent);
                _performanceChart.Series["الذاكرة"].Points.AddXY(currentTime, 
                    performance.AvailableMemoryMB > 0 ? (performance.ProcessMemoryMB / performance.AvailableMemoryMB) * 100 : 0);

                // الاحتفاظ بآخر 20 نقطة فقط
                if (_performanceChart.Series["المعالج"].Points.Count > 20)
                {
                    _performanceChart.Series["المعالج"].Points.RemoveAt(0);
                    _performanceChart.Series["الذاكرة"].Points.RemoveAt(0);
                }

                _performanceChart.Invalidate();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الرسم البياني");
            }
        }

        private void UpdateSystemHealth(SystemHealthStatus health)
        {
            try
            {
                var healthText = $"حالة النظام العامة: {GetHealthText(health.OverallHealth)}\n";
                healthText += $"آخر فحص: {health.Timestamp:HH:mm:ss}\n\n";

                if (health.Issues.Any())
                {
                    healthText += "المشاكل المكتشفة:\n";
                    foreach (var issue in health.Issues)
                    {
                        healthText += $"• {issue}\n";
                    }
                }
                else
                {
                    healthText += "لا توجد مشاكل مكتشفة.";
                }

                _systemHealthLabel.Text = healthText;
                _systemHealthLabel.ForeColor = GetHealthColor(health.OverallHealth);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث حالة النظام");
            }
        }

        private string GetHealthText(HealthLevel health)
        {
            return health switch
            {
                HealthLevel.Good => "جيدة",
                HealthLevel.Warning => "تحذير",
                HealthLevel.Critical => "حرجة",
                _ => "غير معروفة"
            };
        }

        private Color GetHealthColor(HealthLevel health)
        {
            return health switch
            {
                HealthLevel.Good => Color.Green,
                HealthLevel.Warning => Color.Orange,
                HealthLevel.Critical => Color.Red,
                _ => Color.Gray
            };
        }

        private void OptimizeButton_Click(object? sender, EventArgs e)
        {
            try
            {
                _optimizeButton.Enabled = false;
                _optimizeButton.Text = "جاري التحسين...";

                _performanceService.OptimizePerformance();

                MessageBox.Show("تم تحسين الأداء بنجاح.", "تحسين الأداء",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                RefreshData();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين الأداء");
                MessageBox.Show("حدث خطأ أثناء تحسين الأداء.", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _optimizeButton.Enabled = true;
                _optimizeButton.Text = "تحسين الأداء";
            }
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            RefreshData();
        }

        private void RefreshTimer_Tick(object? sender, EventArgs e)
        {
            if (this.Visible && !this.IsDisposed)
            {
                RefreshData();
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
