# دليل المستخدم - نظام إدارة مزرعة الأسماك
# User Guide - Fish Farm Management System

## البدء السريع | Quick Start

### 1. تشغيل البرنامج لأول مرة
- انقر مرتين على FishFarmManagement.exe
- سيتم إنشاء قاعدة البيانات تلقائياً
- استخدم بيانات الدخول الافتراضية:
  * المستخدم: admin
  * كلمة المرور: Admin123!

### 2. الشاشة الرئيسية
- لوحة التحكم: عرض سريع للإحصائيات
- القوائم الجانبية: الوصول لجميع الوظائف
- شريط الحالة: معلومات النظام والمستخدم

## الوظائف الأساسية | Main Features

### إدارة المزرعة | Farm Management
- إضافة معلومات المزرعة الأساسية
- تحديد الموقع والمساحة
- إعداد معلومات الاتصال

### إدارة الأحواض | Pond Management
- إضافة أحواض جديدة
- تحديد أبعاد ومواصفات كل حوض
- تتبع حالة الأحواض (نشط/غير نشط)
- ربط الأحواض بدورات الإنتاج

### دورات الإنتاج | Production Cycles
- بدء دورة إنتاج جديدة
- تحديد نوع وكمية الأسماك
- تتبع مراحل النمو
- حساب معدلات التحويل الغذائي

### إدارة الأعلاف | Feed Management
- تسجيل أنواع الأعلاف المختلفة
- تتبع كميات الأعلاف المستخدمة
- حساب تكلفة التغذية
- مراقبة مخزون الأعلاف

### إدارة الأدوية | Medication Management
- تسجيل الأدوية والعلاجات
- جدولة العلاجات الدورية
- تتبع فترات السحب
- مراقبة انتهاء الصلاحية

### تسجيل النفوق | Mortality Recording
- تسجيل حالات النفوق اليومية
- تحديد أسباب النفوق
- حساب معدلات البقاء
- تحليل أنماط النفوق

### إدارة المخزون | Inventory Management
- تتبع جميع المواد والمعدات
- تسجيل عمليات الشراء والاستهلاك
- تنبيهات نفاد المخزون
- تقييم المخزون

### إدارة الموظفين | Employee Management
- إضافة بيانات الموظفين
- تحديد الأدوار والصلاحيات
- تتبع ساعات العمل
- حساب الرواتب الأساسية

## التقارير | Reports

### التقارير المتاحة
1. تقرير الإنتاج اليومي
2. تقرير استهلاك الأعلاف
3. تقرير النفوق والأمراض
4. تقرير المخزون
5. تقرير الموظفين
6. تقرير الربحية المبسط

### إنشاء التقارير
- اختر نوع التقرير من القائمة
- حدد الفترة الزمنية
- اختر تنسيق الإخراج (PDF/Excel)
- انقر "إنشاء التقرير"

## النسخ الاحتياطي | Backup & Restore

### إنشاء نسخة احتياطية
- اذهب إلى قائمة "أدوات"
- اختر "نسخ احتياطي"
- حدد موقع الحفظ
- انقر "إنشاء نسخة احتياطية"

### استعادة النسخة الاحتياطية
- اذهب إلى قائمة "أدوات"
- اختر "استعادة"
- حدد ملف النسخة الاحتياطية
- انقر "استعادة"

## الإعدادات | Settings

### الإعدادات العامة
- لغة البرنامج
- تنسيق التاريخ والوقت
- العملة المستخدمة
- معلومات الشركة

### إعدادات قاعدة البيانات
- مسار قاعدة البيانات
- النسخ الاحتياطي التلقائي
- تنظيف البيانات القديمة

### إعدادات الأمان
- تغيير كلمة المرور
- مدة انتهاء الجلسة
- صلاحيات المستخدمين

## استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة وحلولها

#### البرنامج لا يعمل
- تأكد من تثبيت .NET 8.0 Runtime
- تشغيل البرنامج كمدير
- فحص ملفات السجل

#### مشاكل قاعدة البيانات
- احذف ملف FishFarmDatabase.db
- أعد تشغيل البرنامج
- استعد من نسخة احتياطية

#### البرنامج بطيء
- أغلق البرامج الأخرى
- تنظيف قاعدة البيانات
- زيادة ذاكرة النظام

#### مشاكل التقارير
- تأكد من وجود مجلد Reports
- فحص صلاحيات الكتابة
- تحديث برامج عرض PDF

## نصائح للاستخدام الأمثل | Best Practices

### إدخال البيانات
- أدخل البيانات بانتظام يومياً
- تأكد من دقة الأرقام والتواريخ
- استخدم وحدات قياس ثابتة
- احفظ نسخ احتياطية دورية

### الصيانة الدورية
- نظف قاعدة البيانات شهرياً
- احذف الملفات المؤقتة
- راجع دقة البيانات
- حدث معلومات الاتصال

### الأمان
- غير كلمة المرور دورياً
- لا تشارك بيانات الدخول
- احفظ نسخ احتياطية في مكان آمن
- راقب سجلات النشاط

## الدعم الفني | Technical Support

### طرق التواصل
- البريد الإلكتروني: <EMAIL>
- الهاتف: متاح عند الطلب
- الموقع الإلكتروني: github.com/fishfarm-management

### معلومات مطلوبة عند طلب الدعم
- إصدار البرنامج
- نظام التشغيل
- وصف المشكلة
- رسائل الخطأ (إن وجدت)
- ملفات السجل

---
© 2025 طارق حسين صالح - جميع الحقوق محفوظة
