# ملاحظة مهمة: تم تحويل النظام إلى SQLite

⚠️ **تنبيه**: تم تحويل نظام إدارة مزرعة الأسماك من PostgreSQL إلى SQLite لتبسيط التثبيت والاستخدام.

## النظام الحالي يستخدم SQLite

- لا حاجة لتثبيت PostgreSQL
- قاعدة البيانات محفوظة في ملف واحد: `FishFarmDatabase.db`
- سهولة في النسخ الاحتياطي والنقل
- لا حاجة لإعداد خادم قاعدة بيانات منفصل

---

# إعداد PostgreSQL لنظام إدارة مزرعة الأسماك (مرجع تاريخي)

## متطلبات النظام (لم تعد مطلوبة)

- PostgreSQL 12 أو أحدث
- .NET 8.0 SDK
- Windows 10/11 أو Windows Server 2019/2022

## تثبيت PostgreSQL

### الطريقة الأولى: تثبيت PostgreSQL محلياً

1. **تحميل PostgreSQL:**
   - اذهب إلى https://www.********ql.org/download/windows/
   - حمل أحدث إصدار من PostgreSQL

2. **تثبيت PostgreSQL:**
   - شغل ملف التثبيت
   - اختر كلمة مرور قوية للمستخدم `********`
   - اتبع خطوات التثبيت الافتراضية
   - تأكد من تثبيت pgAdmin 4 (أداة إدارة قاعدة البيانات)

3. **إنشاء قاعدة البيانات:**
   ```sql
   -- افتح pgAdmin أو psql واكتب الأوامر التالية:
   CREATE DATABASE "FishFarmDatabase";
   ```

### الطريقة الثانية: استخدام Docker

```bash
# تشغيل PostgreSQL باستخدام Docker
docker run --name ********-fishfarm -e POSTGRES_PASSWORD=******** -e POSTGRES_DB=FishFarmDatabase -p 5432:5432 -d ********:15

# للتحقق من تشغيل الحاوية
docker ps
```

## إعداد سلسلة الاتصال

تأكد من أن سلسلة الاتصال في `appsettings.json` صحيحة:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=FishFarmDatabase;Username=********;Password=********;Port=5432;Pooling=true;SSL Mode=Prefer;Trust Server Certificate=true;"
  }
}
```

### تخصيص سلسلة الاتصال

قم بتعديل المعاملات التالية حسب إعدادك:

- `Host`: عنوان الخادم (localhost للتثبيت المحلي)
- `Database`: اسم قاعدة البيانات (FishFarmDatabase)
- `Username`: اسم المستخدم (******** افتراضياً)
- `Password`: كلمة المرور التي اخترتها أثناء التثبيت
- `Port`: منفذ الاتصال (5432 افتراضياً)

## تشغيل التطبيق

1. **تطبيق الهجرات:**
   ```bash
   dotnet ef database update --project FishFarmManagement.DAL --startup-project FishFarmManagement
   ```

2. **تشغيل التطبيق:**
   ```bash
   dotnet run --project FishFarmManagement
   ```

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## استكشاف الأخطاء

### خطأ الاتصال بقاعدة البيانات

إذا واجهت خطأ في الاتصال:

1. تأكد من تشغيل خدمة PostgreSQL
2. تحقق من صحة سلسلة الاتصال
3. تأكد من وجود قاعدة البيانات
4. تحقق من إعدادات الجدار الناري

### التحقق من حالة PostgreSQL

```bash
# في Windows (Command Prompt كمدير)
sc query ********ql-x64-15

# أو باستخدام Services.msc
# ابحث عن "********ql" في قائمة الخدمات
```

### إعادة إنشاء قاعدة البيانات

إذا كنت تريد إعادة إنشاء قاعدة البيانات من البداية:

```sql
-- احذف قاعدة البيانات الموجودة
DROP DATABASE IF EXISTS "FishFarmDatabase";

-- أنشئ قاعدة بيانات جديدة
CREATE DATABASE "FishFarmDatabase";
```

ثم شغل الأمر:
```bash
dotnet ef database update --project FishFarmManagement.DAL --startup-project FishFarmManagement
```

## الميزات الجديدة مع PostgreSQL

- أداء أفضل مع البيانات الكبيرة
- دعم أفضل للمعاملات المتزامنة
- إمكانيات نسخ احتياطي متقدمة
- دعم أنواع بيانات متقدمة
- قابلية توسع أفضل

## النسخ الاحتياطي والاستعادة

### إنشاء نسخة احتياطية
```bash
pg_dump -h localhost -U ******** -d FishFarmDatabase > backup.sql
```

### استعادة النسخة الاحتياطية
```bash
psql -h localhost -U ******** -d FishFarmDatabase < backup.sql
```

## الدعم الفني

إذا واجهت أي مشاكل، تأكد من:
1. تشغيل PostgreSQL على المنفذ الصحيح
2. صحة بيانات الاتصال
3. وجود الصلاحيات المناسبة للمستخدم
4. تطبيق جميع الهجرات بنجاح
