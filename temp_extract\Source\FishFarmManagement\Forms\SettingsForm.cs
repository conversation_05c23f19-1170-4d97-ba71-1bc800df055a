﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ Ø§Ù„Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª
    /// Settings form
    /// </summary>
    public partial class SettingsForm : Form
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<SettingsForm> _logger;

        // UI Controls
        private TabControl tabControl;
        private TabPage generalTabPage;
        private TabPage databaseTabPage;
        private TabPage farmInfoTabPage;

        public SettingsForm(IConfiguration configuration, ILogger<SettingsForm> logger)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "Ø§Ù„Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            CreateTabControl();
            CreateGeneralTab();
            CreateDatabaseTab();
            CreateFarmInfoTab();
        }

        private void CreateTabControl()
        {
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            generalTabPage = new TabPage("Ø§Ù„Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ø¹Ø§Ù…Ø©");
            databaseTabPage = new TabPage("Ù‚Ø§Ø¹Ø¯Ø© Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª");
            farmInfoTabPage = new TabPage("Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø§Ù„Ù…Ø²Ø±Ø¹Ø©");

            tabControl.TabPages.AddRange(new TabPage[] { generalTabPage, databaseTabPage, farmInfoTabPage });
            this.Controls.Add(tabControl);
        }

        private void CreateGeneralTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            int y = 20;
            int spacing = 40;

            // Application Name
            var appNameLabel = new Label
            {
                Text = "Ø§Ø³Ù… Ø§Ù„ØªØ·Ø¨ÙŠÙ‚:",
                Location = new Point(20, y),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            var appNameTextBox = new TextBox
            {
                Text = _configuration["Application:Name"] ?? "Ù†Ø¸Ø§Ù… Ø¥Ø¯Ø§Ø±Ø© Ù…Ø²Ø±Ø¹Ø© Ø§Ù„Ø£Ø³Ù…Ø§Ùƒ",
                Location = new Point(130, y),
                Size = new Size(300, 23),
                ReadOnly = true
            };

            y += spacing;

            // Application Version
            var versionLabel = new Label
            {
                Text = "Ø§Ù„Ø¥ØµØ¯Ø§Ø±:",
                Location = new Point(20, y),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            var versionTextBox = new TextBox
            {
                Text = _configuration["Application:Version"] ?? "1.0.0",
                Location = new Point(130, y),
                Size = new Size(300, 23),
                ReadOnly = true
            };

            y += spacing;

            // Language Settings
            var languageLabel = new Label
            {
                Text = "Ø§Ù„Ù„ØºØ©:",
                Location = new Point(20, y),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            var languageComboBox = new ComboBox
            {
                Location = new Point(130, y),
                Size = new Size(200, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            languageComboBox.Items.AddRange(new[] { "Ø§Ù„Ø¹Ø±Ø¨ÙŠØ©", "English" });
            languageComboBox.SelectedIndex = 0;

            y += spacing;

            // Auto Backup
            var autoBackupCheckBox = new CheckBox
            {
                Text = "Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠ Ø§Ù„ØªÙ„Ù‚Ø§Ø¦ÙŠ",
                Location = new Point(130, y),
                Size = new Size(200, 23),
                CheckAlign = ContentAlignment.MiddleRight,
                Checked = true
            };

            panel.Controls.AddRange(new Control[]
            {
                appNameLabel, appNameTextBox,
                versionLabel, versionTextBox,
                languageLabel, languageComboBox,
                autoBackupCheckBox
            });

            generalTabPage.Controls.Add(panel);
        }

        private void CreateDatabaseTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            int y = 20;
            int spacing = 40;

            // Connection String
            var connectionLabel = new Label
            {
                Text = "Ø³Ù„Ø³Ù„Ø© Ø§Ù„Ø§ØªØµØ§Ù„:",
                Location = new Point(20, y),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            var connectionTextBox = new TextBox
            {
                Text = _configuration.GetConnectionString("DefaultConnection") ?? "",
                Location = new Point(130, y),
                Size = new Size(350, 23),
                ReadOnly = true
            };

            y += spacing;

            // Auto Migrate
            var autoMigrateCheckBox = new CheckBox
            {
                Text = "Ø§Ù„ØªØ­Ø¯ÙŠØ« Ø§Ù„ØªÙ„Ù‚Ø§Ø¦ÙŠ Ù„Ù‚Ø§Ø¹Ø¯Ø© Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª",
                Location = new Point(130, y),
                Size = new Size(250, 23),
                CheckAlign = ContentAlignment.MiddleRight,
                Checked = _configuration.GetValue<bool>("Database:AutoMigrate")
            };

            y += spacing;

            // Database Actions
            var backupButton = new Button
            {
                Text = "Ø¥Ù†Ø´Ø§Ø¡ Ù†Ø³Ø®Ø© Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©",
                Location = new Point(130, y),
                Size = new Size(150, 30)
            };
            backupButton.Click += BackupDatabase_Click;

            var optimizeButton = new Button
            {
                Text = "ØªØ­Ø³ÙŠÙ† Ù‚Ø§Ø¹Ø¯Ø© Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª",
                Location = new Point(290, y),
                Size = new Size(150, 30)
            };
            optimizeButton.Click += OptimizeDatabase_Click;

            panel.Controls.AddRange(new Control[]
            {
                connectionLabel, connectionTextBox,
                autoMigrateCheckBox,
                backupButton, optimizeButton
            });

            databaseTabPage.Controls.Add(panel);
        }

        private void CreateFarmInfoTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            int y = 20;
            int spacing = 35;

            // Farm Name
            var farmNameLabel = new Label
            {
                Text = "Ø§Ø³Ù… Ø§Ù„Ù…Ø²Ø±Ø¹Ø©:",
                Location = new Point(20, y),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            var farmNameTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(300, 23)
            };

            y += spacing;

            // Owner Name
            var ownerNameLabel = new Label
            {
                Text = "Ø§Ø³Ù… Ø§Ù„Ù…Ø§Ù„Ùƒ:",
                Location = new Point(20, y),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            var ownerNameTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(300, 23)
            };

            y += spacing;

            // Address
            var addressLabel = new Label
            {
                Text = "Ø§Ù„Ø¹Ù†ÙˆØ§Ù†:",
                Location = new Point(20, y),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            var addressTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(350, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            y += 80;

            // Phone
            var phoneLabel = new Label
            {
                Text = "Ø§Ù„Ù‡Ø§ØªÙ:",
                Location = new Point(20, y),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            var phoneTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(200, 23)
            };

            y += spacing + 20;

            // Save Button
            var saveButton = new Button
            {
                Text = "Ø­ÙØ¸ Ø§Ù„Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª",
                Location = new Point(200, y),
                Size = new Size(120, 30)
            };
            saveButton.Click += SaveSettings_Click;

            panel.Controls.AddRange(new Control[]
            {
                farmNameLabel, farmNameTextBox,
                ownerNameLabel, ownerNameTextBox,
                addressLabel, addressTextBox,
                phoneLabel, phoneTextBox,
                saveButton
            });

            farmInfoTabPage.Controls.Add(panel);
        }

        private void BackupDatabase_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("Ø¥Ù†Ø´Ø§Ø¡ Ù†Ø³Ø®Ø© Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void OptimizeDatabase_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("ØªØ­Ø³ÙŠÙ† Ù‚Ø§Ø¹Ø¯Ø© Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void SaveSettings_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("ØªÙ… Ø­ÙØ¸ Ø§Ù„Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}



