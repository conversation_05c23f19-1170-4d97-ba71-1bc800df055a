using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// الموظف
    /// Employee
    /// </summary>
    public class Employee : BaseEntity
    {
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم الكامل يجب أن يكون أقل من 100 حرف")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الجنسية مطلوبة")]
        [StringLength(50, ErrorMessage = "الجنسية يجب أن تكون أقل من 50 حرف")]
        public string Nationality { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "رقم الإقامة يجب أن يكون أقل من 50 حرف")]
        public string ResidenceNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "المنصب مطلوب")]
        [StringLength(100, ErrorMessage = "المنصب يجب أن يكون أقل من 100 حرف")]
        public string Position { get; set; } = string.Empty;

        [Required(ErrorMessage = "تاريخ الانضمام مطلوب")]
        public DateTime JoinDate { get; set; }

        public DateTime? LeaveDate { get; set; }

        [Required(ErrorMessage = "الراتب الأساسي مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "الراتب الأساسي يجب أن يكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal BaseSalary { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "نشط"; // نشط، متوقف، مستقيل، مفصول

        [StringLength(200, ErrorMessage = "معلومات الاتصال يجب أن تكون أقل من 200 حرف")]
        public string ContactInfo { get; set; } = string.Empty;

        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// العنوان
        /// Address
        /// </summary>
        [StringLength(500)]
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// رقم الهوية الوطنية
        /// National ID number
        /// </summary>
        [StringLength(20)]
        public string NationalId { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الميلاد
        /// Birth date
        /// </summary>
        public DateTime? BirthDate { get; set; }

        /// <summary>
        /// الحالة الاجتماعية
        /// Marital status
        /// </summary>
        [StringLength(20)]
        public string MaritalStatus { get; set; } = string.Empty;

        /// <summary>
        /// عدد الأطفال
        /// Number of children
        /// </summary>
        [Range(0, 20)]
        public int NumberOfChildren { get; set; }

        // Navigation Properties
        public virtual ICollection<Payroll> Payrolls { get; set; } = new List<Payroll>();

        /// <summary>
        /// حساب عدد سنوات الخدمة
        /// Calculate years of service
        /// </summary>
        public int GetYearsOfService()
        {
            var endDate = LeaveDate ?? DateTime.Now;
            return (endDate - JoinDate).Days / 365;
        }

        /// <summary>
        /// حساب عدد أشهر الخدمة
        /// Calculate months of service
        /// </summary>
        public int GetMonthsOfService()
        {
            var endDate = LeaveDate ?? DateTime.Now;
            return ((endDate.Year - JoinDate.Year) * 12) + endDate.Month - JoinDate.Month;
        }

        /// <summary>
        /// التحقق من أن الموظف نشط
        /// Check if employee is active
        /// </summary>
        public bool IsActive => Status == "نشط" && !LeaveDate.HasValue;

        /// <summary>
        /// حساب العمر
        /// Calculate age
        /// </summary>
        public int GetAge()
        {
            if (!BirthDate.HasValue) return 0;
            
            var today = DateTime.Today;
            var age = today.Year - BirthDate.Value.Year;
            if (BirthDate.Value.Date > today.AddYears(-age)) age--;
            return age;
        }
    }
}
