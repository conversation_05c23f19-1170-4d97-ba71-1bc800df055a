﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.Models;
using FishFarmManagement.DAL.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ø¯ÙˆØ±Ø§Øª Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©
    /// Production cycle management form
    /// </summary>
    public partial class ProductionCycleForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProductionCycleForm> _logger;

        // UI Controls
        private DataGridView cyclesDataGridView;
        private ToolStripButton addButton;
        private ToolStripButton editButton;
        private ToolStripButton deleteButton;
        private ToolStripButton refreshButton;
        private ToolStripButton viewDetailsButton;
        private ToolStripButton completeCycleButton;
        private TextBox searchTextBox;
        private ComboBox statusFilterComboBox;
        private ToolStrip toolStrip;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusStripLabel;

        public ProductionCycleForm(IUnitOfWork unitOfWork, ILogger<ProductionCycleForm> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            LoadCyclesAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ø¯ÙˆØ±Ø§Øª Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            CreateToolStrip();
            CreateMainPanel();
            CreateStatusStrip();
        }

        private void CreateToolStrip()
        {
            toolStrip = new ToolStrip
            {
                ImageScalingSize = new Size(24, 24),
                Font = new Font("Segoe UI", 9F)
            };

            addButton = new ToolStripButton("Ø¥Ø¶Ø§ÙØ© Ø¯ÙˆØ±Ø© Ø¬Ø¯ÙŠØ¯Ø©", null, AddCycle_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };

            editButton = new ToolStripButton("ØªØ¹Ø¯ÙŠÙ„", null, EditCycle_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            deleteButton = new ToolStripButton("Ø­Ø°Ù", null, DeleteCycle_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            completeCycleButton = new ToolStripButton("Ø¥Ù†Ù‡Ø§Ø¡ Ø§Ù„Ø¯ÙˆØ±Ø©", null, CompleteCycle_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            viewDetailsButton = new ToolStripButton("Ø¹Ø±Ø¶ Ø§Ù„ØªÙØ§ØµÙŠÙ„", null, ViewDetails_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            refreshButton = new ToolStripButton("ØªØ­Ø¯ÙŠØ«", null, RefreshData_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };

            toolStrip.Items.AddRange(new ToolStripItem[]
            {
                addButton,
                new ToolStripSeparator(),
                editButton,
                deleteButton,
                new ToolStripSeparator(),
                completeCycleButton,
                new ToolStripSeparator(),
                viewDetailsButton,
                new ToolStripSeparator(),
                refreshButton
            });

            this.Controls.Add(toolStrip);
        }

        private void CreateMainPanel()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            // Create filter panel
            var filterPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Top
            };

            // Search textbox
            var searchLabel = new Label
            {
                Text = "Ø§Ù„Ø¨Ø­Ø«:",
                Location = new Point(10, 15),
                Size = new Size(50, 20)
            };

            searchTextBox = new TextBox
            {
                Location = new Point(70, 12),
                Size = new Size(200, 25)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            // Status filter
            var statusLabel = new Label
            {
                Text = "Ø§Ù„Ø­Ø§Ù„Ø©:",
                Location = new Point(290, 15),
                Size = new Size(50, 20)
            };

            statusFilterComboBox = new ComboBox
            {
                Location = new Point(350, 12),
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            statusFilterComboBox.Items.AddRange(new[] { "Ø§Ù„ÙƒÙ„", "Ù†Ø´Ø·", "Ù…ÙƒØªÙ…Ù„", "Ù…ØªÙˆÙ‚Ù", "Ù…Ù„ØºÙŠ" });
            statusFilterComboBox.SelectedIndex = 0;
            statusFilterComboBox.SelectedIndexChanged += StatusFilter_SelectedIndexChanged;

            filterPanel.Controls.AddRange(new Control[] { searchLabel, searchTextBox, statusLabel, statusFilterComboBox });

            // Create data grid view
            cyclesDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            SetupDataGridViewColumns();
            cyclesDataGridView.SelectionChanged += CyclesDataGridView_SelectionChanged;
            cyclesDataGridView.CellDoubleClick += CyclesDataGridView_CellDoubleClick;

            mainPanel.Controls.Add(cyclesDataGridView);
            mainPanel.Controls.Add(filterPanel);
            this.Controls.Add(mainPanel);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();
            statusStripLabel = new ToolStripStatusLabel("Ø¬Ø§Ù‡Ø²")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };
            statusStrip.Items.Add(statusStripLabel);
            this.Controls.Add(statusStrip);
        }

        private void SetupDataGridViewColumns()
        {
            cyclesDataGridView.Columns.Clear();

            cyclesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "Ø§Ù„Ù…Ø¹Ø±Ù",
                DataPropertyName = "Id",
                Visible = false
            });

            cyclesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CycleName",
                HeaderText = "Ø§Ø³Ù… Ø§Ù„Ø¯ÙˆØ±Ø©",
                DataPropertyName = "CycleName",
                Width = 150
            });

            cyclesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "StartDate",
                HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø¨Ø¯Ø§ÙŠØ©",
                DataPropertyName = "StartDate",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            cyclesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ExpectedEndDate",
                HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ù†Ù‡Ø§ÙŠØ© Ø§Ù„Ù…ØªÙˆÙ‚Ø¹",
                DataPropertyName = "ExpectedEndDate",
                Width = 140,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            cyclesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "EndDate",
                HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ù†Ù‡Ø§ÙŠØ© Ø§Ù„ÙØ¹Ù„ÙŠ",
                DataPropertyName = "EndDate",
                Width = 140,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            cyclesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "Ø§Ù„Ø­Ø§Ù„Ø©",
                DataPropertyName = "Status",
                Width = 80
            });

            cyclesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "BudgetAmount",
                HeaderText = "Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ©",
                DataPropertyName = "BudgetAmount",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            cyclesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Notes",
                HeaderText = "Ø§Ù„Ù…Ù„Ø§Ø­Ø¸Ø§Øª",
                DataPropertyName = "Notes",
                Width = 200
            });

            cyclesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø¥Ù†Ø´Ø§Ø¡",
                DataPropertyName = "CreatedDate",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd HH:mm" }
            });
        }

        private async void LoadCyclesAsync()
        {
            try
            {
                statusStripLabel.Text = "Ø¬Ø§Ø±ÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª...";
                this.Cursor = Cursors.WaitCursor;

                var cycles = await _unitOfWork.ProductionCycles.GetAllAsync();
                cyclesDataGridView.DataSource = cycles.ToList();

                statusStripLabel.Text = $"ØªÙ… ØªØ­Ù…ÙŠÙ„ {cycles.Count()} Ø¯ÙˆØ±Ø© Ø¥Ù†ØªØ§Ø¬ÙŠØ©";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø¯ÙˆØ±Ø§Øª Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusStripLabel.Text = "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª";
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void CyclesDataGridView_SelectionChanged(object? sender, EventArgs e)
        {
            bool hasSelection = cyclesDataGridView.SelectedRows.Count > 0;
            editButton.Enabled = hasSelection;
            deleteButton.Enabled = hasSelection;
            viewDetailsButton.Enabled = hasSelection;

            if (hasSelection)
            {
                var selectedCycle = cyclesDataGridView.SelectedRows[0].DataBoundItem as ProductionCycle;
                completeCycleButton.Enabled = selectedCycle?.Status == "Ù†Ø´Ø·";
            }
            else
            {
                completeCycleButton.Enabled = false;
            }
        }

        private void CyclesDataGridView_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                ViewDetails_Click(sender, e);
            }
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void StatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            try
            {
                var bindingSource = cyclesDataGridView.DataSource as List<ProductionCycle>;
                if (bindingSource == null) return;

                var filteredData = bindingSource.AsEnumerable();

                // Apply search filter
                if (!string.IsNullOrWhiteSpace(searchTextBox.Text))
                {
                    var searchTerm = searchTextBox.Text.ToLower();
                    filteredData = filteredData.Where(c =>
                        c.CycleName.ToLower().Contains(searchTerm) ||
                        c.Notes.ToLower().Contains(searchTerm));
                }

                // Apply status filter
                if (statusFilterComboBox.SelectedIndex > 0)
                {
                    var selectedStatus = statusFilterComboBox.SelectedItem.ToString();
                    filteredData = filteredData.Where(c => c.Status == selectedStatus);
                }

                cyclesDataGridView.DataSource = filteredData.ToList();
                statusStripLabel.Text = $"Ø¹Ø±Ø¶ {filteredData.Count()} Ù…Ù† Ø£ØµÙ„ {bindingSource.Count} Ø¯ÙˆØ±Ø©";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ·Ø¨ÙŠÙ‚ Ø§Ù„Ù…Ø±Ø´Ø­Ø§Øª");
            }
        }

        private void AddCycle_Click(object? sender, EventArgs e)
        {
            try
            {
                var addForm = new ProductionCycleAddEditForm(_unitOfWork, _logger);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadCyclesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¶Ø§ÙØ© Ø¯ÙˆØ±Ø© Ø¥Ù†ØªØ§Ø¬ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ø§Ù„Ù†Ù…ÙˆØ°Ø¬: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditCycle_Click(object? sender, EventArgs e)
        {
            try
            {
                if (cyclesDataGridView.SelectedRows.Count == 0) return;

                var selectedCycle = cyclesDataGridView.SelectedRows[0].DataBoundItem as ProductionCycle;
                if (selectedCycle == null) return;

                var editForm = new ProductionCycleAddEditForm(_unitOfWork, _logger, selectedCycle);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadCyclesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ ØªØ¹Ø¯ÙŠÙ„ Ø¯ÙˆØ±Ø© Ø¥Ù†ØªØ§Ø¬ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ø§Ù„Ù†Ù…ÙˆØ°Ø¬: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteCycle_Click(object? sender, EventArgs e)
        {
            try
            {
                if (cyclesDataGridView.SelectedRows.Count == 0) return;

                var selectedCycle = cyclesDataGridView.SelectedRows[0].DataBoundItem as ProductionCycle;
                if (selectedCycle == null) return;

                var result = MessageBox.Show(
                    $"Ù‡Ù„ Ø£Ù†Øª Ù…ØªØ£ÙƒØ¯ Ù…Ù† Ø­Ø°Ù Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ© '{selectedCycle.CycleName}'ØŸ\n\nÙ‡Ø°Ø§ Ø§Ù„Ø¥Ø¬Ø±Ø§Ø¡ Ù„Ø§ ÙŠÙ…ÙƒÙ† Ø§Ù„ØªØ±Ø§Ø¬Ø¹ Ø¹Ù†Ù‡.",
                    "ØªØ£ÙƒÙŠØ¯ Ø§Ù„Ø­Ø°Ù",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    statusStripLabel.Text = "Ø¬Ø§Ø±ÙŠ Ø­Ø°Ù Ø§Ù„Ø¯ÙˆØ±Ø©...";
                    this.Cursor = Cursors.WaitCursor;

                    await _unitOfWork.ProductionCycles.DeleteAsync(selectedCycle);
                    await _unitOfWork.SaveChangesAsync();

                    MessageBox.Show("ØªÙ… Ø­Ø°Ù Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ© Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadCyclesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„Ø¯ÙˆØ±Ø©: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                statusStripLabel.Text = "Ø¬Ø§Ù‡Ø²";
            }
        }

        private async void CompleteCycle_Click(object? sender, EventArgs e)
        {
            try
            {
                if (cyclesDataGridView.SelectedRows.Count == 0) return;

                var selectedCycle = cyclesDataGridView.SelectedRows[0].DataBoundItem as ProductionCycle;
                if (selectedCycle == null || selectedCycle.Status != "Ù†Ø´Ø·") return;

                var result = MessageBox.Show(
                    $"Ù‡Ù„ Ø£Ù†Øª Ù…ØªØ£ÙƒØ¯ Ù…Ù† Ø¥Ù†Ù‡Ø§Ø¡ Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ© '{selectedCycle.CycleName}'ØŸ\n\nØ³ÙŠØªÙ… ØªØºÙŠÙŠØ± Ø­Ø§Ù„ØªÙ‡Ø§ Ø¥Ù„Ù‰ 'Ù…ÙƒØªÙ…Ù„' ÙˆØªØ­Ø¯ÙŠØ¯ ØªØ§Ø±ÙŠØ® Ø§Ù„Ù†Ù‡Ø§ÙŠØ© Ø§Ù„ÙØ¹Ù„ÙŠ.",
                    "ØªØ£ÙƒÙŠØ¯ Ø¥Ù†Ù‡Ø§Ø¡ Ø§Ù„Ø¯ÙˆØ±Ø©",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    statusStripLabel.Text = "Ø¬Ø§Ø±ÙŠ Ø¥Ù†Ù‡Ø§Ø¡ Ø§Ù„Ø¯ÙˆØ±Ø©...";
                    this.Cursor = Cursors.WaitCursor;

                    selectedCycle.Status = "Ù…ÙƒØªÙ…Ù„";
                    selectedCycle.EndDate = DateTime.Now;
                    selectedCycle.UpdateModificationDate();

                    await _unitOfWork.ProductionCycles.UpdateAsync(selectedCycle);
                    await _unitOfWork.SaveChangesAsync();

                    MessageBox.Show("ØªÙ… Ø¥Ù†Ù‡Ø§Ø¡ Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ© Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadCyclesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ù‡Ø§Ø¡ Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ù‡Ø§Ø¡ Ø§Ù„Ø¯ÙˆØ±Ø©: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                statusStripLabel.Text = "Ø¬Ø§Ù‡Ø²";
            }
        }

        private void ViewDetails_Click(object? sender, EventArgs e)
        {
            try
            {
                if (cyclesDataGridView.SelectedRows.Count == 0) return;

                var selectedCycle = cyclesDataGridView.SelectedRows[0].DataBoundItem as ProductionCycle;
                if (selectedCycle == null) return;

                var detailsForm = new ProductionCycleDetailsForm(selectedCycle, _unitOfWork);
                detailsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¹Ø±Ø¶ ØªÙØ§ØµÙŠÙ„ Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¹Ø±Ø¶ Ø§Ù„ØªÙØ§ØµÙŠÙ„: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshData_Click(object? sender, EventArgs e)
        {
            LoadCyclesAsync();
        }
    }
}



