# Fix duplicate private async void declarations
$files = @(
    "FishFarmManagement\Forms\BalanceSheetReportForm.cs",
    "FishFarmManagement\Forms\EnhancedDashboardForm.cs", 
    "FishFarmManagement\Forms\EmployeeManagementForm.cs",
    "FishFarmManagement\Forms\IncomeStatementReportForm.cs",
    "FishFarmManagement\Forms\EnhancedAccountingForm.cs",
    "FishFarmManagement\Forms\PondsReportForm.cs",
    "FishFarmManagement\Forms\EnhancedBackupForm.cs",
    "FishFarmManagement\Forms\PondManagementForm.cs",
    "FishFarmManagement\Forms\ProductionReportForm.cs"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Fixing $file"
        $content = Get-Content $file -Raw
        $content = $content -replace 'private async void private async void private async void', 'private async void'
        Set-Content -Path $file -Value $content -NoNewline
    }
}

Write-Host "Done fixing syntax errors"
