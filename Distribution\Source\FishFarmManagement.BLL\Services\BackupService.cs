using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

using System.IO.Compression;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة النسخ الاحتياطي المبسطة
    /// Simplified backup service
    /// </summary>
    public class BackupService
    {
        private readonly ILogger<BackupService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _backupPath;
        private readonly string _databasePath;

        public BackupService(ILogger<BackupService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _backupPath = _configuration["Backup:BackupPath"] ?? "Backups";
            _databasePath = _configuration.GetConnectionString("DefaultConnection")?.Replace("Data Source=", "") ?? "FishFarmDatabase.db";
            
            // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            if (!Directory.Exists(_backupPath))
            {
                Directory.CreateDirectory(_backupPath);
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// Create backup
        /// </summary>
        public async Task<BackupResult> CreateBackupAsync(string? backupName = null)
        {
            try
            {
                _logger.LogInformation("بدء إنشاء نسخة احتياطية");

                backupName ??= $"backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                var backupFileName = $"{backupName}.zip";
                var backupFilePath = Path.Combine(_backupPath, backupFileName);

                // إنشاء مجلد مؤقت للنسخة الاحتياطية
                var tempBackupDir = Path.Combine(Path.GetTempPath(), $"fishfarm_backup_{Guid.NewGuid():N}");
                Directory.CreateDirectory(tempBackupDir);

                try
                {
                    // نسخ قاعدة البيانات
                    if (File.Exists(_databasePath))
                    {
                        var dbBackupPath = Path.Combine(tempBackupDir, Path.GetFileName(_databasePath));
                        File.Copy(_databasePath, dbBackupPath, true);
                        _logger.LogDebug("تم نسخ قاعدة البيانات");
                    }

                    // نسخ ملفات الإعدادات
                    var configFiles = new[] { "appsettings.json", "appsettings.Production.json" };
                    foreach (var configFile in configFiles)
                    {
                        if (File.Exists(configFile))
                        {
                            var configBackupPath = Path.Combine(tempBackupDir, configFile);
                            File.Copy(configFile, configBackupPath, true);
                        }
                    }

                    // نسخ مجلد السجلات (آخر 7 أيام فقط)
                    var logsPath = "Logs";
                    if (Directory.Exists(logsPath))
                    {
                        var logsBackupPath = Path.Combine(tempBackupDir, "Logs");
                        Directory.CreateDirectory(logsBackupPath);
                        
                        var recentLogFiles = Directory.GetFiles(logsPath, "*.txt")
                            .Where(f => File.GetCreationTime(f) > DateTime.Now.AddDays(-7));
                        
                        foreach (var logFile in recentLogFiles)
                        {
                            var logBackupPath = Path.Combine(logsBackupPath, Path.GetFileName(logFile));
                            File.Copy(logFile, logBackupPath, true);
                        }
                    }

                    // إنشاء ملف معلومات النسخة الاحتياطية
                    var infoFilePath = Path.Combine(tempBackupDir, "backup_info.txt");
                    var backupInfo = $@"نسخة احتياطية لنظام إدارة مزرعة الأسماك
تاريخ الإنشاء: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
اسم النسخة: {backupName}
إصدار النظام: 1.0.1
";
                    await File.WriteAllTextAsync(infoFilePath, backupInfo);

                    // ضغط النسخة الاحتياطية
                    ZipFile.CreateFromDirectory(tempBackupDir, backupFilePath, CompressionLevel.Optimal, false);

                    var backupFileInfo = new FileInfo(backupFilePath);
                    var result = new BackupResult
                    {
                        IsSuccess = true,
                        BackupPath = backupFilePath,
                        BackupName = backupName,
                        CreatedDate = DateTime.Now,
                        FileSizeBytes = backupFileInfo.Length,
                        Message = "تم إنشاء النسخة الاحتياطية بنجاح"
                    };

                    _logger.LogInformation("تم إنشاء النسخة الاحتياطية بنجاح: {BackupPath}", backupFilePath);
                    return result;
                }
                finally
                {
                    // تنظيف المجلد المؤقت
                    if (Directory.Exists(tempBackupDir))
                    {
                        Directory.Delete(tempBackupDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية");
                return new BackupResult
                {
                    IsSuccess = false,
                    Message = $"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// استعادة نسخة احتياطية
        /// Restore backup
        /// </summary>
        public async Task<RestoreResult> RestoreBackupAsync(string backupFilePath)
        {
            try
            {
                _logger.LogInformation("بدء استعادة النسخة الاحتياطية: {BackupPath}", backupFilePath);

                if (!File.Exists(backupFilePath))
                {
                    return new RestoreResult
                    {
                        IsSuccess = false,
                        Message = "ملف النسخة الاحتياطية غير موجود"
                    };
                }

                // إنشاء مجلد مؤقت للاستعادة
                var tempRestoreDir = Path.Combine(Path.GetTempPath(), $"fishfarm_restore_{Guid.NewGuid():N}");
                Directory.CreateDirectory(tempRestoreDir);

                try
                {
                    // فك ضغط النسخة الاحتياطية
                    ZipFile.ExtractToDirectory(backupFilePath, tempRestoreDir);

                    // استعادة قاعدة البيانات
                    var dbBackupPath = Path.Combine(tempRestoreDir, Path.GetFileName(_databasePath));
                    if (File.Exists(dbBackupPath))
                    {
                        // إنشاء نسخة احتياطية من قاعدة البيانات الحالية
                        if (File.Exists(_databasePath))
                        {
                            var currentDbBackup = $"{_databasePath}.backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                            File.Copy(_databasePath, currentDbBackup, true);
                            _logger.LogInformation("تم إنشاء نسخة احتياطية من قاعدة البيانات الحالية");
                        }

                        File.Copy(dbBackupPath, _databasePath, true);
                        _logger.LogDebug("تم استعادة قاعدة البيانات");
                    }

                    // استعادة ملفات الإعدادات
                    var configFiles = new[] { "appsettings.json", "appsettings.Production.json" };
                    foreach (var configFile in configFiles)
                    {
                        var configBackupPath = Path.Combine(tempRestoreDir, configFile);
                        if (File.Exists(configBackupPath))
                        {
                            File.Copy(configBackupPath, configFile, true);
                        }
                    }

                    var result = new RestoreResult
                    {
                        IsSuccess = true,
                        RestoredDate = DateTime.Now,
                        Message = "تم استعادة النسخة الاحتياطية بنجاح"
                    };

                    _logger.LogInformation("تم استعادة النسخة الاحتياطية بنجاح");
                    return result;
                }
                finally
                {
                    // تنظيف المجلد المؤقت
                    if (Directory.Exists(tempRestoreDir))
                    {
                        Directory.Delete(tempRestoreDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استعادة النسخة الاحتياطية");
                return new RestoreResult
                {
                    IsSuccess = false,
                    Message = $"خطأ في استعادة النسخة الاحتياطية: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية
        /// Get backup list
        /// </summary>
        public async Task<IEnumerable<BackupInfo>> GetBackupListAsync()
        {
            try
            {
                if (!Directory.Exists(_backupPath))
                {
                    return Enumerable.Empty<BackupInfo>();
                }

                var backupFiles = Directory.GetFiles(_backupPath, "*.zip");
                var backupInfos = new List<BackupInfo>();

                foreach (var backupFile in backupFiles)
                {
                    var fileInfo = new FileInfo(backupFile);
                    backupInfos.Add(new BackupInfo
                    {
                        Name = Path.GetFileNameWithoutExtension(backupFile),
                        FilePath = backupFile,
                        CreatedDate = fileInfo.CreationTime,
                        FileSizeBytes = fileInfo.Length
                    });
                }

                return await Task.FromResult(backupInfos.OrderByDescending(b => b.CreatedDate));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على قائمة النسخ الاحتياطية");
                return Enumerable.Empty<BackupInfo>();
            }
        }

        /// <summary>
        /// حذف نسخة احتياطية
        /// Delete backup
        /// </summary>
        public async Task<bool> DeleteBackupAsync(string backupFilePath)
        {
            try
            {
                if (File.Exists(backupFilePath))
                {
                    File.Delete(backupFilePath);
                    _logger.LogInformation("تم حذف النسخة الاحتياطية: {BackupPath}", backupFilePath);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف النسخة الاحتياطية: {BackupPath}", backupFilePath);
                return false;
            }
        }
    }

    // النماذج المساعدة
    public class BackupResult
    {
        public bool IsSuccess { get; set; }
        public string? BackupPath { get; set; }
        public string? BackupName { get; set; }
        public DateTime CreatedDate { get; set; }
        public long FileSizeBytes { get; set; }
        public string? Message { get; set; }
    }

    public class RestoreResult
    {
        public bool IsSuccess { get; set; }
        public DateTime RestoredDate { get; set; }
        public string? Message { get; set; }
    }

    public class BackupInfo
    {
        public string? Name { get; set; }
        public string? FilePath { get; set; }
        public DateTime CreatedDate { get; set; }
        public long FileSizeBytes { get; set; }
    }
}
