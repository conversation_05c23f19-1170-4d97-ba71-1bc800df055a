using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج فحص التحديثات
    /// Update check form
    /// </summary>
    public partial class UpdateCheckForm : Form
    {
        private readonly UpdateService _updateService;
        private readonly ILogger<UpdateCheckForm> _logger;
        private UpdateInfo? _availableUpdate;

        // Controls
        private Label _statusLabel;
        private ProgressBar _progressBar;
        private Button _checkButton;
        private Button _downloadButton;
        private Button _installButton;
        private Button _closeButton;
        private RichTextBox _releaseNotesTextBox;
        private Label _currentVersionLabel;
        private Label _newVersionLabel;

        public UpdateCheckForm(UpdateService updateService, ILogger<UpdateCheckForm> logger)
        {
            _updateService = updateService;
            _logger = logger;
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "فحص التحديثات - نظام إدارة مزرعة الأسماك";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Current version label
            _currentVersionLabel = new Label
            {
                Text = $"الإصدار الحالي: {GetCurrentVersion()}",
                Location = new Point(20, 20),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            // New version label
            _newVersionLabel = new Label
            {
                Text = "الإصدار الجديد: غير متاح",
                Location = new Point(20, 50),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.Gray
            };

            // Status label
            _statusLabel = new Label
            {
                Text = "جاهز للفحص",
                Location = new Point(20, 80),
                Size = new Size(550, 25),
                Font = new Font("Segoe UI", 9F)
            };

            // Progress bar
            _progressBar = new ProgressBar
            {
                Location = new Point(20, 110),
                Size = new Size(550, 25),
                Visible = false
            };

            // Release notes
            var releaseNotesLabel = new Label
            {
                Text = "ملاحظات الإصدار:",
                Location = new Point(20, 150),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            _releaseNotesTextBox = new RichTextBox
            {
                Location = new Point(20, 175),
                Size = new Size(550, 200),
                ReadOnly = true,
                Font = new Font("Segoe UI", 9F),
                Text = "لا توجد ملاحظات متاحة"
            };

            // Buttons
            _checkButton = new Button
            {
                Text = "فحص التحديثات",
                Location = new Point(20, 390),
                Size = new Size(120, 35),
                Font = new Font("Segoe UI", 9F),
                UseVisualStyleBackColor = true
            };
            _checkButton.Click += CheckButton_Click;

            _downloadButton = new Button
            {
                Text = "تحميل التحديث",
                Location = new Point(150, 390),
                Size = new Size(120, 35),
                Font = new Font("Segoe UI", 9F),
                UseVisualStyleBackColor = true,
                Enabled = false
            };
            _downloadButton.Click += DownloadButton_Click;

            _installButton = new Button
            {
                Text = "تثبيت التحديث",
                Location = new Point(280, 390),
                Size = new Size(120, 35),
                Font = new Font("Segoe UI", 9F),
                UseVisualStyleBackColor = true,
                Enabled = false
            };
            _installButton.Click += InstallButton_Click;

            _closeButton = new Button
            {
                Text = "إغلاق",
                Location = new Point(450, 390),
                Size = new Size(120, 35),
                Font = new Font("Segoe UI", 9F),
                UseVisualStyleBackColor = true,
                DialogResult = DialogResult.Cancel
            };

            // Add controls to form
            this.Controls.AddRange(new Control[]
            {
                _currentVersionLabel,
                _newVersionLabel,
                _statusLabel,
                _progressBar,
                releaseNotesLabel,
                _releaseNotesTextBox,
                _checkButton,
                _downloadButton,
                _installButton,
                _closeButton
            });

            this.ResumeLayout(false);
        }

        private void InitializeForm()
        {
            try
            {
                _logger.LogInformation("تم فتح نموذج فحص التحديثات");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة نموذج فحص التحديثات");
            }
        }

        private async void CheckButton_Click(object? sender, EventArgs e)
        {
            try
            {
                SetControlsEnabled(false);
                _statusLabel.Text = "جاري فحص التحديثات...";
                _statusLabel.ForeColor = Color.Blue;
                _progressBar.Visible = true;
                _progressBar.Style = ProgressBarStyle.Marquee;

                _availableUpdate = await _updateService.CheckForUpdatesAsync();

                if (_availableUpdate != null)
                {
                    _statusLabel.Text = "تحديث جديد متاح!";
                    _statusLabel.ForeColor = Color.Green;
                    _newVersionLabel.Text = $"الإصدار الجديد: {_availableUpdate.Version}";
                    _newVersionLabel.ForeColor = Color.Green;
                    _releaseNotesTextBox.Text = _availableUpdate.ReleaseNotes;
                    _downloadButton.Enabled = true;

                    if (_availableUpdate.IsRequired)
                    {
                        _statusLabel.Text += " (تحديث مطلوب)";
                        _statusLabel.ForeColor = Color.Red;
                    }
                }
                else
                {
                    _statusLabel.Text = "لا توجد تحديثات جديدة";
                    _statusLabel.ForeColor = Color.Gray;
                    _newVersionLabel.Text = "الإصدار الجديد: غير متاح";
                    _newVersionLabel.ForeColor = Color.Gray;
                    _releaseNotesTextBox.Text = "أنت تستخدم أحدث إصدار من البرنامج.";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص التحديثات");
                _statusLabel.Text = "خطأ في فحص التحديثات";
                _statusLabel.ForeColor = Color.Red;
                MessageBox.Show("حدث خطأ أثناء فحص التحديثات. يرجى المحاولة لاحقاً.", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _progressBar.Visible = false;
                SetControlsEnabled(true);
            }
        }

        private async void DownloadButton_Click(object? sender, EventArgs e)
        {
            if (_availableUpdate == null) return;

            try
            {
                SetControlsEnabled(false);
                _statusLabel.Text = "جاري تحميل التحديث...";
                _statusLabel.ForeColor = Color.Blue;
                _progressBar.Visible = true;
                _progressBar.Style = ProgressBarStyle.Continuous;
                _progressBar.Value = 0;

                var progress = new Progress<int>(value =>
                {
                    if (InvokeRequired)
                    {
                        Invoke(() => _progressBar.Value = value);
                    }
                    else
                    {
                        _progressBar.Value = value;
                    }
                });

                var success = await _updateService.DownloadAndInstallUpdateAsync(_availableUpdate, progress);

                if (success)
                {
                    _statusLabel.Text = "تم تحميل التحديث بنجاح";
                    _statusLabel.ForeColor = Color.Green;
                    _installButton.Enabled = true;
                    _downloadButton.Enabled = false;

                    MessageBox.Show("تم تحميل التحديث بنجاح. يمكنك الآن تثبيته.", "نجح التحميل",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    _statusLabel.Text = "فشل في تحميل التحديث";
                    _statusLabel.ForeColor = Color.Red;
                    MessageBox.Show("فشل في تحميل التحديث. يرجى المحاولة لاحقاً.", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل التحديث");
                _statusLabel.Text = "خطأ في تحميل التحديث";
                _statusLabel.ForeColor = Color.Red;
                MessageBox.Show("حدث خطأ أثناء تحميل التحديث.", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _progressBar.Visible = false;
                SetControlsEnabled(true);
            }
        }

        private void InstallButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "سيتم إعادة تشغيل البرنامج لتطبيق التحديث. هل تريد المتابعة؟",
                    "تأكيد التثبيت",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _logger.LogInformation("بدء تثبيت التحديث");
                    _updateService.RestartApplication();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تثبيت التحديث");
                MessageBox.Show("حدث خطأ أثناء تثبيت التحديث.", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetControlsEnabled(bool enabled)
        {
            _checkButton.Enabled = enabled;
            _downloadButton.Enabled = enabled && _availableUpdate != null;
            _installButton.Enabled = enabled && _availableUpdate != null;
        }

        private string GetCurrentVersion()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "1.0.0.0";
            }
            catch
            {
                return "1.0.0.0";
            }
        }
    }
}
