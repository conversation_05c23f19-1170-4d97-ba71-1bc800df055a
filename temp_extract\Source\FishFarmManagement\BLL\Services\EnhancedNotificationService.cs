using Microsoft.Extensions.Logging;
using FishFarmManagement.Models;
using FishFarmManagement.DAL.Interfaces;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة التنبيهات المحسنة
    /// Enhanced notification service
    /// </summary>
    public class EnhancedNotificationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<EnhancedNotificationService> _logger;

        public EnhancedNotificationService(IUnitOfWork unitOfWork, ILogger<EnhancedNotificationService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// فحص جميع التنبيهات والإشعارات
        /// Check all alerts and notifications
        /// </summary>
        public async Task<List<SystemAlert>> CheckAllAlertsAsync()
        {
            try
            {
                _logger.LogInformation("فحص جميع التنبيهات");

                var alerts = new List<SystemAlert>();

                // فحص تنبيهات المخزون
                var inventoryAlerts = await CheckInventoryAlertsAsync();
                alerts.AddRange(inventoryAlerts);

                // فحص تنبيهات الأحواض
                var pondAlerts = await CheckPondAlertsAsync();
                alerts.AddRange(pondAlerts);

                // فحص تنبيهات الدورات الإنتاجية
                var cycleAlerts = await CheckProductionCycleAlertsAsync();
                alerts.AddRange(cycleAlerts);

                // فحص التنبيهات المالية
                var financialAlerts = await CheckFinancialAlertsAsync();
                alerts.AddRange(financialAlerts);

                // فحص تنبيهات الموظفين
                var employeeAlerts = await CheckEmployeeAlertsAsync();
                alerts.AddRange(employeeAlerts);

                // فحص تنبيهات جودة المياه
                var waterQualityAlerts = await CheckWaterQualityAlertsAsync();
                alerts.AddRange(waterQualityAlerts);

                // ترتيب التنبيهات حسب الأولوية
                alerts = alerts.OrderByDescending(a => a.Priority)
                              .ThenByDescending(a => a.CreatedDate)
                              .ToList();

                _logger.LogInformation("تم فحص {AlertCount} تنبيه", alerts.Count);
                return alerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص التنبيهات");
                throw;
            }
        }

        /// <summary>
        /// فحص تنبيهات المخزون
        /// Check inventory alerts
        /// </summary>
        public async Task<List<SystemAlert>> CheckInventoryAlertsAsync()
        {
            var alerts = new List<SystemAlert>();

            try
            {
                var inventoryItems = await _unitOfWork.Inventories.GetAllAsync();

                foreach (var item in inventoryItems.Where(i => i.Status == InventoryStatus.Active))
                {
                    // تنبيه المخزون المنخفض
                    if (item.Quantity <= item.MinimumStock)
                    {
                        alerts.Add(new SystemAlert
                        {
                            Type = AlertType.LowStock,
                            Priority = item.Quantity == 0 ? AlertPriority.Critical : AlertPriority.High,
                            Title = "مخزون منخفض",
                            Message = $"الصنف '{item.ItemName}' وصل للحد الأدنى. الكمية الحالية: {item.Quantity} {item.Unit}",
                            RelatedEntityId = item.Id,
                            RelatedEntityType = "Inventory",
                            CreatedDate = DateTime.Now,
                            IsRead = false
                        });
                    }

                    // تنبيه انتهاء الصلاحية
                    if (item.ExpiryDate.HasValue)
                    {
                        var daysToExpiry = (item.ExpiryDate.Value - DateTime.Now).Days;
                        
                        if (daysToExpiry <= 0)
                        {
                            alerts.Add(new SystemAlert
                            {
                                Type = AlertType.Expired,
                                Priority = AlertPriority.Critical,
                                Title = "صنف منتهي الصلاحية",
                                Message = $"الصنف '{item.ItemName}' منتهي الصلاحية منذ {Math.Abs(daysToExpiry)} يوم",
                                RelatedEntityId = item.Id,
                                RelatedEntityType = "Inventory",
                                CreatedDate = DateTime.Now,
                                IsRead = false
                            });
                        }
                        else if (daysToExpiry <= 30)
                        {
                            alerts.Add(new SystemAlert
                            {
                                Type = AlertType.ExpiryWarning,
                                Priority = daysToExpiry <= 7 ? AlertPriority.High : AlertPriority.Medium,
                                Title = "تحذير انتهاء الصلاحية",
                                Message = $"الصنف '{item.ItemName}' سينتهي خلال {daysToExpiry} يوم",
                                RelatedEntityId = item.Id,
                                RelatedEntityType = "Inventory",
                                CreatedDate = DateTime.Now,
                                IsRead = false
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص تنبيهات المخزون");
            }

            return alerts;
        }

        /// <summary>
        /// فحص تنبيهات الأحواض
        /// Check pond alerts
        /// </summary>
        public async Task<List<SystemAlert>> CheckPondAlertsAsync()
        {
            var alerts = new List<SystemAlert>();

            try
            {
                var ponds = await _unitOfWork.Ponds.GetAllAsync();

                foreach (var pond in ponds.Where(p => p.Status == "نشط"))
                {
                    // تنبيه موعد الحصاد
                    if (pond.ExpectedHarvestDate.HasValue)
                    {
                        var daysToHarvest = (pond.ExpectedHarvestDate.Value - DateTime.Now).Days;
                        
                        if (daysToHarvest <= 0)
                        {
                            alerts.Add(new SystemAlert
                            {
                                Type = AlertType.HarvestDue,
                                Priority = AlertPriority.High,
                                Title = "موعد الحصاد",
                                Message = $"الحوض '{pond.PondNumber}' جاهز للحصاد",
                                RelatedEntityId = pond.Id,
                                RelatedEntityType = "Pond",
                                CreatedDate = DateTime.Now,
                                IsRead = false
                            });
                        }
                        else if (daysToHarvest <= 7)
                        {
                            alerts.Add(new SystemAlert
                            {
                                Type = AlertType.HarvestWarning,
                                Priority = AlertPriority.Medium,
                                Title = "اقتراب موعد الحصاد",
                                Message = $"الحوض '{pond.PondNumber}' سيكون جاهز للحصاد خلال {daysToHarvest} يوم",
                                RelatedEntityId = pond.Id,
                                RelatedEntityType = "Pond",
                                CreatedDate = DateTime.Now,
                                IsRead = false
                            });
                        }
                    }

                    // تنبيه معدل النفوق المرتفع
                    var mortalityRate = await CalculatePondMortalityRateAsync(pond.Id);
                    if (mortalityRate > 5) // أكثر من 5%
                    {
                        alerts.Add(new SystemAlert
                        {
                            Type = AlertType.HighMortality,
                            Priority = mortalityRate > 10 ? AlertPriority.Critical : AlertPriority.High,
                            Title = "معدل نفوق مرتفع",
                            Message = $"الحوض '{pond.PondNumber}' لديه معدل نفوق مرتفع: {mortalityRate:F1}%",
                            RelatedEntityId = pond.Id,
                            RelatedEntityType = "Pond",
                            CreatedDate = DateTime.Now,
                            IsRead = false
                        });
                    }

                    // تنبيه عدم التغذية
                    var lastFeedingDate = await GetLastFeedingDateAsync(pond.Id);
                    if (lastFeedingDate.HasValue)
                    {
                        var daysSinceFeeding = (DateTime.Now - lastFeedingDate.Value).Days;
                        if (daysSinceFeeding > 2)
                        {
                            alerts.Add(new SystemAlert
                            {
                                Type = AlertType.FeedingOverdue,
                                Priority = daysSinceFeeding > 3 ? AlertPriority.High : AlertPriority.Medium,
                                Title = "تأخر في التغذية",
                                Message = $"الحوض '{pond.PondNumber}' لم يتم تغذيته منذ {daysSinceFeeding} يوم",
                                RelatedEntityId = pond.Id,
                                RelatedEntityType = "Pond",
                                CreatedDate = DateTime.Now,
                                IsRead = false
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص تنبيهات الأحواض");
            }

            return alerts;
        }

        /// <summary>
        /// فحص تنبيهات الدورات الإنتاجية
        /// Check production cycle alerts
        /// </summary>
        public async Task<List<SystemAlert>> CheckProductionCycleAlertsAsync()
        {
            var alerts = new List<SystemAlert>();

            try
            {
                var cycles = await _unitOfWork.ProductionCycles.GetAllAsync();

                foreach (var cycle in cycles.Where(c => c.Status == "نشط"))
                {
                    // تنبيه اقتراب نهاية الدورة
                    if (cycle.ExpectedEndDate != default(DateTime))
                    {
                        var daysToEnd = (cycle.ExpectedEndDate - DateTime.Now).Days;
                        
                        if (daysToEnd <= 30 && daysToEnd > 0)
                        {
                            alerts.Add(new SystemAlert
                            {
                                Type = AlertType.CycleEndWarning,
                                Priority = daysToEnd <= 7 ? AlertPriority.High : AlertPriority.Medium,
                                Title = "اقتراب نهاية الدورة",
                                Message = $"الدورة '{cycle.CycleName}' ستنتهي خلال {daysToEnd} يوم",
                                RelatedEntityId = cycle.Id,
                                RelatedEntityType = "ProductionCycle",
                                CreatedDate = DateTime.Now,
                                IsRead = false
                            });
                        }
                    }

                    // تنبيه تجاوز المدة المتوقعة
                    var cycleDuration = (DateTime.Now - cycle.StartDate).Days;
                    if (cycleDuration > 180) // أكثر من 6 أشهر
                    {
                        alerts.Add(new SystemAlert
                        {
                            Type = AlertType.CycleOverdue,
                            Priority = AlertPriority.Medium,
                            Title = "دورة طويلة المدى",
                            Message = $"الدورة '{cycle.CycleName}' تستمر منذ {cycleDuration} يوم",
                            RelatedEntityId = cycle.Id,
                            RelatedEntityType = "ProductionCycle",
                            CreatedDate = DateTime.Now,
                            IsRead = false
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص تنبيهات الدورات الإنتاجية");
            }

            return alerts;
        }

        /// <summary>
        /// فحص التنبيهات المالية
        /// Check financial alerts
        /// </summary>
        public async Task<List<SystemAlert>> CheckFinancialAlertsAsync()
        {
            var alerts = new List<SystemAlert>();

            try
            {
                // فحص التدفق النقدي
                var currentMonth = DateTime.Now;
                var monthStart = new DateTime(currentMonth.Year, currentMonth.Month, 1);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                var monthlyRevenue = await GetMonthlyRevenueAsync(monthStart, monthEnd);
                var monthlyExpenses = await GetMonthlyExpensesAsync(monthStart, monthEnd);
                var cashFlow = monthlyRevenue - monthlyExpenses;

                if (cashFlow < 0)
                {
                    alerts.Add(new SystemAlert
                    {
                        Type = AlertType.NegativeCashFlow,
                        Priority = AlertPriority.High,
                        Title = "تدفق نقدي سالب",
                        Message = $"التدفق النقدي لهذا الشهر سالب: {cashFlow:C2}",
                        RelatedEntityId = null,
                        RelatedEntityType = "Financial",
                        CreatedDate = DateTime.Now,
                        IsRead = false
                    });
                }

                // فحص الميزانية
                var budgetVariance = await CheckBudgetVarianceAsync(monthStart, monthEnd);
                if (budgetVariance > 20) // تجاوز الميزانية بأكثر من 20%
                {
                    alerts.Add(new SystemAlert
                    {
                        Type = AlertType.BudgetExceeded,
                        Priority = AlertPriority.Medium,
                        Title = "تجاوز الميزانية",
                        Message = $"تم تجاوز الميزانية المحددة بنسبة {budgetVariance:F1}%",
                        RelatedEntityId = null,
                        RelatedEntityType = "Financial",
                        CreatedDate = DateTime.Now,
                        IsRead = false
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص التنبيهات المالية");
            }

            return alerts;
        }

        /// <summary>
        /// فحص تنبيهات الموظفين
        /// Check employee alerts
        /// </summary>
        public async Task<List<SystemAlert>> CheckEmployeeAlertsAsync()
        {
            var alerts = new List<SystemAlert>();

            try
            {
                var employees = await _unitOfWork.Employees.GetAllAsync();

                foreach (var employee in employees.Where(e => e.Status == EmployeeStatus.Active))
                {
                    // تنبيه انتهاء العقد (استخدام LeaveDate كبديل)
                    if (employee.LeaveDate.HasValue)
                    {
                        var daysToContractEnd = (employee.LeaveDate.Value - DateTime.Now).Days;
                        
                        if (daysToContractEnd <= 30 && daysToContractEnd > 0)
                        {
                            alerts.Add(new SystemAlert
                            {
                                Type = AlertType.ContractExpiry,
                                Priority = daysToContractEnd <= 7 ? AlertPriority.High : AlertPriority.Medium,
                                Title = "انتهاء عقد موظف",
                                Message = $"عقد الموظف '{employee.FullName}' سينتهي خلال {daysToContractEnd} يوم",
                                RelatedEntityId = employee.Id,
                                RelatedEntityType = "Employee",
                                CreatedDate = DateTime.Now,
                                IsRead = false
                            });
                        }
                    }

                    // تنبيه عيد ميلاد الموظف
                    if (employee.BirthDate.HasValue)
                    {
                        var today = DateTime.Now.Date;
                        var birthday = new DateTime(today.Year, employee.BirthDate.Value.Month, employee.BirthDate.Value.Day);
                        
                        if (birthday == today)
                        {
                            alerts.Add(new SystemAlert
                            {
                                Type = AlertType.EmployeeBirthday,
                                Priority = AlertPriority.Low,
                                Title = "عيد ميلاد موظف",
                                Message = $"اليوم عيد ميلاد الموظف '{employee.FullName}'",
                                RelatedEntityId = employee.Id,
                                RelatedEntityType = "Employee",
                                CreatedDate = DateTime.Now,
                                IsRead = false
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص تنبيهات الموظفين");
            }

            return alerts;
        }

        /// <summary>
        /// فحص تنبيهات جودة المياه
        /// Check water quality alerts
        /// </summary>
        public async Task<List<SystemAlert>> CheckWaterQualityAlertsAsync()
        {
            var alerts = new List<SystemAlert>();

            try
            {
                // TODO: إضافة WaterQualityRecords إلى IUnitOfWork عند إنشاء النموذج
                // var recentTests = await _unitOfWork.WaterQualityRecords.FindAsync(w =>
                //     w.TestDate >= DateTime.Now.AddDays(-7));
                var recentTests = new List<dynamic>(); // مؤقت

                // TODO: تنفيذ فحص جودة المياه عند إضافة النموذج
                /*
                foreach (var test in recentTests)
                {
                    // فحص مستوى الأكسجين
                    if (test.OxygenLevel < 5.0m) // أقل من 5 ppm
                    {
                        alerts.Add(new SystemAlert
                        {
                            Type = AlertType.LowOxygen,
                            Priority = test.OxygenLevel < 3.0m ? AlertPriority.Critical : AlertPriority.High,
                            Title = "مستوى أكسجين منخفض",
                            Message = $"مستوى الأكسجين في الحوض '{test.Pond?.PondNumber}' منخفض: {test.OxygenLevel} ppm",
                            RelatedEntityId = test.PondId,
                            RelatedEntityType = "Pond",
                            CreatedDate = DateTime.Now,
                            IsRead = false
                        });
                    }
                */

                    // TODO: باقي فحوصات جودة المياه
                    /*
                    // فحص درجة الحموضة
                    if (test.PHLevel < 6.5m || test.PHLevel > 8.5m)
                    {
                        alerts.Add(new SystemAlert
                        {
                            Type = AlertType.PHImbalance,
                            Priority = AlertPriority.Medium,
                            Title = "خلل في درجة الحموضة",
                            Message = $"درجة الحموضة في الحوض '{test.Pond?.PondNumber}' غير طبيعية: {test.PHLevel}",
                            RelatedEntityId = test.PondId,
                            RelatedEntityType = "Pond",
                            CreatedDate = DateTime.Now,
                            IsRead = false
                        });
                    }

                    // فحص درجة الحرارة
                    if (test.Temperature < 20m || test.Temperature > 30m)
                    {
                        alerts.Add(new SystemAlert
                        {
                            Type = AlertType.TemperatureAlert,
                            Priority = AlertPriority.Medium,
                            Title = "درجة حرارة غير مناسبة",
                            Message = $"درجة الحرارة في الحوض '{test.Pond?.PondNumber}' غير مناسبة: {test.Temperature}°C",
                            RelatedEntityId = test.PondId,
                            RelatedEntityType = "Pond",
                            CreatedDate = DateTime.Now,
                            IsRead = false
                        });
                    }
                    */
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص تنبيهات جودة المياه");
            }

            return alerts;
        }

        // Helper methods
        private async Task<decimal> CalculatePondMortalityRateAsync(int pondId)
        {
            var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
            if (pond == null) return 0;

            var recentMortalities = await _unitOfWork.FishMortalities.FindAsync(m =>
                m.PondId == pondId && m.MortalityDate >= DateTime.Now.AddDays(-30));

            var totalMortality = recentMortalities.Sum(m => m.DeadFishCount);
            return pond.FishCount > 0 ? (decimal)totalMortality / pond.FishCount * 100 : 0;
        }

        private async Task<DateTime?> GetLastFeedingDateAsync(int pondId)
        {
            var lastFeeding = await _unitOfWork.FeedConsumptions
                .FindAsync(f => f.PondId == pondId);
            
            return lastFeeding.OrderByDescending(f => f.FeedingDate)
                             .FirstOrDefault()?.FeedingDate;
        }

        private async Task<decimal> GetMonthlyRevenueAsync(DateTime fromDate, DateTime toDate)
        {
            var revenues = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionDate >= fromDate && t.TransactionDate <= toDate &&
                t.TransactionType == "إيراد");
            return revenues.Sum(r => r.TotalAmount);
        }

        private async Task<decimal> GetMonthlyExpensesAsync(DateTime fromDate, DateTime toDate)
        {
            var expenses = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionDate >= fromDate && t.TransactionDate <= toDate &&
                t.TransactionType == "مصروف");
            return expenses.Sum(e => e.TotalAmount);
        }

        private async Task<decimal> CheckBudgetVarianceAsync(DateTime fromDate, DateTime toDate)
        {
            // هذا مثال بسيط - يمكن تطويره ليشمل ميزانية فعلية من قاعدة البيانات
            var actualExpenses = await GetMonthlyExpensesAsync(fromDate, toDate);
            var budgetedExpenses = 50000m; // ميزانية افتراضية
            
            return budgetedExpenses > 0 ? ((actualExpenses - budgetedExpenses) / budgetedExpenses) * 100 : 0;
        }
    }

    // Alert enums and models
    public enum AlertType
    {
        LowStock,
        Expired,
        ExpiryWarning,
        HarvestDue,
        HarvestWarning,
        HighMortality,
        FeedingOverdue,
        CycleEndWarning,
        CycleOverdue,
        NegativeCashFlow,
        BudgetExceeded,
        ContractExpiry,
        EmployeeBirthday,
        LowOxygen,
        PHImbalance,
        TemperatureAlert
    }

    public enum AlertPriority
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    public class SystemAlert
    {
        public int Id { get; set; }
        public AlertType Type { get; set; }
        public AlertPriority Priority { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public int? RelatedEntityId { get; set; }
        public string? RelatedEntityType { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsRead { get; set; }
        public DateTime? ReadDate { get; set; }
    }
}
