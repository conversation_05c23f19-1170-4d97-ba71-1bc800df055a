@echo off
chcp 65001 >nul
echo ========================================
echo    نظام إدارة مزرعة الأسماك - البناء التجاري
echo    Fish Farm Management - Commercial Build
echo ========================================
echo.

:: التحقق من وجود .NET SDK
echo [1/8] التحقق من .NET SDK...
dotnet --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo خطأ: .NET SDK غير مثبت
    echo يرجى تثبيت .NET 8.0 SDK من: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)
echo ✓ .NET SDK متوفر

:: تنظيف المشروع
echo.
echo [2/8] تنظيف المشروع...
dotnet clean --verbosity quiet
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "FishFarmManagement\bin" rmdir /s /q "FishFarmManagement\bin"
if exist "FishFarmManagement\obj" rmdir /s /q "FishFarmManagement\obj"
if exist "FishFarmManagement.Models\bin" rmdir /s /q "FishFarmManagement.Models\bin"
if exist "FishFarmManagement.Models\obj" rmdir /s /q "FishFarmManagement.Models\obj"
if exist "FishFarmManagement.DAL\bin" rmdir /s /q "FishFarmManagement.DAL\bin"
if exist "FishFarmManagement.DAL\obj" rmdir /s /q "FishFarmManagement.DAL\obj"
if exist "FishFarmManagement.BLL\bin" rmdir /s /q "FishFarmManagement.BLL\bin"
if exist "FishFarmManagement.BLL\obj" rmdir /s /q "FishFarmManagement.BLL\obj"
if exist "FishFarmManagement.Tests\bin" rmdir /s /q "FishFarmManagement.Tests\bin"
if exist "FishFarmManagement.Tests\obj" rmdir /s /q "FishFarmManagement.Tests\obj"
echo ✓ تم تنظيف المشروع

:: استعادة الحزم
echo.
echo [3/8] استعادة الحزم...
dotnet restore --verbosity quiet
if %ERRORLEVEL% neq 0 (
    echo خطأ: فشل في استعادة الحزم
    pause
    exit /b 1
)
echo ✓ تم استعادة الحزم

:: بناء المشاريع بالترتيب
echo.
echo [4/8] بناء المشاريع...

echo   - بناء Models...
dotnet build FishFarmManagement.Models\FishFarmManagement.Models.csproj --configuration Release --verbosity quiet
if %ERRORLEVEL% neq 0 (
    echo خطأ: فشل في بناء Models
    pause
    exit /b 1
)

echo   - بناء DAL...
dotnet build FishFarmManagement.DAL\FishFarmManagement.DAL.csproj --configuration Release --verbosity quiet
if %ERRORLEVEL% neq 0 (
    echo خطأ: فشل في بناء DAL
    pause
    exit /b 1
)

echo   - بناء BLL...
dotnet build FishFarmManagement.BLL\FishFarmManagement.BLL.csproj --configuration Release --verbosity quiet
if %ERRORLEVEL% neq 0 (
    echo خطأ: فشل في بناء BLL
    pause
    exit /b 1
)

echo   - بناء التطبيق الرئيسي...
dotnet build FishFarmManagement\FishFarmManagement.csproj --configuration Release --verbosity quiet
if %ERRORLEVEL% neq 0 (
    echo خطأ: فشل في بناء التطبيق الرئيسي
    pause
    exit /b 1
)

echo ✓ تم بناء جميع المشاريع

:: تشغيل الاختبارات
echo.
echo [5/8] تشغيل الاختبارات...
dotnet test FishFarmManagement.Tests\FishFarmManagement.Tests.csproj --configuration Release --verbosity quiet --no-build --logger "console;verbosity=minimal"
if %ERRORLEVEL% neq 0 (
    echo تحذير: فشلت بعض الاختبارات، لكن سيستمر البناء
    echo يرجى مراجعة نتائج الاختبارات قبل التوزيع
)

:: إنشاء مجلدات التوزيع
echo.
echo [6/8] إنشاء مجلدات التوزيع...
if not exist "Distribution" mkdir "Distribution"
if not exist "Distribution\Setup" mkdir "Distribution\Setup"
if not exist "Distribution\Portable" mkdir "Distribution\Portable"
if not exist "Distribution\Documentation" mkdir "Distribution\Documentation"
if not exist "Distribution\Samples" mkdir "Distribution\Samples"
echo ✓ تم إنشاء مجلدات التوزيع

:: نسخ الملفات المبنية
echo.
echo [7/8] نسخ الملفات المبنية...

:: نسخ التطبيق المحمول
echo   - إنشاء النسخة المحمولة...
xcopy "FishFarmManagement\bin\Release\net8.0-windows\*" "Distribution\Portable\" /E /I /Y /Q
if %ERRORLEVEL% neq 0 (
    echo خطأ: فشل في نسخ الملفات للنسخة المحمولة
    pause
    exit /b 1
)

:: إضافة ملفات إضافية للنسخة المحمولة
echo   - إضافة ملفات إضافية...
if not exist "Distribution\Portable\Backups" mkdir "Distribution\Portable\Backups"
if not exist "Distribution\Portable\Reports" mkdir "Distribution\Portable\Reports"
if not exist "Distribution\Portable\Logs" mkdir "Distribution\Portable\Logs"
if not exist "Distribution\Portable\Updates" mkdir "Distribution\Portable\Updates"

:: نسخ ملف README للنسخة المحمولة
echo # نظام إدارة مزرعة الأسماك - النسخة المحمولة > "Distribution\Portable\README.txt"
echo. >> "Distribution\Portable\README.txt"
echo هذه النسخة المحمولة من نظام إدارة مزرعة الأسماك. >> "Distribution\Portable\README.txt"
echo. >> "Distribution\Portable\README.txt"
echo للتشغيل: >> "Distribution\Portable\README.txt"
echo 1. انقر مرتين على FishFarmManagement.exe >> "Distribution\Portable\README.txt"
echo 2. سيتم إنشاء ترخيص تجريبي تلقائياً >> "Distribution\Portable\README.txt"
echo 3. استخدم: admin / Admin123! للدخول >> "Distribution\Portable\README.txt"
echo. >> "Distribution\Portable\README.txt"
echo للدعم الفني: <EMAIL> >> "Distribution\Portable\README.txt"

echo ✓ تم إنشاء النسخة المحمولة

:: نسخ ملفات التوثيق
echo   - نسخ ملفات التوثيق...
copy "README.md" "Distribution\Documentation\" >nul 2>&1
copy "DOCUMENTATION.md" "Distribution\Documentation\" >nul 2>&1
copy "INSTALLATION.md" "Distribution\Documentation\" >nul 2>&1
copy "LICENSE" "Distribution\Documentation\" >nul 2>&1
copy "CHANGELOG.md" "Distribution\Documentation\" >nul 2>&1
echo ✓ تم نسخ ملفات التوثيق

:: إنشاء ملفات العينة
echo   - إنشاء ملفات العينة...
echo # ملفات عينة لنظام إدارة مزرعة الأسماك > "Distribution\Samples\README.txt"
echo. >> "Distribution\Samples\README.txt"
echo هذا المجلد يحتوي على ملفات عينة لمساعدتك في بدء استخدام النظام. >> "Distribution\Samples\README.txt"
echo. >> "Distribution\Samples\README.txt"
echo - قاعدة بيانات عينة مع بيانات تجريبية >> "Distribution\Samples\README.txt"
echo - تقارير عينة بصيغ مختلفة >> "Distribution\Samples\README.txt"
echo - ملفات إعدادات عينة >> "Distribution\Samples\README.txt"
echo ✓ تم إنشاء ملفات العينة

:: إنشاء ملفات ZIP
echo.
echo [8/8] إنشاء ملفات التوزيع...

:: النسخة المحمولة
echo   - إنشاء النسخة المحمولة (ZIP)...
powershell -Command "Compress-Archive -Path 'Distribution\Portable\*' -DestinationPath 'Distribution\FishFarmManagement-Portable.zip' -Force" >nul 2>&1
echo ✓ تم إنشاء FishFarmManagement-Portable.zip

:: ملفات التوثيق
echo   - إنشاء ملف التوثيق (ZIP)...
powershell -Command "Compress-Archive -Path 'Distribution\Documentation\*' -DestinationPath 'Distribution\FishFarmManagement-Documentation.zip' -Force" >nul 2>&1
echo ✓ تم إنشاء FishFarmManagement-Documentation.zip

:: ملفات العينة
echo   - إنشاء ملف العينات (ZIP)...
powershell -Command "Compress-Archive -Path 'Distribution\Samples\*' -DestinationPath 'Distribution\FishFarmManagement-Samples.zip' -Force" >nul 2>&1
echo ✓ تم إنشاء FishFarmManagement-Samples.zip

echo.
echo ========================================
echo    تم البناء بنجاح! | Build Successful!
echo ========================================
echo.
echo الملفات المتاحة في مجلد Distribution:
echo.
echo 📦 FishFarmManagement-Portable.zip
echo    - النسخة المحمولة (تشغيل مباشر)
echo    - لا تحتاج تثبيت
echo    - مناسبة للاستخدام الفوري
echo.
echo 📋 FishFarmManagement-Documentation.zip
echo    - ملفات التوثيق الشاملة
echo    - دليل المستخدم والمطور
echo    - أمثلة الاستخدام
echo.
echo 🎯 FishFarmManagement-Samples.zip
echo    - ملفات عينة للبدء السريع
echo    - قاعدة بيانات تجريبية
echo    - تقارير عينة
echo.
echo 📁 Setup\ (لإنشاء ملف التثبيت)
echo    - يحتاج Inno Setup Compiler
echo    - لإنشاء FishFarmManagement-Setup.exe
echo.
echo للاستفسارات والدعم: <EMAIL>
echo.
pause 