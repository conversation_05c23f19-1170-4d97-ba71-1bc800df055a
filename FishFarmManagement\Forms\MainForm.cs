using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// النموذج الرئيسي للتطبيق
    /// Main application form
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IConfiguration _configuration;
        private readonly ILogger<MainForm> _logger;
        private readonly IAuthenticationService _authenticationService;
        private readonly IAuthorizationService _authorizationService;

        // Dashboard card labels for updating values
        private Label _activePondsLabel;
        private Label _activeCyclesLabel;
        private Label _totalEmployeesLabel;
        private Label _todayTransactionsLabel;

        public MainForm(IServiceProvider serviceProvider, IConfiguration configuration, ILogger<MainForm> logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _authenticationService = _serviceProvider.GetRequiredService<IAuthenticationService>();
            _authorizationService = _serviceProvider.GetRequiredService<IAuthorizationService>();

            InitializeComponent();
            InitializeApplication();

            // Subscribe to Load event for async initialization
            this.Load += MainForm_Load;
        }

        private void InitializeComponent()
        {
            // Form properties
            Text = _configuration["Application:Name"] ?? "نظام إدارة مزرعة الأسماك";
            Size = new(1200, 800);
            StartPosition = FormStartPosition.CenterScreen;
            WindowState = FormWindowState.Maximized;
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;

            // Create menu strip
            CreateMenuStrip();

            // Create toolbar
            CreateToolStrip();

            // Create status bar
            CreateStatusStrip();

            // Create main panel
            CreateMainPanel();
        }

        private void CreateMenuStrip()
        {
            var menuStrip = new MenuStrip
            {
                RightToLeft = RightToLeft.Yes,
                Font = new Font("Segoe UI", 10F)
            };

            // File menu
            var fileMenu = new ToolStripMenuItem("ملف") { Name = "fileMenu" };
            fileMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("جديد", null, (s, e) => NewFile()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("نسخ احتياطي", null, (s, e) => CreateBackup()),
                new ToolStripMenuItem("استعادة", null, (s, e) => RestoreBackup()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("خروج", null, (s, e) => Close())
            });

            // Management menu
            var managementMenu = new ToolStripMenuItem("الإدارة") { Name = "managementMenu" };
            managementMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("إدارة الأحواض", null, (s, e) => OpenPondManagement()) { Name = "pondManagementToolStripMenuItem" },
                new ToolStripMenuItem("دورات الإنتاج", null, (s, e) => OpenProductionCycles()) { Name = "productionCyclesToolStripMenuItem" },
                new ToolStripMenuItem("إدارة الموظفين", null, (s, e) => OpenEmployeeManagement()) { Name = "employeeManagementToolStripMenuItem" },
                new ToolStripMenuItem("المخزون", null, (s, e) => OpenInventoryManagement()) { Name = "inventoryToolStripMenuItem" }
            });

            // Accounting menu
            var accountingMenu = new ToolStripMenuItem("المحاسبة") { Name = "accountingMenu" };
            accountingMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("القيود المحاسبية", null, (s, e) => OpenAccounting()) { Name = "accountingEntriesToolStripMenuItem" },
                new ToolStripMenuItem("دليل الحسابات", null, (s, e) => OpenChartOfAccounts()) { Name = "chartOfAccountsToolStripMenuItem" },
                new ToolStripMenuItem("مراكز التكلفة", null, (s, e) => OpenCostCenters()) { Name = "costCentersToolStripMenuItem" }
            });

            // Reports menu
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("تقارير الإنتاج", null, (s, e) => OpenProductionReports()),
                new ToolStripMenuItem("التقارير المالية", null, (s, e) => OpenFinancialReports()),
                new ToolStripMenuItem("تقارير الموظفين", null, (s, e) => OpenEmployeeReports())
            });

            // Tools menu
            var toolsMenu = new ToolStripMenuItem("الأدوات") { Name = "toolsMenu" };
            toolsMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("الإعدادات", null, (s, e) => OpenSettings()),
                new ToolStripMenuItem("معلومات المزرعة", null, (s, e) => OpenFarmInfo()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("تحسين قاعدة البيانات", null, (s, e) => OptimizeDatabase())
            });

            // Help menu
            var helpMenu = new ToolStripMenuItem("المساعدة") { Name = "helpMenu" };
            helpMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("فحص التحديثات", null, (s, e) => CheckForUpdates()),
                new ToolStripMenuItem("مراقب الأداء", null, (s, e) => ShowPerformanceMonitor()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("دليل المستخدم", null, (s, e) => ShowUserGuide()),
                new ToolStripMenuItem("حول البرنامج", null, (s, e) => ShowAbout())
            });

            menuStrip.Items.AddRange(new ToolStripItem[]
            {
                fileMenu, managementMenu, accountingMenu, reportsMenu, toolsMenu, helpMenu
            });

            MainMenuStrip = menuStrip;
            Controls.Add(menuStrip);
        }

        private void CreateToolStrip()
        {
            var toolStrip = new ToolStrip
            {
                ImageScalingSize = new Size(32, 32),
                Font = new Font("Segoe UI", 9F)
            };

            toolStrip.Items.AddRange(new ToolStripItem[]
            {
                new ToolStripButton("الأحواض", null, (s, e) => OpenPondManagement()) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText },
                new ToolStripButton("الدورات", null, (s, e) => OpenProductionCycles()) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText },
                new ToolStripButton("الموظفين", null, (s, e) => OpenEmployeeManagement()) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText },
                new ToolStripSeparator(),
                new ToolStripButton("المحاسبة", null, (s, e) => OpenAccounting()) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText },
                new ToolStripButton("التقارير", null, (s, e) => OpenReports()) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText },
                new ToolStripSeparator(),
                new ToolStripButton("نسخ احتياطي", null, (s, e) => CreateBackup()) { DisplayStyle = ToolStripItemDisplayStyle.ImageAndText }
            });

            this.Controls.Add(toolStrip);
        }

        private void CreateStatusStrip()
        {
            var statusStrip = new StatusStrip();
            
            var statusLabel = new ToolStripStatusLabel("جاهز")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var dateLabel = new ToolStripStatusLabel(DateTime.Now.ToString("yyyy/MM/dd"))
            {
                TextAlign = ContentAlignment.MiddleRight
            };

            var timeLabel = new ToolStripStatusLabel(DateTime.Now.ToString("HH:mm"))
            {
                TextAlign = ContentAlignment.MiddleRight
            };

            statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, dateLabel, timeLabel });
            this.Controls.Add(statusStrip);

            // Update time every second
            var timer = new System.Windows.Forms.Timer { Interval = 1000 };
            timer.Tick += (s, e) =>
            {
                timeLabel.Text = DateTime.Now.ToString("HH:mm");
                dateLabel.Text = DateTime.Now.ToString("yyyy/MM/dd");
            };
            timer.Start();
        }

        private void CreateMainPanel()
        {
            // Enhanced main panel with modern gradient background
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(245, 247, 250) // Light modern background
            };

            // Add subtle gradient effect
            mainPanel.Paint += (s, e) =>
            {
                using (var brush = new System.Drawing.Drawing2D.LinearGradientBrush(
                    mainPanel.ClientRectangle,
                    Color.FromArgb(245, 247, 250),
                    Color.FromArgb(235, 240, 245),
                    System.Drawing.Drawing2D.LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, mainPanel.ClientRectangle);
                }
            };

            // Create enhanced dashboard
            CreateEnhancedDashboard(mainPanel);

            this.Controls.Add(mainPanel);
        }

        private void CreateEnhancedDashboard(Panel parent)
        {
            // Create header section
            var headerPanel = CreateDashboardHeader();
            parent.Controls.Add(headerPanel);

            // Create main dashboard content
            var dashboardPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 3, // Increased for better layout
                Padding = new Padding(30, 20, 30, 30),
                BackColor = Color.Transparent
            };

            // Set column and row styles for better proportions
            dashboardPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            dashboardPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            dashboardPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            dashboardPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 40F)); // Main cards
            dashboardPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 40F)); // Secondary cards
            dashboardPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 20F)); // Quick actions

            // Create enhanced dashboard cards with modern design
            var cards = new[]
            {
                CreateEnhancedDashboardCard("الأحواض النشطة", "0", "🐟", Color.FromArgb(52, 152, 219), () => OpenPondManagement(), out _activePondsLabel),
                CreateEnhancedDashboardCard("الدورات الجارية", "0", "🔄", Color.FromArgb(46, 204, 113), () => OpenProductionCycles(), out _activeCyclesLabel),
                CreateEnhancedDashboardCard("إجمالي الموظفين", "0", "👥", Color.FromArgb(155, 89, 182), () => OpenEmployeeManagement(), out _totalEmployeesLabel),
                CreateEnhancedDashboardCard("المعاملات اليوم", "0", "💰", Color.FromArgb(230, 126, 34), () => OpenAccounting(), out _todayTransactionsLabel),
                CreateEnhancedDashboardCard("التقارير", "عرض", "📊", Color.FromArgb(231, 76, 60), () => OpenReports(), out _),
                CreateEnhancedDashboardCard("الإعدادات", "تكوين", "⚙️", Color.FromArgb(52, 73, 94), () => OpenSettings(), out _)
            };

            // Add cards to dashboard
            for (int i = 0; i < cards.Length; i++)
            {
                dashboardPanel.Controls.Add(cards[i], i % 3, i / 3);
            }

            // Add quick action buttons in the bottom row
            var quickActionsPanel = CreateQuickActionsPanel();
            dashboardPanel.Controls.Add(quickActionsPanel, 0, 2);
            dashboardPanel.SetColumnSpan(quickActionsPanel, 3);

            parent.Controls.Add(dashboardPanel);
        }

        private Panel CreateDashboardHeader()
        {
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.Transparent
            };

            var welcomeLabel = new Label
            {
                Text = "مرحباً بك في نظام إدارة مزرعة الأسماك",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            var dateLabel = new Label
            {
                Text = DateTime.Now.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA")),
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.FromArgb(127, 140, 141),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Bottom,
                Height = 25
            };

            headerPanel.Controls.Add(welcomeLabel);
            headerPanel.Controls.Add(dateLabel);

            return headerPanel;
        }

        private Panel CreateEnhancedDashboardCard(string title, string value, string icon, Color color, Action clickAction, out Label valueLabel)
        {
            var card = new Panel
            {
                BackColor = Color.White,
                Margin = new Padding(15),
                Cursor = Cursors.Hand,
                Size = new Size(200, 150)
            };

            // Add modern shadow effect
            card.Paint += (s, e) =>
            {
                // Draw shadow
                using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    e.Graphics.FillRectangle(shadowBrush, new Rectangle(3, 3, card.Width - 3, card.Height - 3));
                }

                // Draw card background with rounded corners effect
                using (var cardBrush = new SolidBrush(card.BackColor))
                {
                    e.Graphics.FillRectangle(cardBrush, new Rectangle(0, 0, card.Width - 3, card.Height - 3));
                }

                // Draw colored top border
                using (var borderPen = new Pen(color, 4))
                {
                    e.Graphics.DrawLine(borderPen, 0, 0, card.Width - 3, 0);
                }
            };

            // Icon label
            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 24F),
                ForeColor = color,
                TextAlign = ContentAlignment.MiddleCenter,
                Location = new Point(10, 15),
                Size = new Size(50, 50),
                BackColor = Color.Transparent
            };

            // Title label
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                TextAlign = ContentAlignment.MiddleLeft,
                Location = new Point(70, 20),
                Size = new Size(120, 25),
                BackColor = Color.Transparent
            };

            // Value label
            valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 28F, FontStyle.Bold),
                ForeColor = color,
                TextAlign = ContentAlignment.MiddleCenter,
                Location = new Point(10, 70),
                Size = new Size(180, 60),
                BackColor = Color.Transparent
            };

            // Add controls
            card.Controls.Add(iconLabel);
            card.Controls.Add(titleLabel);
            card.Controls.Add(valueLabel);

            // Enhanced hover effects
            card.MouseEnter += (s, e) =>
            {
                card.BackColor = Color.FromArgb(248, 250, 252);
                card.Invalidate(); // Redraw for shadow effect
            };

            card.MouseLeave += (s, e) =>
            {
                card.BackColor = Color.White;
                card.Invalidate(); // Redraw for shadow effect
            };

            // Click events
            card.Click += (s, e) => clickAction?.Invoke();
            iconLabel.Click += (s, e) => clickAction?.Invoke();
            titleLabel.Click += (s, e) => clickAction?.Invoke();
            valueLabel.Click += (s, e) => clickAction?.Invoke();

            return card;
        }

        private Panel CreateQuickActionsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                Padding = new Padding(15, 10, 15, 10)
            };

            var titleLabel = new Label
            {
                Text = "إجراءات سريعة",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Dock = DockStyle.Top,
                Height = 30,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var buttonsPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = true,
                Padding = new Padding(0, 5, 0, 0)
            };

            // Create quick action buttons
            var quickButtons = new[]
            {
                CreateQuickActionButton("إضافة حوض جديد", "🐟", Color.FromArgb(52, 152, 219), () => OpenPondManagement()),
                CreateQuickActionButton("بدء دورة إنتاج", "🔄", Color.FromArgb(46, 204, 113), () => OpenProductionCycles()),
                CreateQuickActionButton("إضافة موظف", "👤", Color.FromArgb(155, 89, 182), () => OpenEmployeeManagement()),
                CreateQuickActionButton("معاملة جديدة", "💰", Color.FromArgb(230, 126, 34), () => OpenAccounting()),
                CreateQuickActionButton("تقرير سريع", "📊", Color.FromArgb(231, 76, 60), () => OpenReports())
            };

            foreach (var button in quickButtons)
            {
                buttonsPanel.Controls.Add(button);
            }

            panel.Controls.Add(buttonsPanel);
            panel.Controls.Add(titleLabel);

            return panel;
        }

        private Button CreateQuickActionButton(string text, string icon, Color color, Action clickAction)
        {
            var button = new Button
            {
                Text = $"{icon} {text}",
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.White,
                BackColor = color,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(140, 35),
                Margin = new Padding(5),
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = Color.FromArgb(Math.Max(0, color.R - 20), Math.Max(0, color.G - 20), Math.Max(0, color.B - 20));

            button.Click += (s, e) => clickAction?.Invoke();

            return button;
        }

        private void InitializeApplication()
        {
            try
            {
                _logger.LogInformation("تم بدء تشغيل التطبيق بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة التطبيق");
                MessageBox.Show($"خطأ في تهيئة التطبيق: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void MainForm_Load(object? sender, EventArgs e)
        {
            // Initialize default data first
            try
            {
                var userManagementService = _serviceProvider.GetRequiredService<IUserManagementService>();
                await userManagementService.InitializeDefaultDataAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة البيانات الافتراضية");
            }

            await LoadDashboardDataAsync();
            await ApplyUserPermissionsAsync();
            ShowUserInfo();
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                var statisticsService = _serviceProvider.GetRequiredService<IStatisticsService>();
                var stats = await statisticsService.GetDashboardStatisticsAsync();

                // Update dashboard cards with actual data
                if (_activePondsLabel != null)
                    _activePondsLabel.Text = stats.ActivePonds.ToString();

                if (_activeCyclesLabel != null)
                    _activeCyclesLabel.Text = stats.ActiveCycles.ToString();

                if (_totalEmployeesLabel != null)
                    _totalEmployeesLabel.Text = stats.TotalEmployees.ToString();

                if (_todayTransactionsLabel != null)
                    _todayTransactionsLabel.Text = stats.TodayTransactions.ToString();

                _logger.LogInformation("تم تحميل بيانات لوحة المعلومات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل بيانات لوحة المعلومات");
                MessageBox.Show("حدث خطأ أثناء تحميل بيانات لوحة المعلومات.", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Event handlers for menu items
        private void NewFile() => MessageBox.Show("إنشاء ملف جديد", "معلومات");

        private void CreateBackup()
        {
            var form = _serviceProvider.GetRequiredService<EnhancedBackupForm>();
            form.ShowDialog();
        }

        private void RestoreBackup()
        {
            var form = _serviceProvider.GetRequiredService<EnhancedBackupForm>();
            form.ShowDialog();
        }
        
        private void OpenPondManagement()
        {
            var form = _serviceProvider.GetRequiredService<PondManagementForm>();
            form.ShowDialog();
        }
        
        private void OpenProductionCycles()
        {
            var form = _serviceProvider.GetRequiredService<ProductionCycleForm>();
            form.ShowDialog();
        }
        
        private void OpenEmployeeManagement()
        {
            var form = _serviceProvider.GetRequiredService<EmployeeManagementForm>();
            form.ShowDialog();
        }
        
        private void OpenInventoryManagement()
        {
            var form = _serviceProvider.GetRequiredService<EnhancedInventoryManagementForm>();
            form.ShowDialog();
        }
        
        private void OpenAccounting()
        {
            var form = _serviceProvider.GetRequiredService<EnhancedAccountingForm>();
            form.ShowDialog();
        }

        private void OpenDashboard()
        {
            var form = _serviceProvider.GetRequiredService<EnhancedDashboardForm>();
            form.ShowDialog();
        }
        
        private void OpenChartOfAccounts()
        {
            var form = _serviceProvider.GetRequiredService<ChartOfAccountsForm>();
            form.ShowDialog();
        }

        private void OpenCostCenters()
        {
            var form = _serviceProvider.GetRequiredService<CostCentersForm>();
            form.ShowDialog();
        }
        private void OpenProductionReports()
        {
            var form = _serviceProvider.GetRequiredService<ProductionReportsForm>();
            form.ShowDialog();
        }

        private void OpenFinancialReports()
        {
            var form = _serviceProvider.GetRequiredService<FinancialReportsForm>();
            form.ShowDialog();
        }

        private void OpenEmployeeReports()
        {
            var form = _serviceProvider.GetRequiredService<EmployeeReportsForm>();
            form.ShowDialog();
        }
        
        private void OpenReports()
        {
            var form = _serviceProvider.GetRequiredService<EnhancedReportsForm>();
            form.ShowDialog();
        }
        
        private void OpenSettings()
        {
            var form = _serviceProvider.GetRequiredService<SettingsForm>();
            form.ShowDialog();
        }
        
        private void OpenFarmInfo()
        {
            var form = _serviceProvider.GetRequiredService<FarmInfoForm>();
            form.ShowDialog();
        }

        private void OptimizeDatabase()
        {
            var form = _serviceProvider.GetRequiredService<DatabaseOptimizationForm>();
            form.ShowDialog();
        }

        private void ShowUserGuide()
        {
            var form = _serviceProvider.GetRequiredService<UserGuideForm>();
            form.ShowDialog();
        }
        
        private void ShowAbout()
        {
            var aboutText = $@"
{_configuration["Application:Name"]}
الإصدار: {_configuration["Application:Version"]}

نظام متكامل لإدارة ومحاسبة مزارع الأسماك

المطور: {_configuration["AssemblyCompany"]}
البريد الإلكتروني: <EMAIL>

© 2024 جميع الحقوق محفوظة
";
            MessageBox.Show(aboutText, "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async Task ApplyUserPermissionsAsync()
        {
            try
            {
                var currentUser = _authenticationService.GetCurrentUser();
                if (currentUser == null)
                {
                    _logger.LogWarning("لا يوجد مستخدم مسجل دخول");
                    return;
                }

                // Check permissions for menu items
                var menuStrip = this.Controls.OfType<MenuStrip>().FirstOrDefault();
                if (menuStrip != null)
                {
                    await ApplyMenuPermissionsAsync(menuStrip);
                }

                _logger.LogInformation("تم تطبيق الصلاحيات للمستخدم: {Username}", currentUser.Username);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تطبيق الصلاحيات");
            }
        }

        private async Task ApplyMenuPermissionsAsync(MenuStrip menuStrip)
        {
            foreach (ToolStripMenuItem menuItem in menuStrip.Items.OfType<ToolStripMenuItem>())
            {
                // The switch statement now works because the Name property is set.
                switch (menuItem.Name)
                {
                    case "managementMenu":
                        menuItem.Visible = true;
                        break;
                    case "accountingToolStripMenuItem":
                        menuItem.Visible = true;
                        break;
                    case "reportsMenu":
                        menuItem.Visible = true;
                        break;
                    case "toolsMenu":
                        // Example: Only admins can see the tools menu
                        menuItem.Visible = await _authorizationService.IsSystemAdminAsync();
                        break;
                }

                // Apply permissions to sub-menu items
                await ApplySubMenuPermissionsAsync(menuItem);
            }
        }

        private async Task ApplySubMenuPermissionsAsync(ToolStripMenuItem parentMenuItem)
        {
            foreach (ToolStripMenuItem subMenuItem in parentMenuItem.DropDownItems.OfType<ToolStripMenuItem>())
            {
                // Apply specific permissions based on sub-menu item names
                switch (subMenuItem.Name)
                {
                    case "userManagementToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "roleManagementToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "systemSettingsToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "databaseManagementToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "payrollManagementToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "financialReportsToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "productionReportsToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "employeeReportsToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                    case "inventoryReportsToolStripMenuItem":
                        subMenuItem.Visible = true;
                        break;
                }
            }
        }

        private void ShowUserInfo()
        {
            try
            {
                var currentUser = _authenticationService.GetCurrentUser();
                if (currentUser != null)
                {
                    // Update window title with user info
                    this.Text = $"نظام إدارة مزرعة الأسماك - {currentUser.FullName}";

                    // Update status bar if exists
                    var statusStrip = this.Controls.OfType<StatusStrip>().FirstOrDefault();
                    if (statusStrip != null)
                    {
                        var userLabel = statusStrip.Items.OfType<ToolStripStatusLabel>()
                            .FirstOrDefault(l => l.Name == "userStatusLabel");

                        if (userLabel == null)
                        {
                            userLabel = new ToolStripStatusLabel("userStatusLabel")
                            {
                                Name = "userStatusLabel",
                                Spring = true,
                                TextAlign = ContentAlignment.MiddleRight
                            };
                            statusStrip.Items.Add(userLabel);
                        }

                        userLabel.Text = $"المستخدم: {currentUser.FullName} | آخر دخول: {currentUser.LastLoginDate:yyyy-MM-dd HH:mm}";
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في عرض معلومات المستخدم");
            }
        }

        /// <summary>
        /// فحص التحديثات
        /// Check for updates
        /// </summary>
        private void CheckForUpdates()
        {
            try
            {
                _logger.LogInformation("فتح نموذج فحص التحديثات");

                // هنا يمكن إنشاء UpdateService من خلال DI
                // للتبسيط، سنعرض رسالة
                MessageBox.Show("ميزة فحص التحديثات ستكون متاحة قريباً.", "فحص التحديثات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص التحديثات");
                MessageBox.Show("حدث خطأ أثناء فحص التحديثات.", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض مراقب الأداء
        /// Show performance monitor
        /// </summary>
        private void ShowPerformanceMonitor()
        {
            try
            {
                _logger.LogInformation("فتح مراقب الأداء");

                // هنا يمكن إنشاء PerformanceMonitorService من خلال DI
                // للتبسيط، سنعرض رسالة
                MessageBox.Show("مراقب الأداء سيكون متاحاً قريباً.", "مراقب الأداء",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح مراقب الأداء");
                MessageBox.Show("حدث خطأ أثناء فتح مراقب الأداء.", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
