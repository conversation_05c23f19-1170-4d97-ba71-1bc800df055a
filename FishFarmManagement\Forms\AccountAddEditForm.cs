﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج إضافة/تعديل الحسابات
    /// Account add/edit form
    /// </summary>
    public partial class AccountAddEditForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;
        private readonly Account? _existingAccount;
        private readonly Account? _parentAccount;
        private readonly bool _isEditMode;

        // UI Controls
        private TextBox accountCodeTextBox;
        private TextBox accountNameTextBox;
        private TextBox accountNameEnTextBox;
        private ComboBox accountTypeComboBox;
        private ComboBox parentAccountComboBox;
        private NumericUpDown balanceNumericUpDown;
        private ComboBox statusComboBox;
        private CheckBox isPostableCheckBox;
        private TextBox descriptionTextBox;
        private Button saveButton;
        private Button cancelButton;

        public AccountAddEditForm(IUnitOfWork unitOfWork, ILogger logger, Account? existingAccount = null, Account? parentAccount = null)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _existingAccount = existingAccount;
            _parentAccount = parentAccount;
            _isEditMode = existingAccount != null;

            InitializeComponent();
            LoadDataAsync();
        }

        private void InitializeComponent()
        {
            this.Text = _isEditMode ? "تعديل حساب" : "إضافة حساب جديد";
            this.Size = new Size(500, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
            LayoutControls();
        }

        private void CreateControls()
        {
            // Account Code
            var accountCodeLabel = new Label { Text = "رمز الحساب:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            accountCodeTextBox = new TextBox { Size = new Size(200, 23), MaxLength = 20 };

            // Account Name
            var accountNameLabel = new Label { Text = "اسم الحساب:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            accountNameTextBox = new TextBox { Size = new Size(200, 23), MaxLength = 200 };

            // Account Name English
            var accountNameEnLabel = new Label { Text = "الاسم بالإنجليزية:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            accountNameEnTextBox = new TextBox { Size = new Size(200, 23), MaxLength = 200 };

            // Account Type
            var accountTypeLabel = new Label { Text = "نوع الحساب:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            accountTypeComboBox = new ComboBox { Size = new Size(200, 23), DropDownStyle = ComboBoxStyle.DropDownList };

            // Parent Account
            var parentAccountLabel = new Label { Text = "الحساب الأب:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            parentAccountComboBox = new ComboBox { Size = new Size(200, 23), DropDownStyle = ComboBoxStyle.DropDownList };

            // Balance
            var balanceLabel = new Label { Text = "الرصيد الافتتاحي:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            balanceNumericUpDown = new NumericUpDown { Size = new Size(200, 23), Minimum = decimal.MinValue, Maximum = decimal.MaxValue, DecimalPlaces = 2 };

            // Status
            var statusLabel = new Label { Text = "الحالة:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            statusComboBox = new ComboBox { Size = new Size(200, 23), DropDownStyle = ComboBoxStyle.DropDownList };
            statusComboBox.Items.AddRange(new[] { "نشط", "متوقف", "مغلق" });
            statusComboBox.SelectedIndex = 0;

            // Is Postable
            isPostableCheckBox = new CheckBox { Text = "قابل للترحيل", Size = new Size(200, 23), CheckAlign = ContentAlignment.MiddleRight, Checked = true };

            // Description
            var descriptionLabel = new Label { Text = "الوصف:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            descriptionTextBox = new TextBox { Size = new Size(300, 60), Multiline = true, ScrollBars = ScrollBars.Vertical, MaxLength = 500 };

            // Buttons
            saveButton = new Button { Text = _isEditMode ? "حفظ التعديلات" : "إضافة", Size = new Size(100, 30), DialogResult = DialogResult.OK };
            saveButton.Click += SaveButton_Click;
            cancelButton = new Button { Text = "إلغاء", Size = new Size(100, 30), DialogResult = DialogResult.Cancel };

            this.Controls.AddRange(new Control[]
            {
                accountCodeLabel, accountCodeTextBox,
                accountNameLabel, accountNameTextBox,
                accountNameEnLabel, accountNameEnTextBox,
                accountTypeLabel, accountTypeComboBox,
                parentAccountLabel, parentAccountComboBox,
                balanceLabel, balanceNumericUpDown,
                statusLabel, statusComboBox,
                isPostableCheckBox,
                descriptionLabel, descriptionTextBox,
                saveButton, cancelButton
            });
        }

        private void LayoutControls()
        {
            int x = 20;
            int y = 20;
            int spacing = 35;

            // Account Code
            this.Controls[0].Location = new Point(x + 250, y);
            this.Controls[1].Location = new Point(x + 30, y);
            y += spacing;

            // Account Name
            this.Controls[2].Location = new Point(x + 250, y);
            this.Controls[3].Location = new Point(x + 30, y);
            y += spacing;

            // Account Name English
            this.Controls[4].Location = new Point(x + 250, y);
            this.Controls[5].Location = new Point(x + 30, y);
            y += spacing;

            // Account Type
            this.Controls[6].Location = new Point(x + 250, y);
            this.Controls[7].Location = new Point(x + 30, y);
            y += spacing;

            // Parent Account
            this.Controls[8].Location = new Point(x + 250, y);
            this.Controls[9].Location = new Point(x + 30, y);
            y += spacing;

            // Balance
            this.Controls[10].Location = new Point(x + 250, y);
            this.Controls[11].Location = new Point(x + 30, y);
            y += spacing;

            // Status
            this.Controls[12].Location = new Point(x + 250, y);
            this.Controls[13].Location = new Point(x + 30, y);
            y += spacing;

            // Is Postable
            this.Controls[14].Location = new Point(x + 30, y);
            y += spacing;

            // Description
            this.Controls[15].Location = new Point(x + 250, y);
            this.Controls[16].Location = new Point(x + 30, y);
            y += 80;

            // Buttons
            this.Controls[17].Location = new Point(x + 130, y);
            this.Controls[18].Location = new Point(x + 250, y);
        }

        private async void LoadDataAsync()
        {
            try
            {
                // Load account types
                var accountTypes = await _unitOfWork.AccountTypes.GetAllAsync();
                accountTypeComboBox.DataSource = accountTypes.ToList();
                accountTypeComboBox.DisplayMember = "TypeName";
                accountTypeComboBox.ValueMember = "Id";

                // Load parent accounts
                var accounts = await _unitOfWork.Accounts.GetAllAsync();
                var parentAccounts = accounts.Where(a => a.Id != _existingAccount?.Id).ToList();
                parentAccountComboBox.DataSource = parentAccounts;
                parentAccountComboBox.DisplayMember = "AccountName";
                parentAccountComboBox.ValueMember = "Id";

                // Add empty option for parent account
                parentAccountComboBox.Items.Insert(0, new { Id = (int?)null, AccountName = "-- لا يوجد --" });
                parentAccountComboBox.SelectedIndex = 0;

                if (_existingAccount != null)
                {
                    accountCodeTextBox.Text = _existingAccount.AccountCode;
                    accountNameTextBox.Text = _existingAccount.AccountName;
                    accountNameEnTextBox.Text = _existingAccount.AccountNameEn;
                    accountTypeComboBox.SelectedValue = _existingAccount.AccountTypeId;
                    if (_existingAccount.ParentAccountId.HasValue)
                    {
                        parentAccountComboBox.SelectedValue = _existingAccount.ParentAccountId.Value;
                    }
                    balanceNumericUpDown.Value = _existingAccount.Balance;
                    statusComboBox.SelectedItem = _existingAccount.Status;
                    isPostableCheckBox.Checked = _existingAccount.IsPostable;
                    descriptionTextBox.Text = _existingAccount.Description;
                }
                else if (_parentAccount != null)
                {
                    parentAccountComboBox.SelectedValue = _parentAccount.Id;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل البيانات");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                this.Cursor = Cursors.WaitCursor;
                saveButton.Enabled = false;

                var account = _existingAccount ?? new Account();
                
                account.AccountCode = accountCodeTextBox.Text.Trim();
                account.AccountName = accountNameTextBox.Text.Trim();
                account.AccountNameEn = accountNameEnTextBox.Text.Trim();
                account.AccountTypeId = (int)accountTypeComboBox.SelectedValue;
                account.ParentAccountId = parentAccountComboBox.SelectedValue as int?;
                account.Balance = balanceNumericUpDown.Value;
                account.Status = statusComboBox.SelectedItem?.ToString() ?? "نشط";
                account.IsPostable = isPostableCheckBox.Checked;
                account.Description = descriptionTextBox.Text.Trim();

                if (_isEditMode)
                {
                    account.UpdateModificationDate();
                    await _unitOfWork.Accounts.UpdateAsync(account);
                    MessageBox.Show("تم تحديث الحساب بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _unitOfWork.Accounts.AddAsync(account);
                    MessageBox.Show("تم إضافة الحساب بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                await _unitOfWork.SaveChangesAsync();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ بيانات الحساب");
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                saveButton.Enabled = true;
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(accountCodeTextBox.Text))
            {
                MessageBox.Show("رمز الحساب مطلوب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                accountCodeTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(accountNameTextBox.Text))
            {
                MessageBox.Show("اسم الحساب مطلوب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                accountNameTextBox.Focus();
                return false;
            }

            if (accountTypeComboBox.SelectedValue == null)
            {
                MessageBox.Show("يجب اختيار نوع الحساب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                accountTypeComboBox.Focus();
                return false;
            }

            return true;
        }
    }
}

