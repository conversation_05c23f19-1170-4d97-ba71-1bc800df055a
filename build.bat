@echo off
echo ========================================
echo    Fish Farm Management System Build
echo ========================================
echo.

echo [1/6] Cleaning previous builds...
dotnet clean
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to clean solution
    pause
    exit /b 1
)

echo [2/6] Restoring NuGet packages...
dotnet restore
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to restore packages
    pause
    exit /b 1
)

echo [3/6] Building Models project...
dotnet build FishFarmManagement.Models\FishFarmManagement.Models.csproj
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to build Models project
    pause
    exit /b 1
)

echo [4/6] Building DAL project...
dotnet build FishFarmManagement.DAL\FishFarmManagement.DAL.csproj
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to build DAL project
    pause
    exit /b 1
)

echo [5/6] Building BLL project...
dotnet build FishFarmManagement.BLL\FishFarmManagement.BLL.csproj
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to build BLL project
    pause
    exit /b 1
)

echo [6/6] Building main application...
dotnet build FishFarmManagement\FishFarmManagement.csproj
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to build main application
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Build completed successfully!
echo ========================================
echo.
echo To run the application, execute:
echo dotnet run --project FishFarmManagement
echo.
pause
