2025-07-26 07:00:37.504 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:00:52.815 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:00:54.385 +03:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-26 07:00:54.485 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 07:00:54.492 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:00:54.495 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:00:54.511 +03:00 [INF] Applying migration '20250724205802_InitialCreate'.
2025-07-26 07:00:54.644 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:00:54.644 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:00:54.644 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:00:54.644 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:00:54.645 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:00:54.645 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:00:54.645 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:00:54.645 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:00:54.645 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE CASCADE
);
2025-07-26 07:00:54.646 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:00:54.646 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:00:54.646 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:00:54.646 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:00:54.647 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:00:54.647 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:00:54.647 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:00:54.647 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-26 07:00:54.647 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-24 23:58:00.3543764', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-24 23:58:00.3543765', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-24 23:58:00.3543765', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-24 23:58:00.3543766', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-24 23:58:00.3543766', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-26 07:00:54.648 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-24 23:58:00.3543917', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-26 07:00:54.648 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-24 23:58:00.3543858', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-24 23:58:00.3543858', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-24 23:58:00.3543859', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-24 23:58:00.3543859', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-24 23:58:00.3543861', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-24 23:58:00.3543861', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-24 23:58:00.3543863', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-26 07:00:54.649 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-26 07:00:54.649 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-26 07:00:54.649 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-26 07:00:54.649 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-26 07:00:54.649 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-26 07:00:54.649 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-26 07:00:54.650 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-26 07:00:54.650 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-26 07:00:54.650 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-26 07:00:54.650 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-26 07:00:54.651 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId" ON "InventoryMovements" ("ItemId");
2025-07-26 07:00:54.651 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-26 07:00:54.651 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-26 07:00:54.651 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-26 07:00:54.651 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-26 07:00:54.652 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-26 07:00:54.652 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-26 07:00:54.652 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-26 07:00:54.652 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-26 07:00:54.652 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-26 07:00:54.652 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-26 07:00:54.653 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-26 07:00:54.653 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724205802_InitialCreate', '8.0.0');
2025-07-26 07:00:54.662 +03:00 [INF] Applying migration '20250724211144_AddUserManagement'.
2025-07-26 07:00:54.756 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP INDEX "IX_InventoryMovements_ItemId";
2025-07-26 07:00:54.757 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:00:54.757 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:00:54.757 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-26 07:00:54.757 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100123'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:00:54.757 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100129'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:00:54.757 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.810013'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:00:54.757 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.810013'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:00:54.758 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100131'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:00:54.758 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100494'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:00:54.758 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100496'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:00:54.758 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100506'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:00:54.758 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100544'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:00:54.758 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100545'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:00:54.758 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100546'
WHERE "Id" = 6;
SELECT changes();
2025-07-26 07:00:54.758 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100547'
WHERE "Id" = 7;
SELECT changes();
2025-07-26 07:00:54.758 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100547'
WHERE "Id" = 8;
SELECT changes();
2025-07-26 07:00:54.759 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100548'
WHERE "Id" = 9;
SELECT changes();
2025-07-26 07:00:54.759 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100549'
WHERE "Id" = 10;
SELECT changes();
2025-07-26 07:00:54.759 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100549'
WHERE "Id" = 11;
SELECT changes();
2025-07-26 07:00:54.759 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.810055'
WHERE "Id" = 12;
SELECT changes();
2025-07-26 07:00:54.759 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100551'
WHERE "Id" = 13;
SELECT changes();
2025-07-26 07:00:54.759 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "FarmInfos" SET "CreatedDate" = '2025-07-25 00:11:43.8100734'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:00:54.759 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-25 00:11:43.8102126', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-25 00:11:43.8102335', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-25 00:11:43.8102376', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-25 00:11:43.810241', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-25 00:11:43.8102437', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-26 07:00:54.760 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-25 00:11:43.8102634', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, NULL, '$2a$11$xajPljvN0VMOVwgwxmR0vetvCHYroT46IZNhyYQDBHYuv1GqGxzKS', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-26 07:00:54.760 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-25 00:11:43.960912', NULL, 'System', '2025-07-25 00:11:43.9609117', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-26 07:00:54.760 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 07:00:54.760 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 07:00:54.760 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-26 07:00:54.760 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-26 07:00:54.760 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-26 07:00:54.761 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-26 07:00:54.761 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-26 07:00:54.761 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ef_temp_InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "BatchNumber" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "Reference" TEXT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:00:54.761 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "ef_temp_InventoryMovements" ("Id", "BatchNumber", "CreatedBy", "CreatedDate", "ItemId", "MovementDate", "MovementType", "Notes", "Quantity", "QuantityAfter", "QuantityBefore", "Reason", "Reference", "TotalValue", "UnitPrice", "UpdatedDate")
SELECT "Id", "BatchNumber", "CreatedBy", "CreatedDate", "ItemId", "MovementDate", "MovementType", "Notes", "Quantity", "QuantityAfter", "QuantityBefore", "Reason", "Reference", "TotalValue", "UnitPrice", "UpdatedDate"
FROM "InventoryMovements";
2025-07-26 07:00:54.767 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA foreign_keys = 0;
2025-07-26 07:00:54.768 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP TABLE "InventoryMovements";
2025-07-26 07:00:54.770 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "ef_temp_InventoryMovements" RENAME TO "InventoryMovements";
2025-07-26 07:00:54.774 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA foreign_keys = 1;
2025-07-26 07:00:54.774 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 07:00:54.774 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 07:00:54.774 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724211144_AddUserManagement', '8.0.0');
2025-07-26 07:05:14.870 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:05:40.473 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:05:42.061 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:05:42.078 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:05:42.093 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:05:42.110 +03:00 [INF] Applying migration '20250726040518_AddMustChangePasswordColumn'.
2025-07-26 07:05:42.242 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "Users" ADD "MustChangePassword" INTEGER NOT NULL DEFAULT 0;
2025-07-26 07:05:42.243 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Notifications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Notifications" PRIMARY KEY AUTOINCREMENT,
    "Title" TEXT NOT NULL,
    "Message" TEXT NOT NULL,
    "Type" INTEGER NOT NULL,
    "Priority" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL,
    "ReadAt" TEXT NULL,
    "ScheduledAt" TEXT NULL,
    "RelatedEntityType" TEXT NULL,
    "RelatedEntityId" INTEGER NULL,
    "ActionUrl" TEXT NULL,
    "IsAutoGenerated" INTEGER NOT NULL,
    "CreatedBy" TEXT NULL,
    "AdditionalData" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:05:42.243 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720449'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:05:42.243 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720451'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:05:42.243 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720451'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:05:42.243 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720452'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:05:42.244 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720453'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:05:42.244 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720597'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:05:42.244 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720598'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:05:42.244 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720602'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:05:42.244 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720623'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:05:42.244 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720623'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:05:42.244 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720624'
WHERE "Id" = 6;
SELECT changes();
2025-07-26 07:05:42.244 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720625'
WHERE "Id" = 7;
SELECT changes();
2025-07-26 07:05:42.245 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720625'
WHERE "Id" = 8;
SELECT changes();
2025-07-26 07:05:42.245 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720626'
WHERE "Id" = 9;
SELECT changes();
2025-07-26 07:05:42.245 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720627'
WHERE "Id" = 10;
SELECT changes();
2025-07-26 07:05:42.245 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720627'
WHERE "Id" = 11;
SELECT changes();
2025-07-26 07:05:42.245 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720628'
WHERE "Id" = 12;
SELECT changes();
2025-07-26 07:05:42.245 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720628'
WHERE "Id" = 13;
SELECT changes();
2025-07-26 07:05:42.245 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "FarmInfos" SET "CreatedDate" = '2025-07-26 07:05:16.9720737'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:05:42.245 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721427'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:05:42.245 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721568'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:05:42.246 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721627'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:05:42.246 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721665'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:05:42.246 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721691'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:05:42.246 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedDate" = '2025-07-26 07:05:17.2015304', "GrantedDate" = '2025-07-26 07:05:17.2015303'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:05:42.246 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = '2025-07-26 07:05:16.9721768', "MustChangePassword" = 0, "PasswordHash" = '$2a$11$Io9.UFRwXMZqmnl8.29pgujYImy4zqqbkrDpTzxaR5LyJHdqitpJy'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:05:42.246 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726040518_AddMustChangePasswordColumn', '8.0.0');
2025-07-26 07:07:14.161 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:07:15.732 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-26 07:07:15.833 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 07:07:15.841 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:07:15.844 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:07:15.859 +03:00 [INF] Applying migration '20250724205802_InitialCreate'.
2025-07-26 07:07:15.985 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:15.986 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:15.986 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:15.986 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:15.986 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:15.987 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:15.987 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:15.987 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:07:15.987 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE CASCADE
);
2025-07-26 07:07:15.988 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:07:15.988 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:07:15.988 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:07:15.989 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:07:15.989 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:07:15.989 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:07:15.989 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:07:15.989 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-26 07:07:15.990 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-24 23:58:00.3543764', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-24 23:58:00.3543765', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-24 23:58:00.3543765', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-24 23:58:00.3543766', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-24 23:58:00.3543766', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-26 07:07:15.990 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-24 23:58:00.3543917', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-26 07:07:15.990 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-24 23:58:00.3543858', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-24 23:58:00.3543858', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-24 23:58:00.3543859', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-24 23:58:00.3543859', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-24 23:58:00.3543861', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-24 23:58:00.3543861', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-24 23:58:00.3543863', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-26 07:07:15.991 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-26 07:07:15.991 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-26 07:07:15.991 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-26 07:07:15.991 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-26 07:07:15.992 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-26 07:07:15.992 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-26 07:07:15.992 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-26 07:07:15.992 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-26 07:07:15.992 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-26 07:07:15.992 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-26 07:07:15.992 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId" ON "InventoryMovements" ("ItemId");
2025-07-26 07:07:15.993 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-26 07:07:15.993 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-26 07:07:15.993 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-26 07:07:15.993 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-26 07:07:15.993 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-26 07:07:15.993 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-26 07:07:15.994 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-26 07:07:15.994 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-26 07:07:15.994 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-26 07:07:15.994 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-26 07:07:15.994 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-26 07:07:15.994 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724205802_InitialCreate', '8.0.0');
2025-07-26 07:07:16.004 +03:00 [INF] Applying migration '20250724211144_AddUserManagement'.
2025-07-26 07:07:16.096 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP INDEX "IX_InventoryMovements_ItemId";
2025-07-26 07:07:16.097 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:16.097 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:16.097 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-26 07:07:16.097 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100123'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:16.097 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100129'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:07:16.098 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.810013'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:07:16.098 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.810013'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:07:16.098 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100131'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:07:16.098 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100494'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:16.098 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100496'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:07:16.098 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100506'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:07:16.098 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100544'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:07:16.098 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100545'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:07:16.099 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100546'
WHERE "Id" = 6;
SELECT changes();
2025-07-26 07:07:16.099 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100547'
WHERE "Id" = 7;
SELECT changes();
2025-07-26 07:07:16.099 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100547'
WHERE "Id" = 8;
SELECT changes();
2025-07-26 07:07:16.099 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100548'
WHERE "Id" = 9;
SELECT changes();
2025-07-26 07:07:16.099 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100549'
WHERE "Id" = 10;
SELECT changes();
2025-07-26 07:07:16.099 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100549'
WHERE "Id" = 11;
SELECT changes();
2025-07-26 07:07:16.099 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.810055'
WHERE "Id" = 12;
SELECT changes();
2025-07-26 07:07:16.099 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100551'
WHERE "Id" = 13;
SELECT changes();
2025-07-26 07:07:16.099 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "FarmInfos" SET "CreatedDate" = '2025-07-25 00:11:43.8100734'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:16.100 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-25 00:11:43.8102126', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-25 00:11:43.8102335', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-25 00:11:43.8102376', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-25 00:11:43.810241', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-25 00:11:43.8102437', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-26 07:07:16.100 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-25 00:11:43.8102634', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, NULL, '$2a$11$xajPljvN0VMOVwgwxmR0vetvCHYroT46IZNhyYQDBHYuv1GqGxzKS', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-26 07:07:16.100 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-25 00:11:43.960912', NULL, 'System', '2025-07-25 00:11:43.9609117', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-26 07:07:16.100 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 07:07:16.100 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 07:07:16.101 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-26 07:07:16.101 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-26 07:07:16.101 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-26 07:07:16.101 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-26 07:07:16.101 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-26 07:07:16.101 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ef_temp_InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "BatchNumber" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "Reference" TEXT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:07:16.102 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "ef_temp_InventoryMovements" ("Id", "BatchNumber", "CreatedBy", "CreatedDate", "ItemId", "MovementDate", "MovementType", "Notes", "Quantity", "QuantityAfter", "QuantityBefore", "Reason", "Reference", "TotalValue", "UnitPrice", "UpdatedDate")
SELECT "Id", "BatchNumber", "CreatedBy", "CreatedDate", "ItemId", "MovementDate", "MovementType", "Notes", "Quantity", "QuantityAfter", "QuantityBefore", "Reason", "Reference", "TotalValue", "UnitPrice", "UpdatedDate"
FROM "InventoryMovements";
2025-07-26 07:07:16.107 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA foreign_keys = 0;
2025-07-26 07:07:16.107 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP TABLE "InventoryMovements";
2025-07-26 07:07:16.109 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "ef_temp_InventoryMovements" RENAME TO "InventoryMovements";
2025-07-26 07:07:16.113 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA foreign_keys = 1;
2025-07-26 07:07:16.113 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 07:07:16.113 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 07:07:16.113 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724211144_AddUserManagement', '8.0.0');
2025-07-26 07:07:16.117 +03:00 [INF] Applying migration '20250726040518_AddMustChangePasswordColumn'.
2025-07-26 07:07:16.185 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "Users" ADD "MustChangePassword" INTEGER NOT NULL DEFAULT 0;
2025-07-26 07:07:16.185 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Notifications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Notifications" PRIMARY KEY AUTOINCREMENT,
    "Title" TEXT NOT NULL,
    "Message" TEXT NOT NULL,
    "Type" INTEGER NOT NULL,
    "Priority" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL,
    "ReadAt" TEXT NULL,
    "ScheduledAt" TEXT NULL,
    "RelatedEntityType" TEXT NULL,
    "RelatedEntityId" INTEGER NULL,
    "ActionUrl" TEXT NULL,
    "IsAutoGenerated" INTEGER NOT NULL,
    "CreatedBy" TEXT NULL,
    "AdditionalData" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:07:16.186 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720449'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:16.186 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720451'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:07:16.186 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720451'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:07:16.186 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720452'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:07:16.186 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-26 07:05:16.9720453'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:07:16.186 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720597'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:16.186 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720598'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:07:16.186 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720602'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:07:16.187 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720623'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:07:16.187 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720623'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:07:16.187 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720624'
WHERE "Id" = 6;
SELECT changes();
2025-07-26 07:07:16.187 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720625'
WHERE "Id" = 7;
SELECT changes();
2025-07-26 07:07:16.187 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720625'
WHERE "Id" = 8;
SELECT changes();
2025-07-26 07:07:16.187 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720626'
WHERE "Id" = 9;
SELECT changes();
2025-07-26 07:07:16.187 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720627'
WHERE "Id" = 10;
SELECT changes();
2025-07-26 07:07:16.187 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720627'
WHERE "Id" = 11;
SELECT changes();
2025-07-26 07:07:16.188 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720628'
WHERE "Id" = 12;
SELECT changes();
2025-07-26 07:07:16.188 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-26 07:05:16.9720628'
WHERE "Id" = 13;
SELECT changes();
2025-07-26 07:07:16.188 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "FarmInfos" SET "CreatedDate" = '2025-07-26 07:05:16.9720737'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:16.188 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721427'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:16.188 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721568'
WHERE "Id" = 2;
SELECT changes();
2025-07-26 07:07:16.188 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721627'
WHERE "Id" = 3;
SELECT changes();
2025-07-26 07:07:16.188 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721665'
WHERE "Id" = 4;
SELECT changes();
2025-07-26 07:07:16.188 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-26 07:05:16.9721691'
WHERE "Id" = 5;
SELECT changes();
2025-07-26 07:07:16.189 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedDate" = '2025-07-26 07:05:17.2015304', "GrantedDate" = '2025-07-26 07:05:17.2015303'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:16.189 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = '2025-07-26 07:05:16.9721768', "MustChangePassword" = 0, "PasswordHash" = '$2a$11$Io9.UFRwXMZqmnl8.29pgujYImy4zqqbkrDpTzxaR5LyJHdqitpJy'
WHERE "Id" = 1;
SELECT changes();
2025-07-26 07:07:16.189 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726040518_AddMustChangePasswordColumn', '8.0.0');
2025-07-26 07:08:19.263 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:08:21.002 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:08:21.036 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:08:33.629 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:08:35.182 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:08:35.197 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:08:35.209 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:08:35.230 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-26 07:17:25.045 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:18:09.209 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:21:36.945 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:22:21.499 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:22:38.779 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:23:22.463 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:23:39.378 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:25:26.759 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:25:29.409 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:25:29.430 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:25:48.158 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:26:07.498 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:27:04.082 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:27:06.344 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:27:06.366 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:27:26.177 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:27:42.988 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:31:39.321 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:32:03.763 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:32:20.522 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:32:21.988 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-26 07:32:22.025 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-07-26 07:32:22.047 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsLock" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK___EFMigrationsLock" PRIMARY KEY,
    "Timestamp" TEXT NOT NULL
);
2025-07-26 07:32:22.052 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-07-26 04:32:22.0483702+00:00');
SELECT changes();
2025-07-26 07:32:22.115 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 07:32:22.121 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:32:22.123 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:32:22.135 +03:00 [INF] Applying migration '20250726043205_InitialSQLiteMigration'.
2025-07-26 07:32:22.267 +03:00 [ERR] Failed executing DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:32:47.689 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:32:49.220 +03:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-26 07:32:49.231 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-26 07:32:49.240 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-07-26 07:32:49.250 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsLock" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK___EFMigrationsLock" PRIMARY KEY,
    "Timestamp" TEXT NOT NULL
);
2025-07-26 07:32:49.256 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-07-26 04:32:49.2513945+00:00');
SELECT changes();
2025-07-26 07:32:49.319 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 07:32:49.331 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:32:49.333 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:32:49.344 +03:00 [INF] Applying migration '20250726043205_InitialSQLiteMigration'.
2025-07-26 07:32:49.477 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:32:49.478 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:32:49.478 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:32:49.478 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:32:49.478 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:32:49.478 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:32:49.479 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Notifications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Notifications" PRIMARY KEY AUTOINCREMENT,
    "Title" TEXT NOT NULL,
    "Message" TEXT NOT NULL,
    "Type" INTEGER NOT NULL,
    "Priority" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL,
    "ReadAt" TEXT NULL,
    "ScheduledAt" TEXT NULL,
    "RelatedEntityType" TEXT NULL,
    "RelatedEntityId" INTEGER NULL,
    "ActionUrl" TEXT NULL,
    "IsAutoGenerated" INTEGER NOT NULL,
    "CreatedBy" TEXT NULL,
    "AdditionalData" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:32:49.479 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:32:49.480 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:32:49.480 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "MustChangePassword" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:32:49.480 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:32:49.480 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:32:49.480 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:32:49.480 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:32:49.481 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:32:49.481 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:32:49.481 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-26 07:32:49.481 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:32:49.481 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:32:49.482 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:32:49.482 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-26 07:32:49.482 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-26 07:32:49.482 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-26 07:00:00', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-26 07:32:49.482 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-26 07:32:49.483 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "MustChangePassword", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-26 07:00:00', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, 0, NULL, '$2a$11$K8gHZ8W8W8W8W8W8W8W8WOeH8W8W8W8W8W8W8W8W8W8W8W8W8W8W8W8', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-26 07:32:49.484 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-26 07:32:49.484 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-26 07:00:00', NULL, 'System', '2025-07-26 07:00:00', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-26 07:32:49.485 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-26 07:32:49.485 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-26 07:32:49.485 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-26 07:32:49.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-26 07:32:49.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-26 07:32:49.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-26 07:32:49.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-26 07:32:49.486 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-26 07:32:49.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-26 07:32:49.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-26 07:32:49.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 07:32:49.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 07:32:49.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-26 07:32:49.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-26 07:32:49.487 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-26 07:32:49.488 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-26 07:32:49.488 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-26 07:32:49.488 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-26 07:32:49.488 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-26 07:32:49.488 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-26 07:32:49.488 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-26 07:32:49.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-26 07:32:49.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-26 07:32:49.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-26 07:32:49.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-26 07:32:49.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-26 07:32:49.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-26 07:32:49.489 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-26 07:32:49.490 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726043205_InitialSQLiteMigration', '9.0.1');
2025-07-26 07:32:49.500 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-07-26 07:35:33.705 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:35:55.654 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:35:57.509 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-26 07:35:57.564 +03:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-07-26 07:35:57.581 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-07-26 04:35:57.5684265+00:00');
SELECT changes();
2025-07-26 07:35:57.668 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 07:35:57.683 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:35:57.686 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:35:57.707 +03:00 [INF] Applying migration '20250726043535_AddMustChangePasswordColumn'.
2025-07-26 07:35:57.770 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726043535_AddMustChangePasswordColumn', '9.0.1');
2025-07-26 07:35:57.832 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-07-26 07:43:41.179 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:44:11.552 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:44:14.274 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-26 07:44:14.286 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-26 07:44:14.297 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-07-26 07:44:14.314 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsLock" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK___EFMigrationsLock" PRIMARY KEY,
    "Timestamp" TEXT NOT NULL
);
2025-07-26 07:44:14.321 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-07-26 04:44:14.3149297+00:00');
SELECT changes();
2025-07-26 07:44:14.395 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 07:44:14.411 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:44:14.413 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:44:14.427 +03:00 [INF] Applying migration '20250726044343_InitialSQLiteOnlyMigration'.
2025-07-26 07:44:14.675 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:44:14.675 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:44:14.676 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:44:14.676 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:44:14.676 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:44:14.676 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:44:14.676 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Notifications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Notifications" PRIMARY KEY AUTOINCREMENT,
    "Title" TEXT NOT NULL,
    "Message" TEXT NOT NULL,
    "Type" INTEGER NOT NULL,
    "Priority" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL,
    "ReadAt" TEXT NULL,
    "ScheduledAt" TEXT NULL,
    "RelatedEntityType" TEXT NULL,
    "RelatedEntityId" INTEGER NULL,
    "ActionUrl" TEXT NULL,
    "IsAutoGenerated" INTEGER NOT NULL,
    "CreatedBy" TEXT NULL,
    "AdditionalData" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:44:14.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:44:14.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:44:14.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "MustChangePassword" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 07:44:14.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:44:14.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:44:14.678 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:44:14.678 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:44:14.678 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:44:14.678 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 07:44:14.678 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-26 07:44:14.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:44:14.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:44:14.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 07:44:14.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-26 07:44:14.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-26 07:44:14.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-26 07:00:00', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-26 07:44:14.680 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-26 07:44:14.680 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "MustChangePassword", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-26 07:00:00', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, 0, NULL, '$2a$11$K8gHZ8W8W8W8W8W8W8W8WOeH8W8W8W8W8W8W8W8W8W8W8W8W8W8W8W8', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-26 07:44:14.681 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-26 07:44:14.681 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-26 07:00:00', NULL, 'System', '2025-07-26 07:00:00', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-26 07:44:14.681 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-26 07:44:14.681 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-26 07:44:14.682 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-26 07:44:14.682 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-26 07:44:14.682 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-26 07:44:14.682 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-26 07:44:14.682 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-26 07:44:14.682 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-26 07:44:14.682 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-26 07:44:14.683 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-26 07:44:14.683 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 07:44:14.700 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 07:44:14.700 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-26 07:44:14.701 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-26 07:44:14.701 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-26 07:44:14.701 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-26 07:44:14.701 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-26 07:44:14.701 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-26 07:44:14.701 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-26 07:44:14.702 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-26 07:44:14.702 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-26 07:44:14.702 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-26 07:44:14.702 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-26 07:44:14.702 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-26 07:44:14.707 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-26 07:44:14.707 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-26 07:44:14.707 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-26 07:44:14.707 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-26 07:44:14.707 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726044343_InitialSQLiteOnlyMigration', '9.0.1');
2025-07-26 07:44:14.718 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-07-26 07:47:19.139 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:48:11.386 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in F:\account pro\fish accounting & management\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-26 07:48:14.013 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-26 07:48:14.118 +03:00 [INF] Executed DbCommand (76ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-07-26 07:48:14.136 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-07-26 04:48:14.1243832+00:00');
SELECT changes();
2025-07-26 07:48:14.246 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 07:48:14.252 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 07:48:14.254 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 07:48:14.274 +03:00 [INF] Applying migration '20250726044723_AddMustChangePasswordToUsers'.
2025-07-26 07:48:14.328 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726044723_AddMustChangePasswordToUsers', '9.0.1');
2025-07-26 07:48:14.335 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-07-26 10:56:24.070 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 10:56:24.103 +03:00 [INF] تم اكتشاف قاعدة بيانات موجودة
2025-07-26 10:56:24.442 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 10:56:24.513 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-26 10:56:24.537 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-26 10:56:24.537 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-26 10:56:24.537 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 10:56:34.861 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 10:56:34.958 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 10:56:35.582 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-26 10:59:02.430 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = غير موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 10:59:02.464 +03:00 [INF] إنشاء قاعدة البيانات باستخدام التهجيرات
2025-07-26 10:59:02.780 +03:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-26 10:59:02.791 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-26 10:59:02.801 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-07-26 10:59:02.818 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsLock" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK___EFMigrationsLock" PRIMARY KEY,
    "Timestamp" TEXT NOT NULL
);
2025-07-26 10:59:02.823 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-07-26 07:59:02.8197902+00:00');
SELECT changes();
2025-07-26 10:59:02.885 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 10:59:02.904 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 10:59:02.908 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 10:59:02.920 +03:00 [INF] Applying migration '20250726044343_InitialSQLiteOnlyMigration'.
2025-07-26 10:59:03.039 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 10:59:03.039 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 10:59:03.039 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 10:59:03.040 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 10:59:03.040 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 10:59:03.040 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 10:59:03.040 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Notifications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Notifications" PRIMARY KEY AUTOINCREMENT,
    "Title" TEXT NOT NULL,
    "Message" TEXT NOT NULL,
    "Type" INTEGER NOT NULL,
    "Priority" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL,
    "ReadAt" TEXT NULL,
    "ScheduledAt" TEXT NULL,
    "RelatedEntityType" TEXT NULL,
    "RelatedEntityId" INTEGER NULL,
    "ActionUrl" TEXT NULL,
    "IsAutoGenerated" INTEGER NOT NULL,
    "CreatedBy" TEXT NULL,
    "AdditionalData" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 10:59:03.040 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 10:59:03.040 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 10:59:03.041 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "MustChangePassword" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 10:59:03.041 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-26 10:59:03.041 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-26 10:59:03.041 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 10:59:03.041 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 10:59:03.042 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 10:59:03.042 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 10:59:03.042 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-26 10:59:03.042 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 10:59:03.042 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 10:59:03.042 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 10:59:03.043 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-26 10:59:03.043 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-26 10:59:03.044 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-26 07:00:00', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-26 10:59:03.044 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-26 10:59:03.044 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "MustChangePassword", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-26 07:00:00', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, 0, NULL, '$2a$11$K8gHZ8W8W8W8W8W8W8W8WOeH8W8W8W8W8W8W8W8W8W8W8W8W8W8W8W8', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-26 10:59:03.045 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-26 10:59:03.045 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-26 07:00:00', NULL, 'System', '2025-07-26 07:00:00', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-26 10:59:03.045 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-26 10:59:03.046 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-26 10:59:03.046 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-26 10:59:03.046 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-26 10:59:03.046 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-26 10:59:03.046 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-26 10:59:03.046 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-26 10:59:03.047 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-26 10:59:03.047 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-26 10:59:03.047 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-26 10:59:03.047 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 10:59:03.047 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 10:59:03.047 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-26 10:59:03.047 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-26 10:59:03.048 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-26 10:59:03.048 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-26 10:59:03.048 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-26 10:59:03.048 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-26 10:59:03.048 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-26 10:59:03.048 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-26 10:59:03.048 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-26 10:59:03.049 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-26 10:59:03.049 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-26 10:59:03.049 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-26 10:59:03.049 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-26 10:59:03.049 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-26 10:59:03.049 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-26 10:59:03.049 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-26 10:59:03.050 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726044343_InitialSQLiteOnlyMigration', '9.0.1');
2025-07-26 10:59:03.050 +03:00 [INF] Applying migration '20250726044723_AddMustChangePasswordToUsers'.
2025-07-26 10:59:03.075 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726044723_AddMustChangePasswordToUsers', '9.0.1');
2025-07-26 10:59:03.088 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-07-26 10:59:03.089 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-26 10:59:03.417 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 10:59:03.496 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-26 10:59:03.520 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-26 10:59:03.520 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-26 10:59:03.520 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 10:59:52.990 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 10:59:53.098 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 10:59:53.700 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-26 11:00:02.754 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:00:02.755 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 11:00:03.099 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-26 11:00:17.097 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:00:17.098 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 11:00:17.283 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p2='?' (DbType = Int32), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0, "LockedUntil" = @p1
WHERE "Id" = @p2
RETURNING 1;
2025-07-26 11:00:28.202 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:00:28.203 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 11:03:24.052 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = غير موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 11:03:24.085 +03:00 [INF] إنشاء قاعدة البيانات باستخدام التهجيرات
2025-07-26 11:03:24.392 +03:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-26 11:03:24.403 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-26 11:03:24.413 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-07-26 11:03:24.430 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsLock" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK___EFMigrationsLock" PRIMARY KEY,
    "Timestamp" TEXT NOT NULL
);
2025-07-26 11:03:24.435 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-07-26 08:03:24.4311688+00:00');
SELECT changes();
2025-07-26 11:03:24.497 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 11:03:24.533 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 11:03:24.537 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 11:03:24.550 +03:00 [INF] Applying migration '20250726044343_InitialSQLiteOnlyMigration'.
2025-07-26 11:03:24.670 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:03:24.671 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:03:24.671 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:03:24.671 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:03:24.671 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:03:24.672 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:03:24.672 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Notifications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Notifications" PRIMARY KEY AUTOINCREMENT,
    "Title" TEXT NOT NULL,
    "Message" TEXT NOT NULL,
    "Type" INTEGER NOT NULL,
    "Priority" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL,
    "ReadAt" TEXT NULL,
    "ScheduledAt" TEXT NULL,
    "RelatedEntityType" TEXT NULL,
    "RelatedEntityId" INTEGER NULL,
    "ActionUrl" TEXT NULL,
    "IsAutoGenerated" INTEGER NOT NULL,
    "CreatedBy" TEXT NULL,
    "AdditionalData" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:03:24.673 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:03:24.673 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:03:24.673 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "MustChangePassword" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:03:24.673 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:03:24.673 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:03:24.673 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:03:24.674 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:03:24.674 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:03:24.674 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:03:24.674 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-26 11:03:24.674 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 11:03:24.675 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 11:03:24.675 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 11:03:24.675 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-26 11:03:24.675 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-26 11:03:24.675 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-26 07:00:00', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-26 11:03:24.676 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-26 11:03:24.676 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "MustChangePassword", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-26 07:00:00', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, 0, NULL, '$2a$11$K8gHZ8W8W8W8W8W8W8W8WOeH8W8W8W8W8W8W8W8W8W8W8W8W8W8W8W8', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-26 11:03:24.676 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-26 11:03:24.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-26 07:00:00', NULL, 'System', '2025-07-26 07:00:00', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-26 11:03:24.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-26 11:03:24.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-26 11:03:24.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-26 11:03:24.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-26 11:03:24.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-26 11:03:24.677 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-26 11:03:24.678 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-26 11:03:24.678 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-26 11:03:24.678 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-26 11:03:24.678 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-26 11:03:24.678 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 11:03:24.678 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 11:03:24.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-26 11:03:24.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-26 11:03:24.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-26 11:03:24.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-26 11:03:24.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-26 11:03:24.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-26 11:03:24.679 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-26 11:03:24.680 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-26 11:03:24.680 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-26 11:03:24.680 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-26 11:03:24.680 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-26 11:03:24.680 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-26 11:03:24.680 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-26 11:03:24.680 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-26 11:03:24.681 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-26 11:03:24.681 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-26 11:03:24.681 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726044343_InitialSQLiteOnlyMigration', '9.0.1');
2025-07-26 11:03:24.681 +03:00 [INF] Applying migration '20250726044723_AddMustChangePasswordToUsers'.
2025-07-26 11:03:24.710 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726044723_AddMustChangePasswordToUsers', '9.0.1');
2025-07-26 11:03:24.724 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-07-26 11:03:24.724 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-26 11:03:25.050 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:03:25.123 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-26 11:03:25.149 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-26 11:03:25.150 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-26 11:03:25.150 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 11:03:33.876 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:03:33.988 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 11:03:34.584 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-26 11:03:43.343 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:03:43.344 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 11:03:43.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-26 11:03:58.695 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:03:58.696 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 11:03:58.874 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p2='?' (DbType = Int32), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0, "LockedUntil" = @p1
WHERE "Id" = @p2
RETURNING 1;
2025-07-26 11:06:14.023 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = غير موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 11:06:14.058 +03:00 [INF] إنشاء قاعدة البيانات باستخدام التهجيرات
2025-07-26 11:06:14.401 +03:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-26 11:06:14.413 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-26 11:06:14.424 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-07-26 11:06:14.442 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsLock" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK___EFMigrationsLock" PRIMARY KEY,
    "Timestamp" TEXT NOT NULL
);
2025-07-26 11:06:14.448 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-07-26 08:06:14.4430537+00:00');
SELECT changes();
2025-07-26 11:06:14.519 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 11:06:14.539 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 11:06:14.544 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 11:06:14.557 +03:00 [INF] Applying migration '20250726044343_InitialSQLiteOnlyMigration'.
2025-07-26 11:06:14.702 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:06:14.703 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:06:14.703 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:06:14.703 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:06:14.703 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:06:14.704 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:06:14.704 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Notifications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Notifications" PRIMARY KEY AUTOINCREMENT,
    "Title" TEXT NOT NULL,
    "Message" TEXT NOT NULL,
    "Type" INTEGER NOT NULL,
    "Priority" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL,
    "ReadAt" TEXT NULL,
    "ScheduledAt" TEXT NULL,
    "RelatedEntityType" TEXT NULL,
    "RelatedEntityId" INTEGER NULL,
    "ActionUrl" TEXT NULL,
    "IsAutoGenerated" INTEGER NOT NULL,
    "CreatedBy" TEXT NULL,
    "AdditionalData" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:06:14.704 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:06:14.704 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:06:14.704 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "MustChangePassword" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:06:14.704 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:06:14.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:06:14.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:06:14.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:06:14.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:06:14.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:06:14.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-26 11:06:14.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 11:06:14.707 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 11:06:14.707 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 11:06:14.709 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-26 11:06:14.709 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-26 11:06:14.709 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-26 07:00:00', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-26 11:06:14.710 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-26 11:06:14.710 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "MustChangePassword", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-26 07:00:00', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, 0, NULL, '$2a$11$K8gHZ8W8W8W8W8W8W8W8WOeH8W8W8W8W8W8W8W8W8W8W8W8W8W8W8W8', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-26 11:06:14.710 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-26 11:06:14.711 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-26 07:00:00', NULL, 'System', '2025-07-26 07:00:00', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-26 11:06:14.711 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-26 11:06:14.711 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-26 11:06:14.711 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-26 11:06:14.711 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-26 11:06:14.712 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-26 11:06:14.712 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-26 11:06:14.712 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-26 11:06:14.712 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-26 11:06:14.712 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-26 11:06:14.712 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-26 11:06:14.712 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 11:06:14.715 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 11:06:14.715 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-26 11:06:14.715 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-26 11:06:14.715 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-26 11:06:14.716 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-26 11:06:14.716 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-26 11:06:14.716 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-26 11:06:14.716 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-26 11:06:14.716 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-26 11:06:14.716 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-26 11:06:14.717 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-26 11:06:14.717 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-26 11:06:14.717 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-26 11:06:14.717 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-26 11:06:14.717 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-26 11:06:14.717 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-26 11:06:14.717 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-26 11:06:14.718 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726044343_InitialSQLiteOnlyMigration', '9.0.1');
2025-07-26 11:06:14.718 +03:00 [INF] Applying migration '20250726044723_AddMustChangePasswordToUsers'.
2025-07-26 11:06:14.750 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726044723_AddMustChangePasswordToUsers', '9.0.1');
2025-07-26 11:06:14.763 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-07-26 11:06:14.764 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-26 11:06:15.085 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:06:15.158 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-26 11:06:15.185 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-26 11:06:15.185 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-26 11:06:15.185 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 11:06:25.778 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:06:25.885 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 11:06:26.478 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-26 11:06:37.494 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:06:37.496 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 11:06:37.832 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-26 11:06:57.892 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:06:57.893 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 11:06:58.073 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p2='?' (DbType = Int32), @p0='?' (DbType = Int32), @p1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0, "LockedUntil" = @p1
WHERE "Id" = @p2
RETURNING 1;
2025-07-26 11:10:11.990 +03:00 [INF] تهيئة قاعدة البيانات: الحالة الحالية = غير موجودة، التهجير التلقائي = true، استخدام نص SQL = false
2025-07-26 11:10:12.025 +03:00 [INF] إنشاء قاعدة البيانات باستخدام التهجيرات
2025-07-26 11:10:12.352 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-26 11:10:12.363 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-26 11:10:12.373 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-07-26 11:10:12.389 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsLock" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK___EFMigrationsLock" PRIMARY KEY,
    "Timestamp" TEXT NOT NULL
);
2025-07-26 11:10:12.394 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-07-26 08:10:12.3900443+00:00');
SELECT changes();
2025-07-26 11:10:12.458 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-26 11:10:12.477 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-26 11:10:12.481 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-26 11:10:12.494 +03:00 [INF] Applying migration '20250726044343_InitialSQLiteOnlyMigration'.
2025-07-26 11:10:12.621 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:10:12.621 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:10:12.621 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:10:12.621 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:10:12.621 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:10:12.622 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:10:12.622 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Notifications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Notifications" PRIMARY KEY AUTOINCREMENT,
    "Title" TEXT NOT NULL,
    "Message" TEXT NOT NULL,
    "Type" INTEGER NOT NULL,
    "Priority" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL,
    "ReadAt" TEXT NULL,
    "ScheduledAt" TEXT NULL,
    "RelatedEntityType" TEXT NULL,
    "RelatedEntityId" INTEGER NULL,
    "ActionUrl" TEXT NULL,
    "IsAutoGenerated" INTEGER NOT NULL,
    "CreatedBy" TEXT NULL,
    "AdditionalData" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:10:12.622 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:10:12.622 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:10:12.622 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "MustChangePassword" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-26 11:10:12.623 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:10:12.623 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:10:12.623 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:10:12.623 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:10:12.623 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:10:12.624 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-26 11:10:12.624 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-26 11:10:12.624 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 11:10:12.625 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 11:10:12.625 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-26 11:10:12.626 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-26 11:10:12.626 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-26 11:10:12.626 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-26 07:00:00', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-26 11:10:12.626 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-26 07:00:00', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-26 07:00:00', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-26 07:00:00', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-26 07:00:00', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-26 07:00:00', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-26 11:10:12.627 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "MustChangePassword", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-26 07:00:00', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, 0, NULL, '$2a$11$K8gHZ8W8W8W8W8W8W8W8WOeH8W8W8W8W8W8W8W8W8W8W8W8W8W8W8W8', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-26 11:10:12.627 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-26 07:00:00', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-26 11:10:12.627 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-26 07:00:00', NULL, 'System', '2025-07-26 07:00:00', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-26 11:10:12.628 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-26 11:10:12.628 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-26 11:10:12.628 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-26 11:10:12.628 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-26 11:10:12.628 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-26 11:10:12.628 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-26 11:10:12.629 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-26 11:10:12.629 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-26 11:10:12.629 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-26 11:10:12.629 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-26 11:10:12.629 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-26 11:10:12.629 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-26 11:10:12.630 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-26 11:10:12.630 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-26 11:10:12.630 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-26 11:10:12.630 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-26 11:10:12.630 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-26 11:10:12.631 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-26 11:10:12.631 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-26 11:10:12.631 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-26 11:10:12.631 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-26 11:10:12.631 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-26 11:10:12.631 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-26 11:10:12.631 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-26 11:10:12.632 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-26 11:10:12.632 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-26 11:10:12.632 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-26 11:10:12.632 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-26 11:10:12.632 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726044343_InitialSQLiteOnlyMigration', '9.0.1');
2025-07-26 11:10:12.632 +03:00 [INF] Applying migration '20250726044723_AddMustChangePasswordToUsers'.
2025-07-26 11:10:12.662 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250726044723_AddMustChangePasswordToUsers', '9.0.1');
2025-07-26 11:10:12.675 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-07-26 11:10:12.676 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-26 11:10:12.676 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-26 11:10:27.709 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:10:27.882 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 11:10:28.498 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-26 11:10:44.466 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-26 11:10:44.468 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u1"."Id", "u1"."CreatedDate", "u1"."Email", "u1"."FailedLoginAttempts", "u1"."FullName", "u1"."IsSystemAdmin", "u1"."LastLoginDate", "u1"."LockedUntil", "u1"."MustChangePassword", "u1"."Notes", "u1"."PasswordHash", "u1"."PhoneNumber", "u1"."Status", "u1"."UpdatedDate", "u1"."Username", "s"."Id", "s"."CreatedDate", "s"."ExpiryDate", "s"."GrantedBy", "s"."GrantedDate", "s"."IsActive", "s"."Notes", "s"."RoleId", "s"."UpdatedDate", "s"."UserId", "s"."Id0", "s"."CreatedDate0", "s"."Description", "s"."IsActive0", "s"."IsSystemRole", "s"."Name", "s"."Permissions", "s"."UpdatedDate0"
FROM (
    SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
    FROM "Users" AS "u"
    WHERE "u"."Username" = @__username_0 AND "u"."Status" = 'نشط'
    LIMIT 1
) AS "u1"
LEFT JOIN (
    SELECT "u0"."Id", "u0"."CreatedDate", "u0"."ExpiryDate", "u0"."GrantedBy", "u0"."GrantedDate", "u0"."IsActive", "u0"."Notes", "u0"."RoleId", "u0"."UpdatedDate", "u0"."UserId", "r"."Id" AS "Id0", "r"."CreatedDate" AS "CreatedDate0", "r"."Description", "r"."IsActive" AS "IsActive0", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate" AS "UpdatedDate0"
    FROM "UserRoles" AS "u0"
    INNER JOIN "Roles" AS "r" ON "u0"."RoleId" = "r"."Id"
) AS "s" ON "u1"."Id" = "s"."UserId"
ORDER BY "u1"."Id", "s"."Id"
2025-07-26 11:10:44.758 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "FailedLoginAttempts" = @p0
WHERE "Id" = @p1
RETURNING 1;
