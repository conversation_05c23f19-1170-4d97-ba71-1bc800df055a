@echo off
chcp 65001 >nul
title التحقق من سلامة الحزمة - Package Verification

echo ========================================
echo    التحقق من سلامة حزمة البرنامج
echo    Package Integrity Verification
echo ========================================
echo.

set "ERROR_COUNT=0"
set "WARNING_COUNT=0"

echo فحص الملفات الأساسية...
echo Checking core files...
echo.

:: فحص الملف التنفيذي الرئيسي
if exist "FishFarmManagement.exe" (
    echo ✓ FishFarmManagement.exe - موجود
) else (
    echo ✗ FishFarmManagement.exe - مفقود
    set /a ERROR_COUNT+=1
)

:: فحص ملف الإعدادات
if exist "appsettings.json" (
    echo ✓ appsettings.json - موجود
) else (
    echo ✗ appsettings.json - مفقود
    set /a ERROR_COUNT+=1
)

:: فحص ملف README
if exist "README.txt" (
    echo ✓ README.txt - موجود
) else (
    echo ⚠ README.txt - مفقود (غير ضروري)
    set /a WARNING_COUNT+=1
)

:: فحص دليل المستخدم
if exist "دليل_المستخدم.txt" (
    echo ✓ دليل_المستخدم.txt - موجود
) else (
    echo ⚠ دليل_المستخدم.txt - مفقود (مُوصى به)
    set /a WARNING_COUNT+=1
)

:: فحص ملف التشغيل
if exist "run.bat" (
    echo ✓ run.bat - موجود
) else (
    echo ⚠ run.bat - مفقود (مُوصى به)
    set /a WARNING_COUNT+=1
)

:: فحص ملف التثبيت
if exist "install.bat" (
    echo ✓ install.bat - موجود
) else (
    echo ⚠ install.bat - مفقود (اختياري)
    set /a WARNING_COUNT+=1
)

echo.
echo فحص المجلدات...
echo Checking directories...
echo.

:: فحص مجلد السجلات
if exist "Logs" (
    echo ✓ مجلد Logs - موجود
) else (
    echo ⚠ مجلد Logs - غير موجود (سيتم إنشاؤه تلقائياً)
    set /a WARNING_COUNT+=1
)

:: فحص مجلد النسخ الاحتياطية
if exist "Backups" (
    echo ✓ مجلد Backups - موجود
) else (
    echo ⚠ مجلد Backups - غير موجود (سيتم إنشاؤه تلقائياً)
    set /a WARNING_COUNT+=1
)

:: فحص مجلد التقارير
if exist "Reports" (
    echo ✓ مجلد Reports - موجود
) else (
    echo ⚠ مجلد Reports - غير موجود (سيتم إنشاؤه تلقائياً)
    set /a WARNING_COUNT+=1
)

echo.
echo فحص متطلبات النظام...
echo Checking system requirements...
echo.

:: فحص إصدار Windows
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo إصدار Windows: %VERSION%

:: فحص .NET Runtime
dotnet --version >nul 2>&1
if %ERRORLEVEL% equ 0 (
    for /f %%i in ('dotnet --version 2^>nul') do set DOTNET_VERSION=%%i
    echo ✓ .NET Runtime: %DOTNET_VERSION%
) else (
    echo ⚠ .NET Runtime - غير مثبت أو غير متاح
    echo   البرنامج قد يعمل بدونه إذا كانت الملفات المطلوبة مضمنة
    set /a WARNING_COUNT+=1
)

:: فحص الذاكرة المتاحة
for /f "skip=1" %%p in ('wmic computersystem get TotalPhysicalMemory') do (
    set /a MEMORY_GB=%%p/1024/1024/1024
    goto :memory_done
)
:memory_done
if %MEMORY_GB% geq 4 (
    echo ✓ الذاكرة: %MEMORY_GB% GB (كافية)
) else (
    echo ⚠ الذاكرة: %MEMORY_GB% GB (قد تكون غير كافية، الحد الأدنى 4 GB)
    set /a WARNING_COUNT+=1
)

:: فحص مساحة القرص
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set FREE_SPACE=%%a
set /a FREE_SPACE_MB=%FREE_SPACE:~0,-3%/1024/1024
if %FREE_SPACE_MB% geq 500 (
    echo ✓ مساحة القرص: %FREE_SPACE_MB% MB (كافية)
) else (
    echo ⚠ مساحة القرص: %FREE_SPACE_MB% MB (قد تكون غير كافية، الحد الأدنى 500 MB)
    set /a WARNING_COUNT+=1
)

echo.
echo فحص الصلاحيات...
echo Checking permissions...
echo.

:: فحص صلاحيات الكتابة
echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    echo ✓ صلاحيات الكتابة - متاحة
    del test_write.tmp >nul 2>&1
) else (
    echo ⚠ صلاحيات الكتابة - محدودة (قد تحتاج تشغيل البرنامج كمدير)
    set /a WARNING_COUNT+=1
)

:: فحص صلاحيات التنفيذ
if exist "FishFarmManagement.exe" (
    echo ✓ صلاحيات التنفيذ - متاحة (افتراضياً)
) else (
    echo ✗ لا يمكن فحص صلاحيات التنفيذ (الملف التنفيذي مفقود)
    set /a ERROR_COUNT+=1
)

echo.
echo ========================================
echo    نتائج الفحص | Verification Results
echo ========================================
echo.

if %ERROR_COUNT% equ 0 (
    if %WARNING_COUNT% equ 0 (
        echo ✅ الحزمة سليمة تماماً!
        echo ✅ Package is completely intact!
        echo.
        echo يمكنك تشغيل البرنامج بأمان.
        echo You can safely run the program.
    ) else (
        echo ⚠️ الحزمة سليمة مع %WARNING_COUNT% تحذير
        echo ⚠️ Package is intact with %WARNING_COUNT% warning(s)
        echo.
        echo البرنامج سيعمل، لكن قد تواجه بعض المشاكل البسيطة.
        echo The program will work, but you might encounter minor issues.
    )
) else (
    echo ❌ توجد %ERROR_COUNT% مشكلة خطيرة في الحزمة
    echo ❌ There are %ERROR_COUNT% critical issue(s) with the package
    echo.
    echo البرنامج قد لا يعمل بشكل صحيح.
    echo The program may not work correctly.
    echo يرجى إعادة تحميل الحزمة أو التواصل مع الدعم الفني.
    echo Please re-download the package or contact technical support.
)

echo.
echo التفاصيل:
echo Details:
echo - الأخطاء الخطيرة: %ERROR_COUNT%
echo - التحذيرات: %WARNING_COUNT%
echo - Critical Errors: %ERROR_COUNT%
echo - Warnings: %WARNING_COUNT%

echo.
echo للدعم الفني: <EMAIL>
echo Technical Support: <EMAIL>
echo.

if %ERROR_COUNT% gtr 0 (
    echo هل تريد محاولة تشغيل البرنامج رغم الأخطاء؟ (Y/N)
    echo Do you want to try running the program despite the errors? (Y/N)
    set /p choice=
    if /i "%choice%" equ "Y" (
        if exist "FishFarmManagement.exe" (
            echo تشغيل البرنامج...
            start "" "FishFarmManagement.exe"
        ) else (
            echo لا يمكن تشغيل البرنامج - الملف التنفيذي مفقود
            echo Cannot run the program - executable file is missing
        )
    )
) else (
    echo هل تريد تشغيل البرنامج الآن؟ (Y/N)
    echo Do you want to run the program now? (Y/N)
    set /p choice=
    if /i "%choice%" equ "Y" (
        if exist "run.bat" (
            call run.bat
        ) else if exist "FishFarmManagement.exe" (
            start "" "FishFarmManagement.exe"
        )
    )
)

pause
