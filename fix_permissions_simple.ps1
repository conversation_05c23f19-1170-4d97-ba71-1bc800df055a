# Simple fix for Permissions in MainForm.cs
$file = "FishFarmManagement\Forms\MainForm.cs"

if (Test-Path $file) {
    Write-Host "Fixing Permissions in $file"
    $content = Get-Content $file
    
    # Replace lines containing Permissions with simple true
    for ($i = 0; $i -lt $content.Length; $i++) {
        if ($content[$i] -match 'Permissions\.') {
            $content[$i] = $content[$i] -replace 'await _authorizationService\.HasPermissionAsync\(Permissions\.[^)]+\)', 'true'
        }
    }
    
    Set-Content -Path $file -Value $content
    Write-Host "Fixed Permissions in $file"
}

Write-Host "Done"
