# 🚀 تشغيل نظام إدارة مزرعة الأسماك v1.0.1
## Running Fish Farm Management System v1.0.1

---

## ⚠️ حالة البرنامج الحالية | Current Program Status

**البرنامج جاهز للتشغيل ولكن يحتاج إلى بناء أولاً**  
**The program is ready to run but needs to be built first**

---

## 🛠️ متطلبات التشغيل | Running Requirements

### 📋 **المتطلبات الأساسية | Basic Requirements**
- ✅ **Windows 10/11** (64-bit)
- ✅ **.NET 8.0 SDK** - [تحميل من هنا](https://dotnet.microsoft.com/download/dotnet/8.0)
- ✅ **Visual Studio 2022** أو **Visual Studio Code** (اختياري)
- ✅ **4 GB RAM** (8 GB مُوصى به)
- ✅ **500 MB** مساحة فارغة

---

## 🔧 خطوات التشغيل | Running Steps

### 1️⃣ **تثبيت .NET 8.0 SDK**

#### أ) التحميل والتثبيت
```powershell
# تحميل .NET 8.0 SDK من الموقع الرسمي
# https://dotnet.microsoft.com/download/dotnet/8.0

# أو استخدام winget (إذا كان متاحاً)
winget install Microsoft.DotNet.SDK.8
```

#### ب) التحقق من التثبيت
```powershell
# التحقق من إصدار .NET
dotnet --version

# يجب أن يظهر: 8.0.x أو أحدث
```

### 2️⃣ **بناء البرنامج | Build the Program**

#### أ) البناء السريع
```powershell
# الانتقال إلى مجلد المشروع
cd "h:\account pro\fish accounting & management\fish accounting & management"

# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build --configuration Release

# تشغيل البرنامج
dotnet run --project FishFarmManagement\FishFarmManagement.csproj
```

#### ب) البناء باستخدام السكريپت
```batch
# تشغيل سكريپت البناء التجاري
.\build-commercial.bat
```

### 3️⃣ **تشغيل البرنامج | Run the Program**

#### أ) التشغيل المباشر
```powershell
# بعد البناء الناجح
cd FishFarmManagement\bin\Release\net8.0-windows
.\FishFarmManagement.exe
```

#### ب) التشغيل من Visual Studio
1. افتح `FishFarmManagement.sln`
2. اضغط `F5` أو `Ctrl+F5`
3. سيتم بناء وتشغيل البرنامج تلقائياً

---

## 🎯 أول تشغيل | First Run

### 🔐 **تسجيل الدخول الافتراضي | Default Login**
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `Admin123!`

### 🆓 **الترخيص التجريبي | Trial License**
- سيتم إنشاء ترخيص تجريبي تلقائياً لمدة 30 يوم
- جميع الميزات متاحة في الفترة التجريبية

### 🏊 **الخطوات الأولى | First Steps**
1. **إعداد المزرعة**: أدخل اسم المزرعة ومعلومات المالك
2. **إضافة حوض**: أنشئ أول حوض للأسماك
3. **بدء دورة**: ابدأ أول دورة إنتاجية
4. **تسجيل تغذية**: سجل أول عملية تغذية

---

## 🔍 استكشاف الأخطاء | Troubleshooting

### ❌ **مشاكل شائعة | Common Issues**

#### 🚫 **خطأ: .NET SDK غير موجود**
```
الحل:
1. تحميل وتثبيت .NET 8.0 SDK
2. إعادة تشغيل Command Prompt
3. التحقق من المتغيرات البيئية
```

#### 🚫 **خطأ: فشل في البناء**
```
الحل:
1. dotnet clean
2. dotnet restore
3. dotnet build --configuration Release
```

#### 🚫 **خطأ: قاعدة البيانات غير موجودة**
```
الحل:
1. سيتم إنشاء قاعدة البيانات تلقائياً في أول تشغيل
2. تأكد من وجود صلاحيات الكتابة في مجلد البرنامج
```

#### 🚫 **خطأ: الترخيص غير صالح**
```
الحل:
1. سيتم إنشاء ترخيص تجريبي تلقائياً
2. للترخيص التجاري، اتصل بـ: <EMAIL>
```

---

## 🎮 ميزات البرنامج الجديدة | New Program Features

### 🔄 **نظام التحديث التلقائي**
- **الموقع**: قائمة المساعدة → فحص التحديثات
- **الوظيفة**: فحص وتحميل التحديثات تلقائياً

### 📊 **مراقب الأداء**
- **الموقع**: قائمة المساعدة → مراقب الأداء
- **الوظيفة**: مراقبة أداء النظام في الوقت الفعلي

### 🔔 **الإشعارات الذكية**
- **تنبيهات التغذية**: تذكير بمواعيد التغذية
- **تنبيهات المخزون**: تحذير من نقص العلف
- **تنبيهات النفوق**: مراقبة معدلات النفوق

### 📄 **تصدير متطور**
- **صيغ جديدة**: PDF, Excel, CSV, HTML, JSON, XML
- **جودة عالية**: تصدير احترافي مع دعم العربية

---

## 📱 لقطات شاشة متوقعة | Expected Screenshots

### 🖥️ **شاشة تسجيل الدخول**
```
┌─────────────────────────────────────┐
│     نظام إدارة مزرعة الأسماك      │
│                                     │
│  اسم المستخدم: [admin        ]    │
│  كلمة المرور:  [••••••••••   ]    │
│                                     │
│         [دخول]    [إلغاء]          │
└─────────────────────────────────────┘
```

### 🖥️ **الشاشة الرئيسية**
```
┌─────────────────────────────────────┐
│ ملف  تحرير  عرض  أدوات  مساعدة   │
├─────────────────────────────────────┤
│                                     │
│  🏊 إدارة الأحواض                  │
│  🐟 الدورات الإنتاجية             │
│  🍽️ إدارة التغذية                 │
│  💰 النظام المحاسبي               │
│  📊 التقارير                       │
│  👥 إدارة الموظفين                │
│                                     │
└─────────────────────────────────────┘
```

---

## 📞 الدعم الفني | Technical Support

### 🆘 **في حالة وجود مشاكل | If You Have Issues**
- **البريد الإلكتروني**: <EMAIL>
- **الموضوع**: Fish Farm Management v1.0.1 - Running Issue
- **وقت الاستجابة**: خلال 24 ساعة

### 📋 **معلومات مطلوبة عند طلب المساعدة**
1. **نظام التشغيل**: Windows version
2. **إصدار .NET**: `dotnet --version`
3. **رسالة الخطأ**: نص الخطأ كاملاً
4. **خطوات الإعادة**: ما فعلته قبل ظهور الخطأ

---

## 🎯 **ملاحظة مهمة | Important Note**

**البرنامج جاهز تماماً للتشغيل ولكن يحتاج فقط إلى:**
1. ✅ تثبيت .NET 8.0 SDK
2. ✅ بناء المشروع باستخدام `dotnet build`
3. ✅ تشغيل البرنامج

**بمجرد توفر .NET SDK، سيعمل البرنامج بكامل ميزاته الجديدة والمحسنة!**

---

## 🎉 **استمتع بتجربة نظام إدارة مزرعة الأسماك المتطور!**

**البرنامج يحتوي على جميع الميزات المتقدمة ويوفر تجربة إدارة احترافية لمزارع الأسماك.**

---

*للمزيد من المساعدة، راجع الملفات التالية:*
- `QUICK_START.md` - دليل البدء السريع
- `INSTALLATION.md` - دليل التثبيت المفصل
- `COMMERCIAL_USER_GUIDE.md` - دليل المستخدم الشامل

**© 2024 طارق حسين صالح Ahmed. جميع الحقوق محفوظة.**
