-- نظام إدارة مزرعة الأسماك - إنشاء قاعدة البيانات
-- Fish Farm Management System - Database Creation Script

-- إنشاء جدول معلومات المزرعة
CREATE TABLE IF NOT EXISTS FarmInfos (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    FarmName TEXT NOT NULL,
    Location TEXT,
    ContactInfo TEXT,
    Email TEXT,
    Phone TEXT,
    Logo BLOB,
    Notes TEXT,
    SupervisorName TEXT DEFAULT 'طارق حسين صالح',
    SupervisorEmail TEXT DEFAULT '<EMAIL>',
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME
);

-- إنشاء جدول أنواع الحسابات
CREATE TABLE IF NOT EXISTS AccountTypes (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    TypeName TEXT NOT NULL,
    TypeCode TEXT NOT NULL UNIQUE,
    Description TEXT,
    Nature TEXT NOT NULL CHECK (Nature IN ('مدين', 'دائن')),
    DisplayOrder INTEGER DEFAULT 0,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME
);

-- إنشاء جدول الحسابات
CREATE TABLE IF NOT EXISTS Accounts (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    AccountTypeId INTEGER NOT NULL,
    AccountCode TEXT NOT NULL UNIQUE,
    AccountName TEXT NOT NULL,
    AccountNameEn TEXT,
    Balance DECIMAL(15,2) DEFAULT 0,
    Status TEXT NOT NULL DEFAULT 'نشط' CHECK (Status IN ('نشط', 'متوقف', 'مغلق')),
    ParentAccountId INTEGER,
    Level INTEGER DEFAULT 1,
    IsPostable BOOLEAN DEFAULT 1,
    Description TEXT,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME,
    FOREIGN KEY (AccountTypeId) REFERENCES AccountTypes(Id),
    FOREIGN KEY (ParentAccountId) REFERENCES Accounts(Id)
);

-- إنشاء جدول الدورات الإنتاجية
CREATE TABLE IF NOT EXISTS ProductionCycles (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    CycleName TEXT NOT NULL,
    StartDate DATETIME NOT NULL,
    EndDate DATETIME,
    ExpectedEndDate DATETIME NOT NULL,
    Status TEXT NOT NULL DEFAULT 'نشط' CHECK (Status IN ('نشط', 'مكتمل', 'متوقف', 'ملغي')),
    BudgetAmount DECIMAL(15,2) DEFAULT 0,
    Notes TEXT,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME
);

-- إنشاء جدول الأحواض
CREATE TABLE IF NOT EXISTS Ponds (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    PondNumber TEXT NOT NULL UNIQUE,
    CycleId INTEGER NOT NULL,
    FishCount INTEGER DEFAULT 0,
    AverageWeight DECIMAL(10,3) DEFAULT 0,
    StockingDate DATETIME NOT NULL,
    ExpectedHarvestDate DATETIME,
    Status TEXT NOT NULL DEFAULT 'نشط' CHECK (Status IN ('نشط', 'محصود', 'فارغ', 'صيانة')),
    Notes TEXT,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME,
    FOREIGN KEY (CycleId) REFERENCES ProductionCycles(Id)
);

-- إنشاء جدول أنواع العلف
CREATE TABLE IF NOT EXISTS FeedTypes (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    FeedName TEXT NOT NULL,
    Brand TEXT,
    PricePerKg DECIMAL(10,2) DEFAULT 0,
    Specifications TEXT,
    Status TEXT NOT NULL DEFAULT 'نشط' CHECK (Status IN ('نشط', 'متوقف')),
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME
);

-- إنشاء جدول استهلاك العلف
CREATE TABLE IF NOT EXISTS FeedConsumptions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    PondId INTEGER NOT NULL,
    FeedTypeId INTEGER NOT NULL,
    Quantity DECIMAL(10,3) NOT NULL,
    FeedingDate DATETIME NOT NULL,
    Cost DECIMAL(10,2) NOT NULL,
    Notes TEXT,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (PondId) REFERENCES Ponds(Id) ON DELETE CASCADE,
    FOREIGN KEY (FeedTypeId) REFERENCES FeedTypes(Id)
);

-- إنشاء جدول نفوق الأسماك
CREATE TABLE IF NOT EXISTS FishMortalities (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    PondId INTEGER NOT NULL,
    DeadFishCount INTEGER NOT NULL,
    MortalityDate DATETIME NOT NULL,
    Cause TEXT NOT NULL,
    Notes TEXT,
    EstimatedWeight DECIMAL(10,3) DEFAULT 0,
    EstimatedLoss DECIMAL(10,2) DEFAULT 0,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (PondId) REFERENCES Ponds(Id) ON DELETE CASCADE
);

-- إنشاء جدول الأدوية
CREATE TABLE IF NOT EXISTS Medications (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    MedicationName TEXT NOT NULL,
    Type TEXT NOT NULL,
    PricePerUnit DECIMAL(10,2) DEFAULT 0,
    Dosage TEXT NOT NULL,
    Unit TEXT DEFAULT 'مل',
    Description TEXT,
    Status TEXT NOT NULL DEFAULT 'نشط' CHECK (Status IN ('نشط', 'متوقف', 'منتهي الصلاحية')),
    ExpiryDate DATETIME,
    Manufacturer TEXT,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME
);

-- إنشاء جدول أدوية الأحواض
CREATE TABLE IF NOT EXISTS PondMedications (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    PondId INTEGER NOT NULL,
    MedicationId INTEGER NOT NULL,
    Quantity DECIMAL(10,3) NOT NULL,
    ApplicationDate DATETIME NOT NULL,
    Cost DECIMAL(10,2) NOT NULL,
    Notes TEXT,
    ReasonForUse TEXT NOT NULL,
    VeterinarianName TEXT,
    WithdrawalPeriodDays INTEGER DEFAULT 0,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (PondId) REFERENCES Ponds(Id) ON DELETE CASCADE,
    FOREIGN KEY (MedicationId) REFERENCES Medications(Id)
);

-- إنشاء جدول الموظفين
CREATE TABLE IF NOT EXISTS Employees (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    FullName TEXT NOT NULL,
    Nationality TEXT NOT NULL,
    ResidenceNumber TEXT,
    Position TEXT NOT NULL,
    JoinDate DATETIME NOT NULL,
    LeaveDate DATETIME,
    BaseSalary DECIMAL(10,2) NOT NULL,
    Status TEXT NOT NULL DEFAULT 'نشط' CHECK (Status IN ('نشط', 'متوقف', 'مستقيل', 'مفصول')),
    ContactInfo TEXT,
    Phone TEXT,
    Email TEXT,
    Address TEXT,
    NationalId TEXT,
    BirthDate DATETIME,
    MaritalStatus TEXT,
    NumberOfChildren INTEGER DEFAULT 0,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME
);

-- إنشاء جدول كشوف الرواتب
CREATE TABLE IF NOT EXISTS Payrolls (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    EmployeeId INTEGER NOT NULL,
    CycleId INTEGER NOT NULL,
    Month INTEGER NOT NULL CHECK (Month BETWEEN 1 AND 12),
    Year INTEGER NOT NULL CHECK (Year BETWEEN 2020 AND 2050),
    BaseSalary DECIMAL(10,2) NOT NULL,
    Allowances DECIMAL(10,2) DEFAULT 0,
    Deductions DECIMAL(10,2) DEFAULT 0,
    NetSalary DECIMAL(10,2) NOT NULL,
    PaymentDate DATETIME,
    Notes TEXT,
    WorkingDays INTEGER DEFAULT 30,
    AbsenceDays INTEGER DEFAULT 0,
    OvertimeHours DECIMAL(5,2) DEFAULT 0,
    OvertimeRate DECIMAL(10,2) DEFAULT 0,
    PaymentStatus TEXT NOT NULL DEFAULT 'معلق' CHECK (PaymentStatus IN ('معلق', 'مدفوع', 'ملغي')),
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (EmployeeId) REFERENCES Employees(Id),
    FOREIGN KEY (CycleId) REFERENCES ProductionCycles(Id),
    UNIQUE(EmployeeId, Month, Year)
);

-- إنشاء جدول المعاملات المحاسبية
CREATE TABLE IF NOT EXISTS Transactions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    CycleId INTEGER NOT NULL,
    TransactionType TEXT NOT NULL,
    ReferenceNumber TEXT NOT NULL UNIQUE,
    TransactionDate DATETIME NOT NULL,
    TotalAmount DECIMAL(15,2) NOT NULL,
    Description TEXT NOT NULL,
    Status TEXT NOT NULL DEFAULT 'معلق' CHECK (Status IN ('معلق', 'مرحل', 'ملغي')),
    CreatedBy TEXT,
    ApprovedBy TEXT,
    ApprovalDate DATETIME,
    Notes TEXT,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME,
    FOREIGN KEY (CycleId) REFERENCES ProductionCycles(Id)
);

-- إنشاء جدول مراكز التكلفة
CREATE TABLE IF NOT EXISTS CostCenters (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    CycleId INTEGER NOT NULL,
    CenterName TEXT NOT NULL,
    CenterCode TEXT NOT NULL,
    Description TEXT,
    AllocatedBudget DECIMAL(15,2) DEFAULT 0,
    ActualSpending DECIMAL(15,2) DEFAULT 0,
    Status TEXT NOT NULL DEFAULT 'نشط' CHECK (Status IN ('نشط', 'متوقف', 'مغلق')),
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME,
    FOREIGN KEY (CycleId) REFERENCES ProductionCycles(Id)
);

-- إنشاء جدول تفاصيل المعاملات
CREATE TABLE IF NOT EXISTS TransactionDetails (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    TransactionId INTEGER NOT NULL,
    AccountId INTEGER NOT NULL,
    DebitAmount DECIMAL(15,2) DEFAULT 0,
    CreditAmount DECIMAL(15,2) DEFAULT 0,
    Description TEXT,
    LineNumber INTEGER DEFAULT 1,
    CostCenterId INTEGER,
    Notes TEXT,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (TransactionId) REFERENCES Transactions(Id) ON DELETE CASCADE,
    FOREIGN KEY (AccountId) REFERENCES Accounts(Id),
    FOREIGN KEY (CostCenterId) REFERENCES CostCenters(Id)
);

-- إنشاء جدول المخزون
CREATE TABLE IF NOT EXISTS Inventories (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ItemName TEXT NOT NULL,
    ItemType TEXT NOT NULL,
    Quantity DECIMAL(10,3) DEFAULT 0,
    UnitPrice DECIMAL(10,2) DEFAULT 0,
    TotalValue DECIMAL(15,2) DEFAULT 0,
    Unit TEXT NOT NULL,
    MinimumStock DECIMAL(10,3) DEFAULT 0,
    MaximumStock DECIMAL(10,3) DEFAULT 0,
    ReorderPoint DECIMAL(10,3) DEFAULT 0,
    StorageLocation TEXT,
    ExpiryDate DATETIME,
    BatchNumber TEXT,
    Supplier TEXT,
    Status TEXT NOT NULL DEFAULT 'نشط' CHECK (Status IN ('نشط', 'متوقف', 'منتهي الصلاحية')),
    Notes TEXT,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_ponds_cycle ON Ponds(CycleId);
CREATE INDEX IF NOT EXISTS idx_ponds_number ON Ponds(PondNumber);
CREATE INDEX IF NOT EXISTS idx_accounts_code ON Accounts(AccountCode);
CREATE INDEX IF NOT EXISTS idx_transactions_ref ON Transactions(ReferenceNumber);
CREATE INDEX IF NOT EXISTS idx_employees_national ON Employees(NationalId);
CREATE INDEX IF NOT EXISTS idx_feed_consumption_date ON FeedConsumptions(FeedingDate);
CREATE INDEX IF NOT EXISTS idx_fish_mortality_date ON FishMortalities(MortalityDate);
CREATE INDEX IF NOT EXISTS idx_payroll_employee_period ON Payrolls(EmployeeId, Month, Year);

-- إدراج البيانات الافتراضية

-- إدراج أنواع الحسابات الافتراضية
INSERT OR IGNORE INTO AccountTypes (Id, TypeName, TypeCode, Description, Nature, DisplayOrder, CreatedDate) VALUES
(1, 'الأصول', '1', 'الأصول الثابتة والمتداولة', 'مدين', 1, datetime('now')),
(2, 'الخصوم', '2', 'الالتزامات والديون', 'دائن', 2, datetime('now')),
(3, 'حقوق الملكية', '3', 'رأس المال والأرباح المحتجزة', 'دائن', 3, datetime('now')),
(4, 'الإيرادات', '4', 'إيرادات المبيعات والخدمات', 'دائن', 4, datetime('now')),
(5, 'المصروفات', '5', 'مصروفات التشغيل والإدارة', 'مدين', 5, datetime('now'));

-- إدراج الحسابات الافتراضية
INSERT OR IGNORE INTO Accounts (Id, AccountTypeId, AccountCode, AccountName, AccountNameEn, CreatedDate) VALUES
(1, 1, '1001', 'النقدية', 'Cash', datetime('now')),
(2, 1, '1002', 'البنك', 'Bank', datetime('now')),
(3, 1, '1003', 'المخزون', 'Inventory', datetime('now')),
(4, 1, '1004', 'الأصول الثابتة', 'Fixed Assets', datetime('now')),
(5, 2, '2001', 'الموردون', 'Suppliers', datetime('now')),
(6, 2, '2002', 'رواتب مستحقة', 'Accrued Salaries', datetime('now')),
(7, 3, '3001', 'رأس المال', 'Capital', datetime('now')),
(8, 3, '3002', 'الأرباح المحتجزة', 'Retained Earnings', datetime('now')),
(9, 4, '4001', 'مبيعات الأسماك', 'Fish Sales', datetime('now')),
(10, 5, '5001', 'مصروفات العلف', 'Feed Expenses', datetime('now')),
(11, 5, '5002', 'مصروفات الرواتب', 'Salary Expenses', datetime('now')),
(12, 5, '5003', 'مصروفات الأدوية', 'Medicine Expenses', datetime('now')),
(13, 5, '5004', 'مصروفات عامة', 'General Expenses', datetime('now'));

-- إدراج معلومات المزرعة الافتراضية
INSERT OR IGNORE INTO FarmInfos (Id, FarmName, Location, Email, Phone, SupervisorName, SupervisorEmail, CreatedDate) VALUES
(1, 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', '<EMAIL>', '+966501234567', 'طارق حسين صالح', '<EMAIL>', datetime('now'));

-- إدراج أنواع علف افتراضية
INSERT OR IGNORE INTO FeedTypes (Id, FeedName, Brand, PricePerKg, Specifications, CreatedDate) VALUES
(1, 'علف نمو صغير', 'أكوا فيد', 3.50, 'بروتين 32% - دهون 6%', datetime('now')),
(2, 'علف نمو متوسط', 'أكوا فيد', 3.25, 'بروتين 28% - دهون 5%', datetime('now')),
(3, 'علف تسمين', 'أكوا فيد', 3.00, 'بروتين 25% - دهون 4%', datetime('now'));

-- إدراج أدوية افتراضية
INSERT OR IGNORE INTO Medications (Id, MedicationName, Type, PricePerUnit, Dosage, Unit, Description, CreatedDate) VALUES
(1, 'أوكسي تتراسايكلين', 'مضاد حيوي', 25.00, '50-75 مج/كجم', 'جرام', 'مضاد حيوي واسع المجال', datetime('now')),
(2, 'فيتامين سي', 'فيتامين', 15.00, '100 مج/كجم علف', 'جرام', 'مقوي عام ومضاد أكسدة', datetime('now')),
(3, 'ملح الطعام', 'مطهر', 2.00, '3-5 جرام/لتر', 'كيلو', 'مطهر طبيعي', datetime('now'));

PRAGMA foreign_keys = ON;
