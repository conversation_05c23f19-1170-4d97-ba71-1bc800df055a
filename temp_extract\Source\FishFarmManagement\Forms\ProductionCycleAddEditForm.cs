﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¶Ø§ÙØ©/ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„Ø¯ÙˆØ±Ø§Øª Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©
    /// Production cycle add/edit form
    /// </summary>
    public partial class ProductionCycleAddEditForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;
        private readonly ProductionCycle? _existingCycle;
        private readonly bool _isEditMode;

        // UI Controls
        private TextBox cycleNameTextBox;
        private DateTimePicker startDatePicker;
        private DateTimePicker expectedEndDatePicker;
        private ComboBox statusComboBox;
        private NumericUpDown budgetAmountNumericUpDown;
        private TextBox notesTextBox;
        private Button saveButton;
        private Button cancelButton;

        public ProductionCycleAddEditForm(IUnitOfWork unitOfWork, ILogger logger, ProductionCycle? existingCycle = null)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _existingCycle = existingCycle;
            _isEditMode = existingCycle != null;

            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = _isEditMode ? "ØªØ¹Ø¯ÙŠÙ„ Ø¯ÙˆØ±Ø© Ø¥Ù†ØªØ§Ø¬ÙŠØ©" : "Ø¥Ø¶Ø§ÙØ© Ø¯ÙˆØ±Ø© Ø¥Ù†ØªØ§Ø¬ÙŠØ© Ø¬Ø¯ÙŠØ¯Ø©";
            this.Size = new Size(500, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
            LayoutControls();
        }

        private void CreateControls()
        {
            // Cycle Name
            var cycleNameLabel = new Label
            {
                Text = "Ø§Ø³Ù… Ø§Ù„Ø¯ÙˆØ±Ø©:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            cycleNameTextBox = new TextBox
            {
                Size = new Size(250, 23),
                MaxLength = 100
            };

            // Start Date
            var startDateLabel = new Label
            {
                Text = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø¨Ø¯Ø§ÙŠØ©:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            startDatePicker = new DateTimePicker
            {
                Size = new Size(250, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // Expected End Date
            var expectedEndDateLabel = new Label
            {
                Text = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ù†Ù‡Ø§ÙŠØ© Ø§Ù„Ù…ØªÙˆÙ‚Ø¹:",
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            expectedEndDatePicker = new DateTimePicker
            {
                Size = new Size(250, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now.AddMonths(6)
            };

            // Status
            var statusLabel = new Label
            {
                Text = "Ø§Ù„Ø­Ø§Ù„Ø©:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            statusComboBox = new ComboBox
            {
                Size = new Size(250, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            statusComboBox.Items.AddRange(new[] { "Ù†Ø´Ø·", "Ù…ÙƒØªÙ…Ù„", "Ù…ØªÙˆÙ‚Ù", "Ù…Ù„ØºÙŠ" });
            statusComboBox.SelectedIndex = 0;

            // Budget Amount
            var budgetLabel = new Label
            {
                Text = "Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ©:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            budgetAmountNumericUpDown = new NumericUpDown
            {
                Size = new Size(250, 23),
                Minimum = 0,
                Maximum = decimal.MaxValue,
                DecimalPlaces = 2,
                ThousandsSeparator = true
            };

            // Notes
            var notesLabel = new Label
            {
                Text = "Ø§Ù„Ù…Ù„Ø§Ø­Ø¸Ø§Øª:",
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            notesTextBox = new TextBox
            {
                Size = new Size(350, 80),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                MaxLength = 1000
            };

            // Buttons
            saveButton = new Button
            {
                Text = _isEditMode ? "Ø­ÙØ¸ Ø§Ù„ØªØ¹Ø¯ÙŠÙ„Ø§Øª" : "Ø¥Ø¶Ø§ÙØ©",
                Size = new Size(100, 30),
                DialogResult = DialogResult.OK
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "Ø¥Ù„ØºØ§Ø¡",
                Size = new Size(100, 30),
                DialogResult = DialogResult.Cancel
            };

            this.Controls.AddRange(new Control[]
            {
                cycleNameLabel, cycleNameTextBox,
                startDateLabel, startDatePicker,
                expectedEndDateLabel, expectedEndDatePicker,
                statusLabel, statusComboBox,
                budgetLabel, budgetAmountNumericUpDown,
                notesLabel, notesTextBox,
                saveButton, cancelButton
            });
        }

        private void LayoutControls()
        {
            int x = 20;
            int y = 20;
            int spacing = 35;

            // Cycle Name
            this.Controls[0].Location = new Point(x + 250, y);
            this.Controls[1].Location = new Point(x + 30, y);
            y += spacing;

            // Start Date
            this.Controls[2].Location = new Point(x + 250, y);
            this.Controls[3].Location = new Point(x + 30, y);
            y += spacing;

            // Expected End Date
            this.Controls[4].Location = new Point(x + 230, y);
            this.Controls[5].Location = new Point(x + 30, y);
            y += spacing;

            // Status
            this.Controls[6].Location = new Point(x + 250, y);
            this.Controls[7].Location = new Point(x + 30, y);
            y += spacing;

            // Budget Amount
            this.Controls[8].Location = new Point(x + 250, y);
            this.Controls[9].Location = new Point(x + 30, y);
            y += spacing;

            // Notes
            this.Controls[10].Location = new Point(x + 250, y);
            this.Controls[11].Location = new Point(x + 30, y);
            y += 100;

            // Buttons
            this.Controls[12].Location = new Point(x + 130, y);
            this.Controls[13].Location = new Point(x + 250, y);
        }

        private void LoadData()
        {
            if (_existingCycle != null)
            {
                cycleNameTextBox.Text = _existingCycle.CycleName;
                startDatePicker.Value = _existingCycle.StartDate;
                expectedEndDatePicker.Value = _existingCycle.ExpectedEndDate;
                statusComboBox.SelectedItem = _existingCycle.Status;
                budgetAmountNumericUpDown.Value = _existingCycle.BudgetAmount;
                notesTextBox.Text = _existingCycle.Notes;
            }
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                this.Cursor = Cursors.WaitCursor;
                saveButton.Enabled = false;

                var cycle = _existingCycle ?? new ProductionCycle();
                
                cycle.CycleName = cycleNameTextBox.Text.Trim();
                cycle.StartDate = startDatePicker.Value;
                cycle.ExpectedEndDate = expectedEndDatePicker.Value;
                cycle.Status = statusComboBox.SelectedItem?.ToString() ?? "Ù†Ø´Ø·";
                cycle.BudgetAmount = budgetAmountNumericUpDown.Value;
                cycle.Notes = notesTextBox.Text.Trim();

                if (_isEditMode)
                {
                    cycle.UpdateModificationDate();
                    await _unitOfWork.ProductionCycles.UpdateAsync(cycle);
                    MessageBox.Show("ØªÙ… ØªØ­Ø¯ÙŠØ« Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ© Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _unitOfWork.ProductionCycles.AddAsync(cycle);
                    MessageBox.Show("ØªÙ… Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ© Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                await _unitOfWork.SaveChangesAsync();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­ÙØ¸ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø¯ÙˆØ±Ø© Ø§Ù„Ø¥Ù†ØªØ§Ø¬ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø­ÙØ¸ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                saveButton.Enabled = true;
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(cycleNameTextBox.Text))
            {
                MessageBox.Show("Ø§Ø³Ù… Ø§Ù„Ø¯ÙˆØ±Ø© Ù…Ø·Ù„ÙˆØ¨", "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cycleNameTextBox.Focus();
                return false;
            }

            if (expectedEndDatePicker.Value <= startDatePicker.Value)
            {
                MessageBox.Show("ØªØ§Ø±ÙŠØ® Ø§Ù„Ù†Ù‡Ø§ÙŠØ© Ø§Ù„Ù…ØªÙˆÙ‚Ø¹ ÙŠØ¬Ø¨ Ø£Ù† ÙŠÙƒÙˆÙ† Ø¨Ø¹Ø¯ ØªØ§Ø±ÙŠØ® Ø§Ù„Ø¨Ø¯Ø§ÙŠØ©", "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                expectedEndDatePicker.Focus();
                return false;
            }

            if (statusComboBox.SelectedIndex < 0)
            {
                MessageBox.Show("ÙŠØ¬Ø¨ Ø§Ø®ØªÙŠØ§Ø± Ø­Ø§Ù„Ø© Ø§Ù„Ø¯ÙˆØ±Ø©", "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                statusComboBox.Focus();
                return false;
            }

            return true;
        }
    }
}



