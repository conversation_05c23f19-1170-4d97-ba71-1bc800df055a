# PowerShell script to fix the most critical compilation errors
# This addresses the CS1585 errors caused by duplicate method modifiers

Write-Host "Fixing compilation errors..." -ForegroundColor Green

$formsPath = "FishFarmManagement\Forms"
$files = Get-ChildItem -Path $formsPath -Filter "*.cs"

foreach ($file in $files) {
    Write-Host "Processing $($file.Name)..." -ForegroundColor Yellow
    
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $modified = $false
    
    # Fix duplicate 'private' modifiers in method signatures
    # Pattern: private async void private async void -> private async void
    if ($content -match 'private\s+async\s+void\s+private\s+async\s+void') {
        $content = $content -replace 'private\s+async\s+void\s+private\s+async\s+void', 'private async void'
        $modified = $true
        Write-Host "  - Fixed duplicate 'private async void' modifiers" -ForegroundColor Green
    }
    
    # Pattern: private void private void -> private void
    if ($content -match 'private\s+void\s+private\s+void') {
        $content = $content -replace 'private\s+void\s+private\s+void', 'private void'
        $modified = $true
        Write-Host "  - Fixed duplicate 'private void' modifiers" -ForegroundColor Green
    }
    
    # Pattern: private async Task private async Task -> private async Task
    if ($content -match 'private\s+async\s+Task\s+private\s+async\s+Task') {
        $content = $content -replace 'private\s+async\s+Task\s+private\s+async\s+Task', 'private async Task'
        $modified = $true
        Write-Host "  - Fixed duplicate 'private async Task' modifiers" -ForegroundColor Green
    }
    
    # More specific patterns for the errors we saw
    $patterns = @(
        'private\s+async\s+void\s+private\s+async\s+void\s+(\w+)',
        'private\s+void\s+private\s+void\s+(\w+)',
        'private\s+async\s+Task\s+private\s+async\s+Task\s+(\w+)'
    )
    
    $replacements = @(
        'private async void $1',
        'private void $1',
        'private async Task $1'
    )
    
    for ($i = 0; $i -lt $patterns.Length; $i++) {
        if ($content -match $patterns[$i]) {
            $content = $content -replace $patterns[$i], $replacements[$i]
            $modified = $true
            Write-Host "  - Fixed pattern $($i + 1)" -ForegroundColor Green
        }
    }
    
    # Fix any remaining duplicate modifiers with a more general approach
    $lines = $content -split "`n"
    $newLines = @()
    
    foreach ($line in $lines) {
        # Check for lines with duplicate private modifiers
        if ($line -match '^\s*private\s+.*private\s+') {
            # Remove duplicate 'private' keywords
            $fixedLine = $line -replace '\bprivate\s+', '', 1  # Remove first occurrence
            $fixedLine = "        private " + $fixedLine.Trim()  # Add single private at beginning
            $newLines += $fixedLine
            $modified = $true
        } else {
            $newLines += $line
        }
    }
    
    if ($modified) {
        $content = $newLines -join "`n"
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        Write-Host "  ✓ Updated $($file.Name)" -ForegroundColor Green
    } else {
        Write-Host "  - No changes needed for $($file.Name)" -ForegroundColor Gray
    }
}

Write-Host "`nCompilation errors fix completed!" -ForegroundColor Green
Write-Host "Please build the project to verify the fixes." -ForegroundColor Yellow
