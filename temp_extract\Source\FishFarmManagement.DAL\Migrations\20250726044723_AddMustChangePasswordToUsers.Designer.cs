﻿// <auto-generated />
using System;
using FishFarmManagement.DAL;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace FishFarmManagement.DAL.Migrations
{
    [DbContext(typeof(FishFarmDbContext))]
    [Migration("20250726044723_AddMustChangePasswordToUsers")]
    partial class AddMustChangePasswordToUsers
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.1");

            modelBuilder.Entity("FishFarmManagement.Models.Account", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AccountCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("AccountNameEn")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("AccountTypeId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Balance")
                        .HasColumnType("decimal(15,2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsPostable")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Level")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ParentAccountId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("AccountCode")
                        .IsUnique();

                    b.HasIndex("AccountTypeId");

                    b.HasIndex("ParentAccountId");

                    b.ToTable("Accounts", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            AccountCode = "1001",
                            AccountName = "النقدية",
                            AccountNameEn = "Cash",
                            AccountTypeId = 1,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        },
                        new
                        {
                            Id = 2,
                            AccountCode = "1002",
                            AccountName = "البنك",
                            AccountNameEn = "Bank",
                            AccountTypeId = 1,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        },
                        new
                        {
                            Id = 3,
                            AccountCode = "1003",
                            AccountName = "المخزون",
                            AccountNameEn = "Inventory",
                            AccountTypeId = 1,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        },
                        new
                        {
                            Id = 4,
                            AccountCode = "1004",
                            AccountName = "الأصول الثابتة",
                            AccountNameEn = "Fixed Assets",
                            AccountTypeId = 1,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        },
                        new
                        {
                            Id = 5,
                            AccountCode = "2001",
                            AccountName = "الموردون",
                            AccountNameEn = "Suppliers",
                            AccountTypeId = 2,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        },
                        new
                        {
                            Id = 6,
                            AccountCode = "2002",
                            AccountName = "رواتب مستحقة",
                            AccountNameEn = "Accrued Salaries",
                            AccountTypeId = 2,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        },
                        new
                        {
                            Id = 7,
                            AccountCode = "3001",
                            AccountName = "رأس المال",
                            AccountNameEn = "Capital",
                            AccountTypeId = 3,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        },
                        new
                        {
                            Id = 8,
                            AccountCode = "3002",
                            AccountName = "الأرباح المحتجزة",
                            AccountNameEn = "Retained Earnings",
                            AccountTypeId = 3,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        },
                        new
                        {
                            Id = 9,
                            AccountCode = "4001",
                            AccountName = "مبيعات الأسماك",
                            AccountNameEn = "Fish Sales",
                            AccountTypeId = 4,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        },
                        new
                        {
                            Id = 10,
                            AccountCode = "5001",
                            AccountName = "مصروفات العلف",
                            AccountNameEn = "Feed Expenses",
                            AccountTypeId = 5,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        },
                        new
                        {
                            Id = 11,
                            AccountCode = "5002",
                            AccountName = "مصروفات الرواتب",
                            AccountNameEn = "Salary Expenses",
                            AccountTypeId = 5,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        },
                        new
                        {
                            Id = 12,
                            AccountCode = "5003",
                            AccountName = "مصروفات الأدوية",
                            AccountNameEn = "Medicine Expenses",
                            AccountTypeId = 5,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        },
                        new
                        {
                            Id = 13,
                            AccountCode = "5004",
                            AccountName = "مصروفات عامة",
                            AccountNameEn = "General Expenses",
                            AccountTypeId = 5,
                            Balance = 0m,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "",
                            IsPostable = true,
                            Level = 1,
                            Status = "نشط"
                        });
                });

            modelBuilder.Entity("FishFarmManagement.Models.AccountType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Nature")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<string>("TypeCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<string>("TypeName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("AccountTypes", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "الأصول الثابتة والمتداولة",
                            DisplayOrder = 1,
                            Nature = "مدين",
                            TypeCode = "1",
                            TypeName = "الأصول"
                        },
                        new
                        {
                            Id = 2,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "الالتزامات والديون",
                            DisplayOrder = 2,
                            Nature = "دائن",
                            TypeCode = "2",
                            TypeName = "الخصوم"
                        },
                        new
                        {
                            Id = 3,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "رأس المال والأرباح المحتجزة",
                            DisplayOrder = 3,
                            Nature = "دائن",
                            TypeCode = "3",
                            TypeName = "حقوق الملكية"
                        },
                        new
                        {
                            Id = 4,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "إيرادات المبيعات والخدمات",
                            DisplayOrder = 4,
                            Nature = "دائن",
                            TypeCode = "4",
                            TypeName = "الإيرادات"
                        },
                        new
                        {
                            Id = 5,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "مصروفات التشغيل والإدارة",
                            DisplayOrder = 5,
                            Nature = "مدين",
                            TypeCode = "5",
                            TypeName = "المصروفات"
                        });
                });

            modelBuilder.Entity("FishFarmManagement.Models.CostCenter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("ActualSpending")
                        .HasColumnType("decimal(15,2)");

                    b.Property<decimal>("AllocatedBudget")
                        .HasColumnType("decimal(15,2)");

                    b.Property<string>("CenterCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("CenterName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("CycleId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CycleId");

                    b.ToTable("CostCenters", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.Employee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("BaseSalary")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime?>("BirthDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactInfo")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("JoinDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("LeaveDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("MaritalStatus")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("NationalId")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Nationality")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("NumberOfChildren")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Position")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ResidenceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NationalId");

                    b.ToTable("Employees", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.FarmInfo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ContactInfo")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FarmName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("Logo")
                        .HasColumnType("BLOB");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("SupervisorEmail")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("SupervisorName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("FarmInfos", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            ContactInfo = "",
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Email = "<EMAIL>",
                            FarmName = "مزرعة الأسماك النموذجية",
                            Location = "المملكة العربية السعودية",
                            Notes = "",
                            Phone = "+966501234567",
                            SupervisorEmail = "<EMAIL>",
                            SupervisorName = "طارق حسين صالح"
                        });
                });

            modelBuilder.Entity("FishFarmManagement.Models.FeedConsumption", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("FeedTypeId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("FeedingDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("PondId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(10,3)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("FeedTypeId");

                    b.HasIndex("FeedingDate");

                    b.HasIndex("PondId");

                    b.ToTable("FeedConsumptions", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.FeedType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Brand")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("FeedName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PricePerKg")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("Specifications")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("FeedTypes", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.FishMortality", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Cause")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("DeadFishCount")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("EstimatedLoss")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("EstimatedWeight")
                        .HasColumnType("decimal(10,3)");

                    b.Property<DateTime>("MortalityDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("PondId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("MortalityDate");

                    b.HasIndex("PondId");

                    b.ToTable("FishMortalities", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.Inventory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("BatchNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("ItemName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ItemType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("MaximumStock")
                        .HasColumnType("decimal(10,3)");

                    b.Property<decimal>("MinimumStock")
                        .HasColumnType("decimal(10,3)");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(10,3)");

                    b.Property<decimal>("ReorderPoint")
                        .HasColumnType("decimal(10,3)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("StorageLocation")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Supplier")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("TotalValue")
                        .HasColumnType("decimal(15,2)");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Inventories", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.InventoryMovement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("BatchNumber")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("ItemId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("MovementDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("MovementType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(10,3)");

                    b.Property<decimal>("QuantityAfter")
                        .HasColumnType("decimal(10,3)");

                    b.Property<decimal>("QuantityBefore")
                        .HasColumnType("decimal(10,3)");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Reference")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("TotalValue")
                        .HasColumnType("decimal(15,2)");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("MovementDate");

                    b.HasIndex("ItemId", "MovementDate");

                    b.ToTable("InventoryMovements", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.Medication", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Dosage")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Manufacturer")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("MedicationName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PricePerUnit")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Medications", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.Notification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ActionUrl")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("AdditionalData")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsAutoGenerated")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("Priority")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("RelatedEntityId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("RelatedEntityType")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ScheduledAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Notifications", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.Payroll", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AbsenceDays")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Allowances")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("BaseSalary")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("CycleId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Deductions")
                        .HasColumnType("decimal(10,2)");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Month")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("NetSalary")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("OvertimeHours")
                        .HasColumnType("decimal(5,2)");

                    b.Property<decimal>("OvertimeRate")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentStatus")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("WorkingDays")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Year")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("CycleId");

                    b.HasIndex("EmployeeId", "Month", "Year")
                        .IsUnique();

                    b.ToTable("Payrolls", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.Pond", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("AverageWeight")
                        .HasColumnType("decimal(10,3)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("CycleId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ExpectedHarvestDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("FishCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("PondNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("StockingDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CycleId");

                    b.HasIndex("PondNumber")
                        .IsUnique();

                    b.ToTable("Ponds", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.PondMedication", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("ApplicationDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("MedicationId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("PondId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(10,3)");

                    b.Property<string>("ReasonForUse")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("VeterinarianName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("WithdrawalPeriodDays")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("MedicationId");

                    b.HasIndex("PondId");

                    b.ToTable("PondMedications", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.ProductionCycle", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("BudgetAmount")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("CycleName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ExpectedEndDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("ProductionCycles", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsSystemRole")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Permissions")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Roles", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "مدير النظام - صلاحيات كاملة",
                            IsActive = true,
                            IsSystemRole = true,
                            Name = "مدير النظام",
                            Permissions = "[\"system.management\",\"user.management\",\"role.management\",\"system.settings\",\"database.management\",\"farm.info.management\",\"pond.management\",\"production.cycle.management\",\"employee.management\",\"payroll.management\",\"accounting.management\",\"transaction.management\",\"financial.reports\",\"inventory.management\",\"inventory.reports\",\"production.reports\",\"employee.reports\",\"general.reports\",\"feeding.records\",\"water.quality.records\",\"weight.records\",\"mortality.records\",\"medication.records\",\"view.dashboard\",\"view.reports\",\"view.data\"]"
                        },
                        new
                        {
                            Id = 2,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "مدير المزرعة - إدارة العمليات اليومية",
                            IsActive = true,
                            IsSystemRole = true,
                            Name = "مدير المزرعة",
                            Permissions = "[\"farm.info.management\",\"pond.management\",\"production.cycle.management\",\"employee.management\",\"inventory.management\",\"inventory.reports\",\"production.reports\",\"employee.reports\",\"feeding.records\",\"water.quality.records\",\"weight.records\",\"mortality.records\",\"medication.records\",\"view.dashboard\",\"view.reports\",\"view.data\"]"
                        },
                        new
                        {
                            Id = 3,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "محاسب - إدارة الحسابات والتقارير المالية",
                            IsActive = true,
                            IsSystemRole = true,
                            Name = "محاسب",
                            Permissions = "[\"accounting.management\",\"transaction.management\",\"financial.reports\",\"payroll.management\",\"inventory.reports\",\"view.dashboard\",\"view.reports\",\"view.data\"]"
                        },
                        new
                        {
                            Id = 4,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "عامل أحواض - تسجيل العمليات اليومية",
                            IsActive = true,
                            IsSystemRole = true,
                            Name = "عامل أحواض",
                            Permissions = "[\"feeding.records\",\"water.quality.records\",\"weight.records\",\"mortality.records\",\"medication.records\",\"view.dashboard\",\"view.data\"]"
                        },
                        new
                        {
                            Id = 5,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Description = "مشاهد - عرض البيانات والتقارير فقط",
                            IsActive = true,
                            IsSystemRole = true,
                            Name = "مشاهد",
                            Permissions = "[\"view.dashboard\",\"view.reports\",\"view.data\"]"
                        });
                });

            modelBuilder.Entity("FishFarmManagement.Models.Transaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ApprovalDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("ApprovedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("CycleId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("ReferenceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(15,2)");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("TransactionType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CycleId");

                    b.HasIndex("ReferenceNumber")
                        .IsUnique();

                    b.ToTable("Transactions", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.TransactionDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AccountId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("CostCenterId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("CreditAmount")
                        .HasColumnType("decimal(15,2)");

                    b.Property<decimal>("DebitAmount")
                        .HasColumnType("decimal(15,2)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("LineNumber")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("TransactionId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("AccountId");

                    b.HasIndex("CostCenterId");

                    b.HasIndex("TransactionId");

                    b.ToTable("TransactionDetails", (string)null);
                });

            modelBuilder.Entity("FishFarmManagement.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("FailedLoginAttempts")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsSystemAdmin")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("LockedUntil")
                        .HasColumnType("TEXT");

                    b.Property<bool>("MustChangePassword")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            Email = "<EMAIL>",
                            FailedLoginAttempts = 0,
                            FullName = "مدير النظام",
                            IsSystemAdmin = true,
                            MustChangePassword = false,
                            PasswordHash = "$2a$11$K8gHZ8W8W8W8W8W8W8W8WOeH8W8W8W8W8W8W8W8W8W8W8W8W8W8W8W8",
                            Status = "نشط",
                            Username = "admin"
                        });
                });

            modelBuilder.Entity("FishFarmManagement.Models.UserRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("GrantedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("GrantedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("RoleId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId", "RoleId")
                        .IsUnique();

                    b.ToTable("UserRoles", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            GrantedBy = "System",
                            GrantedDate = new DateTime(2025, 7, 26, 7, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            RoleId = 1,
                            UserId = 1
                        });
                });

            modelBuilder.Entity("FishFarmManagement.Models.Account", b =>
                {
                    b.HasOne("FishFarmManagement.Models.AccountType", "AccountType")
                        .WithMany("Accounts")
                        .HasForeignKey("AccountTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FishFarmManagement.Models.Account", "ParentAccount")
                        .WithMany("SubAccounts")
                        .HasForeignKey("ParentAccountId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AccountType");

                    b.Navigation("ParentAccount");
                });

            modelBuilder.Entity("FishFarmManagement.Models.CostCenter", b =>
                {
                    b.HasOne("FishFarmManagement.Models.ProductionCycle", "ProductionCycle")
                        .WithMany("CostCenters")
                        .HasForeignKey("CycleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ProductionCycle");
                });

            modelBuilder.Entity("FishFarmManagement.Models.FeedConsumption", b =>
                {
                    b.HasOne("FishFarmManagement.Models.FeedType", "FeedType")
                        .WithMany("FeedConsumptions")
                        .HasForeignKey("FeedTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FishFarmManagement.Models.Pond", "Pond")
                        .WithMany("FeedConsumptions")
                        .HasForeignKey("PondId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FeedType");

                    b.Navigation("Pond");
                });

            modelBuilder.Entity("FishFarmManagement.Models.FishMortality", b =>
                {
                    b.HasOne("FishFarmManagement.Models.Pond", "Pond")
                        .WithMany("FishMortalities")
                        .HasForeignKey("PondId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Pond");
                });

            modelBuilder.Entity("FishFarmManagement.Models.InventoryMovement", b =>
                {
                    b.HasOne("FishFarmManagement.Models.Inventory", "Item")
                        .WithMany()
                        .HasForeignKey("ItemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Item");
                });

            modelBuilder.Entity("FishFarmManagement.Models.Payroll", b =>
                {
                    b.HasOne("FishFarmManagement.Models.ProductionCycle", "ProductionCycle")
                        .WithMany("Payrolls")
                        .HasForeignKey("CycleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FishFarmManagement.Models.Employee", "Employee")
                        .WithMany("Payrolls")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("ProductionCycle");
                });

            modelBuilder.Entity("FishFarmManagement.Models.Pond", b =>
                {
                    b.HasOne("FishFarmManagement.Models.ProductionCycle", "ProductionCycle")
                        .WithMany("Ponds")
                        .HasForeignKey("CycleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ProductionCycle");
                });

            modelBuilder.Entity("FishFarmManagement.Models.PondMedication", b =>
                {
                    b.HasOne("FishFarmManagement.Models.Medication", "Medication")
                        .WithMany("PondMedications")
                        .HasForeignKey("MedicationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FishFarmManagement.Models.Pond", "Pond")
                        .WithMany("PondMedications")
                        .HasForeignKey("PondId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Medication");

                    b.Navigation("Pond");
                });

            modelBuilder.Entity("FishFarmManagement.Models.Transaction", b =>
                {
                    b.HasOne("FishFarmManagement.Models.ProductionCycle", "ProductionCycle")
                        .WithMany("Transactions")
                        .HasForeignKey("CycleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ProductionCycle");
                });

            modelBuilder.Entity("FishFarmManagement.Models.TransactionDetail", b =>
                {
                    b.HasOne("FishFarmManagement.Models.Account", "Account")
                        .WithMany("TransactionDetails")
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FishFarmManagement.Models.CostCenter", "CostCenter")
                        .WithMany("TransactionDetails")
                        .HasForeignKey("CostCenterId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("FishFarmManagement.Models.Transaction", "Transaction")
                        .WithMany("TransactionDetails")
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Account");

                    b.Navigation("CostCenter");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("FishFarmManagement.Models.UserRole", b =>
                {
                    b.HasOne("FishFarmManagement.Models.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FishFarmManagement.Models.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("FishFarmManagement.Models.Account", b =>
                {
                    b.Navigation("SubAccounts");

                    b.Navigation("TransactionDetails");
                });

            modelBuilder.Entity("FishFarmManagement.Models.AccountType", b =>
                {
                    b.Navigation("Accounts");
                });

            modelBuilder.Entity("FishFarmManagement.Models.CostCenter", b =>
                {
                    b.Navigation("TransactionDetails");
                });

            modelBuilder.Entity("FishFarmManagement.Models.Employee", b =>
                {
                    b.Navigation("Payrolls");
                });

            modelBuilder.Entity("FishFarmManagement.Models.FeedType", b =>
                {
                    b.Navigation("FeedConsumptions");
                });

            modelBuilder.Entity("FishFarmManagement.Models.Medication", b =>
                {
                    b.Navigation("PondMedications");
                });

            modelBuilder.Entity("FishFarmManagement.Models.Pond", b =>
                {
                    b.Navigation("FeedConsumptions");

                    b.Navigation("FishMortalities");

                    b.Navigation("PondMedications");
                });

            modelBuilder.Entity("FishFarmManagement.Models.ProductionCycle", b =>
                {
                    b.Navigation("CostCenters");

                    b.Navigation("Payrolls");

                    b.Navigation("Ponds");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("FishFarmManagement.Models.Role", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("FishFarmManagement.Models.Transaction", b =>
                {
                    b.Navigation("TransactionDetails");
                });

            modelBuilder.Entity("FishFarmManagement.Models.User", b =>
                {
                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
