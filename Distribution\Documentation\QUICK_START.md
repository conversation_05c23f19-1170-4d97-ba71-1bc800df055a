# 🚀 دليل البدء السريع
## Quick Start Guide

## ⚡ البدء السريع (5 دقائق)

### 1. التحقق من المتطلبات
```cmd
# تحقق من إصدار .NET
dotnet --version
```
يجب أن يكون الإصدار 8.0.0 أو أحدث.

### 2. البناء والتشغيل
```cmd
# الطريقة الأسرع
test-build.bat

# إذا نجح الاختبار، شغل التطبيق
run.bat
```

### 3. إذا فشل البناء
```cmd
# امسح كل شيء وأعد البناء
rmdir /s /q bin
rmdir /s /q obj
rmdir /s /q FishFarmManagement.Models\bin
rmdir /s /q FishFarmManagement.Models\obj
rmdir /s /q FishFarmManagement.DAL\bin
rmdir /s /q FishFarmManagement.DAL\obj
rmdir /s /q FishFarmManagement.BLL\bin
rmdir /s /q FishFarmManagement.BLL\obj
rmdir /s /q FishFarmManagement\bin
rmdir /s /q FishFarmManagement\obj

# أعد البناء
build.bat
```

## 🔧 حل المشاكل الشائعة

### خطأ: "static types cannot be used as type arguments"
✅ **تم إصلاحه** - استبدال `ILogger<Program>` بـ `ILoggerFactory`

### خطأ: "Target platform must be set to Windows"
✅ **تم إصلاحه** - إزالة `UseWindowsForms` من مشروع Models

### خطأ: "Metadata file could not be found"
**الحل:**
```cmd
dotnet clean
dotnet restore
dotnet build --verbosity detailed
```

### خطأ: ".NET version not supported"
**الحل:**
1. تحميل .NET 8.0 SDK من: https://dotnet.microsoft.com/download/dotnet/8.0
2. إعادة تشغيل Command Prompt
3. التحقق: `dotnet --version`

### خطأ: "Could not find icon file"
✅ **تم إصلاحه** - تم إزالة مرجع الأيقونة مؤقتاً
**لإضافة أيقونة لاحقاً:** راجع ملف `ADD_ICON_INSTRUCTIONS.md`

## 📁 ملفات مساعدة

- `test-build.bat` - اختبار سريع للبناء
- `build.bat` - بناء كامل
- `run.bat` - تشغيل التطبيق
- `build.ps1` - بناء PowerShell (متقدم)

## 🎯 التشغيل الناجح

عند التشغيل الناجح ستظهر:
- نافذة رئيسية بالعربية
- لوحة معلومات مع 6 بطاقات
- قوائم عربية كاملة
- شريط حالة بالتاريخ والوقت

## 📞 المساعدة السريعة

إذا لم تعمل الطرق أعلاه:
1. تأكد من Windows 10/11
2. تأكد من .NET 8.0 SDK
3. شغل Command Prompt كمدير
4. راسل: <EMAIL>

## ⚠️ ملاحظات مهمة

- التطبيق يحتاج Windows فقط
- قاعدة البيانات تُنشأ تلقائياً
- النسخ الاحتياطية في مجلد `Backups`
- السجلات في مجلد `Logs`

---
**نصيحة:** استخدم `test-build.bat` أولاً للتأكد من سلامة البناء قبل التشغيل الكامل.
