﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.Forms
{
    public partial class EmployeeReportsForm : Form
    {
        private readonly ILogger<EmployeeReportsForm> _logger;
        private TabControl reportsTabControl;
        private TabPage attendanceTab;
        private TabPage payrollTab;
        private TabPage performanceTab;
        private TabPage overtimeTab;
        private TabPage leaveTab;

        public EmployeeReportsForm(ILogger<EmployeeReportsForm> logger)
        {
            _logger = logger;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "تقارير الموظفين";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            CreateControls();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(155, 89, 182),
                Padding = new Padding(20, 10, 20, 10)
            };

            var titleLabel = new Label
            {
                Text = "تقارير الموظفين",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 25)
            };

            headerPanel.Controls.Add(titleLabel);

            // Tab control
            reportsTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            // Create tabs
            CreateAttendanceTab();
            CreatePayrollTab();
            CreatePerformanceTab();
            CreateOvertimeTab();
            CreateLeaveTab();

            reportsTabControl.TabPages.AddRange(new TabPage[] 
            { 
                attendanceTab, payrollTab, performanceTab, overtimeTab, leaveTab 
            });

            this.Controls.AddRange(new Control[] { reportsTabControl, headerPanel });
        }

        private void CreateAttendanceTab()
        {
            attendanceTab = new TabPage("تقرير الحضور");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // Controls panel
            var controlPanel = new Panel { Height = 60, Dock = DockStyle.Top };
            
            var employeeLabel = new Label
            {
                Text = "الموظف:",
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var employeeComboBox = new ComboBox
            {
                Size = new Size(200, 25),
                Location = new Point(70, 17),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            employeeComboBox.Items.AddRange(new[] { "جميع الموظفين", "أحمد محمد", "فاطمة علي", "محمد حسن", "سارة أحمد" });
            employeeComboBox.SelectedIndex = 0;

            var fromLabel = new Label
            {
                Text = "من:",
                AutoSize = true,
                Location = new Point(290, 20)
            };

            var fromDatePicker = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(320, 17),
                Value = DateTime.Now.AddMonths(-1)
            };

            var toLabel = new Label
            {
                Text = "إلى:",
                AutoSize = true,
                Location = new Point(450, 20)
            };

            var toDatePicker = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(480, 17),
                Value = DateTime.Now
            };

            var generateButton = new Button
            {
                Text = "إنشاء التقرير",
                Size = new Size(120, 30),
                Location = new Point(620, 15),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            generateButton.Click += (s, e) => GenerateAttendanceReport();

            controlPanel.Controls.AddRange(new Control[] 
            { 
                employeeLabel, employeeComboBox, fromLabel, fromDatePicker, toLabel, toDatePicker, generateButton 
            });

            // Attendance grid
            var attendanceGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                ReadOnly = true,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                Font = new Font("Segoe UI", 9F)
            };

            // Setup attendance grid columns
            attendanceGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "EmployeeName", HeaderText = "اسم الموظف", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Department", HeaderText = "القسم", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "WorkingDays", HeaderText = "أيام العمل", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "PresentDays", HeaderText = "أيام الحضور", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "AbsentDays", HeaderText = "أيام الغياب", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "LateDays", HeaderText = "أيام التأخير", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "OvertimeHours", HeaderText = "ساعات إضافية", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "AttendanceRate", HeaderText = "معدل الحضور (%)", Width = 120 }
            });

            panel.Controls.AddRange(new Control[] { attendanceGrid, controlPanel });
            attendanceTab.Controls.Add(panel);
        }

        private void CreatePayrollTab()
        {
            payrollTab = new TabPage("تقرير الرواتب");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // Controls panel
            var controlPanel = new Panel { Height = 60, Dock = DockStyle.Top };
            
            var monthLabel = new Label
            {
                Text = "الشهر:",
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var monthComboBox = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(70, 17),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            monthComboBox.Items.AddRange(new[] { "الشهر الحالي", "الشهر السابق", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر" });
            monthComboBox.SelectedIndex = 0;

            var yearLabel = new Label
            {
                Text = "السنة:",
                AutoSize = true,
                Location = new Point(240, 20)
            };

            var yearComboBox = new ComboBox
            {
                Size = new Size(100, 25),
                Location = new Point(280, 17),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            for (int year = DateTime.Now.Year - 2; year <= DateTime.Now.Year + 1; year++)
            {
                yearComboBox.Items.Add(year.ToString());
            }
            yearComboBox.SelectedItem = DateTime.Now.Year.ToString();

            var generateButton = new Button
            {
                Text = "إنشاء التقرير",
                Size = new Size(120, 30),
                Location = new Point(400, 15),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            controlPanel.Controls.AddRange(new Control[] { monthLabel, monthComboBox, yearLabel, yearComboBox, generateButton });

            // Payroll summary panel
            var summaryPanel = new Panel { Height = 100, Dock = DockStyle.Top };
            
            var totalSalariesCard = CreateSummaryCard("إجمالي الرواتب", "125,000 ريال", Color.FromArgb(52, 152, 219));
            totalSalariesCard.Location = new Point(20, 20);

            var totalAllowancesCard = CreateSummaryCard("إجمالي البدلات", "15,000 ريال", Color.FromArgb(46, 204, 113));
            totalAllowancesCard.Location = new Point(220, 20);

            var totalDeductionsCard = CreateSummaryCard("إجمالي الخصومات", "8,000 ريال", Color.FromArgb(231, 76, 60));
            totalDeductionsCard.Location = new Point(420, 20);

            var netPayCard = CreateSummaryCard("صافي الرواتب", "132,000 ريال", Color.FromArgb(155, 89, 182));
            netPayCard.Location = new Point(620, 20);

            summaryPanel.Controls.AddRange(new Control[] { totalSalariesCard, totalAllowancesCard, totalDeductionsCard, netPayCard });

            // Payroll details grid
            var payrollGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                ReadOnly = true,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                Font = new Font("Segoe UI", 9F)
            };

            // Setup payroll grid columns
            payrollGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "EmployeeName", HeaderText = "اسم الموظف", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Position", HeaderText = "المنصب", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "BasicSalary", HeaderText = "الراتب الأساسي", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Allowances", HeaderText = "البدلات", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "OvertimePay", HeaderText = "أجر الإضافي", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Deductions", HeaderText = "الخصومات", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "NetSalary", HeaderText = "صافي الراتب", Width = 120 }
            });

            // Format currency columns
            foreach (DataGridViewColumn column in payrollGrid.Columns)
            {
                if (column.Name != "EmployeeName" && column.Name != "Position")
                {
                    column.DefaultCellStyle.Format = "C2";
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                }
            }

            panel.Controls.AddRange(new Control[] { payrollGrid, summaryPanel, controlPanel });
            payrollTab.Controls.Add(panel);
        }

        private void CreatePerformanceTab()
        {
            performanceTab = new TabPage("تقييم الأداء");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            
            var placeholderLabel = new Label
            {
                Text = "تقرير تقييم الأداء\n\n• تقييمات الأداء الشهرية\n• مؤشرات الأداء الرئيسية\n• مقارنة الأداء بين الموظفين\n• خطط التطوير والتحسين",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            panel.Controls.Add(placeholderLabel);
            performanceTab.Controls.Add(panel);
        }

        private void CreateOvertimeTab()
        {
            overtimeTab = new TabPage("الساعات الإضافية");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            
            var placeholderLabel = new Label
            {
                Text = "تقرير الساعات الإضافية\n\n• إجمالي الساعات الإضافية\n• الساعات الإضافية حسب الموظف\n• الساعات الإضافية حسب القسم\n• تكلفة الساعات الإضافية",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            panel.Controls.Add(placeholderLabel);
            overtimeTab.Controls.Add(panel);
        }

        private void CreateLeaveTab()
        {
            leaveTab = new TabPage("الإجازات");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            
            var placeholderLabel = new Label
            {
                Text = "تقرير الإجازات\n\n• رصيد الإجازات\n• الإجازات المستخدمة\n• الإجازات المعلقة\n• إحصائيات الإجازات",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            panel.Controls.Add(placeholderLabel);
            leaveTab.Controls.Add(panel);
        }

        private Panel CreateSummaryCard(string title, string value, Color color)
        {
            var card = new Panel
            {
                Size = new Size(180, 60),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.Gray,
                Location = new Point(10, 10),
                Size = new Size(160, 20)
            };

            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = color,
                Location = new Point(10, 30),
                Size = new Size(160, 25)
            };

            card.Controls.AddRange(new Control[] { titleLabel, valueLabel });
            return card;
        }

        private void GenerateAttendanceReport()
        {
            try
            {
                // Sample attendance data
                var sampleData = new List<dynamic>
                {
                    new { EmployeeName = "أحمد محمد", Department = "الإنتاج", WorkingDays = 22, PresentDays = 20, AbsentDays = 2, LateDays = 1, OvertimeHours = 8, AttendanceRate = 90.9 },
                    new { EmployeeName = "فاطمة علي", Department = "الإنتاج", WorkingDays = 22, PresentDays = 22, AbsentDays = 0, LateDays = 0, OvertimeHours = 4, AttendanceRate = 100.0 },
                    new { EmployeeName = "محمد حسن", Department = "الأعلاف", WorkingDays = 22, PresentDays = 21, AbsentDays = 1, LateDays = 2, OvertimeHours = 6, AttendanceRate = 95.5 },
                    new { EmployeeName = "سارة أحمد", Department = "الصيانة", WorkingDays = 22, PresentDays = 19, AbsentDays = 3, LateDays = 1, OvertimeHours = 2, AttendanceRate = 86.4 },
                    new { EmployeeName = "خالد محمود", Department = "الإدارة", WorkingDays = 22, PresentDays = 22, AbsentDays = 0, LateDays = 0, OvertimeHours = 10, AttendanceRate = 100.0 }
                };

                var attendanceGrid = (DataGridView)attendanceTab.Controls[0].Controls[0];
                attendanceGrid.DataSource = sampleData;

                // Highlight low attendance rates
                foreach (DataGridViewRow row in attendanceGrid.Rows)
                {
                    if (row.Cells["AttendanceRate"].Value != null)
                    {
                        var attendanceRate = Convert.ToDouble(row.Cells["AttendanceRate"].Value);
                        if (attendanceRate < 90)
                        {
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235);
                        }
                    }
                }

                _logger.LogInformation("تم إنشاء تقرير الحضور بنجاح");
                MessageBox.Show("تم إنشاء تقرير الحضور بنجاح", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير الحضور");
                MessageBox.Show($"خطأ في إنشاء تقرير الحضور: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}

