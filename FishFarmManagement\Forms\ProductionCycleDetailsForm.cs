﻿using FishFarmManagement.Models;
using FishFarmManagement.DAL.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج عرض تفاصيل الدورة الإنتاجية
    /// Production cycle details form
    /// </summary>
    public partial class ProductionCycleDetailsForm : Form
    {
        private readonly ProductionCycle _cycle;
        private readonly IUnitOfWork _unitOfWork;

        // UI Controls
        private TabControl tabControl;
        private TabPage generalTabPage;
        private TabPage pondsTabPage;
        private TabPage financialTabPage;

        public ProductionCycleDetailsForm(ProductionCycle cycle, IUnitOfWork unitOfWork)
        {
            _cycle = cycle ?? throw new ArgumentNullException(nameof(cycle));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            
            InitializeComponent();
            LoadDataAsync();
        }

        private void InitializeComponent()
        {
            this.Text = $"تفاصيل الدورة الإنتاجية - {_cycle.CycleName}";
            this.Size = new Size(800, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            CreateTabControl();
            CreateGeneralTab();
            CreatePondsTab();
            CreateFinancialTab();
        }

        private void CreateTabControl()
        {
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            generalTabPage = new TabPage("المعلومات العامة");
            pondsTabPage = new TabPage("الأحواض");
            financialTabPage = new TabPage("المعلومات المالية");

            tabControl.TabPages.AddRange(new TabPage[] { generalTabPage, pondsTabPage, financialTabPage });
            this.Controls.Add(tabControl);
        }

        private void CreateGeneralTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            int y = 20;
            int spacing = 35;

            // Cycle Name
            var cycleNameLabel = CreateLabel("اسم الدورة:", new Point(20, y));
            var cycleNameValue = CreateValueLabel(_cycle.CycleName, new Point(150, y));
            y += spacing;

            // Start Date
            var startDateLabel = CreateLabel("تاريخ البداية:", new Point(20, y));
            var startDateValue = CreateValueLabel(_cycle.StartDate.ToString("yyyy/MM/dd"), new Point(150, y));
            y += spacing;

            // Expected End Date
            var expectedEndDateLabel = CreateLabel("تاريخ النهاية المتوقع:", new Point(20, y));
            var expectedEndDateValue = CreateValueLabel(_cycle.ExpectedEndDate.ToString("yyyy/MM/dd"), new Point(150, y));
            y += spacing;

            // Actual End Date
            var endDateLabel = CreateLabel("تاريخ النهاية الفعلي:", new Point(20, y));
            var endDateValue = CreateValueLabel(_cycle.EndDate?.ToString("yyyy/MM/dd") ?? "لم ينته بعد", new Point(150, y));
            y += spacing;

            // Status
            var statusLabel = CreateLabel("الحالة:", new Point(20, y));
            var statusValue = CreateValueLabel(_cycle.Status, new Point(150, y));
            if (_cycle.Status == "نشط")
            {
                statusValue.ForeColor = Color.Green;
            }
            else if (_cycle.Status == "مكتمل")
            {
                statusValue.ForeColor = Color.Blue;
            }
            statusValue.Font = new Font(statusValue.Font, FontStyle.Bold);
            y += spacing;

            // Duration
            var durationLabel = CreateLabel("مدة الدورة (أيام):", new Point(20, y));
            var durationValue = CreateValueLabel(_cycle.GetCycleDurationInDays().ToString(), new Point(150, y));
            y += spacing;

            // Budget Amount
            var budgetLabel = CreateLabel("الميزانية:", new Point(20, y));
            var budgetValue = CreateValueLabel(_cycle.BudgetAmount.ToString("C"), new Point(150, y));
            y += spacing;

            // Is Overdue
            if (_cycle.IsOverdue)
            {
                var overdueLabel = CreateLabel("حالة التأخير:", new Point(20, y));
                var overdueValue = CreateValueLabel("متأخر عن الموعد المحدد", new Point(150, y));
                overdueValue.ForeColor = Color.Red;
                overdueValue.Font = new Font(overdueValue.Font, FontStyle.Bold);
                y += spacing;
            }

            // Notes
            if (!string.IsNullOrWhiteSpace(_cycle.Notes))
            {
                var notesLabel = CreateLabel("الملاحظات:", new Point(20, y));
                var notesValue = new TextBox
                {
                    Text = _cycle.Notes,
                    Location = new Point(150, y),
                    Size = new Size(400, 60),
                    Multiline = true,
                    ReadOnly = true,
                    ScrollBars = ScrollBars.Vertical,
                    BackColor = Color.FromArgb(240, 240, 240)
                };
                panel.Controls.Add(notesLabel);
                panel.Controls.Add(notesValue);
            }

            panel.Controls.AddRange(new Control[]
            {
                cycleNameLabel, cycleNameValue,
                startDateLabel, startDateValue,
                expectedEndDateLabel, expectedEndDateValue,
                endDateLabel, endDateValue,
                statusLabel, statusValue,
                durationLabel, durationValue,
                budgetLabel, budgetValue
            });

            generalTabPage.Controls.Add(panel);
        }

        private void CreatePondsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            var pondsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            // Setup columns
            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PondNumber",
                HeaderText = "رقم الحوض",
                DataPropertyName = "PondNumber",
                Width = 100
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FishCount",
                HeaderText = "عدد الأسماك",
                DataPropertyName = "FishCount",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N0" }
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AverageWeight",
                HeaderText = "متوسط الوزن (كجم)",
                DataPropertyName = "AverageWeight",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N3" }
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                DataPropertyName = "Status",
                Width = 80
            });

            pondsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "StockingDate",
                HeaderText = "تاريخ التخزين",
                DataPropertyName = "StockingDate",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            panel.Controls.Add(pondsDataGridView);
            pondsTabPage.Controls.Add(panel);
            pondsTabPage.Tag = pondsDataGridView; // Store reference for loading data
        }

        private void CreateFinancialTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            int y = 20;
            int spacing = 35;

            // Budget Amount
            var budgetLabel = CreateLabel("الميزانية المخصصة:", new Point(20, y));
            var budgetValue = CreateValueLabel(_cycle.BudgetAmount.ToString("C"), new Point(200, y));
            y += spacing;

            // Placeholder for financial calculations
            var totalCostLabel = CreateLabel("إجمالي التكاليف:", new Point(20, y));
            var totalCostValue = CreateValueLabel("سيتم حسابها...", new Point(200, y));
            y += spacing;

            var remainingBudgetLabel = CreateLabel("الميزانية المتبقية:", new Point(20, y));
            var remainingBudgetValue = CreateValueLabel("سيتم حسابها...", new Point(200, y));
            y += spacing;

            panel.Controls.AddRange(new Control[]
            {
                budgetLabel, budgetValue,
                totalCostLabel, totalCostValue,
                remainingBudgetLabel, remainingBudgetValue
            });

            financialTabPage.Controls.Add(panel);
        }

        private Label CreateLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(150, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
        }

        private Label CreateValueLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(200, 23),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 10F),
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };
        }

        private async void LoadDataAsync()
        {
            try
            {
                // Load ponds data
                var pondsDataGridView = pondsTabPage.Tag as DataGridView;
                if (pondsDataGridView != null)
                {
                    var ponds = await _unitOfWork.Ponds.FindAsync(p => p.CycleId == _cycle.Id);
                    pondsDataGridView.DataSource = ponds.ToList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}

