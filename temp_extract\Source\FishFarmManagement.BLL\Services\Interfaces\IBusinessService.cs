namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// الواجهة الأساسية لخدمات الأعمال
    /// Base interface for business services
    /// </summary>
    /// <typeparam name="T">نوع الكيان</typeparam>
    /// <typeparam name="TKey">نوع المفتاح الأساسي</typeparam>
    public interface IBusinessService<T, TKey> where T : class
    {
        /// <summary>
        /// الحصول على جميع الكيانات
        /// Get all entities
        /// </summary>
        Task<IEnumerable<T>> GetAllAsync();

        /// <summary>
        /// الحصول على كيان بالمعرف
        /// Get entity by ID
        /// </summary>
        Task<T?> GetByIdAsync(TKey id);

        /// <summary>
        /// إضافة كيان جديد
        /// Add new entity
        /// </summary>
        Task<T> AddAsync(T entity);

        /// <summary>
        /// تحديث كيان
        /// Update entity
        /// </summary>
        Task<T> UpdateAsync(T entity);

        /// <summary>
        /// حذف كيان
        /// Delete entity
        /// </summary>
        Task<bool> DeleteAsync(TKey id);

        /// <summary>
        /// التحقق من صحة الكيان
        /// Validate entity
        /// </summary>
        Task<ValidationResult> ValidateAsync(T entity);
    }

    /// <summary>
    /// نتيجة التحقق من الصحة
    /// Validation result
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();

        public void AddError(string error)
        {
            Errors.Add(error);
            IsValid = false;
        }

        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }
    }
}
