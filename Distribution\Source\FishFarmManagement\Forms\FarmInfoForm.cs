﻿using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace FishFarmManagement.Forms
{
    public partial class FarmInfoForm : Form
    {
        private readonly ILogger<FarmInfoForm> _logger;
        private readonly IConfiguration _configuration;
        private TabControl tabControl;
        private TabPage generalInfoTab;
        private TabPage contactInfoTab;
        private TabPage facilitiesTab;
        private TabPage licenseTab;

        public FarmInfoForm(ILogger<FarmInfoForm> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            InitializeComponent();
            LoadFarmData();
        }

        private void InitializeComponent()
        {
            this.Text = "معلومات المزرعة";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(52, 73, 94),
                Padding = new Padding(20, 10, 20, 10)
            };

            var titleLabel = new Label
            {
                Text = "معلومات المزرعة",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 25)
            };

            headerPanel.Controls.Add(titleLabel);

            // Tab control
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            // Create tabs
            CreateGeneralInfoTab();
            CreateContactInfoTab();
            CreateFacilitiesTab();
            CreateLicenseTab();

            tabControl.TabPages.AddRange(new TabPage[] 
            { 
                generalInfoTab, contactInfoTab, facilitiesTab, licenseTab 
            });

            // Buttons panel
            var buttonsPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Bottom,
                Padding = new Padding(20, 15, 20, 15)
            };

            var saveButton = new Button
            {
                Text = "حفظ",
                Size = new Size(100, 30),
                Location = new Point(20, 15),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            saveButton.Click += SaveButton_Click;

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Size = new Size(100, 30),
                Location = new Point(130, 15),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            cancelButton.Click += (s, e) => this.Close();

            buttonsPanel.Controls.AddRange(new Control[] { saveButton, cancelButton });

            this.Controls.AddRange(new Control[] { tabControl, buttonsPanel, headerPanel });
        }

        private void CreateGeneralInfoTab()
        {
            generalInfoTab = new TabPage("المعلومات العامة");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(30) };

            // Farm name
            var farmNameLabel = new Label
            {
                Text = "اسم المزرعة:",
                AutoSize = true,
                Location = new Point(30, 30),
                Font = new Font("Segoe UI", 10F)
            };

            var farmNameTextBox = new TextBox
            {
                Size = new Size(300, 25),
                Location = new Point(120, 27),
                Font = new Font("Segoe UI", 10F),
                Name = "FarmName"
            };

            // Owner name
            var ownerNameLabel = new Label
            {
                Text = "اسم المالك:",
                AutoSize = true,
                Location = new Point(30, 70),
                Font = new Font("Segoe UI", 10F)
            };

            var ownerNameTextBox = new TextBox
            {
                Size = new Size(300, 25),
                Location = new Point(120, 67),
                Font = new Font("Segoe UI", 10F),
                Name = "OwnerName"
            };

            // Establishment date
            var establishmentDateLabel = new Label
            {
                Text = "تاريخ التأسيس:",
                AutoSize = true,
                Location = new Point(30, 110),
                Font = new Font("Segoe UI", 10F)
            };

            var establishmentDatePicker = new DateTimePicker
            {
                Size = new Size(200, 25),
                Location = new Point(120, 107),
                Font = new Font("Segoe UI", 10F),
                Name = "EstablishmentDate"
            };

            // Farm type
            var farmTypeLabel = new Label
            {
                Text = "نوع المزرعة:",
                AutoSize = true,
                Location = new Point(30, 150),
                Font = new Font("Segoe UI", 10F)
            };

            var farmTypeComboBox = new ComboBox
            {
                Size = new Size(200, 25),
                Location = new Point(120, 147),
                Font = new Font("Segoe UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Name = "FarmType"
            };
            farmTypeComboBox.Items.AddRange(new[] { "مزرعة أسماك المياه العذبة", "مزرعة أسماك المياه المالحة", "مزرعة مختلطة" });

            // Total area
            var totalAreaLabel = new Label
            {
                Text = "المساحة الإجمالية (م²):",
                AutoSize = true,
                Location = new Point(30, 190),
                Font = new Font("Segoe UI", 10F)
            };

            var totalAreaTextBox = new TextBox
            {
                Size = new Size(150, 25),
                Location = new Point(170, 187),
                Font = new Font("Segoe UI", 10F),
                Name = "TotalArea"
            };

            // Water area
            var waterAreaLabel = new Label
            {
                Text = "مساحة المياه (م²):",
                AutoSize = true,
                Location = new Point(30, 230),
                Font = new Font("Segoe UI", 10F)
            };

            var waterAreaTextBox = new TextBox
            {
                Size = new Size(150, 25),
                Location = new Point(150, 227),
                Font = new Font("Segoe UI", 10F),
                Name = "WaterArea"
            };

            // Number of ponds
            var pondsCountLabel = new Label
            {
                Text = "عدد الأحواض:",
                AutoSize = true,
                Location = new Point(30, 270),
                Font = new Font("Segoe UI", 10F)
            };

            var pondsCountTextBox = new TextBox
            {
                Size = new Size(100, 25),
                Location = new Point(120, 267),
                Font = new Font("Segoe UI", 10F),
                Name = "PondsCount"
            };

            // Production capacity
            var capacityLabel = new Label
            {
                Text = "الطاقة الإنتاجية (طن/سنة):",
                AutoSize = true,
                Location = new Point(30, 310),
                Font = new Font("Segoe UI", 10F)
            };

            var capacityTextBox = new TextBox
            {
                Size = new Size(150, 25),
                Location = new Point(180, 307),
                Font = new Font("Segoe UI", 10F),
                Name = "ProductionCapacity"
            };

            panel.Controls.AddRange(new Control[] 
            { 
                farmNameLabel, farmNameTextBox, ownerNameLabel, ownerNameTextBox,
                establishmentDateLabel, establishmentDatePicker, farmTypeLabel, farmTypeComboBox,
                totalAreaLabel, totalAreaTextBox, waterAreaLabel, waterAreaTextBox,
                pondsCountLabel, pondsCountTextBox, capacityLabel, capacityTextBox
            });

            generalInfoTab.Controls.Add(panel);
        }

        private void CreateContactInfoTab()
        {
            contactInfoTab = new TabPage("معلومات الاتصال");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(30) };

            // Address
            var addressLabel = new Label
            {
                Text = "العنوان:",
                AutoSize = true,
                Location = new Point(30, 30),
                Font = new Font("Segoe UI", 10F)
            };

            var addressTextBox = new TextBox
            {
                Size = new Size(400, 60),
                Location = new Point(30, 55),
                Font = new Font("Segoe UI", 10F),
                Multiline = true,
                Name = "Address"
            };

            // City
            var cityLabel = new Label
            {
                Text = "المدينة:",
                AutoSize = true,
                Location = new Point(30, 130),
                Font = new Font("Segoe UI", 10F)
            };

            var cityTextBox = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(80, 127),
                Font = new Font("Segoe UI", 10F),
                Name = "City"
            };

            // Postal code
            var postalCodeLabel = new Label
            {
                Text = "الرمز البريدي:",
                AutoSize = true,
                Location = new Point(300, 130),
                Font = new Font("Segoe UI", 10F)
            };

            var postalCodeTextBox = new TextBox
            {
                Size = new Size(100, 25),
                Location = new Point(390, 127),
                Font = new Font("Segoe UI", 10F),
                Name = "PostalCode"
            };

            // Phone
            var phoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                AutoSize = true,
                Location = new Point(30, 170),
                Font = new Font("Segoe UI", 10F)
            };

            var phoneTextBox = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(110, 167),
                Font = new Font("Segoe UI", 10F),
                Name = "Phone"
            };

            // Mobile
            var mobileLabel = new Label
            {
                Text = "رقم الجوال:",
                AutoSize = true,
                Location = new Point(30, 210),
                Font = new Font("Segoe UI", 10F)
            };

            var mobileTextBox = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(110, 207),
                Font = new Font("Segoe UI", 10F),
                Name = "Mobile"
            };

            // Email
            var emailLabel = new Label
            {
                Text = "البريد الإلكتروني:",
                AutoSize = true,
                Location = new Point(30, 250),
                Font = new Font("Segoe UI", 10F)
            };

            var emailTextBox = new TextBox
            {
                Size = new Size(300, 25),
                Location = new Point(140, 247),
                Font = new Font("Segoe UI", 10F),
                Name = "Email"
            };

            // Website
            var websiteLabel = new Label
            {
                Text = "الموقع الإلكتروني:",
                AutoSize = true,
                Location = new Point(30, 290),
                Font = new Font("Segoe UI", 10F)
            };

            var websiteTextBox = new TextBox
            {
                Size = new Size(300, 25),
                Location = new Point(130, 287),
                Font = new Font("Segoe UI", 10F),
                Name = "Website"
            };

            panel.Controls.AddRange(new Control[] 
            { 
                addressLabel, addressTextBox, cityLabel, cityTextBox,
                postalCodeLabel, postalCodeTextBox, phoneLabel, phoneTextBox,
                mobileLabel, mobileTextBox, emailLabel, emailTextBox,
                websiteLabel, websiteTextBox
            });

            contactInfoTab.Controls.Add(panel);
        }

        private void CreateFacilitiesTab()
        {
            facilitiesTab = new TabPage("المرافق والتجهيزات");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(30) };

            var facilitiesLabel = new Label
            {
                Text = "المرافق والتجهيزات المتوفرة:",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(30, 30)
            };

            var facilitiesCheckedListBox = new CheckedListBox
            {
                Size = new Size(400, 300),
                Location = new Point(30, 60),
                Font = new Font("Segoe UI", 10F),
                Name = "Facilities"
            };

            facilitiesCheckedListBox.Items.AddRange(new object[]
            {
                "مضخات المياه",
                "أنظمة التهوية",
                "أنظمة الترشيح",
                "مولدات الكهرباء",
                "مختبر فحص المياه",
                "مخازن الأعلاف",
                "مخازن المعدات",
                "مكاتب إدارية",
                "غرف الموظفين",
                "مواقف السيارات",
                "أنظمة الأمان والمراقبة",
                "أنظمة الإنذار المبكر"
            });

            panel.Controls.AddRange(new Control[] { facilitiesLabel, facilitiesCheckedListBox });
            facilitiesTab.Controls.Add(panel);
        }

        private void CreateLicenseTab()
        {
            licenseTab = new TabPage("التراخيص والشهادات");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(30) };

            // Commercial license
            var commercialLicenseLabel = new Label
            {
                Text = "رقم السجل التجاري:",
                AutoSize = true,
                Location = new Point(30, 30),
                Font = new Font("Segoe UI", 10F)
            };

            var commercialLicenseTextBox = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(150, 27),
                Font = new Font("Segoe UI", 10F),
                Name = "CommercialLicense"
            };

            // Aquaculture license
            var aquacultureLicenseLabel = new Label
            {
                Text = "رخصة الاستزراع المائي:",
                AutoSize = true,
                Location = new Point(30, 70),
                Font = new Font("Segoe UI", 10F)
            };

            var aquacultureLicenseTextBox = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(170, 67),
                Font = new Font("Segoe UI", 10F),
                Name = "AquacultureLicense"
            };

            // Environmental permit
            var environmentalPermitLabel = new Label
            {
                Text = "التصريح البيئي:",
                AutoSize = true,
                Location = new Point(30, 110),
                Font = new Font("Segoe UI", 10F)
            };

            var environmentalPermitTextBox = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(120, 107),
                Font = new Font("Segoe UI", 10F),
                Name = "EnvironmentalPermit"
            };

            // Health certificate
            var healthCertificateLabel = new Label
            {
                Text = "شهادة صحية:",
                AutoSize = true,
                Location = new Point(30, 150),
                Font = new Font("Segoe UI", 10F)
            };

            var healthCertificateTextBox = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(110, 147),
                Font = new Font("Segoe UI", 10F),
                Name = "HealthCertificate"
            };

            // Quality certificate
            var qualityCertificateLabel = new Label
            {
                Text = "شهادة الجودة:",
                AutoSize = true,
                Location = new Point(30, 190),
                Font = new Font("Segoe UI", 10F)
            };

            var qualityCertificateTextBox = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(110, 187),
                Font = new Font("Segoe UI", 10F),
                Name = "QualityCertificate"
            };

            panel.Controls.AddRange(new Control[] 
            { 
                commercialLicenseLabel, commercialLicenseTextBox,
                aquacultureLicenseLabel, aquacultureLicenseTextBox,
                environmentalPermitLabel, environmentalPermitTextBox,
                healthCertificateLabel, healthCertificateTextBox,
                qualityCertificateLabel, qualityCertificateTextBox
            });

            licenseTab.Controls.Add(panel);
        }

        private void LoadFarmData()
        {
            try
            {
                // Load sample data - replace with actual database query
                SetControlValue("FarmName", "مزرعة الأسماك النموذجية");
                SetControlValue("OwnerName", "أحمد محمد السعيد");
                SetControlValue("EstablishmentDate", DateTime.Now.AddYears(-5));
                SetControlValue("FarmType", "مزرعة أسماك المياه العذبة");
                SetControlValue("TotalArea", "50000");
                SetControlValue("WaterArea", "30000");
                SetControlValue("PondsCount", "12");
                SetControlValue("ProductionCapacity", "500");

                SetControlValue("Address", "طريق الملك فهد، حي الصناعية");
                SetControlValue("City", "الرياض");
                SetControlValue("PostalCode", "12345");
                SetControlValue("Phone", "***********");
                SetControlValue("Mobile", "**********");
                SetControlValue("Email", "<EMAIL>");
                SetControlValue("Website", "www.fishfarm.com");

                SetControlValue("CommercialLicense", "**********");
                SetControlValue("AquacultureLicense", "AQ-2024-001");
                SetControlValue("EnvironmentalPermit", "ENV-2024-001");
                SetControlValue("HealthCertificate", "HC-2024-001");
                SetControlValue("QualityCertificate", "QC-2024-001");

                _logger.LogInformation("تم تحميل معلومات المزرعة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل معلومات المزرعة");
                MessageBox.Show($"خطأ في تحميل معلومات المزرعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetControlValue(string controlName, object value)
        {
            var control = FindControlByName(controlName);
            if (control != null)
            {
                if (control is TextBox textBox)
                    textBox.Text = value?.ToString() ?? "";
                else if (control is DateTimePicker datePicker && value is DateTime dateValue)
                    datePicker.Value = dateValue;
                else if (control is ComboBox comboBox)
                    comboBox.SelectedItem = value?.ToString();
            }
        }

        private Control? FindControlByName(string name)
        {
            foreach (TabPage tab in tabControl.TabPages)
            {
                foreach (Control control in tab.Controls[0].Controls)
                {
                    if (control.Name == name)
                        return control;
                }
            }
            return null;
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // TODO: Save farm information to database
                _logger.LogInformation("تم حفظ معلومات المزرعة بنجاح");
                MessageBox.Show("تم حفظ معلومات المزرعة بنجاح", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ معلومات المزرعة");
                MessageBox.Show($"خطأ في حفظ معلومات المزرعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}

