﻿using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ ØªÙ‚Ø±ÙŠØ± Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ø¯Ø®Ù„
    /// Income Statement Report Form
    /// </summary>
    public partial class IncomeStatementReportForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;

        // UI Controls
        private DateTimePicker fromDatePicker;
        private DateTimePicker toDatePicker;
        private Button generateReportButton;
        private DataGridView reportDataGridView;
        private Label totalRevenueLabel;
        private Label totalExpensesLabel;
        private Label netIncomeLabel;

        public IncomeStatementReportForm(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "ØªÙ‚Ø±ÙŠØ± Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ø¯Ø®Ù„";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
        }

        private void CreateControls()
        {
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 4,
                ColumnCount = 1,
                Padding = new Padding(20)
            };

            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 60));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 50));

            // Date Range Panel
            var datePanel = CreateDateRangePanel();
            mainPanel.Controls.Add(datePanel, 0, 0);

            // Report DataGridView
            reportDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 10F)
            };

            SetupReportColumns();
            mainPanel.Controls.Add(reportDataGridView, 0, 1);

            // Summary Panel
            var summaryPanel = CreateSummaryPanel();
            mainPanel.Controls.Add(summaryPanel, 0, 2);

            // Buttons Panel
            var buttonsPanel = CreateButtonsPanel();
            mainPanel.Controls.Add(buttonsPanel, 0, 3);

            this.Controls.Add(mainPanel);
        }

        private Panel CreateDateRangePanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            var fromLabel = new Label
            {
                Text = "Ù…Ù† ØªØ§Ø±ÙŠØ®:",
                Location = new Point(650, 15),
                Size = new Size(60, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            fromDatePicker = new DateTimePicker
            {
                Location = new Point(480, 15),
                Size = new Size(150, 23),
                Value = DateTime.Now.AddMonths(-1)
            };

            var toLabel = new Label
            {
                Text = "Ø¥Ù„Ù‰ ØªØ§Ø±ÙŠØ®:",
                Location = new Point(400, 15),
                Size = new Size(60, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            toDatePicker = new DateTimePicker
            {
                Location = new Point(230, 15),
                Size = new Size(150, 23),
                Value = DateTime.Now
            };

            generateReportButton = new Button
            {
                Text = "Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±",
                Location = new Point(120, 15),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            generateReportButton.Click += GenerateReport_Click;

            panel.Controls.AddRange(new Control[] { fromLabel, fromDatePicker, toLabel, toDatePicker, generateReportButton });
            return panel;
        }

        private void SetupReportColumns()
        {
            reportDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AccountName",
                HeaderText = "Ø§Ø³Ù… Ø§Ù„Ø­Ø³Ø§Ø¨",
                DataPropertyName = "AccountName",
                Width = 300
            });

            reportDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AccountType",
                HeaderText = "Ù†ÙˆØ¹ Ø§Ù„Ø­Ø³Ø§Ø¨",
                DataPropertyName = "AccountType",
                Width = 150
            });

            reportDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Amount",
                HeaderText = "Ø§Ù„Ù…Ø¨Ù„Øº",
                DataPropertyName = "Amount",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle 
                { 
                    Format = "C",
                    Alignment = DataGridViewContentAlignment.MiddleLeft
                }
            });
        }

        private Panel CreateSummaryPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            totalRevenueLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø¥ÙŠØ±Ø§Ø¯Ø§Øª: 0.00",
                Location = new Point(600, 10),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Green
            };

            totalExpensesLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ù…ØµØ±ÙˆÙØ§Øª: 0.00",
                Location = new Point(600, 35),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Red
            };

            netIncomeLabel = new Label
            {
                Text = "ØµØ§ÙÙŠ Ø§Ù„Ø¯Ø®Ù„: 0.00",
                Location = new Point(600, 60),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.Blue
            };

            panel.Controls.AddRange(new Control[] { totalRevenueLabel, totalExpensesLabel, netIncomeLabel });
            return panel;
        }

        private Panel CreateButtonsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill
            };

            var exportButton = new Button
            {
                Text = "ØªØµØ¯ÙŠØ± Ø¥Ù„Ù‰ Excel",
                Location = new Point(20, 10),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            exportButton.Click += ExportToExcel_Click;

            var printButton = new Button
            {
                Text = "Ø·Ø¨Ø§Ø¹Ø©",
                Location = new Point(160, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            printButton.Click += Print_Click;

            panel.Controls.AddRange(new Control[] { exportButton, printButton });
            return panel;
        }

        private async void private async void private async void GenerateReport_Click(object? sender, EventArgs e)
        {
            try
            {
                var fromDate = fromDatePicker.Value.Date;
                var toDate = toDatePicker.Value.Date.AddDays(1).AddTicks(-1);

                // Get all transactions in date range
                var transactions = await _unitOfWork.Transactions.FindAsync(
                    t => t.TransactionDate >= fromDate && t.TransactionDate <= toDate);

                var transactionDetails = new List<TransactionDetail>();
                foreach (var transaction in transactions)
                {
                    var details = await _unitOfWork.TransactionDetails.FindAsync(
                        td => td.TransactionId == transaction.Id);
                    transactionDetails.AddRange(details);
                }

                // Get accounts
                var accounts = await _unitOfWork.Accounts.GetAllAsync();
                var accountsDict = accounts.ToDictionary(a => a.Id, a => a);

                // Calculate income statement data
                var incomeData = CalculateIncomeStatement(transactionDetails, accountsDict);

                // Display results
                reportDataGridView.DataSource = incomeData.ToList();
                UpdateSummaryLabels(incomeData);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private IEnumerable<IncomeStatementItem> CalculateIncomeStatement(
            List<TransactionDetail> transactionDetails, 
            Dictionary<int, Account> accountsDict)
        {
            var incomeItems = new List<IncomeStatementItem>();

            // Group by account
            var accountGroups = transactionDetails.GroupBy(td => td.AccountId);

            foreach (var group in accountGroups)
            {
                if (!accountsDict.TryGetValue(group.Key, out var account))
                    continue;

                var accountType = account.AccountType?.TypeName ?? "ØºÙŠØ± Ù…Ø­Ø¯Ø¯";
                
                // Calculate net amount (Credit - Debit for revenue accounts, Debit - Credit for expense accounts)
                decimal netAmount = 0;
                if (accountType.Contains("Ø¥ÙŠØ±Ø§Ø¯") || accountType.Contains("Ø¯Ø®Ù„"))
                {
                    netAmount = group.Sum(td => td.CreditAmount - td.DebitAmount);
                }
                else if (accountType.Contains("Ù…ØµØ±ÙˆÙ") || accountType.Contains("ØªÙƒÙ„ÙØ©"))
                {
                    netAmount = group.Sum(td => td.DebitAmount - td.CreditAmount);
                }

                if (netAmount != 0)
                {
                    incomeItems.Add(new IncomeStatementItem
                    {
                        AccountName = account.AccountName,
                        AccountType = accountType,
                        Amount = netAmount
                    });
                }
            }

            return incomeItems.OrderBy(i => i.AccountType).ThenBy(i => i.AccountName);
        }

        private void UpdateSummaryLabels(IEnumerable<IncomeStatementItem> incomeData)
        {
            var totalRevenue = incomeData
                .Where(i => i.AccountType.Contains("Ø¥ÙŠØ±Ø§Ø¯") || i.AccountType.Contains("Ø¯Ø®Ù„"))
                .Sum(i => i.Amount);

            var totalExpenses = incomeData
                .Where(i => i.AccountType.Contains("Ù…ØµØ±ÙˆÙ") || i.AccountType.Contains("ØªÙƒÙ„ÙØ©"))
                .Sum(i => i.Amount);

            var netIncome = totalRevenue - totalExpenses;

            totalRevenueLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø¥ÙŠØ±Ø§Ø¯Ø§Øª: {totalRevenue:C}";
            totalExpensesLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ù…ØµØ±ÙˆÙØ§Øª: {totalExpenses:C}";
            netIncomeLabel.Text = $"ØµØ§ÙÙŠ Ø§Ù„Ø¯Ø®Ù„: {netIncome:C}";

            // Color coding for net income
            netIncomeLabel.ForeColor = netIncome >= 0 ? Color.Green : Color.Red;
        }

        private void ExportToExcel_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("ØªØµØ¯ÙŠØ± Ø¥Ù„Ù‰ Excel - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Print_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("Ø§Ù„Ø·Ø¨Ø§Ø¹Ø© - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    public class IncomeStatementItem
    {
        public string AccountName { get; set; } = string.Empty;
        public string AccountType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
    }
}



