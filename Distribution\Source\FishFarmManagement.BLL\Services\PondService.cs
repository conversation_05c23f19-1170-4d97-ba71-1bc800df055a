using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة إدارة الأحواض
    /// Pond management service
    /// </summary>
    public class PondService : IPondService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<PondService> _logger;

        public PondService(IUnitOfWork unitOfWork, ILogger<PondService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<Pond>> GetAllAsync()
        {
            try
            {
                return await _unitOfWork.Ponds.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على جميع الأحواض");
                throw;
            }
        }

        public async Task<Pond?> GetByIdAsync(int id)
        {
            try
            {
                return await _unitOfWork.Ponds.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الحوض بالمعرف {PondId}", id);
                throw;
            }
        }

        public async Task<Pond> AddAsync(Pond pond)
        {
            try
            {
                var validation = await ValidateAsync(pond);
                if (!validation.IsValid)
                {
                    throw new ArgumentException($"بيانات الحوض غير صحيحة: {string.Join(", ", validation.Errors)}");
                }

                // Check for duplicate pond number
                var existingPond = await _unitOfWork.Ponds.FirstOrDefaultAsync(p => p.PondNumber == pond.PondNumber);
                if (existingPond != null)
                {
                    throw new InvalidOperationException($"رقم الحوض {pond.PondNumber} موجود بالفعل");
                }

                var addedPond = await _unitOfWork.Ponds.AddAsync(pond);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إضافة الحوض {PondNumber} بنجاح", pond.PondNumber);
                return addedPond;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة الحوض {PondNumber}", pond.PondNumber);
                throw;
            }
        }

        public async Task<Pond> UpdateAsync(Pond pond)
        {
            try
            {
                var validation = await ValidateAsync(pond);
                if (!validation.IsValid)
                {
                    throw new ArgumentException($"بيانات الحوض غير صحيحة: {string.Join(", ", validation.Errors)}");
                }

                var existingPond = await _unitOfWork.Ponds.GetByIdAsync(pond.Id);
                if (existingPond == null)
                {
                    throw new InvalidOperationException($"الحوض بالمعرف {pond.Id} غير موجود");
                }

                // Check for duplicate pond number (excluding current pond)
                var duplicatePond = await _unitOfWork.Ponds.FirstOrDefaultAsync(p => p.PondNumber == pond.PondNumber && p.Id != pond.Id);
                if (duplicatePond != null)
                {
                    throw new InvalidOperationException($"رقم الحوض {pond.PondNumber} موجود بالفعل");
                }

                pond.UpdateModificationDate();
                var updatedPond = await _unitOfWork.Ponds.UpdateAsync(pond);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث الحوض {PondNumber} بنجاح", pond.PondNumber);
                return updatedPond;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الحوض {PondId}", pond.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var pond = await _unitOfWork.Ponds.GetByIdAsync(id);
                if (pond == null)
                {
                    return false;
                }

                // Check if pond has related data
                var hasFeedConsumption = await _unitOfWork.FeedConsumptions.ExistsAsync(fc => fc.PondId == id);
                var hasMortality = await _unitOfWork.FishMortalities.ExistsAsync(fm => fm.PondId == id);
                var hasMedication = await _unitOfWork.PondMedications.ExistsAsync(pm => pm.PondId == id);

                if (hasFeedConsumption || hasMortality || hasMedication)
                {
                    throw new InvalidOperationException("لا يمكن حذف الحوض لوجود بيانات مرتبطة به");
                }

                await _unitOfWork.Ponds.DeleteAsync(pond);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم حذف الحوض {PondNumber} بنجاح", pond.PondNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الحوض {PondId}", id);
                throw;
            }
        }

        public async Task<ValidationResult> ValidateAsync(Pond pond)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(pond.PondNumber))
            {
                result.AddError("رقم الحوض مطلوب");
            }

            if (pond.CycleId <= 0)
            {
                result.AddError("معرف الدورة الإنتاجية مطلوب");
            }

            if (pond.FishCount < 0)
            {
                result.AddError("عدد الأسماك لا يمكن أن يكون سالب");
            }

            if (pond.AverageWeight < 0)
            {
                result.AddError("متوسط الوزن لا يمكن أن يكون سالب");
            }

            if (pond.StockingDate > DateTime.Now)
            {
                result.AddWarning("تاريخ التخزين في المستقبل");
            }

            // Check if production cycle exists
            var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(pond.CycleId);
            if (cycle == null)
            {
                result.AddError("الدورة الإنتاجية المحددة غير موجودة");
            }

            return result;
        }

        public async Task<IEnumerable<Pond>> GetPondsByCycleAsync(int cycleId)
        {
            try
            {
                return await _unitOfWork.Ponds.FindAsync(p => p.CycleId == cycleId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على أحواض الدورة {CycleId}", cycleId);
                throw;
            }
        }

        public async Task<IEnumerable<Pond>> GetActivePondsAsync()
        {
            try
            {
                return await _unitOfWork.Ponds.FindAsync(p => p.Status == "نشط");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأحواض النشطة");
                throw;
            }
        }

        public async Task<bool> AddFeedConsumptionAsync(int pondId, FeedConsumption feedConsumption)
        {
            try
            {
                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                if (pond == null)
                {
                    throw new InvalidOperationException($"الحوض بالمعرف {pondId} غير موجود");
                }

                feedConsumption.PondId = pondId;
                await _unitOfWork.FeedConsumptions.AddAsync(feedConsumption);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إضافة استهلاك علف للحوض {PondNumber}", pond.PondNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة استهلاك علف للحوض {PondId}", pondId);
                throw;
            }
        }

        public async Task<bool> RecordFishMortalityAsync(int pondId, FishMortality mortality)
        {
            try
            {
                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                if (pond == null)
                {
                    throw new InvalidOperationException($"الحوض بالمعرف {pondId} غير موجود");
                }

                mortality.PondId = pondId;
                await _unitOfWork.FishMortalities.AddAsync(mortality);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تسجيل نفوق {DeadCount} سمكة في الحوض {PondNumber}", mortality.DeadFishCount, pond.PondNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل نفوق الأسماك للحوض {PondId}", pondId);
                throw;
            }
        }

        public async Task<bool> AddMedicationAsync(int pondId, PondMedication medication)
        {
            try
            {
                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                if (pond == null)
                {
                    throw new InvalidOperationException($"الحوض بالمعرف {pondId} غير موجود");
                }

                medication.PondId = pondId;
                await _unitOfWork.PondMedications.AddAsync(medication);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إضافة دواء للحوض {PondNumber}", pond.PondNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة دواء للحوض {PondId}", pondId);
                throw;
            }
        }

        public async Task<decimal> GetTotalFeedConsumptionAsync(int pondId)
        {
            try
            {
                var consumptions = await _unitOfWork.FeedConsumptions.FindAsync(fc => fc.PondId == pondId);
                return consumptions.Sum(fc => fc.Quantity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إجمالي استهلاك العلف للحوض {PondId}", pondId);
                throw;
            }
        }

        public async Task<decimal> GetTotalFeedCostAsync(int pondId)
        {
            try
            {
                var consumptions = await _unitOfWork.FeedConsumptions.FindAsync(fc => fc.PondId == pondId);
                return consumptions.Sum(fc => fc.Cost);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إجمالي تكلفة العلف للحوض {PondId}", pondId);
                throw;
            }
        }

        public async Task<decimal> GetMortalityRateAsync(int pondId)
        {
            try
            {
                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                if (pond == null || pond.FishCount == 0)
                {
                    return 0;
                }

                var mortalities = await _unitOfWork.FishMortalities.FindAsync(fm => fm.PondId == pondId);
                var totalDeadFish = mortalities.Sum(fm => fm.DeadFishCount);
                
                return (decimal)totalDeadFish / pond.FishCount * 100;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب معدل النفوق للحوض {PondId}", pondId);
                throw;
            }
        }

        public async Task<PondStatusReport> GetPondStatusReportAsync(int pondId)
        {
            try
            {
                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                if (pond == null)
                {
                    throw new InvalidOperationException($"الحوض بالمعرف {pondId} غير موجود");
                }

                var mortalities = await _unitOfWork.FishMortalities.FindAsync(fm => fm.PondId == pondId);
                var totalDeadFish = mortalities.Sum(fm => fm.DeadFishCount);
                var currentFishCount = pond.FishCount - totalDeadFish;
                var mortalityRate = pond.FishCount > 0 ? (decimal)totalDeadFish / pond.FishCount * 100 : 0;

                var totalFeedConsumption = await GetTotalFeedConsumptionAsync(pondId);
                var totalFeedCost = await GetTotalFeedCostAsync(pondId);

                var daysInProduction = (DateTime.Now - pond.StockingDate).Days;
                var estimatedTotalWeight = currentFishCount * pond.AverageWeight;

                var report = new PondStatusReport
                {
                    PondId = pond.Id,
                    PondNumber = pond.PondNumber,
                    InitialFishCount = pond.FishCount,
                    CurrentFishCount = currentFishCount,
                    DeadFishCount = totalDeadFish,
                    MortalityRate = mortalityRate,
                    TotalFeedConsumption = totalFeedConsumption,
                    TotalFeedCost = totalFeedCost,
                    AverageWeight = pond.AverageWeight,
                    EstimatedTotalWeight = estimatedTotalWeight,
                    DaysInProduction = daysInProduction,
                    ExpectedHarvestDate = pond.ExpectedHarvestDate,
                    IsReadyForHarvest = await IsReadyForHarvestAsync(pondId)
                };

                // Add alerts
                if (mortalityRate > 5)
                {
                    report.Alerts.Add($"معدل النفوق مرتفع: {mortalityRate:F2}%");
                }

                if (pond.ExpectedHarvestDate.HasValue && DateTime.Now > pond.ExpectedHarvestDate.Value)
                {
                    report.Alerts.Add("تأخر موعد الحصاد المتوقع");
                }

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير حالة الحوض {PondId}", pondId);
                throw;
            }
        }

        public async Task<bool> IsReadyForHarvestAsync(int pondId)
        {
            try
            {
                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                if (pond == null)
                {
                    return false;
                }

                // Check if expected harvest date has passed
                if (pond.ExpectedHarvestDate.HasValue && DateTime.Now >= pond.ExpectedHarvestDate.Value)
                {
                    return true;
                }

                // Check if average weight meets minimum requirements (example: 500g)
                if (pond.AverageWeight >= 0.5m)
                {
                    return true;
                }

                // Check if production cycle duration is sufficient (example: 6 months)
                var daysInProduction = (DateTime.Now - pond.StockingDate).Days;
                if (daysInProduction >= 180)
                {
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من جاهزية الحصاد للحوض {PondId}", pondId);
                throw;
            }
        }

        public async Task<FeedPurchaseOrder> GenerateFeedPurchaseOrderAsync(int pondId)
        {
            try
            {
                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                if (pond == null)
                {
                    throw new InvalidOperationException($"الحوض بالمعرف {pondId} غير موجود");
                }

                // Get the most commonly used feed type for this pond
                var feedConsumptions = await _unitOfWork.FeedConsumptions.FindAsync(fc => fc.PondId == pondId);
                var mostUsedFeedTypeId = feedConsumptions
                    .GroupBy(fc => fc.FeedTypeId)
                    .OrderByDescending(g => g.Sum(fc => fc.Quantity))
                    .FirstOrDefault()?.Key ?? 1;

                var feedType = await _unitOfWork.FeedTypes.GetByIdAsync(mostUsedFeedTypeId);
                if (feedType == null)
                {
                    throw new InvalidOperationException("نوع العلف غير موجود");
                }

                // Calculate required quantity based on fish count and feeding rate
                // Assuming 3% of body weight per day for 30 days
                var currentFishCount = pond.FishCount - (await _unitOfWork.FishMortalities.FindAsync(fm => fm.PondId == pondId)).Sum(fm => fm.DeadFishCount);
                var totalWeight = currentFishCount * pond.AverageWeight;
                var dailyFeedRequirement = totalWeight * 0.03m; // 3% of body weight
                var monthlyRequirement = dailyFeedRequirement * 30;

                var order = new FeedPurchaseOrder
                {
                    PondId = pondId,
                    PondNumber = pond.PondNumber,
                    FeedTypeId = mostUsedFeedTypeId,
                    FeedTypeName = feedType.FeedName,
                    RequiredQuantity = monthlyRequirement,
                    EstimatedCost = monthlyRequirement * feedType.PricePerKg,
                    RequiredDate = DateTime.Now.AddDays(7), // Required in a week
                    Justification = $"طلب علف شهري للحوض {pond.PondNumber} - عدد الأسماك: {currentFishCount}"
                };

                return order;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد طلب شراء العلف للحوض {PondId}", pondId);
                throw;
            }
        }
    }
}
