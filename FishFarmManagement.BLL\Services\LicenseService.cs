using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Timers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Models.License;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة إدارة التراخيص التجارية
    /// Commercial license management service
    /// </summary>
    public class LicenseService : ILicenseService
    {
        private readonly ILogger<LicenseService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _licenseFilePath;
        private readonly string _hardwareId;
        private System.Timers.Timer? _licenseCheckTimer;

        public LicenseService(ILogger<LicenseService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _licenseFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "license.dat");
            _hardwareId = GetHardwareId();

            // تهيئة التحقق الدوري من الترخيص
            InitializeLicenseCheck();
        }

        /// <summary>
        /// التحقق من صلاحية الترخيص
        /// Validate license
        /// </summary>
        public async Task<LicenseValidationResult> ValidateLicenseAsync(string licenseKey)
        {
            try
            {
                _logger.LogInformation("بدء التحقق من الترخيص: {LicenseKey}", MaskLicenseKey(licenseKey));

                // التحقق من صحة تنسيق مفتاح الترخيص
                if (!IsValidLicenseKeyFormat(licenseKey))
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        Message = "تنسيق مفتاح الترخيص غير صحيح",
                        Error = LicenseValidationError.InvalidKey
                    };
                }

                // محاولة التحقق من الإنترنت (اختياري)
                var hasInternet = await CheckInternetConnectionAsync();
                if (!hasInternet)
                {
                    _logger.LogWarning("لا يوجد اتصال بالإنترنت، سيتم التحقق من الترخيص المحلي");
                }

                // التحقق من الترخيص المحلي أولاً
                var localLicense = GetLocalLicense();
                if (localLicense != null && localLicense.LicenseKey == licenseKey)
                {
                    if (localLicense.IsValid())
                    {
                        return new LicenseValidationResult
                        {
                            IsValid = true,
                            Message = "الترخيص صالح",
                            License = localLicense
                        };
                    }
                    else
                    {
                        return new LicenseValidationResult
                        {
                            IsValid = false,
                            Message = "الترخيص منتهي الصلاحية",
                            Error = LicenseValidationError.Expired
                        };
                    }
                }

                // إذا كان هناك اتصال بالإنترنت، تحقق من الخادم
                if (hasInternet)
                {
                    return await ValidateLicenseWithServerAsync(licenseKey);
                }

                // إذا لم يكن هناك اتصال بالإنترنت، إنشاء ترخيص تجريبي
                if (string.IsNullOrEmpty(localLicense?.LicenseKey))
                {
                    var trialLicense = await CreateTrialLicenseAsync();
                    return new LicenseValidationResult
                    {
                        IsValid = true,
                        Message = "تم إنشاء ترخيص تجريبي (30 يوم)",
                        License = trialLicense
                    };
                }

                return new LicenseValidationResult
                {
                    IsValid = false,
                    Message = "لا يمكن التحقق من الترخيص بدون اتصال بالإنترنت",
                    Error = LicenseValidationError.NetworkError
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من الترخيص");
                return new LicenseValidationResult
                {
                    IsValid = false,
                    Message = "حدث خطأ في التحقق من الترخيص",
                    Error = LicenseValidationError.ServerError
                };
            }
        }

        /// <summary>
        /// تفعيل الترخيص
        /// Activate license
        /// </summary>
        public async Task<LicenseActivationResult> ActivateLicenseAsync(string licenseKey)
        {
            try
            {
                _logger.LogInformation("بدء تفعيل الترخيص: {LicenseKey}", MaskLicenseKey(licenseKey));

                // التحقق من الترخيص أولاً
                var validationResult = await ValidateLicenseAsync(licenseKey);
                if (!validationResult.IsValid)
                {
                    return new LicenseActivationResult
                    {
                        Success = false,
                        Message = validationResult.Message,
                        Error = MapValidationErrorToActivationError(validationResult.Error)
                    };
                }

                var license = validationResult.License!;

                // التحقق من عدد التفعيلات
                if (license.ActivationCount >= license.GetMaxActivations())
                {
                    return new LicenseActivationResult
                    {
                        Success = false,
                        Message = "تم الوصول للحد الأقصى لعدد التفعيلات",
                        Error = LicenseActivationError.MaxActivationsReached
                    };
                }

                // تحديث معلومات التفعيل
                license.HardwareId = _hardwareId;
                license.LastActivationDate = DateTime.Now;
                license.ActivationCount++;
                license.UpdatedDate = DateTime.Now;

                // حفظ الترخيص محلياً
                var saveResult = await SaveLicenseLocallyAsync(license);
                if (!saveResult)
                {
                    return new LicenseActivationResult
                    {
                        Success = false,
                        Message = "فشل في حفظ معلومات الترخيص",
                        Error = LicenseActivationError.HardwareError
                    };
                }

                _logger.LogInformation("تم تفعيل الترخيص بنجاح");
                return new LicenseActivationResult
                {
                    Success = true,
                    Message = "تم تفعيل الترخيص بنجاح",
                    License = license
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تفعيل الترخيص");
                return new LicenseActivationResult
                {
                    Success = false,
                    Message = "حدث خطأ في تفعيل الترخيص",
                    Error = LicenseActivationError.ServerError
                };
            }
        }

        /// <summary>
        /// الحصول على معلومات الترخيص الحالي
        /// Get current license information
        /// </summary>
        public async Task<LicenseInfo?> GetCurrentLicenseAsync()
        {
            var localLicense = GetLocalLicense();
            if (localLicense != null && localLicense.IsValid())
            {
                return localLicense;
            }

            // إذا لم يكن هناك ترخيص صالح، إنشاء ترخيص تجريبي
            if (localLicense == null)
            {
                return await CreateTrialLicenseAsync();
            }

            return null;
        }

        /// <summary>
        /// التحقق من صلاحية الترخيص للمزرعة
        /// Check license validity for farm
        /// </summary>
        public async Task<bool> IsLicenseValidForFarmAsync(int pondCount, int userCount)
        {
            var license = await GetCurrentLicenseAsync();
            return license?.IsValidForFarm(pondCount, userCount) ?? false;
        }

        /// <summary>
        /// الحصول على معلومات الترخيص المحلية
        /// Get local license information
        /// </summary>
        public LicenseInfo? GetLocalLicense()
        {
            try
            {
                if (!File.Exists(_licenseFilePath))
                {
                    return null;
                }

                var encryptedData = File.ReadAllBytes(_licenseFilePath);
                var decryptedData = DecryptLicenseData(encryptedData);
                var license = JsonSerializer.Deserialize<LicenseInfo>(decryptedData);
                
                return license;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في قراءة الترخيص المحلي");
                return null;
            }
        }

        /// <summary>
        /// حفظ معلومات الترخيص محلياً
        /// Save license information locally
        /// </summary>
        public async Task<bool> SaveLicenseLocallyAsync(LicenseInfo license)
        {
            try
            {
                var jsonData = JsonSerializer.Serialize(license);
                var encryptedData = EncryptLicenseData(jsonData);
                
                await File.WriteAllBytesAsync(_licenseFilePath, encryptedData);
                
                _logger.LogInformation("تم حفظ الترخيص محلياً بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ الترخيص محلياً");
                return false;
            }
        }

        /// <summary>
        /// التحقق من انتهاء صلاحية الترخيص
        /// Check license expiration
        /// </summary>
        public async Task<LicenseExpirationResult> CheckExpirationAsync()
        {
            var license = await GetCurrentLicenseAsync();
            if (license == null)
            {
                return new LicenseExpirationResult
                {
                    IsExpired = true,
                    ExpiresSoon = false,
                    RemainingDays = 0,
                    Message = "لا يوجد ترخيص صالح"
                };
            }

            var remainingDays = license.GetRemainingDays();
            var expiresSoon = license.ExpiresSoon();

            return new LicenseExpirationResult
            {
                IsExpired = remainingDays <= 0,
                ExpiresSoon = expiresSoon,
                RemainingDays = remainingDays,
                Message = remainingDays <= 0 
                    ? "الترخيص منتهي الصلاحية" 
                    : expiresSoon 
                        ? $"الترخيص ينتهي خلال {remainingDays} يوم" 
                        : $"الترخيص صالح لمدة {remainingDays} يوم"
            };
        }

        /// <summary>
        /// إنشاء ترخيص تجريبي
        /// Create trial license
        /// </summary>
        public async Task<LicenseInfo> CreateTrialLicenseAsync()
        {
            var trialLicense = new LicenseInfo
            {
                LicenseKey = GenerateTrialLicenseKey(),
                Type = LicenseType.Trial,
                CustomerName = "مستخدم تجريبي",
                CustomerEmail = "<EMAIL>",
                IssueDate = DateTime.Now,
                ExpirationDate = DateTime.Now.AddDays(30),
                MaxUsers = 1,
                MaxPonds = 5,
                IsActive = true,
                HardwareId = _hardwareId,
                LastActivationDate = DateTime.Now,
                ActivationCount = 1,
                Notes = "ترخيص تجريبي - 30 يوم"
            };

            await SaveLicenseLocallyAsync(trialLicense);
            _logger.LogInformation("تم إنشاء ترخيص تجريبي جديد");

            return trialLicense;
        }

        /// <summary>
        /// الحصول على معرف الجهاز
        /// Get hardware identifier
        /// </summary>
        public string GetHardwareId()
        {
            try
            {
                var hardwareInfo = new StringBuilder();
                
                // CPU ID
                hardwareInfo.Append(Environment.ProcessorCount);
                
                // Machine Name
                hardwareInfo.Append(Environment.MachineName);
                
                // OS Version
                hardwareInfo.Append(Environment.OSVersion);
                
                // User Name
                hardwareInfo.Append(Environment.UserName);

                // Create hash
                using var sha256 = SHA256.Create();
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(hardwareInfo.ToString()));
                return Convert.ToBase64String(hash).Substring(0, 16);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء معرف الجهاز");
                return "UNKNOWN_HARDWARE";
            }
        }

        /// <summary>
        /// التحقق من اتصال الإنترنت للتفعيل
        /// Check internet connection for activation
        /// </summary>
        public async Task<bool> CheckInternetConnectionAsync()
        {
            try
            {
                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(5);
                
                var response = await client.GetAsync("http://www.google.com");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        #region Private Methods

        private bool IsValidLicenseKeyFormat(string licenseKey)
        {
            if (string.IsNullOrWhiteSpace(licenseKey))
                return false;

            // تنسيق مفتاح الترخيص: XXXX-XXXX-XXXX-XXXX
            var parts = licenseKey.Split('-');
            return parts.Length == 4 && parts.All(p => p.Length == 4 && p.All(c => char.IsLetterOrDigit(c)));
        }

        private async Task<LicenseValidationResult> ValidateLicenseWithServerAsync(string licenseKey)
        {
            // في الإنتاج، سيتم الاتصال بخادم الترخيص
            // للآن، سنقوم بمحاكاة التحقق
            await Task.Delay(1000); // محاكاة تأخير الشبكة

            // إنشاء ترخيص وهمي للاختبار
            var fakeLicense = new LicenseInfo
            {
                LicenseKey = licenseKey,
                Type = LicenseType.Basic,
                CustomerName = "عميل تجريبي",
                CustomerEmail = "<EMAIL>",
                IssueDate = DateTime.Now.AddDays(-30),
                ExpirationDate = DateTime.Now.AddYears(1),
                MaxUsers = 3,
                MaxPonds = 20,
                IsActive = true
            };

            return new LicenseValidationResult
            {
                IsValid = true,
                Message = "تم التحقق من الترخيص بنجاح",
                License = fakeLicense
            };
        }

        private string GenerateTrialLicenseKey()
        {
            var random = new Random();
            var key = new StringBuilder();
            
            for (int i = 0; i < 4; i++)
            {
                if (i > 0) key.Append('-');
                for (int j = 0; j < 4; j++)
                {
                    key.Append((char)('A' + random.Next(26)));
                }
            }
            
            return key.ToString();
        }

        private string MaskLicenseKey(string licenseKey)
        {
            if (string.IsNullOrEmpty(licenseKey) || licenseKey.Length < 8)
                return "***";

            return licenseKey.Substring(0, 4) + "****" + licenseKey.Substring(licenseKey.Length - 4);
        }

        private LicenseActivationError MapValidationErrorToActivationError(LicenseValidationError? error)
        {
            return error switch
            {
                LicenseValidationError.InvalidKey => LicenseActivationError.InvalidKey,
                LicenseValidationError.Expired => LicenseActivationError.InvalidKey,
                LicenseValidationError.MaxActivationsReached => LicenseActivationError.MaxActivationsReached,
                LicenseValidationError.NetworkError => LicenseActivationError.NetworkError,
                LicenseValidationError.ServerError => LicenseActivationError.ServerError,
                _ => LicenseActivationError.None
            };
        }

        private byte[] EncryptLicenseData(string data)
        {
            // تشفير بسيط للبيانات (في الإنتاج، استخدم تشفير أقوى)
            var key = Encoding.UTF8.GetBytes("FishFarm2024Key!");
            using var aes = Aes.Create();
            aes.Key = key;
            aes.GenerateIV();

            using var encryptor = aes.CreateEncryptor();
            var plainBytes = Encoding.UTF8.GetBytes(data);
            var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);

            var result = new byte[aes.IV.Length + encryptedBytes.Length];
            Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
            Array.Copy(encryptedBytes, 0, result, aes.IV.Length, encryptedBytes.Length);

            return result;
        }

        private string DecryptLicenseData(byte[] encryptedData)
        {
            // فك تشفير البيانات
            var key = Encoding.UTF8.GetBytes("FishFarm2024Key!");
            using var aes = Aes.Create();
            aes.Key = key;

            var iv = new byte[16];
            var encryptedBytes = new byte[encryptedData.Length - 16];
            
            Array.Copy(encryptedData, 0, iv, 0, 16);
            Array.Copy(encryptedData, 16, encryptedBytes, 0, encryptedBytes.Length);
            
            aes.IV = iv;

            using var decryptor = aes.CreateDecryptor();
            var decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);
            
            return Encoding.UTF8.GetString(decryptedBytes);
        }

        #endregion

        #region Periodic License Check

        /// <summary>
        /// تهيئة التحقق الدوري من الترخيص
        /// Initialize periodic license check
        /// </summary>
        private void InitializeLicenseCheck()
        {
            try
            {
                _licenseCheckTimer = new System.Timers.Timer(3600000); // كل ساعة
                _licenseCheckTimer.Elapsed += async (sender, e) => await CheckLicenseStatusPeriodically();
                _licenseCheckTimer.AutoReset = true;
                _licenseCheckTimer.Start();

                _logger.LogInformation("تم تهيئة التحقق الدوري من الترخيص");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة التحقق الدوري من الترخيص");
            }
        }

        /// <summary>
        /// التحقق الدوري من حالة الترخيص
        /// Periodic license status check
        /// </summary>
        private async Task CheckLicenseStatusPeriodically()
        {
            try
            {
                var currentLicense = GetLocalLicense();
                if (currentLicense != null)
                {
                    if (!currentLicense.IsValid())
                    {
                        _logger.LogWarning("الترخيص منتهي الصلاحية أو غير صالح");
                        // يمكن إضافة إشعار للمستخدم هنا
                    }
                    else if (currentLicense.ExpiresSoon(7))
                    {
                        _logger.LogInformation("الترخيص سينتهي قريباً: {Days} أيام متبقية", currentLicense.GetRemainingDays());
                        // يمكن إضافة تنبيه للمستخدم هنا
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق الدوري من الترخيص");
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _licenseCheckTimer?.Stop();
            _licenseCheckTimer?.Dispose();
        }

        #endregion
    }
}