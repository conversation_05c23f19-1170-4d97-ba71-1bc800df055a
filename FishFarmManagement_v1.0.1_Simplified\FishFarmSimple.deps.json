{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"FishFarmSimple/1.0.0": {"dependencies": {"FishFarmManagement.BLL": "1.0.0", "FishFarmManagement.DAL": "1.0.0", "FishFarmManagement.Models": "1.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.1", "Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Json": "9.0.1", "Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Hosting": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Console": "9.0.1", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "8.0.18"}, "runtime": {"FishFarmSimple.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.18": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "********", "fileVersion": "13.0.1825.31117"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "8.0.1825.31117"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "8.0.1825.31117"}, "clretwrc.dll": {"fileVersion": "8.0.1825.31117"}, "clrgc.dll": {"fileVersion": "8.0.1825.31117"}, "clrjit.dll": {"fileVersion": "8.0.1825.31117"}, "coreclr.dll": {"fileVersion": "8.0.1825.31117"}, "createdump.exe": {"fileVersion": "8.0.1825.31117"}, "hostfxr.dll": {"fileVersion": "8.0.1825.31117"}, "hostpolicy.dll": {"fileVersion": "8.0.1825.31117"}, "mscordaccore.dll": {"fileVersion": "8.0.1825.31117"}, "mscordaccore_amd64_amd64_8.0.1825.31117.dll": {"fileVersion": "8.0.1825.31117"}, "mscordbi.dll": {"fileVersion": "8.0.1825.31117"}, "mscorrc.dll": {"fileVersion": "8.0.1825.31117"}, "msquic.dll": {"fileVersion": "*******"}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "BCrypt.Net-Next/4.0.3": {"runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FluentValidation/11.8.0": {"runtime": {"lib/net7.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "********"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.Sqlite.Core/9.0.1": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.1", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.1": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.1": {}, "Microsoft.EntityFrameworkCore.Relational/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyModel": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.1"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.1": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.1", "Microsoft.EntityFrameworkCore.Relational": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyModel": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore.Tools/8.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Caching.Memory/9.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Physical": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Json/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Json": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Physical": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.DependencyModel/9.0.1": {"dependencies": {"System.Text.Encodings.Web": "9.0.1", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "9.0.0.1", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Diagnostics/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "System.Diagnostics.DiagnosticSource": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileSystemGlobbing": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Hosting/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "9.0.1", "Microsoft.Extensions.Configuration.CommandLine": "9.0.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.1", "Microsoft.Extensions.Configuration.Json": "9.0.1", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.1", "Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Diagnostics": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Physical": "9.0.1", "Microsoft.Extensions.Hosting.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Configuration": "9.0.1", "Microsoft.Extensions.Logging.Console": "9.0.1", "Microsoft.Extensions.Logging.Debug": "9.0.1", "Microsoft.Extensions.Logging.EventLog": "9.0.1", "Microsoft.Extensions.Logging.EventSource": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "System.Diagnostics.DiagnosticSource": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.Console/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Configuration": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.Debug/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "System.Diagnostics.EventLog": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Options/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Primitives/9.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.core/2.1.10": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"native": {"runtimes/win-x64/native/e_sqlite3.dll": {"fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Diagnostics.DiagnosticSource/9.0.1": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.Diagnostics.EventLog/9.0.1": {"runtime": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.IO.Pipelines/9.0.1": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.Memory/4.5.3": {}, "System.Text.Encodings.Web/9.0.1": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.Text.Json/9.0.1": {"dependencies": {"System.IO.Pipelines": "9.0.1", "System.Text.Encodings.Web": "9.0.1"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "FishFarmManagement.BLL/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "FishFarmManagement.DAL": "1.0.0", "FishFarmManagement.Models": "1.0.0", "FluentValidation": "11.8.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.1"}, "runtime": {"FishFarmManagement.BLL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FishFarmManagement.DAL/1.0.0": {"dependencies": {"FishFarmManagement.Models": "1.0.0", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.1", "Microsoft.EntityFrameworkCore.Tools": "8.0.0"}, "runtime": {"FishFarmManagement.DAL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FishFarmManagement.Models/1.0.0": {"dependencies": {"BCrypt.Net-Next": "4.0.3", "System.ComponentModel.Annotations": "5.0.0"}, "runtime": {"FishFarmManagement.Models.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"FishFarmSimple/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.18": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "BCrypt.Net-Next/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "path": "bcrypt.net-next/4.0.3", "hashPath": "bcrypt.net-next.4.0.3.nupkg.sha512"}, "FluentValidation/11.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-TiEaaYqSIAS+wXQv46LRm8Mca092XYNePD0n231Hqlkmm5FuToZoWnjI1ef/vJUxBzqR7/k2vaVQXxijNgVKRQ==", "path": "fluentvalidation/11.8.0", "hashPath": "fluentvalidation.11.8.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-useMNbAupB8gpEp/SjanW3LvvyFG9DWPMUcXFwVNjNuFWIxNcrs5zOu9BTmNJEyfDpLlrsSBmcBv7keYVG8UhA==", "path": "microsoft.data.sqlite.core/9.0.1", "hashPath": "microsoft.data.sqlite.core.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-E25w4XugXNykTr5Y/sLDGaQ4lf67n9aXVPvsdGsIZjtuLmbvb9AoYP8D50CDejY8Ro4D9GK2kNHz5lWHqSK+wg==", "path": "microsoft.entityframeworkcore/9.0.1", "hashPath": "microsoft.entityframeworkcore.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qy+taGVLUs82zeWfc32hgGL8Z02ZqAneYvqZiiXbxF4g4PBUcPRuxHM9K20USmpeJbn4/fz40GkCbyyCy5ojOA==", "path": "microsoft.entityframeworkcore.abstractions/9.0.1", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-c6ZZJZhPKrXFkE2z/81PmuT69HBL6Y68Cl0xJ5SRrDjJyq5Aabkq15yCqPg9RQ3R0aFLVaJok2DA8R3TKpejDQ==", "path": "microsoft.entityframeworkcore.analyzers/9.0.1", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-7Iu0h4oevRvH4IwPzmxuIJGYRt55TapoREGlluk75KCO7lenN0+QnzCl6cQDY48uDoxAUpJbpK2xW7o8Ix69dw==", "path": "microsoft.entityframeworkcore.relational/9.0.1", "hashPath": "microsoft.entityframeworkcore.relational.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rt8P3/rJClgwlebCzAXdFt5/TemuP5IqBXLIjE2ZeJgaaDezPt9g8Pk3dqUj8YXb4pKcrFvuzZylYMZLCZWJzA==", "path": "microsoft.entityframeworkcore.sqlite/9.0.1", "hashPath": "microsoft.entityframeworkcore.sqlite.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-eAo/tMaOLN2KI3KYwnhl9Ibmtry3gdRpVxdSxzyFiS1q8zvPNKtHU+fi1723JyuQEhUGpp551aQZIKGMmenk+Q==", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.1", "hashPath": "microsoft.entityframeworkcore.sqlite.core.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zRdaXiiB1gEA0b+AJTd2+drh78gkEA4HyZ1vqNZrKq4xwW8WwavSiQsoeb1UsIMZkocLMBbhQYWClkZzuTKEgQ==", "path": "microsoft.entityframeworkcore.tools/8.0.0", "hashPath": "microsoft.entityframeworkcore.tools.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Eghsg9SyIvq0c8x6cUpe71BbQoOmsytXxqw2+ZNiTnP8a8SBLKgEor1zZeWhC0588IbS2M0PP4gXGAd9qF862Q==", "path": "microsoft.extensions.caching.abstractions/9.0.1", "hashPath": "microsoft.extensions.caching.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Je<PERSON>+PP0BCKMwwLezPGDaciJSTfcFG4KjsG8rX4XZ6RSvzdxofrFmcnmW2L4+cWUcZSBTQ+Dd7H5Gs9XZz/OlCA==", "path": "microsoft.extensions.caching.memory/9.0.1", "hashPath": "microsoft.extensions.caching.memory.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-VuthqFS+ju6vT8W4wevdhEFiRi1trvQtkzWLonApfF5USVzzDcTBoY3F24WvN/tffLSrycArVfX1bThm/9xY2A==", "path": "microsoft.extensions.configuration/9.0.1", "hashPath": "microsoft.extensions.configuration.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+4hfFIY1UjBCXFTTOd+ojlDPq6mep3h5Vq5SYE3Pjucr7dNXmq4S/6P/LoVnZFz2e/5gWp/om4svUFgznfULcA==", "path": "microsoft.extensions.configuration.abstractions/9.0.1", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-w7kAyu1Mm7eParRV6WvGNNwA8flPTub16fwH49h7b/yqJZFTgYxnOVCuiah3G2bgseJMEq4DLjjsyQRvsdzRgA==", "path": "microsoft.extensions.configuration.binder/9.0.1", "hashPath": "microsoft.extensions.configuration.binder.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5WC1OsXfljC1KHEyL0yefpAyt1UZjrZ0/xyOqFowc5VntbE79JpCYOTSYFlxEuXm3Oq5xsgU2YXeZLTgAAX+DA==", "path": "microsoft.extensions.configuration.commandline/9.0.1", "hashPath": "microsoft.extensions.configuration.commandline.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5HShUdF8KFAUSzoEu0DOFbX09FlcFtHxEalowyjM7Kji0EjdF0DLjHajb2IBvoqsExAYox+Z2GfbfGF7dH7lKQ==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.1", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-QBOI8YVAyKqeshYOyxSe6co22oag431vxMu5xQe1EjXMkYE4xK4J71xLCW3/bWKmr9Aoy1VqGUARSLFnotk4Bg==", "path": "microsoft.extensions.configuration.fileextensions/9.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-z+g+lgPET1JRDjsOkFe51rkkNcnJgvOK5UIpeTfF1iAi0GkBJz5/yUuTa8a9V8HUh4gj4xFT5WGoMoXoSDKfGg==", "path": "microsoft.extensions.configuration.json/9.0.1", "hashPath": "microsoft.extensions.configuration.json.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-esGPOgLZ1tZddEomexhrU+LJ5YIsuJdkh0tU7r4WVpNZ15dLuMPqPW4Xe4txf3T2PDUX2ILe3nYQEDjZjfSEJg==", "path": "microsoft.extensions.configuration.usersecrets/9.0.1", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qZI42ASAe3hr2zMSA6UjM92pO1LeDq5DcwkgSowXXPY8I56M76pEKrnmsKKbxagAf39AJxkH2DY4sb72ixyOrg==", "path": "microsoft.extensions.dependencyinjection/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Tr74eP0oQ3AyC24ch17N8PuEkrPbD0JqIfENCYqmgKYNOmL8wQKzLJu3ObxTUDrjnn4rHoR1qKa37/eQyHmCDA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FHPy9cbb0y09riEpsrU5XYpOgf4nTfHj7a0m1wLC5DosGtjJn9g03gGg1GTJmEdRFBQrJwbwTnHqLCdNLsoYgA==", "path": "microsoft.extensions.dependencymodel/9.0.1", "hashPath": "microsoft.extensions.dependencymodel.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4ZmP6turxMFsNwK/MCko2fuIITaYYN/eXyyIRq1FjLDKnptdbn6xMb7u0zfSMzCGpzkx4RxH/g1jKN2IchG7uA==", "path": "microsoft.extensions.diagnostics/9.0.1", "hashPath": "microsoft.extensions.diagnostics.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pfAPuVtHvG6dvZtAa0OQbXdDqq6epnr8z0/IIUjdmV0tMeI8Aj9KxDXvdDvqr+qNHTkmA7pZpChNxwNZt4GXVg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.1", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-DguZYt1DWL05+8QKWL3b6bW7A2pC5kYFMY5iXM6W2M23jhvcNa8v6AU8PvVJBcysxHwr9/jax0agnwoBumsSwg==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.1", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-TKDMNRS66UTMEVT38/tU9hA63UTMvzI3DyNm5mx8+JCf3BaOtxgrvWLCI1y3J52PzT5yNl/T2KN5Z0KbApLZcg==", "path": "microsoft.extensions.fileproviders.physical/9.0.1", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Mxcp9NXuQMvAnudRZcgIb5SqlWrlullQzntBLTwuv0MPIJ5LqiGwbRqiyxgdk+vtCoUkplb0oXy5kAw1t469Ug==", "path": "microsoft.extensions.filesystemglobbing/9.0.1", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-3wZNcVvC8RW44HDqqmIq+BqF5pgmTQdbNdR9NyYw33JSMnJuclwoJ2PEkrJ/KvD1U/hmqHVL3l5If+Hn3D1fWA==", "path": "microsoft.extensions.hosting/9.0.1", "hashPath": "microsoft.extensions.hosting.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CwSMhLNe8HLkfbFzdz0CHWJhtWH3TtfZSicLBd/itFD+NqQtfGHmvqXHQbaFFl3mQB5PBb2gxwzWQyW2pIj7PA==", "path": "microsoft.extensions.hosting.abstractions/9.0.1", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-E/k5r7S44DOW+08xQPnNbO8DKAQHhkspDboTThNJ6Z3/QBb4LC6gStNWzVmy3IvW7sUD+iJKf4fj0xEkqE7vnQ==", "path": "microsoft.extensions.logging/9.0.1", "hashPath": "microsoft.extensions.logging.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-w2gUqXN/jNIuvqYwX3lbXagsizVNXYyt6LlF57+tMve4JYCEgCMMAjRce6uKcDASJgpMbErRT1PfHy2OhbkqEA==", "path": "microsoft.extensions.logging.abstractions/9.0.1", "hashPath": "microsoft.extensions.logging.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-MeZePlyu3/74Wk4FHYSzXijADJUhWa7gxtaphLxhS8zEPWdJuBCrPo0sezdCSZaKCL+cZLSLobrb7xt2zHOxZQ==", "path": "microsoft.extensions.logging.configuration/9.0.1", "hashPath": "microsoft.extensions.logging.configuration.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-YUzguHYlWfp4upfYlpVe3dnY59P25wc+/YLJ9/NQcblT3EvAB1CObQulClll7NtnFbbx4Js0a0UfyS8SbRsWXQ==", "path": "microsoft.extensions.logging.console/9.0.1", "hashPath": "microsoft.extensions.logging.console.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pzdyibIV8k4sym0Sszcp2MJCuXrpOGs9qfOvY+hCRu8k4HbdVoeKOLnacxHK6vEPITX5o5FjjsZW2zScLXTjYA==", "path": "microsoft.extensions.logging.debug/9.0.1", "hashPath": "microsoft.extensions.logging.debug.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+a4RlbwFWjsMujNNhf1Jy9Nm5CpMT+nxXxfgrkRSloPo0OAWhPSPsrFo6VWpvgIPPS41qmfAVWr3DqAmOoVZgQ==", "path": "microsoft.extensions.logging.eventlog/9.0.1", "hashPath": "microsoft.extensions.logging.eventlog.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-d47ZRZUOg1dGOX+yisWScQ7w4+92OlR9beS2UXaiadUCA3RFoZzobzVgrzBX7Oo/qefx9LxdRcaeFpWKb3BNBw==", "path": "microsoft.extensions.logging.eventsource/9.0.1", "hashPath": "microsoft.extensions.logging.eventsource.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nggoNKnWcsBIAaOWHA+53XZWrslC7aGeok+aR+epDPRy7HI7GwMnGZE8yEsL2Onw7kMOHVHwKcsDls1INkNUJQ==", "path": "microsoft.extensions.options/9.0.1", "hashPath": "microsoft.extensions.options.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-8RRKWtuU4fR+8MQLR/8CqZwZ9yc2xCpllw/WPRY7kskIqEq0hMcEI4AfUJO72yGiK2QJkrsDcUvgB5Yc+3+lyg==", "path": "microsoft.extensions.options.configurationextensions/9.0.1", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bHtTesA4lrSGD1ZUaMIx6frU3wyy0vYtTa/hM6gGQu5QNrydObv8T5COiGUWsisflAfmsaFOe9Xvw5NSO99z0g==", "path": "microsoft.extensions.primitives/9.0.1", "hashPath": "microsoft.extensions.primitives.9.0.1.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-yOcDWx4P/s1I83+7gQlgQLmhny2eNcU0cfo1NBWi+en4EAI38Jau+/neT85gUW6w1s7+FUJc2qNOmmwGLIREqA==", "path": "system.diagnostics.diagnosticsource/9.0.1", "hashPath": "system.diagnostics.diagnosticsource.9.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-iVnDpgYJsRaRFnk77kcLA3+913WfWDtnAKrQl9tQ5ahqKANTaJKmQdsuPWWiAPWE9pk1Kj4Pg9JGXWfFYYyakQ==", "path": "system.diagnostics.eventlog/9.0.1", "hashPath": "system.diagnostics.eventlog.9.0.1.nupkg.sha512"}, "System.IO.Pipelines/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uXf5o8eV/gtzDQY4lGROLFMWQvcViKcF8o4Q6KpIOjloAQXrnscQSu6gTxYJMHuNJnh7szIF9AzkaEq+zDLoEg==", "path": "system.io.pipelines/9.0.1", "hashPath": "system.io.pipelines.9.0.1.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XkspqduP2t1e1x2vBUAD/xZ5ZDvmywuUwsmB93MvyQLospJfqtX0GsR/kU0vUL2h4kmvf777z3txV2W4NrQ9Qg==", "path": "system.text.encodings.web/9.0.1", "hashPath": "system.text.encodings.web.9.0.1.nupkg.sha512"}, "System.Text.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-eqWHDZqYPv1PvuvoIIx5pF74plL3iEOZOl/0kQP+Y0TEbtgNnM2W6k8h8EPYs+LTJZsXuWa92n5W5sHTWvE3VA==", "path": "system.text.json/9.0.1", "hashPath": "system.text.json.9.0.1.nupkg.sha512"}, "FishFarmManagement.BLL/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FishFarmManagement.DAL/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FishFarmManagement.Models/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}, "runtimes": {"win-x64": ["win", "any", "base"]}}