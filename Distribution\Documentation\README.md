# نظام إدارة مزرعة الأسماك المتكامل
## Fish Farm Management System

### 📋 نظرة عامة
نظام متكامل لإدارة ومحاسبة مزارع الأسماك مطور بلغة C# مع قاعدة بيانات SQLite وواجهة Windows Forms. يوفر النظام حلولاً شاملة لإدارة الأحواض، الدورات الإنتاجية، الموظفين، والمحاسبة المالية.

### 🎯 الميزات الرئيسية

#### 🐟 إدارة الأحواض
- تسجيل بيانات كل حوض (رقم الحوض، عدد الأسماك، متوسط الوزن)
- تتبع تواريخ التربية والحصاد المتوقع
- إدارة استهلاك العلف وتسجيل الأسماك النافقة
- تتبع الأدوية والإضافات المستخدمة
- توليد طلبات شراء العلف تلقائياً

#### 🔄 إدارة الدورات الإنتاجية
- إنشاء دورات إنتاجية مدتها 7 أشهر (قابلة للتعديل)
- ربط كل دورة بمركز تكلفة محاسبي
- إدارة تواريخ البداية والنهاية مع إمكانية التمديد

#### 👥 شؤون العاملين
- تسجيل بيانات الموظفين الكاملة
- إدارة الرواتب الشهرية مع الخصومات والإضافات
- ربط الرواتب بمراكز التكلفة

#### 💰 النظام المحاسبي المتكامل
- تسجيل جميع المعاملات المالية
- استخدام كل دورة كمركز تكلفة
- توليد التقارير المحاسبية والمالية
- دعم القيود اليدوية والتحقق من التوازن

#### 🏢 إدارة معلومات المزرعة
- إدخال وتحديث بيانات المزرعة
- دعم تحميل الشعار والهوية البصرية

### 🏗️ الهيكلية المعمارية

النظام يتبع معمارية الطبقات (Layered Architecture):

```
┌─────────────────────────────────────┐
│        Presentation Layer (UI)      │
│     Windows Forms Interface         │
├─────────────────────────────────────┤
│      Business Logic Layer (BLL)     │
│    Services & Business Rules        │
├─────────────────────────────────────┤
│     Data Access Layer (DAL)         │
│   Entity Framework & Repositories   │
├─────────────────────────────────────┤
│         Database Layer              │
│          SQLite Database            │
└─────────────────────────────────────┘
```

### 🗃️ قاعدة البيانات

#### الجداول الرئيسية:
- **FarmInfo**: معلومات المزرعة
- **ProductionCycles**: الدورات الإنتاجية
- **Ponds**: الأحواض
- **FeedTypes & FeedConsumption**: أنواع العلف واستهلاكه
- **FishMortality**: نفوق الأسماك
- **Medications & PondMedications**: الأدوية
- **Employees & Payroll**: الموظفين والرواتب
- **Accounts & Transactions**: النظام المحاسبي
- **CostCenters**: مراكز التكلفة
- **Inventory**: المخزون

### 🛠️ التقنيات المستخدمة

- **اللغة**: C# (.NET 8.0)
- **قاعدة البيانات**: SQLite
- **ORM**: Entity Framework Core
- **واجهة المستخدم**: Windows Forms
- **حقن التبعيات**: Microsoft.Extensions.DependencyInjection
- **التسجيل**: Serilog
- **التحقق من الصحة**: FluentValidation

### 📦 متطلبات النظام

- Windows 10/11
- .NET 8.0 Runtime
- 100 MB مساحة فارغة على القرص الصلب
- 2 GB RAM (الحد الأدنى)

### 🚀 التثبيت والتشغيل

1. **استنساخ المشروع**:
```bash
git clone https://github.com/your-repo/fish-farm-management.git
cd fish-farm-management
```

2. **بناء المشروع**:
```bash
dotnet restore
dotnet build
```

3. **تشغيل التطبيق**:
```bash
dotnet run --project FishFarmManagement
```

### ⚙️ الإعدادات

يمكن تخصيص النظام من خلال ملف `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=FishFarmDatabase.db;Cache=Shared"
  },
  "Application": {
    "Language": "ar",
    "AutoBackup": {
      "Enabled": true,
      "IntervalHours": 24
    }
  }
}
```

### 📊 التقارير المتاحة

- تقارير الإنتاج والأداء
- التقارير المالية والمحاسبية
- تقارير الموظفين والرواتب
- تحليل الربحية لكل دورة
- مقارنة الدورات الإنتاجية

### 🔒 الأمان والنسخ الاحتياطي

- نسخ احتياطي تلقائي يومي
- إمكانية النسخ الاحتياطي اليدوي
- استعادة البيانات من النسخ الاحتياطية
- تحسين قاعدة البيانات التلقائي

### 🌐 دعم اللغات

- العربية (افتراضي)
- الإنجليزية
- واجهة قابلة للتوطين

### 👨‍💻 المطور

**طارق حسين صالح**
- البريد الإلكتروني: <EMAIL>
- المشرف على المشروع

### 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

### 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [CONTRIBUTING.md](CONTRIBUTING.md) للحصول على تفاصيل حول عملية التطوير.

### 📝 سجل التغييرات

راجع [CHANGELOG.md](CHANGELOG.md) لمعرفة التحديثات والتغييرات في كل إصدار.

### 🆘 الدعم

للحصول على الدعم:
1. راجع [الوثائق](docs/)
2. ابحث في [القضايا المفتوحة](issues)
3. أنشئ [قضية جديدة](issues/new)
4. راسل المطور مباشرة

### 🔮 الخطط المستقبلية

- [ ] تطوير واجهة ويب
- [ ] دعم قواعد بيانات أخرى
- [ ] تطبيق محمول
- [ ] تكامل مع أنظمة خارجية
- [ ] ذكاء اصطناعي للتنبؤات

---

**© 2024 طارق حسين صالح - جميع الحقوق محفوظة**
