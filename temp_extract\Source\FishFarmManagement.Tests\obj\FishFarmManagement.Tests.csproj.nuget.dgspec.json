{"format": 1, "restore": {"F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Tests\\FishFarmManagement.Tests.csproj": {}}, "projects": {"F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.BLL\\FishFarmManagement.BLL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.BLL\\FishFarmManagement.BLL.csproj", "projectName": "FishFarmManagement.BLL", "projectPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.BLL\\FishFarmManagement.BLL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.BLL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.DAL\\FishFarmManagement.DAL.csproj": {"projectPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.DAL\\FishFarmManagement.DAL.csproj"}, "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj": {"projectPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.303/PortableRuntimeIdentifierGraph.json"}}}, "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.DAL\\FishFarmManagement.DAL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.DAL\\FishFarmManagement.DAL.csproj", "projectName": "FishFarmManagement.DAL", "projectPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.DAL\\FishFarmManagement.DAL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.DAL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj": {"projectPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.1, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.303/PortableRuntimeIdentifierGraph.json"}}}, "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj", "projectName": "FishFarmManagement.Models", "projectPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.303/PortableRuntimeIdentifierGraph.json"}}}, "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Tests\\FishFarmManagement.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Tests\\FishFarmManagement.Tests.csproj", "projectName": "FishFarmManagement.Tests", "projectPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Tests\\FishFarmManagement.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.BLL\\FishFarmManagement.BLL.csproj": {"projectPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.BLL\\FishFarmManagement.BLL.csproj"}, "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.DAL\\FishFarmManagement.DAL.csproj": {"projectPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.DAL\\FishFarmManagement.DAL.csproj"}, "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj": {"projectPath": "F:\\account pro\\fish accounting & management\\fish accounting & management\\FishFarmManagement.Models\\FishFarmManagement.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[6.12.0, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "Moq": {"target": "Package", "version": "[4.20.69, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.6.1, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.303/PortableRuntimeIdentifierGraph.json"}}}}}