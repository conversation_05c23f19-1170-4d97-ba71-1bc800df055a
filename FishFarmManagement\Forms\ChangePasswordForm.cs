﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج تغيير كلمة المرور
    /// Change password form
    /// </summary>
    public partial class ChangePasswordForm : Form
    {
        private readonly IAuthenticationService _authenticationService;
        private readonly ILogger<ChangePasswordForm> _logger;
        private readonly User _user;
        private readonly bool _isForced;

        // Controls
        private Label _titleLabel;
        private Label _instructionLabel;
        private Label _currentPasswordLabel;
        private TextBox _currentPasswordTextBox;
        private Label _newPasswordLabel;
        private TextBox _newPasswordTextBox;
        private Label _confirmPasswordLabel;
        private TextBox _confirmPasswordTextBox;
        private Button _changeButton;
        private Button _cancelButton;
        private Label _statusLabel;

        public ChangePasswordForm(IServiceProvider serviceProvider, User user, bool isForced = false)
        {
            _authenticationService = serviceProvider.GetRequiredService<IAuthenticationService>();
            _logger = serviceProvider.GetRequiredService<ILogger<ChangePasswordForm>>();
            _user = user ?? throw new ArgumentNullException(nameof(user));
            _isForced = isForced;

            InitializeComponent();
            SetupForm();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "تغيير كلمة المرور";
            this.Size = new Size(400, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Title label
            _titleLabel = new Label
            {
                Text = _isForced ? "يجب تغيير كلمة المرور" : "تغيير كلمة المرور",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = _isForced ? Color.Red : Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40
            };

            // Instruction label
            _instructionLabel = new Label
            {
                Text = _isForced 
                    ? "لأسباب أمنية، يجب عليك تغيير كلمة المرور الافتراضية قبل المتابعة."
                    : "أدخل كلمة المرور الحالية والجديدة.",
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.DarkGray,
                TextAlign = ContentAlignment.MiddleCenter,
                Location = new Point(20, 50),
                Size = new Size(360, 40),
                AutoSize = false
            };

            // Current password
            _currentPasswordLabel = new Label
            {
                Text = "كلمة المرور الحالية:",
                Location = new Point(20, 100),
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            _currentPasswordTextBox = new TextBox
            {
                Location = new Point(150, 100),
                Size = new Size(200, 23),
                UseSystemPasswordChar = true,
                RightToLeft = RightToLeft.No
            };

            // New password
            _newPasswordLabel = new Label
            {
                Text = "كلمة المرور الجديدة:",
                Location = new Point(20, 140),
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            _newPasswordTextBox = new TextBox
            {
                Location = new Point(150, 140),
                Size = new Size(200, 23),
                UseSystemPasswordChar = true,
                RightToLeft = RightToLeft.No
            };

            // Confirm password
            _confirmPasswordLabel = new Label
            {
                Text = "تأكيد كلمة المرور:",
                Location = new Point(20, 180),
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            _confirmPasswordTextBox = new TextBox
            {
                Location = new Point(150, 180),
                Size = new Size(200, 23),
                UseSystemPasswordChar = true,
                RightToLeft = RightToLeft.No
            };

            // Status label
            _statusLabel = new Label
            {
                Location = new Point(20, 220),
                Size = new Size(360, 23),
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Color.Red
            };

            // Buttons
            _changeButton = new Button
            {
                Text = "تغيير كلمة المرور",
                Location = new Point(150, 260),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            _changeButton.FlatAppearance.BorderSize = 0;
            _changeButton.Click += ChangeButton_Click;

            _cancelButton = new Button
            {
                Text = _isForced ? "إنهاء البرنامج" : "إلغاء",
                Location = new Point(280, 260),
                Size = new Size(100, 30),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            _cancelButton.FlatAppearance.BorderSize = 0;
            _cancelButton.Click += CancelButton_Click;

            // Add controls
            this.Controls.AddRange(new Control[]
            {
                _titleLabel,
                _instructionLabel,
                _currentPasswordLabel,
                _currentPasswordTextBox,
                _newPasswordLabel,
                _newPasswordTextBox,
                _confirmPasswordLabel,
                _confirmPasswordTextBox,
                _statusLabel,
                _changeButton,
                _cancelButton
            });

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            // Set focus to current password
            _currentPasswordTextBox.Focus();

            // Handle Enter key
            this.KeyPreview = true;
            this.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    ChangeButton_Click(this, EventArgs.Empty);
                }
                else if (e.KeyCode == Keys.Escape && !_isForced)
                {
                    CancelButton_Click(this, EventArgs.Empty);
                }
            };

            // If forced, disable close button
            if (_isForced)
            {
                this.ControlBox = false;
            }
        }

        private async void ChangeButton_Click(object sender, EventArgs e)
        {
            try
            {
                SetControlsEnabled(false);
                ShowStatus("جاري تغيير كلمة المرور...", Color.Blue);

                // Validate inputs
                if (!ValidateInputs())
                {
                    return;
                }

                // Change password
                var success = await _authenticationService.ChangePasswordAsync(
                    _user.Id, 
                    _currentPasswordTextBox.Text, 
                    _newPasswordTextBox.Text);

                if (success)
                {
                    ShowStatus("تم تغيير كلمة المرور بنجاح", Color.Green);
                    await Task.Delay(1000); // Show success message briefly
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    ShowStatus("كلمة المرور الحالية غير صحيحة", Color.Red);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير كلمة المرور");
                ShowStatus("حدث خطأ أثناء تغيير كلمة المرور", Color.Red);
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            if (_isForced)
            {
                // If password change is forced, exit the application
                Application.Exit();
            }
            else
            {
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
        }

        private bool ValidateInputs()
        {
            if (string.IsNullOrWhiteSpace(_currentPasswordTextBox.Text))
            {
                ShowStatus("يرجى إدخال كلمة المرور الحالية", Color.Red);
                _currentPasswordTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(_newPasswordTextBox.Text))
            {
                ShowStatus("يرجى إدخال كلمة المرور الجديدة", Color.Red);
                _newPasswordTextBox.Focus();
                return false;
            }

            if (_newPasswordTextBox.Text.Length < 6)
            {
                ShowStatus("كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل", Color.Red);
                _newPasswordTextBox.Focus();
                return false;
            }

            if (_newPasswordTextBox.Text != _confirmPasswordTextBox.Text)
            {
                ShowStatus("كلمة المرور الجديدة وتأكيدها غير متطابقتين", Color.Red);
                _confirmPasswordTextBox.Focus();
                return false;
            }

            if (_currentPasswordTextBox.Text == _newPasswordTextBox.Text)
            {
                ShowStatus("كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية", Color.Red);
                _newPasswordTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ShowStatus(string message, Color color)
        {
            _statusLabel.Text = message;
            _statusLabel.ForeColor = color;
        }

        private void SetControlsEnabled(bool enabled)
        {
            _currentPasswordTextBox.Enabled = enabled;
            _newPasswordTextBox.Enabled = enabled;
            _confirmPasswordTextBox.Enabled = enabled;
            _changeButton.Enabled = enabled;
            _cancelButton.Enabled = enabled;
        }
    }
}

