using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// خدمة حساب إحصائيات الدورة الإنتاجية
    /// Production cycle calculator service interface
    /// </summary>
    public interface IProductionCycleCalculatorService
    {
        /// <summary>
        /// حساب إجمالي تكلفة الدورة
        /// Calculate total cycle cost
        /// </summary>
        Task<decimal> CalculateTotalCostAsync(int cycleId);

        /// <summary>
        /// حساب إجمالي الإنتاج للدورة
        /// Calculate total cycle production
        /// </summary>
        Task<decimal> CalculateTotalProductionAsync(int cycleId);

        /// <summary>
        /// حساب الربحية المتوقعة للدورة
        /// Calculate expected cycle profitability
        /// </summary>
        Task<decimal> CalculateExpectedProfitabilityAsync(int cycleId);

        /// <summary>
        /// حساب إحصائيات الدورة الشاملة
        /// Calculate comprehensive cycle statistics
        /// </summary>
        Task<CycleStatistics> CalculateStatisticsAsync(int cycleId);

        /// <summary>
        /// حساب تكلفة العلف للدورة
        /// Calculate feed cost for cycle
        /// </summary>
        Task<decimal> CalculateFeedCostAsync(int cycleId);

        /// <summary>
        /// حساب تكلفة الأدوية للدورة
        /// Calculate medication cost for cycle
        /// </summary>
        Task<decimal> CalculateMedicationCostAsync(int cycleId);

        /// <summary>
        /// حساب تكلفة العمالة للدورة
        /// Calculate labor cost for cycle
        /// </summary>
        Task<decimal> CalculateLaborCostAsync(int cycleId);

        /// <summary>
        /// حساب تكلفة التشغيل للدورة
        /// Calculate operational cost for cycle
        /// </summary>
        Task<decimal> CalculateOperationalCostAsync(int cycleId);

        /// <summary>
        /// حساب معدل النمو للدورة
        /// Calculate growth rate for cycle
        /// </summary>
        Task<decimal> CalculateGrowthRateAsync(int cycleId);

        /// <summary>
        /// حساب معدل الوفيات للدورة
        /// Calculate mortality rate for cycle
        /// </summary>
        Task<decimal> CalculateMortalityRateAsync(int cycleId);

        /// <summary>
        /// حساب معدل تحويل العلف للدورة
        /// Calculate feed conversion ratio for cycle
        /// </summary>
        Task<decimal> CalculateFeedConversionRatioAsync(int cycleId);

        /// <summary>
        /// حساب الإيرادات المتوقعة للدورة
        /// Calculate expected revenue for cycle
        /// </summary>
        Task<decimal> CalculateExpectedRevenueAsync(int cycleId);

        /// <summary>
        /// حساب هامش الربح للدورة
        /// Calculate profit margin for cycle
        /// </summary>
        Task<decimal> CalculateProfitMarginAsync(int cycleId);

        /// <summary>
        /// حساب العائد على الاستثمار للدورة
        /// Calculate return on investment for cycle
        /// </summary>
        Task<decimal> CalculateROIAsync(int cycleId);

        /// <summary>
        /// حساب الكفاءة الإنتاجية للدورة
        /// Calculate production efficiency for cycle
        /// </summary>
        Task<decimal> CalculateProductionEfficiencyAsync(int cycleId);

        /// <summary>
        /// حساب التكلفة لكل كيلوجرام منتج
        /// Calculate cost per kilogram produced
        /// </summary>
        Task<decimal> CalculateCostPerKgAsync(int cycleId);

        /// <summary>
        /// حساب متوسط وزن السمك في الدورة
        /// Calculate average fish weight in cycle
        /// </summary>
        Task<decimal> CalculateAverageFishWeightAsync(int cycleId);

        /// <summary>
        /// حساب الكثافة السمكية في الأحواض
        /// Calculate fish density in ponds
        /// </summary>
        Task<decimal> CalculateFishDensityAsync(int cycleId);

        /// <summary>
        /// حساب استهلاك العلف اليومي
        /// Calculate daily feed consumption
        /// </summary>
        Task<decimal> CalculateDailyFeedConsumptionAsync(int cycleId);

        /// <summary>
        /// حساب معدل النمو اليومي
        /// Calculate daily growth rate
        /// </summary>
        Task<decimal> CalculateDailyGrowthRateAsync(int cycleId);
    }

    /// <summary>
    /// إحصائيات الدورة الإنتاجية
    /// Production cycle statistics
    /// </summary>
    public class CycleStatistics
    {
        public int CycleId { get; set; }
        public string CycleName { get; set; } = string.Empty;
        public int TotalPonds { get; set; }
        public int ActivePonds { get; set; }
        public int TotalFishCount { get; set; }
        public decimal TotalBiomass { get; set; }
        public decimal TotalCost { get; set; }
        public decimal EstimatedRevenue { get; set; }
        public decimal EstimatedProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public int DaysElapsed { get; set; }
        public int DaysRemaining { get; set; }
        public decimal CompletionPercentage { get; set; }
        public decimal FeedCost { get; set; }
        public decimal MedicationCost { get; set; }
        public decimal LaborCost { get; set; }
        public decimal OperationalCost { get; set; }
        public decimal GrowthRate { get; set; }
        public decimal MortalityRate { get; set; }
        public decimal FeedConversionRatio { get; set; }
        public decimal ROI { get; set; }
        public decimal ProductionEfficiency { get; set; }
        public decimal CostPerKg { get; set; }
        public decimal AverageFishWeight { get; set; }
        public decimal FishDensity { get; set; }
        public decimal DailyFeedConsumption { get; set; }
        public decimal DailyGrowthRate { get; set; }
    }
}
