﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج إضافة/تعديل الموظفين
    /// Employee add/edit form
    /// </summary>
    public partial class EmployeeAddEditForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;
        private readonly Employee? _existingEmployee;
        private readonly bool _isEditMode;

        // UI Controls
        private TextBox fullNameTextBox;
        private TextBox nationalityTextBox;
        private TextBox residenceNumberTextBox;
        private TextBox positionTextBox;
        private DateTimePicker joinDatePicker;
        private DateTimePicker leaveDatePicker;
        private CheckBox hasLeaveDateCheckBox;
        private NumericUpDown baseSalaryNumericUpDown;
        private ComboBox statusComboBox;
        private TextBox phoneTextBox;
        private TextBox emailTextBox;
        private TextBox addressTextBox;
        private TextBox nationalIdTextBox;
        private DateTimePicker birthDatePicker;
        private CheckBox hasBirthDateCheckBox;
        private ComboBox maritalStatusComboBox;
        private NumericUpDown numberOfChildrenNumericUpDown;
        private Button saveButton;
        private Button cancelButton;

        public EmployeeAddEditForm(IUnitOfWork unitOfWork, ILogger logger, Employee? existingEmployee = null)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _existingEmployee = existingEmployee;
            _isEditMode = existingEmployee != null;

            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = _isEditMode ? "تعديل موظف" : "إضافة موظف جديد";
            this.Size = new Size(600, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.AutoScroll = true;

            CreateControls();
            LayoutControls();
        }

        private void CreateControls()
        {
            // Full Name
            var fullNameLabel = new Label { Text = "الاسم الكامل:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            fullNameTextBox = new TextBox { Size = new Size(250, 23), MaxLength = 100 };

            // Nationality
            var nationalityLabel = new Label { Text = "الجنسية:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            nationalityTextBox = new TextBox { Size = new Size(250, 23), MaxLength = 50 };

            // Residence Number
            var residenceLabel = new Label { Text = "رقم الإقامة:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            residenceNumberTextBox = new TextBox { Size = new Size(250, 23), MaxLength = 50 };

            // Position
            var positionLabel = new Label { Text = "المنصب:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            positionTextBox = new TextBox { Size = new Size(250, 23), MaxLength = 100 };

            // Join Date
            var joinDateLabel = new Label { Text = "تاريخ الانضمام:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            joinDatePicker = new DateTimePicker { Size = new Size(250, 23), Format = DateTimePickerFormat.Short, Value = DateTime.Now };

            // Leave Date
            var leaveDateLabel = new Label { Text = "تاريخ المغادرة:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            hasLeaveDateCheckBox = new CheckBox { Text = "تحديد تاريخ المغادرة", Size = new Size(200, 23), CheckAlign = ContentAlignment.MiddleRight };
            hasLeaveDateCheckBox.CheckedChanged += HasLeaveDateCheckBox_CheckedChanged;
            leaveDatePicker = new DateTimePicker { Size = new Size(250, 23), Format = DateTimePickerFormat.Short, Value = DateTime.Now, Enabled = false };

            // Base Salary
            var baseSalaryLabel = new Label { Text = "الراتب الأساسي:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            baseSalaryNumericUpDown = new NumericUpDown { Size = new Size(250, 23), Minimum = 0, Maximum = decimal.MaxValue, DecimalPlaces = 2, ThousandsSeparator = true };

            // Status
            var statusLabel = new Label { Text = "الحالة:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            statusComboBox = new ComboBox { Size = new Size(250, 23), DropDownStyle = ComboBoxStyle.DropDownList };
            statusComboBox.Items.AddRange(new[] { "نشط", "متوقف", "مستقيل", "مفصول" });
            statusComboBox.SelectedIndex = 0;

            // Phone
            var phoneLabel = new Label { Text = "الهاتف:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            phoneTextBox = new TextBox { Size = new Size(250, 23), MaxLength = 20 };

            // Email
            var emailLabel = new Label { Text = "البريد الإلكتروني:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            emailTextBox = new TextBox { Size = new Size(250, 23), MaxLength = 100 };

            // Address
            var addressLabel = new Label { Text = "العنوان:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            addressTextBox = new TextBox { Size = new Size(350, 60), Multiline = true, ScrollBars = ScrollBars.Vertical, MaxLength = 500 };

            // National ID
            var nationalIdLabel = new Label { Text = "رقم الهوية:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            nationalIdTextBox = new TextBox { Size = new Size(250, 23), MaxLength = 20 };

            // Birth Date
            var birthDateLabel = new Label { Text = "تاريخ الميلاد:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            hasBirthDateCheckBox = new CheckBox { Text = "تحديد تاريخ الميلاد", Size = new Size(200, 23), CheckAlign = ContentAlignment.MiddleRight };
            hasBirthDateCheckBox.CheckedChanged += HasBirthDateCheckBox_CheckedChanged;
            birthDatePicker = new DateTimePicker { Size = new Size(250, 23), Format = DateTimePickerFormat.Short, Value = DateTime.Now.AddYears(-25), Enabled = false };

            // Marital Status
            var maritalStatusLabel = new Label { Text = "الحالة الاجتماعية:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            maritalStatusComboBox = new ComboBox { Size = new Size(250, 23), DropDownStyle = ComboBoxStyle.DropDownList };
            maritalStatusComboBox.Items.AddRange(new[] { "", "أعزب", "متزوج", "مطلق", "أرمل" });

            // Number of Children
            var numberOfChildrenLabel = new Label { Text = "عدد الأطفال:", Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            numberOfChildrenNumericUpDown = new NumericUpDown { Size = new Size(250, 23), Minimum = 0, Maximum = 20 };

            // Buttons
            saveButton = new Button { Text = _isEditMode ? "حفظ التعديلات" : "إضافة", Size = new Size(100, 30), DialogResult = DialogResult.OK };
            saveButton.Click += SaveButton_Click;
            cancelButton = new Button { Text = "إلغاء", Size = new Size(100, 30), DialogResult = DialogResult.Cancel };

            this.Controls.AddRange(new Control[]
            {
                fullNameLabel, fullNameTextBox,
                nationalityLabel, nationalityTextBox,
                residenceLabel, residenceNumberTextBox,
                positionLabel, positionTextBox,
                joinDateLabel, joinDatePicker,
                leaveDateLabel, hasLeaveDateCheckBox, leaveDatePicker,
                baseSalaryLabel, baseSalaryNumericUpDown,
                statusLabel, statusComboBox,
                phoneLabel, phoneTextBox,
                emailLabel, emailTextBox,
                addressLabel, addressTextBox,
                nationalIdLabel, nationalIdTextBox,
                birthDateLabel, hasBirthDateCheckBox, birthDatePicker,
                maritalStatusLabel, maritalStatusComboBox,
                numberOfChildrenLabel, numberOfChildrenNumericUpDown,
                saveButton, cancelButton
            });
        }

        private void LayoutControls()
        {
            int x = 20;
            int y = 20;
            int spacing = 35;

            // Full Name
            this.Controls[0].Location = new Point(x + 350, y);
            this.Controls[1].Location = new Point(x + 80, y);
            y += spacing;

            // Nationality
            this.Controls[2].Location = new Point(x + 350, y);
            this.Controls[3].Location = new Point(x + 80, y);
            y += spacing;

            // Residence Number
            this.Controls[4].Location = new Point(x + 350, y);
            this.Controls[5].Location = new Point(x + 80, y);
            y += spacing;

            // Position
            this.Controls[6].Location = new Point(x + 350, y);
            this.Controls[7].Location = new Point(x + 80, y);
            y += spacing;

            // Join Date
            this.Controls[8].Location = new Point(x + 350, y);
            this.Controls[9].Location = new Point(x + 80, y);
            y += spacing;

            // Leave Date
            this.Controls[10].Location = new Point(x + 350, y);
            this.Controls[11].Location = new Point(x + 80, y);
            y += spacing;
            this.Controls[12].Location = new Point(x + 80, y);
            y += spacing;

            // Base Salary
            this.Controls[13].Location = new Point(x + 350, y);
            this.Controls[14].Location = new Point(x + 80, y);
            y += spacing;

            // Status
            this.Controls[15].Location = new Point(x + 350, y);
            this.Controls[16].Location = new Point(x + 80, y);
            y += spacing;

            // Phone
            this.Controls[17].Location = new Point(x + 350, y);
            this.Controls[18].Location = new Point(x + 80, y);
            y += spacing;

            // Email
            this.Controls[19].Location = new Point(x + 350, y);
            this.Controls[20].Location = new Point(x + 80, y);
            y += spacing;

            // Address
            this.Controls[21].Location = new Point(x + 350, y);
            this.Controls[22].Location = new Point(x + 80, y);
            y += 80;

            // National ID
            this.Controls[23].Location = new Point(x + 350, y);
            this.Controls[24].Location = new Point(x + 80, y);
            y += spacing;

            // Birth Date
            this.Controls[25].Location = new Point(x + 350, y);
            this.Controls[26].Location = new Point(x + 80, y);
            y += spacing;
            this.Controls[27].Location = new Point(x + 80, y);
            y += spacing;

            // Marital Status
            this.Controls[28].Location = new Point(x + 350, y);
            this.Controls[29].Location = new Point(x + 80, y);
            y += spacing;

            // Number of Children
            this.Controls[30].Location = new Point(x + 350, y);
            this.Controls[31].Location = new Point(x + 80, y);
            y += spacing + 20;

            // Buttons
            this.Controls[32].Location = new Point(x + 180, y);
            this.Controls[33].Location = new Point(x + 300, y);
        }

        private void LoadData()
        {
            if (_existingEmployee != null)
            {
                fullNameTextBox.Text = _existingEmployee.FullName;
                nationalityTextBox.Text = _existingEmployee.Nationality;
                residenceNumberTextBox.Text = _existingEmployee.ResidenceNumber;
                positionTextBox.Text = _existingEmployee.Position;
                joinDatePicker.Value = _existingEmployee.JoinDate;
                
                if (_existingEmployee.LeaveDate.HasValue)
                {
                    hasLeaveDateCheckBox.Checked = true;
                    leaveDatePicker.Value = _existingEmployee.LeaveDate.Value;
                }

                baseSalaryNumericUpDown.Value = _existingEmployee.BaseSalary;
                statusComboBox.SelectedItem = _existingEmployee.Status;
                phoneTextBox.Text = _existingEmployee.Phone;
                emailTextBox.Text = _existingEmployee.Email;
                addressTextBox.Text = _existingEmployee.Address;
                nationalIdTextBox.Text = _existingEmployee.NationalId;
                
                if (_existingEmployee.BirthDate.HasValue)
                {
                    hasBirthDateCheckBox.Checked = true;
                    birthDatePicker.Value = _existingEmployee.BirthDate.Value;
                }

                maritalStatusComboBox.SelectedItem = _existingEmployee.MaritalStatus;
                numberOfChildrenNumericUpDown.Value = _existingEmployee.NumberOfChildren;
            }
        }

        private void HasLeaveDateCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            leaveDatePicker.Enabled = hasLeaveDateCheckBox.Checked;
        }

        private void HasBirthDateCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            birthDatePicker.Enabled = hasBirthDateCheckBox.Checked;
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                this.Cursor = Cursors.WaitCursor;
                saveButton.Enabled = false;

                var employee = _existingEmployee ?? new Employee();
                
                employee.FullName = fullNameTextBox.Text.Trim();
                employee.Nationality = nationalityTextBox.Text.Trim();
                employee.ResidenceNumber = residenceNumberTextBox.Text.Trim();
                employee.Position = positionTextBox.Text.Trim();
                employee.JoinDate = joinDatePicker.Value;
                employee.LeaveDate = hasLeaveDateCheckBox.Checked ? leaveDatePicker.Value : null;
                employee.BaseSalary = baseSalaryNumericUpDown.Value;
                employee.Status = statusComboBox.SelectedItem?.ToString() ?? "نشط";
                employee.Phone = phoneTextBox.Text.Trim();
                employee.Email = emailTextBox.Text.Trim();
                employee.Address = addressTextBox.Text.Trim();
                employee.NationalId = nationalIdTextBox.Text.Trim();
                employee.BirthDate = hasBirthDateCheckBox.Checked ? birthDatePicker.Value : null;
                employee.MaritalStatus = maritalStatusComboBox.SelectedItem?.ToString() ?? "";
                employee.NumberOfChildren = (int)numberOfChildrenNumericUpDown.Value;

                if (_isEditMode)
                {
                    employee.UpdateModificationDate();
                    await _unitOfWork.Employees.UpdateAsync(employee);
                    MessageBox.Show("تم تحديث الموظف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _unitOfWork.Employees.AddAsync(employee);
                    MessageBox.Show("تم إضافة الموظف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                await _unitOfWork.SaveChangesAsync();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ بيانات الموظف");
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                saveButton.Enabled = true;
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(fullNameTextBox.Text))
            {
                MessageBox.Show("الاسم الكامل مطلوب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                fullNameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(nationalityTextBox.Text))
            {
                MessageBox.Show("الجنسية مطلوبة", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nationalityTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(positionTextBox.Text))
            {
                MessageBox.Show("المنصب مطلوب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                positionTextBox.Focus();
                return false;
            }

            if (baseSalaryNumericUpDown.Value <= 0)
            {
                MessageBox.Show("الراتب الأساسي مطلوب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                baseSalaryNumericUpDown.Focus();
                return false;
            }

            if (statusComboBox.SelectedIndex < 0)
            {
                MessageBox.Show("يجب اختيار حالة الموظف", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                statusComboBox.Focus();
                return false;
            }

            return true;
        }
    }
}

