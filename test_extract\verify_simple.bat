@echo off
title Package Verification

echo ========================================
echo    Fish Farm Management Package Check
echo ========================================
echo.

set ERROR_COUNT=0
set WARNING_COUNT=0

echo Checking core files...
echo.

if exist "FishFarmManagement.exe" (
    echo [OK] FishFarmManagement.exe - Found
) else (
    echo [ERROR] FishFarmManagement.exe - Missing
    set /a ERROR_COUNT+=1
)

if exist "appsettings.json" (
    echo [OK] appsettings.json - Found
) else (
    echo [ERROR] appsettings.json - Missing
    set /a ERROR_COUNT+=1
)

if exist "README.txt" (
    echo [OK] README.txt - Found
) else (
    echo [WARNING] README.txt - Missing
    set /a WARNING_COUNT+=1
)

if exist "run.bat" (
    echo [OK] run.bat - Found
) else (
    echo [WARNING] run.bat - Missing
    set /a WARNING_COUNT+=1
)

echo.
echo Checking directories...
echo.

if exist "Logs" (
    echo [OK] Logs directory - Found
) else (
    echo [INFO] Logs directory - Will be created automatically
)

if exist "Backups" (
    echo [OK] Backups directory - Found
) else (
    echo [INFO] Backups directory - Will be created automatically
)

if exist "Reports" (
    echo [OK] Reports directory - Found
) else (
    echo [INFO] Reports directory - Will be created automatically
)

echo.
echo Checking system requirements...
echo.

dotnet --version >nul 2>&1
if %ERRORLEVEL% equ 0 (
    for /f %%i in ('dotnet --version 2^>nul') do set DOTNET_VERSION=%%i
    echo [OK] .NET Runtime: %DOTNET_VERSION%
) else (
    echo [WARNING] .NET Runtime - Not installed or not available
    echo           Program may work if runtime files are included
    set /a WARNING_COUNT+=1
)

echo.
echo ========================================
echo    Verification Results
echo ========================================
echo.

if %ERROR_COUNT% equ 0 (
    if %WARNING_COUNT% equ 0 (
        echo [SUCCESS] Package is complete and ready to use!
        echo.
        echo You can safely run the program.
    ) else (
        echo [WARNING] Package is usable with %WARNING_COUNT% warning(s)
        echo.
        echo The program should work, but you might encounter minor issues.
    )
) else (
    echo [ERROR] Package has %ERROR_COUNT% critical issue(s)
    echo.
    echo The program may not work correctly.
    echo Please re-download the package or contact support.
)

echo.
echo Summary:
echo - Critical Errors: %ERROR_COUNT%
echo - Warnings: %WARNING_COUNT%

echo.
echo Technical Support: <EMAIL>
echo.

if %ERROR_COUNT% equ 0 (
    echo Do you want to run the program now? (Y/N)
    set /p choice=
    if /i "%choice%" equ "Y" (
        if exist "run.bat" (
            call run.bat
        ) else if exist "FishFarmManagement.exe" (
            start "" "FishFarmManagement.exe"
        ) else (
            echo Cannot run - executable not found
        )
    )
)

pause
