﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.Models;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.BLL.Services;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ Ù…Ø­Ø³Ù† Ù„Ù„Ù…Ø­Ø§Ø³Ø¨Ø© ÙˆØ§Ù„Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ù…Ø§Ù„ÙŠØ©
    /// Enhanced accounting and financial management form
    /// </summary>
    public partial class EnhancedAccountingForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly EnhancedReportService _reportService;
        private readonly ILogger<EnhancedAccountingForm> _logger;

        // UI Controls
        private TabControl tabControl;
        private TabPage transactionsTab;
        private TabPage budgetTab;
        private TabPage profitabilityTab;
        private TabPage dashboardTab;

        // Transactions Tab
        private DataGridView transactionsDataGridView;
        private ToolStripButton addTransactionButton;
        private ToolStripButton editTransactionButton;
        private ToolStripButton deleteTransactionButton;
        private ComboBox transactionTypeComboBox;
        private DateTimePicker fromDatePicker;
        private DateTimePicker toDatePicker;
        private Button filterButton;
        private TextBox searchTextBox;

        // Dashboard Tab
        private Label totalRevenueLabel;
        private Label totalExpensesLabel;
        private Label netProfitLabel;
        private Label profitMarginLabel;
        private Panel chartPanel;
        private ListBox recentTransactionsListBox;

        // Budget Tab
        private DataGridView budgetDataGridView;
        private Button setBudgetButton;
        private ProgressBar budgetProgressBar;
        private Label budgetStatusLabel;

        // Status
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;

        public EnhancedAccountingForm(IUnitOfWork unitOfWork, EnhancedReportService reportService, ILogger<EnhancedAccountingForm> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            LoadInitialDataAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "Ø§Ù„Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ù…Ø§Ù„ÙŠØ© Ø§Ù„Ù…Ø­Ø³Ù†Ø©";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            CreateTabControl();
            CreateDashboardTab();
            CreateTransactionsTab();
            CreateBudgetTab();
            CreateProfitabilityTab();
            CreateStatusStrip();
        }

        private void CreateTabControl()
        {
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            dashboardTab = new TabPage("Ù„ÙˆØ­Ø© Ø§Ù„Ù…Ø¹Ù„ÙˆÙ…Ø§Øª");
            transactionsTab = new TabPage("Ø§Ù„Ù…Ø¹Ø§Ù…Ù„Ø§Øª Ø§Ù„Ù…Ø§Ù„ÙŠØ©");
            budgetTab = new TabPage("Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ©");
            profitabilityTab = new TabPage("ØªØ­Ù„ÙŠÙ„ Ø§Ù„Ø±Ø¨Ø­ÙŠØ©");

            tabControl.TabPages.AddRange(new TabPage[] {
                dashboardTab, transactionsTab, budgetTab, profitabilityTab
            });

            this.Controls.Add(tabControl);
        }

        private void CreateDashboardTab()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Financial summary cards
            var summaryPanel = new Panel
            {
                Height = 150,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(245, 245, 245)
            };

            // Revenue card
            var revenueCard = CreateSummaryCard("Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø¥ÙŠØ±Ø§Ø¯Ø§Øª", "0 Ø±ÙŠØ§Ù„", Color.FromArgb(46, 204, 113), new Point(20, 20));
            totalRevenueLabel = revenueCard.Controls.OfType<Label>().Last();

            // Expenses card
            var expensesCard = CreateSummaryCard("Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ù…ØµØ±ÙˆÙØ§Øª", "0 Ø±ÙŠØ§Ù„", Color.FromArgb(231, 76, 60), new Point(320, 20));
            totalExpensesLabel = expensesCard.Controls.OfType<Label>().Last();

            // Net profit card
            var profitCard = CreateSummaryCard("ØµØ§ÙÙŠ Ø§Ù„Ø±Ø¨Ø­", "0 Ø±ÙŠØ§Ù„", Color.FromArgb(52, 152, 219), new Point(620, 20));
            netProfitLabel = profitCard.Controls.OfType<Label>().Last();

            // Profit margin card
            var marginCard = CreateSummaryCard("Ù‡Ø§Ù…Ø´ Ø§Ù„Ø±Ø¨Ø­", "0%", Color.FromArgb(155, 89, 182), new Point(920, 20));
            profitMarginLabel = marginCard.Controls.OfType<Label>().Last();

            summaryPanel.Controls.AddRange(new Control[] { revenueCard, expensesCard, profitCard, marginCard });

            // Content panel
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(0, 20, 0, 0)
            };

            // Chart panel (placeholder)
            chartPanel = new Panel
            {
                Size = new Size(700, 300),
                Location = new Point(20, 20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var chartLabel = new Label
            {
                Text = "Ø§Ù„Ø±Ø³Ù… Ø§Ù„Ø¨ÙŠØ§Ù†ÙŠ Ù„Ù„Ø¥ÙŠØ±Ø§Ø¯Ø§Øª ÙˆØ§Ù„Ù…ØµØ±ÙˆÙØ§Øª",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Size = new Size(300, 25),
                Location = new Point(200, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var chartPlaceholder = new Label
            {
                Text = "Ø³ÙŠØªÙ… Ø¹Ø±Ø¶ Ø§Ù„Ø±Ø³Ù… Ø§Ù„Ø¨ÙŠØ§Ù†ÙŠ Ù‡Ù†Ø§",
                Size = new Size(200, 50),
                Location = new Point(250, 125),
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Color.Gray
            };

            chartPanel.Controls.AddRange(new Control[] { chartLabel, chartPlaceholder });

            // Recent transactions panel
            var recentPanel = new Panel
            {
                Size = new Size(400, 300),
                Location = new Point(740, 20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var recentLabel = new Label
            {
                Text = "Ø§Ù„Ù…Ø¹Ø§Ù…Ù„Ø§Øª Ø§Ù„Ø£Ø®ÙŠØ±Ø©",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Size = new Size(150, 25),
                Location = new Point(125, 10),
                TextAlign = ContentAlignment.MiddleCenter
            };

            recentTransactionsListBox = new ListBox
            {
                Size = new Size(360, 250),
                Location = new Point(20, 40),
                Font = new Font("Segoe UI", 9F)
            };

            recentPanel.Controls.AddRange(new Control[] { recentLabel, recentTransactionsListBox });

            contentPanel.Controls.AddRange(new Control[] { chartPanel, recentPanel });
            mainPanel.Controls.AddRange(new Control[] { summaryPanel, contentPanel });
            dashboardTab.Controls.Add(mainPanel);
        }

        private Panel CreateSummaryCard(string title, string value, Color color, Point location)
        {
            var card = new Panel
            {
                Size = new Size(280, 110),
                Location = location,
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var colorBar = new Panel
            {
                Size = new Size(5, 110),
                Location = new Point(0, 0),
                BackColor = color
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Size = new Size(250, 25),
                Location = new Point(20, 15),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                Size = new Size(250, 40),
                Location = new Point(20, 45),
                ForeColor = color
            };

            var iconLabel = new Label
            {
                Text = "ðŸ’°",
                Font = new Font("Segoe UI", 20F),
                Size = new Size(40, 40),
                Location = new Point(220, 35),
                TextAlign = ContentAlignment.MiddleCenter
            };

            card.Controls.AddRange(new Control[] { colorBar, titleLabel, valueLabel, iconLabel });
            return card;
        }

        private void CreateTransactionsTab()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Filter panel
            var filterPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(245, 245, 245),
                Padding = new Padding(10)
            };

            var typeLabel = new Label
            {
                Text = "Ù†ÙˆØ¹ Ø§Ù„Ù…Ø¹Ø§Ù…Ù„Ø©:",
                Size = new Size(80, 23),
                Location = new Point(10, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            transactionTypeComboBox = new ComboBox
            {
                Size = new Size(120, 23),
                Location = new Point(100, 12),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            transactionTypeComboBox.Items.AddRange(new[] { "Ø§Ù„ÙƒÙ„", "Ø¥ÙŠØ±Ø§Ø¯", "Ù…ØµØ±ÙˆÙ" });
            transactionTypeComboBox.SelectedIndex = 0;

            var fromLabel = new Label
            {
                Text = "Ù…Ù† ØªØ§Ø±ÙŠØ®:",
                Size = new Size(60, 23),
                Location = new Point(240, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            fromDatePicker = new DateTimePicker
            {
                Size = new Size(120, 23),
                Location = new Point(310, 12),
                Value = DateTime.Now.AddMonths(-1)
            };

            var toLabel = new Label
            {
                Text = "Ø¥Ù„Ù‰ ØªØ§Ø±ÙŠØ®:",
                Size = new Size(60, 23),
                Location = new Point(450, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            toDatePicker = new DateTimePicker
            {
                Size = new Size(120, 23),
                Location = new Point(520, 12),
                Value = DateTime.Now
            };

            filterButton = new Button
            {
                Text = "ØªØ·Ø¨ÙŠÙ‚ Ø§Ù„Ù…Ø±Ø´Ø­",
                Size = new Size(100, 25),
                Location = new Point(660, 11),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            filterButton.Click += FilterTransactions_Click;

            var searchLabel = new Label
            {
                Text = "Ø§Ù„Ø¨Ø­Ø«:",
                Size = new Size(50, 23),
                Location = new Point(10, 45),
                TextAlign = ContentAlignment.MiddleRight
            };

            searchTextBox = new TextBox
            {
                Size = new Size(200, 23),
                Location = new Point(70, 42),
                PlaceholderText = "Ø§Ù„Ø¨Ø­Ø« ÙÙŠ Ø§Ù„ÙˆØµÙ..."
            };
            searchTextBox.TextChanged += SearchTransactions_TextChanged;

            filterPanel.Controls.AddRange(new Control[] {
                typeLabel, transactionTypeComboBox, fromLabel, fromDatePicker,
                toLabel, toDatePicker, filterButton, searchLabel, searchTextBox
            });

            // Toolbar
            var toolbar = new ToolStrip
            {
                Height = 40,
                Font = new Font("Segoe UI", 9F)
            };

            addTransactionButton = new ToolStripButton("Ø¥Ø¶Ø§ÙØ© Ù…Ø¹Ø§Ù…Ù„Ø©", null, AddTransaction_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White
            };

            editTransactionButton = new ToolStripButton("ØªØ¹Ø¯ÙŠÙ„", null, EditTransaction_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            deleteTransactionButton = new ToolStripButton("Ø­Ø°Ù", null, DeleteTransaction_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            toolbar.Items.AddRange(new ToolStripItem[] {
                addTransactionButton, editTransactionButton, deleteTransactionButton
            });

            // Data grid
            transactionsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            CreateTransactionsColumns();
            transactionsDataGridView.SelectionChanged += TransactionsDataGridView_SelectionChanged;

            mainPanel.Controls.Add(transactionsDataGridView);
            mainPanel.Controls.Add(toolbar);
            mainPanel.Controls.Add(filterPanel);
            transactionsTab.Controls.Add(mainPanel);
        }

        private void CreateTransactionsColumns()
        {
            transactionsDataGridView.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "TransactionDate", HeaderText = "Ø§Ù„ØªØ§Ø±ÙŠØ®", DataPropertyName = "TransactionDate", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "TransactionType", HeaderText = "Ø§Ù„Ù†ÙˆØ¹", DataPropertyName = "TransactionType", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Description", HeaderText = "Ø§Ù„ÙˆØµÙ", DataPropertyName = "Description", Width = 250 },
                new DataGridViewTextBoxColumn { Name = "Amount", HeaderText = "Ø§Ù„Ù…Ø¨Ù„Øº", DataPropertyName = "TotalAmount", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Category", HeaderText = "Ø§Ù„ÙØ¦Ø©", DataPropertyName = "Category", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "PaymentMethod", HeaderText = "Ø·Ø±ÙŠÙ‚Ø© Ø§Ù„Ø¯ÙØ¹", DataPropertyName = "PaymentMethod", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Notes", HeaderText = "Ù…Ù„Ø§Ø­Ø¸Ø§Øª", DataPropertyName = "Notes", Width = 200 }
            });

            transactionsDataGridView.Columns["TransactionDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            transactionsDataGridView.Columns["Amount"].DefaultCellStyle.Format = "C2";
        }

        private void CreateBudgetTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            var titleLabel = new Label
            {
                Text = "Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ©",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };

            budgetStatusLabel = new Label
            {
                Text = "Ø­Ø§Ù„Ø© Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ©: Ù„Ù… ÙŠØªÙ… ØªØ­Ø¯ÙŠØ¯ Ù…ÙŠØ²Ø§Ù†ÙŠØ©",
                Font = new Font("Segoe UI", 12F),
                Size = new Size(400, 25),
                Location = new Point(20, 60),
                ForeColor = Color.Orange
            };

            budgetProgressBar = new ProgressBar
            {
                Size = new Size(400, 25),
                Location = new Point(20, 90),
                Style = ProgressBarStyle.Continuous
            };

            setBudgetButton = new Button
            {
                Text = "ØªØ­Ø¯ÙŠØ¯ Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ©",
                Size = new Size(120, 35),
                Location = new Point(20, 130),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            setBudgetButton.Click += SetBudget_Click;

            budgetDataGridView = new DataGridView
            {
                Size = new Size(800, 300),
                Location = new Point(20, 180),
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            CreateBudgetColumns();

            panel.Controls.AddRange(new Control[] {
                titleLabel, budgetStatusLabel, budgetProgressBar,
                setBudgetButton, budgetDataGridView
            });

            budgetTab.Controls.Add(panel);
        }

        private void CreateBudgetColumns()
        {
            budgetDataGridView.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Category", HeaderText = "Ø§Ù„ÙØ¦Ø©", DataPropertyName = "Category", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "BudgetAmount", HeaderText = "Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ© Ø§Ù„Ù…Ø­Ø¯Ø¯Ø©", DataPropertyName = "BudgetAmount", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "ActualAmount", HeaderText = "Ø§Ù„Ù…Ø¨Ù„Øº Ø§Ù„ÙØ¹Ù„ÙŠ", DataPropertyName = "ActualAmount", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Variance", HeaderText = "Ø§Ù„Ø§Ù†Ø­Ø±Ø§Ù", DataPropertyName = "Variance", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "VariancePercentage", HeaderText = "Ù†Ø³Ø¨Ø© Ø§Ù„Ø§Ù†Ø­Ø±Ø§Ù", DataPropertyName = "VariancePercentage", Width = 120 }
            });

            budgetDataGridView.Columns["BudgetAmount"].DefaultCellStyle.Format = "C2";
            budgetDataGridView.Columns["ActualAmount"].DefaultCellStyle.Format = "C2";
            budgetDataGridView.Columns["Variance"].DefaultCellStyle.Format = "C2";
            budgetDataGridView.Columns["VariancePercentage"].DefaultCellStyle.Format = "P2";
        }

        private void CreateProfitabilityTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            var titleLabel = new Label
            {
                Text = "ØªØ­Ù„ÙŠÙ„ Ø§Ù„Ø±Ø¨Ø­ÙŠØ©",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };

            var analysisButton = new Button
            {
                Text = "ØªØ­Ù„ÙŠÙ„ Ø§Ù„Ø±Ø¨Ø­ÙŠØ© Ø§Ù„Ø´Ø§Ù…Ù„",
                Size = new Size(200, 40),
                Location = new Point(20, 70),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            analysisButton.Click += AnalyzeProfitability_Click;

            var costAnalysisButton = new Button
            {
                Text = "ØªØ­Ù„ÙŠÙ„ Ø§Ù„ØªÙƒØ§Ù„ÙŠÙ",
                Size = new Size(200, 40),
                Location = new Point(240, 70),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            costAnalysisButton.Click += AnalyzeCosts_Click;

            var roiButton = new Button
            {
                Text = "Ø­Ø³Ø§Ø¨ Ø§Ù„Ø¹Ø§Ø¦Ø¯ Ø¹Ù„Ù‰ Ø§Ù„Ø§Ø³ØªØ«Ù…Ø§Ø±",
                Size = new Size(200, 40),
                Location = new Point(460, 70),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            roiButton.Click += CalculateROI_Click;

            panel.Controls.AddRange(new Control[] {
                titleLabel, analysisButton, costAnalysisButton, roiButton
            });

            profitabilityTab.Controls.Add(panel);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();
            statusLabel = new ToolStripStatusLabel("Ø¬Ø§Ù‡Ø²");
            statusStrip.Items.Add(statusLabel);
            this.Controls.Add(statusStrip);
        }

        private async void LoadInitialDataAsync()
        {
            try
            {
                await LoadDashboardDataAsync();
                await LoadTransactionsAsync();
                statusLabel.Text = "ØªÙ… ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø¨Ù†Ø¬Ø§Ø­";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø£ÙˆÙ„ÙŠØ©");
                statusLabel.Text = "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª";
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                var fromDate = DateTime.Now.AddMonths(-1);
                var toDate = DateTime.Now;

                var financialReport = await _reportService.GenerateFinancialReportAsync(fromDate, toDate);

                // Update summary cards
                totalRevenueLabel.Text = $"{financialReport.TotalRevenue:C2}";
                totalExpensesLabel.Text = $"{financialReport.TotalExpenses:C2}";
                netProfitLabel.Text = $"{financialReport.NetProfit:C2}";
                profitMarginLabel.Text = $"{financialReport.ProfitMargin:F1}%";

                // Update profit margin color based on value
                if (financialReport.ProfitMargin > 20)
                    profitMarginLabel.ForeColor = Color.Green;
                else if (financialReport.ProfitMargin > 10)
                    profitMarginLabel.ForeColor = Color.Orange;
                else
                    profitMarginLabel.ForeColor = Color.Red;

                // Load recent transactions
                var recentTransactions = await _unitOfWork.Transactions.FindAsync(
                    t => t.TransactionDate >= DateTime.Now.AddDays(-7));

                recentTransactionsListBox.Items.Clear();
                foreach (var transaction in recentTransactions.OrderByDescending(t => t.TransactionDate).Take(10))
                {
                    var item = $"{transaction.TransactionDate:MM/dd} - {transaction.Description} - {transaction.TotalAmount:C2}";
                    recentTransactionsListBox.Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ù„ÙˆØ­Ø© Ø§Ù„Ù…Ø¹Ù„ÙˆÙ…Ø§Øª");
                throw;
            }
        }

        private async Task LoadTransactionsAsync()
        {
            try
            {
                var fromDate = fromDatePicker.Value.Date;
                var toDate = toDatePicker.Value.Date.AddDays(1).AddSeconds(-1);

                var transactions = await _unitOfWork.Transactions.FindAsync(t =>
                    t.TransactionDate >= fromDate && t.TransactionDate <= toDate);

                // Apply type filter
                if (transactionTypeComboBox.SelectedIndex > 0)
                {
                    var selectedType = transactionTypeComboBox.SelectedItem.ToString();
                    transactions = transactions.Where(t => t.TransactionType == selectedType);
                }

                // Apply search filter
                if (!string.IsNullOrWhiteSpace(searchTextBox.Text))
                {
                    var searchTerm = searchTextBox.Text.ToLower();
                    transactions = transactions.Where(t =>
                        t.Description.ToLower().Contains(searchTerm) ||
                        t.Notes?.ToLower().Contains(searchTerm) == true);
                }

                var transactionsList = transactions.OrderByDescending(t => t.TransactionDate).ToList();
                transactionsDataGridView.DataSource = transactionsList;

                statusLabel.Text = $"ØªÙ… ØªØ­Ù…ÙŠÙ„ {transactionsList.Count} Ù…Ø¹Ø§Ù…Ù„Ø©";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ù…Ø¹Ø§Ù…Ù„Ø§Øª");
                throw;
            }
        }

        // Event Handlers
        private void TransactionsDataGridView_SelectionChanged(object? sender, EventArgs e)
        {
            bool hasSelection = transactionsDataGridView.SelectedRows.Count > 0;
            editTransactionButton.Enabled = hasSelection;
            deleteTransactionButton.Enabled = hasSelection;
        }

        private async void FilterTransactions_Click(object? sender, EventArgs e)
        {
            await LoadTransactionsAsync();
        }

        private async void SearchTransactions_TextChanged(object sender, EventArgs e)
        {
            // Add a small delay to avoid too many database calls
            await Task.Delay(500);
            await LoadTransactionsAsync();
        }

        private void AddTransaction_Click(object? sender, EventArgs e)
        {
            try
            {
                // Open transaction add/edit form
                MessageBox.Show("Ø³ÙŠØªÙ… ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¶Ø§ÙØ© Ù…Ø¹Ø§Ù…Ù„Ø© Ø¬Ø¯ÙŠØ¯Ø©", "Ø¥Ø¶Ø§ÙØ© Ù…Ø¹Ø§Ù…Ù„Ø©",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¥Ø¶Ø§ÙØ© Ù…Ø¹Ø§Ù…Ù„Ø©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ø¹Ø§Ù…Ù„Ø©: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditTransaction_Click(object? sender, EventArgs e)
        {
            try
            {
                if (transactionsDataGridView.SelectedRows.Count == 0) return;

                MessageBox.Show("Ø³ÙŠØªÙ… ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„Ù…Ø¹Ø§Ù…Ù„Ø©", "ØªØ¹Ø¯ÙŠÙ„ Ù…Ø¹Ø§Ù…Ù„Ø©",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ¹Ø¯ÙŠÙ„ Ù…Ø¹Ø§Ù…Ù„Ø©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„Ù…Ø¹Ø§Ù…Ù„Ø©: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteTransaction_Click(object? sender, EventArgs e)
        {
            try
            {
                if (transactionsDataGridView.SelectedRows.Count == 0) return;

                var selectedTransaction = transactionsDataGridView.SelectedRows[0].DataBoundItem as Transaction;
                if (selectedTransaction == null) return;

                var result = MessageBox.Show(
                    $"Ù‡Ù„ Ø£Ù†Øª Ù…ØªØ£ÙƒØ¯ Ù…Ù† Ø­Ø°Ù Ø§Ù„Ù…Ø¹Ø§Ù…Ù„Ø© '{selectedTransaction.Description}'ØŸ\n\nÙ‡Ø°Ø§ Ø§Ù„Ø¥Ø¬Ø±Ø§Ø¡ Ù„Ø§ ÙŠÙ…ÙƒÙ† Ø§Ù„ØªØ±Ø§Ø¬Ø¹ Ø¹Ù†Ù‡.",
                    "ØªØ£ÙƒÙŠØ¯ Ø§Ù„Ø­Ø°Ù",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    await _unitOfWork.Transactions.DeleteAsync(selectedTransaction);
                    await _unitOfWork.SaveChangesAsync();

                    await LoadTransactionsAsync();
                    await LoadDashboardDataAsync();

                    statusLabel.Text = "ØªÙ… Ø­Ø°Ù Ø§Ù„Ù…Ø¹Ø§Ù…Ù„Ø© Ø¨Ù†Ø¬Ø§Ø­";
                    MessageBox.Show("ØªÙ… Ø­Ø°Ù Ø§Ù„Ù…Ø¹Ø§Ù…Ù„Ø© Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ù…Ø¹Ø§Ù…Ù„Ø©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„Ù…Ø¹Ø§Ù…Ù„Ø©: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetBudget_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("Ø³ÙŠØªÙ… ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ ØªØ­Ø¯ÙŠØ¯ Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ©", "ØªØ­Ø¯ÙŠØ¯ Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ©",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ø¯ÙŠØ¯ Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ø¯ÙŠØ¯ Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ©: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void private async void private async void AnalyzeProfitability_Click(object? sender, EventArgs e)
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;
                statusLabel.Text = "Ø¬Ø§Ø±ÙŠ ØªØ­Ù„ÙŠÙ„ Ø§Ù„Ø±Ø¨Ø­ÙŠØ©...";

                var fromDate = DateTime.Now.AddMonths(-12);
                var toDate = DateTime.Now;

                var financialReport = await _reportService.GenerateFinancialReportAsync(fromDate, toDate);

                var analysisMessage = $@"ØªØ­Ù„ÙŠÙ„ Ø§Ù„Ø±Ø¨Ø­ÙŠØ© Ù„Ù„ÙØªØ±Ø© Ù…Ù† {fromDate:yyyy/MM/dd} Ø¥Ù„Ù‰ {toDate:yyyy/MM/dd}:

Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø¥ÙŠØ±Ø§Ø¯Ø§Øª: {financialReport.TotalRevenue:C2}
Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ù…ØµØ±ÙˆÙØ§Øª: {financialReport.TotalExpenses:C2}
ØµØ§ÙÙŠ Ø§Ù„Ø±Ø¨Ø­: {financialReport.NetProfit:C2}
Ù‡Ø§Ù…Ø´ Ø§Ù„Ø±Ø¨Ø­: {financialReport.ProfitMargin:F2}%

Ø§Ù„ØªÙ‚ÙŠÙŠÙ…:
{(financialReport.ProfitMargin > 20 ? "Ù…Ù…ØªØ§Ø² - Ù‡Ø§Ù…Ø´ Ø±Ø¨Ø­ Ø¹Ø§Ù„ÙŠ" :
  financialReport.ProfitMargin > 10 ? "Ø¬ÙŠØ¯ - Ù‡Ø§Ù…Ø´ Ø±Ø¨Ø­ Ù…ØªÙˆØ³Ø·" :
  "ÙŠØ­ØªØ§Ø¬ ØªØ­Ø³ÙŠÙ† - Ù‡Ø§Ù…Ø´ Ø±Ø¨Ø­ Ù…Ù†Ø®ÙØ¶")}

Ø§Ù„ØªÙˆØµÙŠØ§Øª:
{(financialReport.ProfitMargin < 10 ? "â€¢ Ù…Ø±Ø§Ø¬Ø¹Ø© Ø§Ù„ØªÙƒØ§Ù„ÙŠÙ ÙˆØªÙ‚Ù„ÙŠÙ„ Ø§Ù„Ù…ØµØ±ÙˆÙØ§Øª ØºÙŠØ± Ø§Ù„Ø¶Ø±ÙˆØ±ÙŠØ©\nâ€¢ Ø§Ù„Ø¨Ø­Ø« Ø¹Ù† Ù…ØµØ§Ø¯Ø± Ø¥ÙŠØ±Ø§Ø¯Ø§Øª Ø¥Ø¶Ø§ÙÙŠØ©" :
  "â€¢ Ø§Ù„Ø­ÙØ§Ø¸ Ø¹Ù„Ù‰ Ø§Ù„Ø£Ø¯Ø§Ø¡ Ø§Ù„Ø­Ø§Ù„ÙŠ\nâ€¢ Ø§Ù„Ø¨Ø­Ø« Ø¹Ù† ÙØ±Øµ Ù„Ù„Ù†Ù…Ùˆ")}";

                MessageBox.Show(analysisMessage, "ØªØ­Ù„ÙŠÙ„ Ø§Ù„Ø±Ø¨Ø­ÙŠØ©",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                statusLabel.Text = "ØªÙ… Ø¥ÙƒÙ…Ø§Ù„ ØªØ­Ù„ÙŠÙ„ Ø§Ù„Ø±Ø¨Ø­ÙŠØ©";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù„ÙŠÙ„ Ø§Ù„Ø±Ø¨Ø­ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù„ÙŠÙ„ Ø§Ù„Ø±Ø¨Ø­ÙŠØ©: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù„ÙŠÙ„ Ø§Ù„Ø±Ø¨Ø­ÙŠØ©";
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void AnalyzeCosts_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("Ø³ÙŠØªÙ… ÙØªØ­ ØªØ­Ù„ÙŠÙ„ Ø§Ù„ØªÙƒØ§Ù„ÙŠÙ Ø§Ù„ØªÙØµÙŠÙ„ÙŠ", "ØªØ­Ù„ÙŠÙ„ Ø§Ù„ØªÙƒØ§Ù„ÙŠÙ",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù„ÙŠÙ„ Ø§Ù„ØªÙƒØ§Ù„ÙŠÙ");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù„ÙŠÙ„ Ø§Ù„ØªÙƒØ§Ù„ÙŠÙ: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CalculateROI_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("Ø³ÙŠØªÙ… Ø­Ø³Ø§Ø¨ Ø§Ù„Ø¹Ø§Ø¦Ø¯ Ø¹Ù„Ù‰ Ø§Ù„Ø§Ø³ØªØ«Ù…Ø§Ø±", "Ø§Ù„Ø¹Ø§Ø¦Ø¯ Ø¹Ù„Ù‰ Ø§Ù„Ø§Ø³ØªØ«Ù…Ø§Ø±",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­Ø³Ø§Ø¨ Ø§Ù„Ø¹Ø§Ø¦Ø¯ Ø¹Ù„Ù‰ Ø§Ù„Ø§Ø³ØªØ«Ù…Ø§Ø±");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø­Ø³Ø§Ø¨ Ø§Ù„Ø¹Ø§Ø¦Ø¯ Ø¹Ù„Ù‰ Ø§Ù„Ø§Ø³ØªØ«Ù…Ø§Ø±: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}



