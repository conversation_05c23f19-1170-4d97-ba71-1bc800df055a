using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Threading.Tasks;

namespace FishFarmManagement.BLL.Services
{
    public class UserManagementService : IUserManagementService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<UserManagementService> _logger;

        public UserManagementService(IUnitOfWork unitOfWork, ILogger<UserManagementService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task InitializeDefaultDataAsync()
        {
            try
            {
                // Check if any user exists. If so, assume initialization is done.
                if (await _unitOfWork.Users.AnyAsync(u => true))
                {
                    return;
                }

                _logger.LogInformation("No users found. Initializing default data...");

                // Create default role
                var adminRole = new Role
                {
                    RoleName = "Admin",
                    Description = "Administrator with full access"
                    // In a real app, you would also add all permissions here.
                };
                await _unitOfWork.Roles.AddAsync(adminRole);
                await _unitOfWork.SaveChangesAsync();

                // Create default admin user
                var adminUser = new User
                {
                    Username = "admin",
                    FullName = "المدير العام",
                    Email = "<EMAIL>",
                    RoleId = adminRole.Id,
                    Status = UserStatus.Active,
                    MustChangePassword = true // Force password change on first login
                };
                adminUser.SetPassword("admin123"); // Default password

                await _unitOfWork.Users.AddAsync(adminUser);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Default admin user and role created successfully.");
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "An error occurred during default data initialization.");
                throw;
            }
        }

        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            try
            {
                return await _unitOfWork.Users.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على جميع المستخدمين");
                throw;
            }
        }

        public async Task<User?> GetUserByIdAsync(int id)
        {
            try
            {
                return await _unitOfWork.Users.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المستخدم بالمعرف {Id}", id);
                throw;
            }
        }

        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            try
            {
                var users = await _unitOfWork.Users.FindAsync(u => u.Username == username);
                return users.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المستخدم باسم المستخدم {Username}", username);
                throw;
            }
        }

        public async Task<User> CreateUserAsync(User user)
        {
            try
            {
                await _unitOfWork.Users.AddAsync(user);
                await _unitOfWork.SaveChangesAsync();
                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء المستخدم");
                throw;
            }
        }

        public async Task<User> UpdateUserAsync(User user)
        {
            try
            {
                await _unitOfWork.Users.UpdateAsync(user);
                await _unitOfWork.SaveChangesAsync();
                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المستخدم");
                throw;
            }
        }

        public async Task<bool> DeleteUserAsync(int id)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(id);
                if (user == null) return false;

                await _unitOfWork.Users.DeleteAsync(user);
                await _unitOfWork.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المستخدم");
                throw;
            }
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId);
                if (user == null) return false;

                if (!user.VerifyPassword(currentPassword)) return false;

                user.SetPassword(newPassword);
                await _unitOfWork.Users.UpdateAsync(user);
                await _unitOfWork.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير كلمة المرور");
                throw;
            }
        }
    }
}