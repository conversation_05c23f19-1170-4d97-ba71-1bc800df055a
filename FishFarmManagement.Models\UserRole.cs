using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// ربط المستخدم بالدور
    /// User role relationship
    /// </summary>
    public class UserRole : BaseEntity
    {
        /// <summary>
        /// معرف المستخدم
        /// User ID
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// المستخدم
        /// User
        /// </summary>
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        /// <summary>
        /// معرف الدور
        /// Role ID
        /// </summary>
        [Required]
        public int RoleId { get; set; }

        /// <summary>
        /// الدور
        /// Role
        /// </summary>
        [ForeignKey("RoleId")]
        public virtual Role Role { get; set; } = null!;

        /// <summary>
        /// تاريخ منح الدور
        /// Date role was granted
        /// </summary>
        [Required]
        public DateTime GrantedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ انتهاء الدور (اختياري)
        /// Role expiry date (optional)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// من منح الدور
        /// Who granted the role
        /// </summary>
        [Required]
        [StringLength(100)]
        public string GrantedBy { get; set; } = string.Empty;

        /// <summary>
        /// هل الدور نشط
        /// Is role active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ملاحظات
        /// Notes
        /// </summary>
        [StringLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// التحقق من أن الدور لم ينته
        /// Check if role is not expired
        /// </summary>
        public bool IsExpired()
        {
            return ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now;
        }

        /// <summary>
        /// التحقق من أن الدور صالح
        /// Check if role is valid
        /// </summary>
        public bool IsValid()
        {
            return IsActive && !IsExpired();
        }
    }
}
