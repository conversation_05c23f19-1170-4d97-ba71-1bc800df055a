using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// حركة المخزون
    /// Inventory movement
    /// </summary>
    public class InventoryMovement : BaseEntity
    {
        /// <summary>
        /// معرف الصنف
        /// Item ID
        /// </summary>
        [Required]
        public int ItemId { get; set; }

        /// <summary>
        /// الصنف
        /// Item
        /// </summary>
        [ForeignKey("ItemId")]
        public virtual Inventory Item { get; set; } = null!;

        /// <summary>
        /// تاريخ الحركة
        /// Movement date
        /// </summary>
        [Required]
        public DateTime MovementDate { get; set; } = DateTime.Now;

        /// <summary>
        /// نوع الحركة
        /// Movement type
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MovementType { get; set; } = string.Empty; // إضافة، استهلاك، تعديل، جرد

        /// <summary>
        /// الكمية (موجبة للإضافة، سالبة للاستهلاك)
        /// Quantity (positive for addition, negative for consumption)
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(10,3)")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// سعر الوحدة وقت الحركة
        /// Unit price at time of movement
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// القيمة الإجمالية للحركة
        /// Total value of movement
        /// </summary>
        [Column(TypeName = "decimal(15,2)")]
        public decimal TotalValue { get; set; }

        /// <summary>
        /// الكمية قبل الحركة
        /// Quantity before movement
        /// </summary>
        [Column(TypeName = "decimal(10,3)")]
        public decimal QuantityBefore { get; set; }

        /// <summary>
        /// الكمية بعد الحركة
        /// Quantity after movement
        /// </summary>
        [Column(TypeName = "decimal(10,3)")]
        public decimal QuantityAfter { get; set; }

        /// <summary>
        /// سبب الحركة
        /// Movement reason
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// رقم الدفعة
        /// Batch number
        /// </summary>
        [StringLength(50)]
        public string? BatchNumber { get; set; }

        /// <summary>
        /// المرجع (رقم الفاتورة، رقم الطلب، إلخ)
        /// Reference (invoice number, order number, etc.)
        /// </summary>
        [StringLength(100)]
        public string? Reference { get; set; }

        /// <summary>
        /// المستخدم الذي قام بالحركة
        /// User who performed the movement
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// ملاحظات إضافية
        /// Additional notes
        /// </summary>
        [StringLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// حساب القيمة الإجمالية
        /// Calculate total value
        /// </summary>
        public void CalculateTotalValue()
        {
            TotalValue = Math.Abs(Quantity) * UnitPrice;
        }
    }

    /// <summary>
    /// أنواع حركة المخزون
    /// Inventory movement types
    /// </summary>
    public static class InventoryMovementType
    {
        public const string Purchase = "شراء";
        public const string Consumption = "استهلاك";
        public const string Adjustment = "تعديل";
        public const string Count = "جرد";
        public const string Transfer = "نقل";
        public const string Return = "إرجاع";
        public const string Damage = "تلف";
        public const string Expiry = "انتهاء صلاحية";
    }
}
