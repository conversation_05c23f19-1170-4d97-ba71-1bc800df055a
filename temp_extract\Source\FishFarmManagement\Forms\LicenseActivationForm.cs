﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Models.License;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ ØªÙØ¹ÙŠÙ„ Ø§Ù„ØªØ±Ø®ÙŠØµ Ø§Ù„ØªØ¬Ø§Ø±ÙŠ
    /// Commercial license activation form
    /// </summary>
    public partial class LicenseActivationForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILicenseService _licenseService;
        private readonly ILogger<LicenseActivationForm> _logger;

        public LicenseActivationForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _licenseService = serviceProvider.GetRequiredService<ILicenseService>();
            _logger = serviceProvider.GetRequiredService<ILogger<LicenseActivationForm>>();

            InitializeComponent();
            LoadCurrentLicense();
        }

        private void InitializeComponent()
        {
            // Form properties
            this.Text = "ØªÙØ¹ÙŠÙ„ Ø§Ù„ØªØ±Ø®ÙŠØµ Ø§Ù„ØªØ¬Ø§Ø±ÙŠ";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Create main panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Title
            var titleLabel = new Label
            {
                Text = "ØªÙØ¹ÙŠÙ„ Ø§Ù„ØªØ±Ø®ÙŠØµ Ø§Ù„ØªØ¬Ø§Ø±ÙŠ",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40,
                ForeColor = Color.FromArgb(0, 120, 215)
            };

            // Current license info panel
            var currentLicensePanel = CreateCurrentLicensePanel();

            // License key input panel
            var licenseKeyPanel = CreateLicenseKeyPanel();

            // Buttons panel
            var buttonsPanel = CreateButtonsPanel();

            // Add controls to main panel
            mainPanel.Controls.Add(titleLabel);
            mainPanel.Controls.Add(currentLicensePanel);
            mainPanel.Controls.Add(licenseKeyPanel);
            mainPanel.Controls.Add(buttonsPanel);

            this.Controls.Add(mainPanel);
        }

        private Panel CreateCurrentLicensePanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 120,
                Padding = new Padding(10),
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "Ø§Ù„ØªØ±Ø®ÙŠØµ Ø§Ù„Ø­Ø§Ù„ÙŠ:",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true
            };

            _currentLicenseLabel = new Label
            {
                Text = "Ø¬Ø§Ø±ÙŠ Ø§Ù„ØªØ­Ù…ÙŠÙ„...",
                Font = new Font("Segoe UI", 10F),
                Location = new Point(10, 35),
                Size = new Size(550, 80),
                AutoSize = false
            };

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(_currentLicenseLabel);

            return panel;
        }

        private Panel CreateLicenseKeyPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 150,
                Padding = new Padding(10)
            };

            var titleLabel = new Label
            {
                Text = "Ù…ÙØªØ§Ø­ Ø§Ù„ØªØ±Ø®ÙŠØµ Ø§Ù„Ø¬Ø¯ÙŠØ¯:",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true
            };

            var instructionLabel = new Label
            {
                Text = "Ø£Ø¯Ø®Ù„ Ù…ÙØªØ§Ø­ Ø§Ù„ØªØ±Ø®ÙŠØµ Ø§Ù„ØªØ¬Ø§Ø±ÙŠ Ø§Ù„Ù…ÙƒÙˆÙ† Ù…Ù† 16 Ø­Ø±Ù Ù…Ù‚Ø³Ù…Ø© Ø¥Ù„Ù‰ 4 Ù…Ø¬Ù…ÙˆØ¹Ø§Øª",
                Font = new Font("Segoe UI", 9F),
                Location = new Point(10, 35),
                Size = new Size(550, 30),
                ForeColor = Color.Gray
            };

            _licenseKeyTextBox = new TextBox
            {
                Location = new Point(10, 70),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 12F),
                TextAlign = HorizontalAlignment.Center,
                MaxLength = 19, // XXXX-XXXX-XXXX-XXXX
                PlaceholderText = "XXXX-XXXX-XXXX-XXXX"
            };

            _licenseKeyTextBox.TextChanged += LicenseKeyTextBox_TextChanged;

            var formatButton = new Button
            {
                Text = "ØªÙ†Ø³ÙŠÙ‚ ØªÙ„Ù‚Ø§Ø¦ÙŠ",
                Location = new Point(320, 70),
                Size = new Size(100, 25),
                Font = new Font("Segoe UI", 9F)
            };
            formatButton.Click += FormatButton_Click;

            _statusLabel = new Label
            {
                Location = new Point(10, 105),
                Size = new Size(550, 30),
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.Gray
            };

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(instructionLabel);
            panel.Controls.Add(_licenseKeyTextBox);
            panel.Controls.Add(formatButton);
            panel.Controls.Add(_statusLabel);

            return panel;
        }

        private Panel CreateButtonsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                Padding = new Padding(10)
            };

            _activateButton = new Button
            {
                Text = "ØªÙØ¹ÙŠÙ„ Ø§Ù„ØªØ±Ø®ÙŠØµ",
                Size = new Size(120, 35),
                Location = new Point(10, 12),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                Enabled = false
            };
            _activateButton.Click += ActivateButton_Click;

            var trialButton = new Button
            {
                Text = "ØªØ±Ø®ÙŠØµ ØªØ¬Ø±ÙŠØ¨ÙŠ (30 ÙŠÙˆÙ…)",
                Size = new Size(150, 35),
                Location = new Point(140, 12),
                Font = new Font("Segoe UI", 9F),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White
            };
            trialButton.Click += TrialButton_Click;

            var cancelButton = new Button
            {
                Text = "Ø¥Ù„ØºØ§Ø¡",
                Size = new Size(80, 35),
                Location = new Point(500, 12),
                Font = new Font("Segoe UI", 10F)
            };
            cancelButton.Click += (s, e) => this.Close();

            panel.Controls.Add(_activateButton);
            panel.Controls.Add(trialButton);
            panel.Controls.Add(cancelButton);

            return panel;
        }

        private async void LoadCurrentLicense()
        {
            try
            {
                var license = await _licenseService.GetCurrentLicenseAsync();
                if (license != null)
                {
                    var status = license.IsValid() ? "ØµØ§Ù„Ø­" : "Ù…Ù†ØªÙ‡ÙŠ Ø§Ù„ØµÙ„Ø§Ø­ÙŠØ©";
                    var remainingDays = license.GetRemainingDays();
                    
                    _currentLicenseLabel.Text = $"Ø§Ù„Ù†ÙˆØ¹: {license.GetTypeName()}\n" +
                                               $"Ø§Ù„Ø­Ø§Ù„Ø©: {status}\n" +
                                               $"Ø§Ù„Ø£ÙŠØ§Ù… Ø§Ù„Ù…ØªØ¨Ù‚ÙŠØ©: {remainingDays} ÙŠÙˆÙ…\n" +
                                               $"Ø§Ù„Ø­Ø¯ Ø§Ù„Ø£Ù‚ØµÙ‰ Ù„Ù„Ø£Ø­ÙˆØ§Ø¶: {license.MaxPonds}\n" +
                                               $"Ø§Ù„Ø­Ø¯ Ø§Ù„Ø£Ù‚ØµÙ‰ Ù„Ù„Ù…Ø³ØªØ®Ø¯Ù…ÙŠÙ†: {license.MaxUsers}";

                    _currentLicenseLabel.ForeColor = license.IsValid() ? Color.Green : Color.Red;
                }
                else
                {
                    _currentLicenseLabel.Text = "Ù„Ø§ ÙŠÙˆØ¬Ø¯ ØªØ±Ø®ÙŠØµ Ù…Ø«Ø¨Øª";
                    _currentLicenseLabel.ForeColor = Color.Orange;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø§Ù„ØªØ±Ø®ÙŠØµ");
                _currentLicenseLabel.Text = "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø§Ù„ØªØ±Ø®ÙŠØµ";
                _currentLicenseLabel.ForeColor = Color.Red;
            }
        }

        private void LicenseKeyTextBox_TextChanged(object sender, EventArgs e)
        {
            var text = _licenseKeyTextBox.Text.Replace("-", "");
            
            // ØªÙ†Ø³ÙŠÙ‚ ØªÙ„Ù‚Ø§Ø¦ÙŠ
            if (text.Length > 16) text = text.Substring(0, 16);
            
            var formatted = "";
            for (int i = 0; i < text.Length; i++)
            {
                if (i > 0 && i % 4 == 0) formatted += "-";
                formatted += text[i].ToString().ToUpper();
            }
            
            if (formatted != _licenseKeyTextBox.Text)
            {
                _licenseKeyTextBox.Text = formatted;
                _licenseKeyTextBox.SelectionStart = formatted.Length;
            }

            // Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† ØµØ­Ø© Ø§Ù„ØªÙ†Ø³ÙŠÙ‚
            var isValid = IsValidLicenseKeyFormat(formatted);
            _activateButton.Enabled = isValid;
            
            if (isValid)
            {
                _statusLabel.Text = "âœ“ ØªÙ†Ø³ÙŠÙ‚ Ù…ÙØªØ§Ø­ Ø§Ù„ØªØ±Ø®ÙŠØµ ØµØ­ÙŠØ­";
                _statusLabel.ForeColor = Color.Green;
            }
            else if (formatted.Length > 0)
            {
                _statusLabel.Text = "âœ— ØªÙ†Ø³ÙŠÙ‚ Ù…ÙØªØ§Ø­ Ø§Ù„ØªØ±Ø®ÙŠØµ ØºÙŠØ± ØµØ­ÙŠØ­";
                _statusLabel.ForeColor = Color.Red;
            }
            else
            {
                _statusLabel.Text = "";
            }
        }

        private void FormatButton_Click(object? sender, EventArgs e)
        {
            var text = _licenseKeyTextBox.Text.Replace("-", "").ToUpper();
            var formatted = "";
            
            for (int i = 0; i < Math.Min(text.Length, 16); i++)
            {
                if (i > 0 && i % 4 == 0) formatted += "-";
                formatted += text[i];
            }
            
            _licenseKeyTextBox.Text = formatted;
            _licenseKeyTextBox.Focus();
        }

        private async void ActivateButton_Click(object? sender, EventArgs e)
        {
            try
            {
                _activateButton.Enabled = false;
                _activateButton.Text = "Ø¬Ø§Ø±ÙŠ Ø§Ù„ØªÙØ¹ÙŠÙ„...";
                Cursor = Cursors.WaitCursor;

                var licenseKey = _licenseKeyTextBox.Text.Trim();
                
                // Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† Ø§ØªØµØ§Ù„ Ø§Ù„Ø¥Ù†ØªØ±Ù†Øª
                var hasInternet = await _licenseService.CheckInternetConnectionAsync();
                if (!hasInternet)
                {
                    var result = MessageBox.Show(
                        "Ù„Ø§ ÙŠÙˆØ¬Ø¯ Ø§ØªØµØ§Ù„ Ø¨Ø§Ù„Ø¥Ù†ØªØ±Ù†Øª. Ù‡Ù„ ØªØ±ÙŠØ¯ Ø§Ù„Ù…ØªØ§Ø¨Ø¹Ø© Ù…Ø¹ Ø§Ù„ØªØ±Ø®ÙŠØµ Ø§Ù„Ù…Ø­Ù„ÙŠØŸ",
                        "ØªØ­Ø°ÙŠØ±",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Warning);
                    
                    if (result != DialogResult.Yes)
                    {
                        return;
                    }
                }

                // ØªÙØ¹ÙŠÙ„ Ø§Ù„ØªØ±Ø®ÙŠØµ
                var activationResult = await _licenseService.ActivateLicenseAsync(licenseKey);
                
                if (activationResult.Success)
                {
                    MessageBox.Show(
                        $"ØªÙ… ØªÙØ¹ÙŠÙ„ Ø§Ù„ØªØ±Ø®ÙŠØµ Ø¨Ù†Ø¬Ø§Ø­!\n\n" +
                        $"Ø§Ù„Ù†ÙˆØ¹: {activationResult.License?.GetTypeName()}\n" +
                        $"ØªØ§Ø±ÙŠØ® Ø§Ù„Ø§Ù†ØªÙ‡Ø§Ø¡: {activationResult.License?.ExpirationDate:dd/MM/yyyy}\n" +
                        $"Ø§Ù„Ø­Ø¯ Ø§Ù„Ø£Ù‚ØµÙ‰ Ù„Ù„Ø£Ø­ÙˆØ§Ø¶: {activationResult.License?.MaxPonds}\n" +
                        $"Ø§Ù„Ø­Ø¯ Ø§Ù„Ø£Ù‚ØµÙ‰ Ù„Ù„Ù…Ø³ØªØ®Ø¯Ù…ÙŠÙ†: {activationResult.License?.MaxUsers}",
                        "Ù†Ø¬Ø­ Ø§Ù„ØªÙØ¹ÙŠÙ„",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);

                    LoadCurrentLicense();
                    this.Close();
                }
                else
                {
                    var errorMessage = GetErrorMessage(activationResult.Error);
                    MessageBox.Show(
                        $"ÙØ´Ù„ ÙÙŠ ØªÙØ¹ÙŠÙ„ Ø§Ù„ØªØ±Ø®ÙŠØµ:\n{errorMessage}",
                        "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„ØªÙØ¹ÙŠÙ„",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªÙØ¹ÙŠÙ„ Ø§Ù„ØªØ±Ø®ÙŠØµ");
                MessageBox.Show(
                    "Ø­Ø¯Ø« Ø®Ø·Ø£ ØºÙŠØ± Ù…ØªÙˆÙ‚Ø¹ Ø£Ø«Ù†Ø§Ø¡ ØªÙØ¹ÙŠÙ„ Ø§Ù„ØªØ±Ø®ÙŠØµ",
                    "Ø®Ø·Ø£",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
            finally
            {
                _activateButton.Enabled = true;
                _activateButton.Text = "ØªÙØ¹ÙŠÙ„ Ø§Ù„ØªØ±Ø®ÙŠØµ";
                Cursor = Cursors.Default;
            }
        }

        private async void TrialButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "Ù‡Ù„ ØªØ±ÙŠØ¯ Ø¥Ù†Ø´Ø§Ø¡ ØªØ±Ø®ÙŠØµ ØªØ¬Ø±ÙŠØ¨ÙŠ Ù„Ù…Ø¯Ø© 30 ÙŠÙˆÙ…ØŸ\n" +
                    "Ù‡Ø°Ø§ Ø§Ù„ØªØ±Ø®ÙŠØµ ÙŠØ³Ù…Ø­ Ø¨Ù€:\n" +
                    "â€¢ 5 Ø£Ø­ÙˆØ§Ø¶ ÙƒØ­Ø¯ Ø£Ù‚ØµÙ‰\n" +
                    "â€¢ Ù…Ø³ØªØ®Ø¯Ù… ÙˆØ§Ø­Ø¯\n" +
                    "â€¢ Ø¬Ù…ÙŠØ¹ Ø§Ù„Ù…ÙŠØ²Ø§Øª Ø§Ù„Ø£Ø³Ø§Ø³ÙŠØ©",
                    "ØªØ±Ø®ÙŠØµ ØªØ¬Ø±ÙŠØ¨ÙŠ",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var trialLicense = await _licenseService.CreateTrialLicenseAsync();
                    
                    MessageBox.Show(
                        $"ØªÙ… Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªØ±Ø®ÙŠØµ Ø§Ù„ØªØ¬Ø±ÙŠØ¨ÙŠ Ø¨Ù†Ø¬Ø§Ø­!\n\n" +
                        $"Ù…ÙØªØ§Ø­ Ø§Ù„ØªØ±Ø®ÙŠØµ: {trialLicense.LicenseKey}\n" +
                        $"ØªØ§Ø±ÙŠØ® Ø§Ù„Ø§Ù†ØªÙ‡Ø§Ø¡: {trialLicense.ExpirationDate:dd/MM/yyyy}\n" +
                        $"Ø§Ù„Ø­Ø¯ Ø§Ù„Ø£Ù‚ØµÙ‰ Ù„Ù„Ø£Ø­ÙˆØ§Ø¶: {trialLicense.MaxPonds}\n" +
                        $"Ø§Ù„Ø­Ø¯ Ø§Ù„Ø£Ù‚ØµÙ‰ Ù„Ù„Ù…Ø³ØªØ®Ø¯Ù…ÙŠÙ†: {trialLicense.MaxUsers}",
                        "Ø§Ù„ØªØ±Ø®ÙŠØµ Ø§Ù„ØªØ¬Ø±ÙŠØ¨ÙŠ",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);

                    LoadCurrentLicense();
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªØ±Ø®ÙŠØµ Ø§Ù„ØªØ¬Ø±ÙŠØ¨ÙŠ");
                MessageBox.Show(
                    "Ø­Ø¯Ø« Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªØ±Ø®ÙŠØµ Ø§Ù„ØªØ¬Ø±ÙŠØ¨ÙŠ",
                    "Ø®Ø·Ø£",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        private bool IsValidLicenseKeyFormat(string licenseKey)
        {
            if (string.IsNullOrWhiteSpace(licenseKey))
                return false;

            var parts = licenseKey.Split('-');
            return parts.Length == 4 && 
                   parts.All(p => p.Length == 4 && p.All(c => char.IsLetterOrDigit(c)));
        }

        private string GetErrorMessage(LicenseActivationError? error)
        {
            return error switch
            {
                LicenseActivationError.InvalidKey => "Ù…ÙØªØ§Ø­ Ø§Ù„ØªØ±Ø®ÙŠØµ ØºÙŠØ± ØµØ­ÙŠØ­ Ø£Ùˆ Ù…Ù†ØªÙ‡ÙŠ Ø§Ù„ØµÙ„Ø§Ø­ÙŠØ©",
                LicenseActivationError.AlreadyActivated => "Ù‡Ø°Ø§ Ø§Ù„ØªØ±Ø®ÙŠØµ Ù…ÙØ¹Ù„ Ø¨Ø§Ù„ÙØ¹Ù„ Ø¹Ù„Ù‰ Ø¬Ù‡Ø§Ø² Ø¢Ø®Ø±",
                LicenseActivationError.MaxActivationsReached => "ØªÙ… Ø§Ù„ÙˆØµÙˆÙ„ Ù„Ù„Ø­Ø¯ Ø§Ù„Ø£Ù‚ØµÙ‰ Ù„Ø¹Ø¯Ø¯ Ø§Ù„ØªÙØ¹ÙŠÙ„Ø§Øª",
                LicenseActivationError.NetworkError => "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø§ØªØµØ§Ù„ Ø¨Ø§Ù„Ø´Ø¨ÙƒØ©",
                LicenseActivationError.ServerError => "Ø®Ø·Ø£ ÙÙŠ Ø®Ø§Ø¯Ù… Ø§Ù„ØªØ±Ø®ÙŠØµ",
                LicenseActivationError.HardwareError => "Ø®Ø·Ø£ ÙÙŠ Ù…Ø¹Ø±Ù Ø§Ù„Ø¬Ù‡Ø§Ø²",
                _ => "Ø®Ø·Ø£ ØºÙŠØ± Ù…Ø¹Ø±ÙˆÙ"
            };
        }

        // Controls
        private Label _currentLicenseLabel = null!;
        private TextBox _licenseKeyTextBox = null!;
        private Label _statusLabel = null!;
        private Button _activateButton = null!;
    }
} 


