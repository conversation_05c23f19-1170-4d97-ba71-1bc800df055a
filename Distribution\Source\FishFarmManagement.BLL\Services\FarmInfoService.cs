using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة إدارة معلومات المزرعة
    /// Farm information management service
    /// </summary>
    public class FarmInfoService : IFarmInfoService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<FarmInfoService> _logger;

        public FarmInfoService(IUnitOfWork unitOfWork, ILogger<FarmInfoService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<FarmInfo>> GetAllAsync()
        {
            try
            {
                return await _unitOfWork.FarmInfos.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على جميع معلومات المزارع");
                throw;
            }
        }

        public async Task<FarmInfo?> GetByIdAsync(int id)
        {
            try
            {
                return await _unitOfWork.FarmInfos.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معلومات المزرعة {FarmId}", id);
                throw;
            }
        }

        public async Task<FarmInfo> AddAsync(FarmInfo entity)
        {
            try
            {
                var validationResult = await ValidateAsync(entity);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"بيانات المزرعة غير صحيحة: {string.Join(", ", validationResult.Errors)}");
                }

                entity.CreatedDate = DateTime.Now;

                var result = await _unitOfWork.FarmInfos.AddAsync(entity);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إضافة معلومات مزرعة جديدة: {FarmName}", entity.FarmName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة معلومات المزرعة {FarmName}", entity.FarmName);
                throw;
            }
        }

        public async Task<FarmInfo> UpdateAsync(FarmInfo entity)
        {
            try
            {
                var validationResult = await ValidateAsync(entity);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"بيانات المزرعة غير صحيحة: {string.Join(", ", validationResult.Errors)}");
                }

                entity.UpdatedDate = DateTime.Now;

                var result = await _unitOfWork.FarmInfos.UpdateAsync(entity);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث معلومات المزرعة: {FarmName}", entity.FarmName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث معلومات المزرعة {FarmId}", entity.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var farmInfo = await _unitOfWork.FarmInfos.GetByIdAsync(id);
                if (farmInfo == null)
                {
                    return false;
                }

                await _unitOfWork.FarmInfos.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم حذف معلومات المزرعة: {FarmName}", farmInfo.FarmName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف معلومات المزرعة {FarmId}", id);
                throw;
            }
        }

        public async Task<ValidationResult> ValidateAsync(FarmInfo entity)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(entity.FarmName))
            {
                result.AddError("اسم المزرعة مطلوب");
            }

            if (string.IsNullOrWhiteSpace(entity.SupervisorName))
            {
                result.AddError("اسم المشرف مطلوب");
            }

            // Remove validation for non-existent properties
            // The FarmInfo model doesn't have EstablishedDate or TotalArea

            return result;
        }

        public async Task<FarmInfo?> GetCurrentFarmInfoAsync()
        {
            try
            {
                var farmInfos = await _unitOfWork.FarmInfos.GetAllAsync();
                return farmInfos.FirstOrDefault(); // Assuming single farm setup
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معلومات المزرعة الحالية");
                throw;
            }
        }

        public async Task<FarmInfo> UpdateFarmInfoAsync(FarmInfo farmInfo)
        {
            try
            {
                var existingFarm = await GetCurrentFarmInfoAsync();
                if (existingFarm != null)
                {
                    farmInfo.Id = existingFarm.Id;
                    return await UpdateAsync(farmInfo);
                }
                else
                {
                    return await AddAsync(farmInfo);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث معلومات المزرعة");
                throw;
            }
        }

        public async Task<bool> UpdateFarmLogoAsync(byte[] logoData)
        {
            try
            {
                var farmInfo = await GetCurrentFarmInfoAsync();
                if (farmInfo == null)
                {
                    return false;
                }

                farmInfo.Logo = logoData;
                farmInfo.UpdatedDate = DateTime.Now;

                await _unitOfWork.FarmInfos.UpdateAsync(farmInfo);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث شعار المزرعة");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث شعار المزرعة");
                throw;
            }
        }

        public async Task<byte[]?> GetFarmLogoAsync()
        {
            try
            {
                var farmInfo = await GetCurrentFarmInfoAsync();
                return farmInfo?.Logo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على شعار المزرعة");
                throw;
            }
        }

        public async Task<FarmInfo> CreateDefaultFarmInfoAsync()
        {
            try
            {
                var defaultFarmInfo = new FarmInfo
                {
                    FarmName = "مزرعة الأسماك الجديدة",
                    Location = "غير محدد",
                    ContactInfo = "غير محدد",
                    Phone = "غير محدد",
                    Email = "غير محدد",
                    Notes = "معلومات افتراضية للمزرعة",
                    SupervisorName = "طارق حسين صالح",
                    SupervisorEmail = "<EMAIL>",
                    CreatedDate = DateTime.Now
                };

                return await AddAsync(defaultFarmInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء معلومات مزرعة افتراضية");
                throw;
            }
        }

        public async Task<bool> FarmInfoExistsAsync()
        {
            try
            {
                var farmInfos = await _unitOfWork.FarmInfos.GetAllAsync();
                return farmInfos.Any();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود معلومات المزرعة");
                throw;
            }
        }

        public async Task<FarmStatistics> GetFarmStatisticsAsync()
        {
            try
            {
                var farmInfo = await GetCurrentFarmInfoAsync();
                if (farmInfo == null)
                {
                    throw new InvalidOperationException("معلومات المزرعة غير موجودة");
                }

                var cycles = await _unitOfWork.ProductionCycles.GetAllAsync();
                var ponds = await _unitOfWork.Ponds.GetAllAsync();
                var employees = await _unitOfWork.Employees.GetAllAsync();

                var statistics = new FarmStatistics
                {
                    FarmName = farmInfo.FarmName,
                    EstablishedDate = DateTime.Now, // Use current date as placeholder
                    TotalCycles = cycles.Count(),
                    ActiveCycles = cycles.Count(c => c.Status == "نشطة"),
                    CompletedCycles = cycles.Count(c => c.Status == "مكتملة"),
                    TotalPonds = ponds.Count(),
                    ActivePonds = ponds.Count(p => p.Status == "نشط"),
                    TotalEmployees = employees.Count(),
                    ActiveEmployees = employees.Count(e => e.Status == "نشط"),
                    TotalProduction = ponds.Sum(p => p.FishCount * p.AverageWeight),
                    LastUpdated = DateTime.Now
                };

                // Calculate financial statistics
                var completedCycles = cycles.Where(c => c.Status == "مكتملة");
                if (completedCycles.Any())
                {
                    statistics.AverageCycleDuration = (decimal)completedCycles
                        .Where(c => c.EndDate.HasValue)
                        .Average(c => (c.EndDate!.Value - c.StartDate).Days);
                }

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على إحصائيات المزرعة");
                throw;
            }
        }

        public async Task<byte[]> ExportFarmInfoAsync(string format = "PDF")
        {
            try
            {
                var farmInfo = await GetCurrentFarmInfoAsync();
                if (farmInfo == null)
                {
                    throw new InvalidOperationException("معلومات المزرعة غير موجودة");
                }

                // This would typically use a document generation library
                // For now, returning empty byte array as placeholder
                _logger.LogInformation("تصدير معلومات المزرعة بصيغة {Format}", format);
                return new byte[0];
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير معلومات المزرعة");
                throw;
            }
        }
    }
}
