using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text.Json;

namespace FishFarmManagement.Helpers
{
    /// <summary>
    /// مساعد تحسين الأداء للتوزيع التجاري
    /// Performance optimizer for commercial distribution
    /// </summary>
    public class PerformanceOptimizer
    {
        private readonly ILogger<PerformanceOptimizer> _logger;
        private readonly string _optimizationLogPath;
        private readonly Dictionary<string, object> _cache;
        private readonly object _cacheLock = new object();

        public PerformanceOptimizer(ILogger<PerformanceOptimizer> logger)
        {
            _logger = logger;
            _optimizationLogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "performance.log");
            _cache = new Dictionary<string, object>();
        }

        /// <summary>
        /// تحسين قاعدة البيانات
        /// Optimize database
        /// </summary>
        public async Task<OptimizationResult> OptimizeDatabaseAsync()
        {
            var result = new OptimizationResult();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("بدء تحسين قاعدة البيانات");

                // تحسين الفهارس
                result.IndexOptimization = await OptimizeIndexesAsync();

                // تنظيف البيانات المؤقتة
                result.DataCleanup = await CleanupTemporaryDataAsync();

                // تحسين الاستعلامات
                result.QueryOptimization = await OptimizeQueriesAsync();

                // تحسين الذاكرة
                result.MemoryOptimization = await OptimizeMemoryAsync();

                stopwatch.Stop();
                result.ExecutionTime = stopwatch.ElapsedMilliseconds;
                result.Success = true;

                _logger.LogInformation("تم تحسين قاعدة البيانات بنجاح في {Time}ms", result.ExecutionTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين قاعدة البيانات");
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// تحسين الذاكرة المؤقتة
        /// Optimize memory cache
        /// </summary>
        public async Task<MemoryOptimizationResult> OptimizeMemoryAsync()
        {
            var result = new MemoryOptimizationResult();
            var initialMemory = GC.GetTotalMemory(false);

            try
            {
                // تنظيف الذاكرة المؤقتة
                lock (_cacheLock)
                {
                    _cache.Clear();
                }

                // إجبار جمع القمامة
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var finalMemory = GC.GetTotalMemory(false);
                result.MemoryFreed = initialMemory - finalMemory;
                result.Success = true;

                _logger.LogInformation("تم تحسين الذاكرة: تم تحرير {MemoryFreed} bytes", result.MemoryFreed);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين الذاكرة");
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// تحسين الفهارس
        /// Optimize database indexes
        /// </summary>
        private async Task<bool> OptimizeIndexesAsync()
        {
            try
            {
                // في الإنتاج، سيتم تنفيذ أوامر SQL لتحسين الفهارس
                await Task.Delay(100); // محاكاة العملية
                
                _logger.LogInformation("تم تحسين فهارس قاعدة البيانات");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين الفهارس");
                return false;
            }
        }

        /// <summary>
        /// تنظيف البيانات المؤقتة
        /// Cleanup temporary data
        /// </summary>
        private async Task<bool> CleanupTemporaryDataAsync()
        {
            try
            {
                var tempDirectories = new[]
                {
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Temp"),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Cache"),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "Old")
                };

                foreach (var dir in tempDirectories)
                {
                    if (Directory.Exists(dir))
                    {
                        var files = Directory.GetFiles(dir, "*", SearchOption.AllDirectories);
                        foreach (var file in files)
                        {
                            try
                            {
                                var fileInfo = new FileInfo(file);
                                if (fileInfo.LastAccessTime < DateTime.Now.AddDays(-7))
                                {
                                    File.Delete(file);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "فشل في حذف الملف المؤقت: {File}", file);
                            }
                        }
                    }
                }

                _logger.LogInformation("تم تنظيف البيانات المؤقتة");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنظيف البيانات المؤقتة");
                return false;
            }
        }

        /// <summary>
        /// تحسين الاستعلامات
        /// Optimize queries
        /// </summary>
        private async Task<bool> OptimizeQueriesAsync()
        {
            try
            {
                // تحليل الاستعلامات البطيئة وتحسينها
                await Task.Delay(200); // محاكاة العملية
                
                _logger.LogInformation("تم تحسين الاستعلامات");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين الاستعلامات");
                return false;
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الأداء
        /// Get performance statistics
        /// </summary>
        public PerformanceStatistics GetPerformanceStatistics()
        {
            var stats = new PerformanceStatistics
            {
                Timestamp = DateTime.Now,
                TotalMemory = GC.GetTotalMemory(false),
                AvailableMemory = GetAvailableMemory(),
                CpuUsage = GetCpuUsage(),
                DiskSpace = GetDiskSpace(),
                CacheSize = _cache.Count,
                ProcessUptime = Process.GetCurrentProcess().TotalProcessorTime
            };

            return stats;
        }

        /// <summary>
        /// الحصول على الذاكرة المتاحة
        /// Get available memory
        /// </summary>
        private long GetAvailableMemory()
        {
            try
            {
                var computerInfo = new Microsoft.VisualBasic.Devices.ComputerInfo();
                return computerInfo.AvailablePhysicalMemory;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// الحصول على استخدام المعالج
        /// Get CPU usage
        /// </summary>
        private double GetCpuUsage()
        {
            try
            {
                var cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                cpuCounter.NextValue(); // أول قراءة
                Thread.Sleep(100);
                return Math.Round(cpuCounter.NextValue(), 2);
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// الحصول على مساحة القرص
        /// Get disk space
        /// </summary>
        private DiskSpaceInfo GetDiskSpace()
        {
            try
            {
                var drive = new DriveInfo(Path.GetPathRoot(AppDomain.CurrentDomain.BaseDirectory)!);
                return new DiskSpaceInfo
                {
                    TotalSpace = drive.TotalSize,
                    AvailableSpace = drive.AvailableFreeSpace,
                    UsedSpace = drive.TotalSize - drive.AvailableFreeSpace
                };
            }
            catch
            {
                return new DiskSpaceInfo();
            }
        }

        /// <summary>
        /// حفظ إحصائيات الأداء
        /// Save performance statistics
        /// </summary>
        public async Task SavePerformanceStatisticsAsync(PerformanceStatistics stats)
        {
            try
            {
                var logEntry = new
                {
                    Timestamp = stats.Timestamp,
                    TotalMemory = stats.TotalMemory,
                    AvailableMemory = stats.AvailableMemory,
                    CpuUsage = stats.CpuUsage,
                    CacheSize = stats.CacheSize
                };

                var json = JsonSerializer.Serialize(logEntry);
                await File.AppendAllTextAsync(_optimizationLogPath, json + Environment.NewLine);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ إحصائيات الأداء");
            }
        }

        /// <summary>
        /// تحسين الأداء التلقائي
        /// Automatic performance optimization
        /// </summary>
        public async Task AutoOptimizeAsync()
        {
            var stats = GetPerformanceStatistics();
            
            // تحسين تلقائي إذا كان استخدام الذاكرة عالي
            if (stats.TotalMemory > 500 * 1024 * 1024) // أكثر من 500 MB
            {
                _logger.LogInformation("تشغيل التحسين التلقائي بسبب استخدام الذاكرة العالي");
                await OptimizeMemoryAsync();
            }

            // تحسين تلقائي إذا كان استخدام المعالج عالي
            if (stats.CpuUsage > 80)
            {
                _logger.LogInformation("تشغيل التحسين التلقائي بسبب استخدام المعالج العالي");
                await OptimizeDatabaseAsync();
            }

            // حفظ الإحصائيات
            await SavePerformanceStatisticsAsync(stats);
        }
    }

    /// <summary>
    /// نتيجة التحسين
    /// Optimization result
    /// </summary>
    public class OptimizationResult
    {
        public bool Success { get; set; }
        public long ExecutionTime { get; set; }
        public string? ErrorMessage { get; set; }
        public bool IndexOptimization { get; set; }
        public bool DataCleanup { get; set; }
        public bool QueryOptimization { get; set; }
        public bool MemoryOptimization { get; set; }
    }

    /// <summary>
    /// نتيجة تحسين الذاكرة
    /// Memory optimization result
    /// </summary>
    public class MemoryOptimizationResult
    {
        public bool Success { get; set; }
        public long MemoryFreed { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// إحصائيات الأداء
    /// Performance statistics
    /// </summary>
    public class PerformanceStatistics
    {
        public DateTime Timestamp { get; set; }
        public long TotalMemory { get; set; }
        public long AvailableMemory { get; set; }
        public double CpuUsage { get; set; }
        public DiskSpaceInfo DiskSpace { get; set; } = new();
        public int CacheSize { get; set; }
        public TimeSpan ProcessUptime { get; set; }
    }

    /// <summary>
    /// معلومات مساحة القرص
    /// Disk space information
    /// </summary>
    public class DiskSpaceInfo
    {
        public long TotalSpace { get; set; }
        public long AvailableSpace { get; set; }
        public long UsedSpace { get; set; }
    }
} 