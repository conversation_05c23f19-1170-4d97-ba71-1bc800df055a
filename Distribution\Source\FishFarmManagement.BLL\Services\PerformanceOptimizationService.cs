using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using FishFarmManagement.DAL;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة تحسين الأداء المبسطة
    /// Simplified performance optimization service
    /// </summary>
    public class PerformanceOptimizationService
    {
        private readonly ILogger<PerformanceOptimizationService> _logger;
        private readonly IConfiguration _configuration;
        private readonly FishFarmDbContext _context;
        private System.Timers.Timer? _optimizationTimer;
        private readonly bool _isEnabled;

        public PerformanceOptimizationService(
            ILogger<PerformanceOptimizationService> logger,
            IConfiguration configuration,
            FishFarmDbContext context)
        {
            _logger = logger;
            _configuration = configuration;
            _context = context;
            _isEnabled = bool.Parse(_configuration["Application:Performance:OptimizeMemory"] ?? "true");

            if (_isEnabled)
            {
                InitializeOptimization();
            }
        }

        /// <summary>
        /// تهيئة التحسين التلقائي
        /// Initialize automatic optimization
        /// </summary>
        private void InitializeOptimization()
        {
            try
            {
                // تشغيل التحسين كل 30 دقيقة
                _optimizationTimer = new System.Timers.Timer(1800000); // 30 دقيقة
                _optimizationTimer.Elapsed += OnOptimizationTimerElapsed;
                _optimizationTimer.AutoReset = true;
                _optimizationTimer.Start();

                _logger.LogInformation("تم تهيئة خدمة تحسين الأداء");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة خدمة تحسين الأداء");
            }
        }

        /// <summary>
        /// تحسين الذاكرة
        /// Optimize memory
        /// </summary>
        public void OptimizeMemory()
        {
            try
            {
                _logger.LogDebug("بدء تحسين الذاكرة");

                // تنظيف الذاكرة
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                // ضغط الذاكرة
                GC.Collect(2, GCCollectionMode.Forced, true, true);

                _logger.LogDebug("تم تحسين الذاكرة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين الذاكرة");
            }
        }

        /// <summary>
        /// تحسين قاعدة البيانات
        /// Optimize database
        /// </summary>
        public async Task OptimizeDatabaseAsync()
        {
            try
            {
                _logger.LogDebug("بدء تحسين قاعدة البيانات");

                // تنفيذ VACUUM لقاعدة بيانات SQLite (مبسط)
                // await _context.Database.ExecuteSqlRawAsync("VACUUM;");

                // إعادة فهرسة الجداول (مبسط)
                // await _context.Database.ExecuteSqlRawAsync("REINDEX;");

                // تحليل الجداول لتحسين الاستعلامات (مبسط)
                // await _context.Database.ExecuteSqlRawAsync("ANALYZE;");

                // تحسين مبسط - فقط تنظيف الذاكرة
                GC.Collect();

                _logger.LogInformation("تم تحسين قاعدة البيانات");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين قاعدة البيانات");
            }
        }

        /// <summary>
        /// تنظيف الملفات المؤقتة
        /// Clean temporary files
        /// </summary>
        public void CleanTemporaryFiles()
        {
            try
            {
                _logger.LogDebug("بدء تنظيف الملفات المؤقتة");

                var tempPaths = new[]
                {
                    "Temp",
                    Path.GetTempPath(),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Temp")
                };

                foreach (var tempPath in tempPaths)
                {
                    if (Directory.Exists(tempPath))
                    {
                        CleanDirectory(tempPath, TimeSpan.FromDays(7));
                    }
                }

                _logger.LogDebug("تم تنظيف الملفات المؤقتة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنظيف الملفات المؤقتة");
            }
        }

        /// <summary>
        /// تنظيف مجلد
        /// Clean directory
        /// </summary>
        private void CleanDirectory(string directoryPath, TimeSpan maxAge)
        {
            try
            {
                var directory = new DirectoryInfo(directoryPath);
                if (!directory.Exists) return;

                var cutoffDate = DateTime.Now - maxAge;

                // حذف الملفات القديمة
                var oldFiles = directory.GetFiles()
                    .Where(f => f.CreationTime < cutoffDate && f.LastAccessTime < cutoffDate);

                foreach (var file in oldFiles)
                {
                    try
                    {
                        file.Delete();
                    }
                    catch
                    {
                        // تجاهل الأخطاء في حذف الملفات المحمية
                    }
                }

                // حذف المجلدات الفارغة
                var emptyDirectories = directory.GetDirectories()
                    .Where(d => !d.GetFiles("*", SearchOption.AllDirectories).Any());

                foreach (var dir in emptyDirectories)
                {
                    try
                    {
                        dir.Delete(true);
                    }
                    catch
                    {
                        // تجاهل الأخطاء في حذف المجلدات المحمية
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "خطأ في تنظيف المجلد: {Directory}", directoryPath);
            }
        }

        /// <summary>
        /// تحسين ملفات السجل
        /// Optimize log files
        /// </summary>
        public void OptimizeLogFiles()
        {
            try
            {
                _logger.LogDebug("بدء تحسين ملفات السجل");

                var logsPath = "Logs";
                if (Directory.Exists(logsPath))
                {
                    // حذف ملفات السجل الأقدم من 30 يوماً
                    CleanDirectory(logsPath, TimeSpan.FromDays(30));

                    // ضغط ملفات السجل الأقدم من 7 أيام
                    var oldLogFiles = Directory.GetFiles(logsPath, "*.txt")
                        .Where(f => File.GetCreationTime(f) < DateTime.Now.AddDays(-7) && 
                                   !f.EndsWith(".gz"));

                    foreach (var logFile in oldLogFiles)
                    {
                        try
                        {
                            CompressLogFile(logFile);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "خطأ في ضغط ملف السجل: {LogFile}", logFile);
                        }
                    }
                }

                _logger.LogDebug("تم تحسين ملفات السجل");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين ملفات السجل");
            }
        }

        /// <summary>
        /// ضغط ملف سجل
        /// Compress log file
        /// </summary>
        private void CompressLogFile(string logFilePath)
        {
            var compressedPath = logFilePath + ".gz";
            
            using var originalFileStream = File.OpenRead(logFilePath);
            using var compressedFileStream = File.Create(compressedPath);
            using var compressionStream = new System.IO.Compression.GZipStream(compressedFileStream, System.IO.Compression.CompressionMode.Compress);
            
            originalFileStream.CopyTo(compressionStream);
            
            // حذف الملف الأصلي بعد الضغط
            File.Delete(logFilePath);
        }

        /// <summary>
        /// تشغيل التحسين الشامل
        /// Run comprehensive optimization
        /// </summary>
        public async Task RunComprehensiveOptimizationAsync()
        {
            try
            {
                _logger.LogInformation("بدء التحسين الشامل للنظام");

                // تحسين الذاكرة
                OptimizeMemory();

                // تحسين قاعدة البيانات
                await OptimizeDatabaseAsync();

                // تنظيف الملفات المؤقتة
                CleanTemporaryFiles();

                // تحسين ملفات السجل
                OptimizeLogFiles();

                _logger.LogInformation("تم إكمال التحسين الشامل للنظام بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحسين الشامل للنظام");
            }
        }

        /// <summary>
        /// معالج التحسين الدوري
        /// Periodic optimization handler
        /// </summary>
        private async void OnOptimizationTimerElapsed(object? sender, System.Timers.ElapsedEventArgs e)
        {
            try
            {
                _logger.LogDebug("بدء التحسين الدوري");

                // تحسين الذاكرة
                OptimizeMemory();

                // تنظيف الملفات المؤقتة (كل ساعة)
                if (DateTime.Now.Minute == 0)
                {
                    CleanTemporaryFiles();
                }

                // تحسين قاعدة البيانات (يومياً في الساعة 2 صباحاً)
                if (DateTime.Now.Hour == 2 && DateTime.Now.Minute < 30)
                {
                    await OptimizeDatabaseAsync();
                    OptimizeLogFiles();
                }

                _logger.LogDebug("تم إكمال التحسين الدوري");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحسين الدوري");
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _optimizationTimer?.Stop();
            _optimizationTimer?.Dispose();
        }
    }
}
