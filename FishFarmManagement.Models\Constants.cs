namespace FishFarmManagement.Models
{
    /// <summary>
    /// ثوابت حالات الدورات الإنتاجية
    /// Production cycle status constants
    /// </summary>
    public static class CycleStatus
    {
        public const string Active = "نشطة";
        public const string Completed = "مكتملة";
        public const string Planned = "مخططة";
        public const string Cancelled = "ملغية";
        public const string Suspended = "متوقفة";
    }

    /// <summary>
    /// ثوابت حالات الموظفين
    /// Employee status constants
    /// </summary>
    public static class EmployeeStatus
    {
        public const string Active = "نشط";
        public const string Inactive = "متوقف";
        public const string Resigned = "مستقيل";
        public const string Terminated = "مفصول";
    }

    /// <summary>
    /// ثوابت حالات الأحواض
    /// Pond status constants
    /// </summary>
    public static class PondStatus
    {
        public const string Active = "نشط";
        public const string Inactive = "غير نشط";
        public const string UnderMaintenance = "تحت الصيانة";
        public const string OutOfService = "خارج الخدمة";
    }

    /// <summary>
    /// ثوابت حالات المخزون
    /// Inventory status constants
    /// </summary>
    public static class InventoryStatus
    {
        public const string Active = "نشط";
        public const string Inactive = "متوقف";
        public const string Expired = "منتهي الصلاحية";
        public const string Damaged = "تالف";
    }

    /// <summary>
    /// ثوابت حالات الحسابات
    /// Account status constants
    /// </summary>
    public static class AccountStatus
    {
        public const string Active = "نشط";
        public const string Inactive = "متوقف";
        public const string Closed = "مغلق";
    }

    /// <summary>
    /// ثوابت أنواع المعاملات
    /// Transaction type constants
    /// </summary>
    public static class TransactionType
    {
        public const string Debit = "مدين";
        public const string Credit = "دائن";
    }

    /// <summary>
    /// ثوابت أنواع الأدوية
    /// Medication type constants
    /// </summary>
    public static class MedicationType
    {
        public const string Antibiotic = "مضاد حيوي";
        public const string Vitamin = "فيتامين";
        public const string Disinfectant = "مطهر";
        public const string Treatment = "علاج";
        public const string Preventive = "وقائي";
    }

    /// <summary>
    /// ثوابت وحدات القياس
    /// Unit of measurement constants
    /// </summary>
    public static class Units
    {
        public const string Kilogram = "كيلو";
        public const string Gram = "جرام";
        public const string Liter = "لتر";
        public const string Milliliter = "مليلتر";
        public const string Piece = "قطعة";
        public const string Box = "صندوق";
        public const string Bag = "كيس";
    }

    /// <summary>
    /// ثوابت أنواع العلف
    /// Feed type constants
    /// </summary>
    public static class FeedCategory
    {
        public const string Starter = "بادئ";
        public const string Grower = "نمو";
        public const string Finisher = "تسمين";
        public const string Maintenance = "صيانة";
    }

    /// <summary>
    /// ثوابت الحالة الاجتماعية
    /// Marital status constants
    /// </summary>
    public static class MaritalStatus
    {
        public const string Single = "أعزب";
        public const string Married = "متزوج";
        public const string Divorced = "مطلق";
        public const string Widowed = "أرمل";
    }

    /// <summary>
    /// ثوابت أنواع التقارير
    /// Report type constants
    /// </summary>
    public static class ReportType
    {
        public const string Daily = "يومي";
        public const string Weekly = "أسبوعي";
        public const string Monthly = "شهري";
        public const string Quarterly = "ربع سنوي";
        public const string Annual = "سنوي";
        public const string Custom = "مخصص";
    }
}
