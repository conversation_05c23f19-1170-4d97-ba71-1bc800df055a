﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FishFarmManagement", "FishFarmManagement\FishFarmManagement.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FishFarmManagement.DAL", "FishFarmManagement.DAL\FishFarmManagement.DAL.csproj", "{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FishFarmManagement.BLL", "FishFarmManagement.BLL\FishFarmManagement.BLL.csproj", "{7E6A3431-6F00-443B-A9A3-80B218F1B04D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FishFarmManagement.Models", "FishFarmManagement.Models\FishFarmManagement.Models.csproj", "{28C20FC7-6F2D-4170-826D-346189FDE4D2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FishFarmManagement.Tests", "FishFarmManagement.Tests\FishFarmManagement.Tests.csproj", "{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.Build.0 = Release|Any CPU
		{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}.Debug|x64.Build.0 = Debug|Any CPU
		{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}.Debug|x86.Build.0 = Debug|Any CPU
		{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}.Release|Any CPU.Build.0 = Release|Any CPU
		{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}.Release|x64.ActiveCfg = Release|Any CPU
		{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}.Release|x64.Build.0 = Release|Any CPU
		{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}.Release|x86.ActiveCfg = Release|Any CPU
		{CB6D4609-3CE5-4AE6-96E4-47C4EA1EEAB9}.Release|x86.Build.0 = Release|Any CPU
		{7E6A3431-6F00-443B-A9A3-80B218F1B04D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7E6A3431-6F00-443B-A9A3-80B218F1B04D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7E6A3431-6F00-443B-A9A3-80B218F1B04D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7E6A3431-6F00-443B-A9A3-80B218F1B04D}.Debug|x64.Build.0 = Debug|Any CPU
		{7E6A3431-6F00-443B-A9A3-80B218F1B04D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7E6A3431-6F00-443B-A9A3-80B218F1B04D}.Debug|x86.Build.0 = Debug|Any CPU
		{7E6A3431-6F00-443B-A9A3-80B218F1B04D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7E6A3431-6F00-443B-A9A3-80B218F1B04D}.Release|Any CPU.Build.0 = Release|Any CPU
		{7E6A3431-6F00-443B-A9A3-80B218F1B04D}.Release|x64.ActiveCfg = Release|Any CPU
		{7E6A3431-6F00-443B-A9A3-80B218F1B04D}.Release|x64.Build.0 = Release|Any CPU
		{7E6A3431-6F00-443B-A9A3-80B218F1B04D}.Release|x86.ActiveCfg = Release|Any CPU
		{7E6A3431-6F00-443B-A9A3-80B218F1B04D}.Release|x86.Build.0 = Release|Any CPU
		{28C20FC7-6F2D-4170-826D-346189FDE4D2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28C20FC7-6F2D-4170-826D-346189FDE4D2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28C20FC7-6F2D-4170-826D-346189FDE4D2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{28C20FC7-6F2D-4170-826D-346189FDE4D2}.Debug|x64.Build.0 = Debug|Any CPU
		{28C20FC7-6F2D-4170-826D-346189FDE4D2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{28C20FC7-6F2D-4170-826D-346189FDE4D2}.Debug|x86.Build.0 = Debug|Any CPU
		{28C20FC7-6F2D-4170-826D-346189FDE4D2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28C20FC7-6F2D-4170-826D-346189FDE4D2}.Release|Any CPU.Build.0 = Release|Any CPU
		{28C20FC7-6F2D-4170-826D-346189FDE4D2}.Release|x64.ActiveCfg = Release|Any CPU
		{28C20FC7-6F2D-4170-826D-346189FDE4D2}.Release|x64.Build.0 = Release|Any CPU
		{28C20FC7-6F2D-4170-826D-346189FDE4D2}.Release|x86.ActiveCfg = Release|Any CPU
		{28C20FC7-6F2D-4170-826D-346189FDE4D2}.Release|x86.Build.0 = Release|Any CPU
		{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}.Debug|x64.Build.0 = Debug|Any CPU
		{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}.Debug|x86.Build.0 = Debug|Any CPU
		{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}.Release|Any CPU.Build.0 = Release|Any CPU
		{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}.Release|x64.ActiveCfg = Release|Any CPU
		{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}.Release|x64.Build.0 = Release|Any CPU
		{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}.Release|x86.ActiveCfg = Release|Any CPU
		{C39018F9-AAFB-4F1F-A6DC-AB012127FAFD}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
