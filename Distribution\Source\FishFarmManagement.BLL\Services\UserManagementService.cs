using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Threading.Tasks;

namespace FishFarmManagement.BLL.Services
{
    public class UserManagementService : IUserManagementService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<UserManagementService> _logger;

        public UserManagementService(IUnitOfWork unitOfWork, ILogger<UserManagementService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task InitializeDefaultDataAsync()
        {
            try
            {
                // Check if any user exists. If so, assume initialization is done.
                if (await _unitOfWork.Users.AnyAsync(u => true))
                {
                    return;
                }

                _logger.LogInformation("No users found. Initializing default data...");

                // Create default role
                var adminRole = new Role
                {
                    RoleName = "Admin",
                    Description = "Administrator with full access"
                    // In a real app, you would also add all permissions here.
                };
                await _unitOfWork.Roles.AddAsync(adminRole);
                await _unitOfWork.SaveChangesAsync();

                // Create default admin user
                var adminUser = new User
                {
                    Username = "admin",
                    FullName = "المدير العام",
                    Email = "<EMAIL>",
                    RoleId = adminRole.Id,
                    Status = UserStatus.Active,
                    MustChangePassword = true // Force password change on first login
                };
                adminUser.SetPassword("admin123"); // Default password

                await _unitOfWork.Users.AddAsync(adminUser);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Default admin user and role created successfully.");
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "An error occurred during default data initialization.");
                throw;
            }
        }
    }
}