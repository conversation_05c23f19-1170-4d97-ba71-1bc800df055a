# PowerShell script to fix all nullability errors in C# files
Write-Host "Fixing nullability errors in C# files..." -ForegroundColor Green

$files = Get-ChildItem -Path "FishFarmManagement/Forms" -Filter "*.cs" -Recurse

foreach ($file in $files) {
    Write-Host "Processing: $($file.Name)" -ForegroundColor Yellow
    
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    
    # Fix multiple "private void private" patterns
    $content = $content -replace 'private void private void private void private void private void private void private void private void private async void private async void private async void private void private async void private async void', 'private async void'
    $content = $content -replace 'private void private void private void private void private void private void private void private void private async void private async void private void private async void private async void private void private async void', 'private async void'
    $content = $content -replace 'private void private void private void private void private void private void private void private void private async void private void private async void private async void private void private async void', 'private async void'
    $content = $content -replace 'private void private void private void private void private void private void private void private void private async void private async void private void private async void', 'private async void'
    $content = $content -replace 'private void private void private void private void private void private void private void private void private async void private void private async void', 'private async void'
    $content = $content -replace 'private void private void private void private void private void private void private void private async void', 'private async void'
    $content = $content -replace 'private void private void private void private void private void private void private void', 'private void'
    $content = $content -replace 'private void private void private void private void private void private void', 'private void'
    $content = $content -replace 'private void private void private void private void private void', 'private void'
    $content = $content -replace 'private void private void private void private void', 'private void'
    $content = $content -replace 'private void private void private void', 'private void'
    $content = $content -replace 'private void private void', 'private void'
    
    # Fix object sender patterns with multiple question marks
    $content = $content -replace 'object sender\?{2,}', 'object? sender'
    $content = $content -replace 'object sender\?{1}', 'object? sender'
    
    # Fix specific patterns
    $content = $content -replace 'private async void private void private void private void private void private void private void private async void private async void private void private async void private async void private void private async void', 'private async void'
    $content = $content -replace 'private async void private async void private void private async void private async void private void private async void', 'private async void'
    $content = $content -replace 'private async void private async void private async void private void private async void private async void private async void private void private async void', 'private async void'
    
    # Fix remaining patterns
    $content = $content -replace 'private void private async void private void private void private async void private void private async void', 'private async void'
    $content = $content -replace 'private void private async void private void private void private async void private void', 'private async void'
    $content = $content -replace 'private void private async void private void private void private async void', 'private async void'
    $content = $content -replace 'private void private async void private void private void', 'private async void'
    $content = $content -replace 'private void private async void private void', 'private async void'
    $content = $content -replace 'private void private async void', 'private async void'
    
    # Fix object sender patterns
    $content = $content -replace 'object sender\?{3,}', 'object? sender'
    $content = $content -replace 'object sender\?{2}', 'object? sender'
    $content = $content -replace 'object sender\?{1}', 'object? sender'
    
    # Save the fixed content
    Set-Content -Path $file.FullName -Value $content -Encoding UTF8
}

Write-Host "All nullability errors have been fixed!" -ForegroundColor Green 