using System.ComponentModel.DataAnnotations;

namespace FishFarmManagement.Models.Configuration
{
    /// <summary>
    /// إعدادات التطبيق الرئيسية
    /// Main application settings
    /// </summary>
    public class ApplicationSettings
    {
        public const string SectionName = "Application";

        [Required]
        public string Name { get; set; } = "نظام إدارة مزرعة الأسماك";

        [Required]
        public string Version { get; set; } = "1.0.0";

        [Required]
        public string Language { get; set; } = "ar";

        [Required]
        public string Theme { get; set; } = "Default";

        public AutoBackupSettings AutoBackup { get; set; } = new();
        public BusinessSettings BusinessSettings { get; set; } = new();
    }

    /// <summary>
    /// إعدادات النسخ الاحتياطي التلقائي
    /// Auto backup settings
    /// </summary>
    public class AutoBackupSettings
    {
        public bool Enabled { get; set; } = true;

        [Range(1, 168)] // 1 hour to 1 week
        public int IntervalHours { get; set; } = 24;

        [Required]
        public string BackupPath { get; set; } = "Backups";

        [Range(1, 365)]
        public int MaxBackupFiles { get; set; } = 30;
    }

    /// <summary>
    /// إعدادات الأعمال
    /// Business settings
    /// </summary>
    public class BusinessSettings
    {
        [Range(0.01, 1000.0)]
        public decimal DefaultFishSellPricePerKg { get; set; } = 15.0m;

        [Range(1, 365)]
        public int LowStockWarningDays { get; set; } = 30;

        [Required]
        public string DefaultCurrency { get; set; } = "ريال سعودي";

        [Range(1, 12)]
        public int FiscalYearStartMonth { get; set; } = 1;

        [Range(0.0, 1.0)]
        public decimal DefaultTaxRate { get; set; } = 0.15m;
    }

    /// <summary>
    /// إعدادات قاعدة البيانات
    /// Database settings
    /// </summary>
    public class DatabaseSettings
    {
        public const string SectionName = "Database";

        public bool AutoMigrate { get; set; } = true;
        public bool SeedDefaultData { get; set; } = true;
        public bool BackupOnStartup { get; set; } = true;
    }

    /// <summary>
    /// إعدادات الأمان
    /// Security settings
    /// </summary>
    public class SecuritySettings
    {
        public const string SectionName = "Security";

        public bool RequireAuthentication { get; set; } = true;

        [Range(5, 480)] // 5 minutes to 8 hours
        public int SessionTimeoutMinutes { get; set; } = 60;

        [Range(3, 10)]
        public int MaxLoginAttempts { get; set; } = 3;

        [Range(5, 60)] // 5 to 60 minutes
        public int AccountLockoutMinutes { get; set; } = 30;

        [Range(6, 50)]
        public int MinPasswordLength { get; set; } = 6;

        public bool RequirePasswordComplexity { get; set; } = false;
    }

    /// <summary>
    /// إعدادات التقارير
    /// Reports settings
    /// </summary>
    public class ReportsSettings
    {
        public const string SectionName = "Reports";

        [Required]
        public string DefaultFormat { get; set; } = "PDF";

        [Required]
        public string OutputPath { get; set; } = "Reports";

        public bool IncludeLogo { get; set; } = true;
        public bool ShowWatermark { get; set; } = false;

        [Range(1, 100)]
        public int MaxRecordsPerReport { get; set; } = 1000;
    }

    /// <summary>
    /// إعدادات الإشعارات
    /// Notifications settings
    /// </summary>
    public class NotificationsSettings
    {
        public const string SectionName = "Notifications";

        public bool ShowAlerts { get; set; } = true;
        public AlertTypesSettings AlertTypes { get; set; } = new();
    }

    /// <summary>
    /// إعدادات أنواع التنبيهات
    /// Alert types settings
    /// </summary>
    public class AlertTypesSettings
    {
        public bool HighMortality { get; set; } = true;
        public bool LowFeedStock { get; set; } = true;
        public bool ExpiredMedications { get; set; } = true;
        public bool OverdueTasks { get; set; } = true;
        public bool WaterQualityIssues { get; set; } = true;
        public bool MaintenanceReminders { get; set; } = true;
    }

    /// <summary>
    /// إعدادات التسجيل
    /// Logging settings
    /// </summary>
    public class LoggingSettings
    {
        public const string SectionName = "Logging";

        [Required]
        public string LogLevel { get; set; } = "Information";

        [Required]
        public string LogPath { get; set; } = "Logs";

        [Range(1, 365)]
        public int RetentionDays { get; set; } = 30;

        public bool EnableFileLogging { get; set; } = true;
        public bool EnableConsoleLogging { get; set; } = true;
        public bool EnableDatabaseLogging { get; set; } = false;
    }
}
