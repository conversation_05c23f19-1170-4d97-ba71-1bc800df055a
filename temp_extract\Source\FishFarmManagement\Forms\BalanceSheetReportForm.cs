﻿using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ ØªÙ‚Ø±ÙŠØ± Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ© Ø§Ù„Ø¹Ù…ÙˆÙ…ÙŠØ©
    /// Balance Sheet Report Form
    /// </summary>
    public partial class BalanceSheetReportForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;

        // UI Controls
        private DateTimePicker asOfDatePicker;
        private Button generateReportButton;
        private DataGridView assetsDataGridView;
        private DataGridView liabilitiesDataGridView;
        private DataGridView equityDataGridView;
        private Label totalAssetsLabel;
        private Label totalLiabilitiesLabel;
        private Label totalEquityLabel;
        private Label balanceCheckLabel;

        public BalanceSheetReportForm(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "ØªÙ‚Ø±ÙŠØ± Ø§Ù„Ù…ÙŠØ²Ø§Ù†ÙŠØ© Ø§Ù„Ø¹Ù…ÙˆÙ…ÙŠØ©";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
        }

        private void CreateControls()
        {
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 4,
                ColumnCount = 1,
                Padding = new Padding(20)
            };

            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 60));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 50));

            // Date Panel
            var datePanel = CreateDatePanel();
            mainPanel.Controls.Add(datePanel, 0, 0);

            // Balance Sheet Content Panel
            var contentPanel = CreateContentPanel();
            mainPanel.Controls.Add(contentPanel, 0, 1);

            // Summary Panel
            var summaryPanel = CreateSummaryPanel();
            mainPanel.Controls.Add(summaryPanel, 0, 2);

            // Buttons Panel
            var buttonsPanel = CreateButtonsPanel();
            mainPanel.Controls.Add(buttonsPanel, 0, 3);

            this.Controls.Add(mainPanel);
        }

        private Panel CreateDatePanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            var asOfLabel = new Label
            {
                Text = "ÙƒÙ…Ø§ ÙÙŠ ØªØ§Ø±ÙŠØ®:",
                Location = new Point(650, 15),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            asOfDatePicker = new DateTimePicker
            {
                Location = new Point(480, 15),
                Size = new Size(150, 23),
                Value = DateTime.Now
            };

            generateReportButton = new Button
            {
                Text = "Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±",
                Location = new Point(350, 15),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            generateReportButton.Click += GenerateReport_Click;

            panel.Controls.AddRange(new Control[] { asOfLabel, asOfDatePicker, generateReportButton });
            return panel;
        }

        private Panel CreateContentPanel()
        {
            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));

            // Assets Panel
            var assetsPanel = CreateAssetsPanel();
            panel.Controls.Add(assetsPanel, 0, 0);

            // Liabilities Panel
            var liabilitiesPanel = CreateLiabilitiesPanel();
            panel.Controls.Add(liabilitiesPanel, 1, 0);

            // Equity Panel
            var equityPanel = CreateEquityPanel();
            panel.Controls.Add(equityPanel, 2, 0);

            return panel;
        }

        private Panel CreateAssetsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(5)
            };

            var titleLabel = new Label
            {
                Text = "Ø§Ù„Ø£ØµÙˆÙ„",
                Dock = DockStyle.Top,
                Height = 30,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White
            };

            assetsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F)
            };

            SetupBalanceSheetColumns(assetsDataGridView);

            panel.Controls.Add(assetsDataGridView);
            panel.Controls.Add(titleLabel);

            return panel;
        }

        private Panel CreateLiabilitiesPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(5)
            };

            var titleLabel = new Label
            {
                Text = "Ø§Ù„Ø®ØµÙˆÙ…",
                Dock = DockStyle.Top,
                Height = 30,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White
            };

            liabilitiesDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F)
            };

            SetupBalanceSheetColumns(liabilitiesDataGridView);

            panel.Controls.Add(liabilitiesDataGridView);
            panel.Controls.Add(titleLabel);

            return panel;
        }

        private Panel CreateEquityPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(5)
            };

            var titleLabel = new Label
            {
                Text = "Ø­Ù‚ÙˆÙ‚ Ø§Ù„Ù…Ù„ÙƒÙŠØ©",
                Dock = DockStyle.Top,
                Height = 30,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White
            };

            equityDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F)
            };

            SetupBalanceSheetColumns(equityDataGridView);

            panel.Controls.Add(equityDataGridView);
            panel.Controls.Add(titleLabel);

            return panel;
        }

        private void SetupBalanceSheetColumns(DataGridView dataGridView)
        {
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AccountName",
                HeaderText = "Ø§Ø³Ù… Ø§Ù„Ø­Ø³Ø§Ø¨",
                DataPropertyName = "AccountName",
                Width = 200
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Balance",
                HeaderText = "Ø§Ù„Ø±ØµÙŠØ¯",
                DataPropertyName = "Balance",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle 
                { 
                    Format = "C",
                    Alignment = DataGridViewContentAlignment.MiddleLeft
                }
            });
        }

        private Panel CreateSummaryPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            totalAssetsLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø£ØµÙˆÙ„: 0.00",
                Location = new Point(800, 10),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Blue
            };

            totalLiabilitiesLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø®ØµÙˆÙ…: 0.00",
                Location = new Point(800, 35),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Red
            };

            totalEquityLabel = new Label
            {
                Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø­Ù‚ÙˆÙ‚ Ø§Ù„Ù…Ù„ÙƒÙŠØ©: 0.00",
                Location = new Point(800, 60),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.Green
            };

            balanceCheckLabel = new Label
            {
                Text = "Ø­Ø§Ù„Ø© Ø§Ù„ØªÙˆØ§Ø²Ù†: Ù…ØªÙˆØ§Ø²Ù†",
                Location = new Point(400, 35),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.Green
            };

            panel.Controls.AddRange(new Control[] { totalAssetsLabel, totalLiabilitiesLabel, totalEquityLabel, balanceCheckLabel });
            return panel;
        }

        private Panel CreateButtonsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill
            };

            var exportButton = new Button
            {
                Text = "ØªØµØ¯ÙŠØ± Ø¥Ù„Ù‰ Excel",
                Location = new Point(20, 10),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            exportButton.Click += ExportToExcel_Click;

            var printButton = new Button
            {
                Text = "Ø·Ø¨Ø§Ø¹Ø©",
                Location = new Point(160, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            printButton.Click += Print_Click;

            panel.Controls.AddRange(new Control[] { exportButton, printButton });
            return panel;
        }

        private async void private async void private async void GenerateReport_Click(object? sender, EventArgs e)
        {
            try
            {
                var asOfDate = asOfDatePicker.Value.Date.AddDays(1).AddTicks(-1);

                // Get all accounts
                var accounts = await _unitOfWork.Accounts.GetAllAsync();
                var accountTypes = await _unitOfWork.AccountTypes.GetAllAsync();
                var accountTypesDict = accountTypes.ToDictionary(at => at.Id, at => at);

                // Get all transaction details up to the specified date
                var transactions = await _unitOfWork.Transactions.FindAsync(t => t.TransactionDate <= asOfDate);
                var transactionDetails = new List<TransactionDetail>();
                
                foreach (var transaction in transactions)
                {
                    var details = await _unitOfWork.TransactionDetails.FindAsync(td => td.TransactionId == transaction.Id);
                    transactionDetails.AddRange(details);
                }

                // Calculate balances
                var balanceSheetData = CalculateBalanceSheet(accounts, accountTypesDict, transactionDetails);

                // Display results
                DisplayBalanceSheetData(balanceSheetData);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„ØªÙ‚Ø±ÙŠØ±: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private BalanceSheetData CalculateBalanceSheet(
            IEnumerable<Account> accounts,
            Dictionary<int, AccountType> accountTypesDict,
            List<TransactionDetail> transactionDetails)
        {
            var data = new BalanceSheetData();

            foreach (var account in accounts)
            {
                var accountDetails = transactionDetails.Where(td => td.AccountId == account.Id);
                var totalDebits = accountDetails.Sum(td => td.DebitAmount);
                var totalCredits = accountDetails.Sum(td => td.CreditAmount);

                // Calculate balance based on account type
                decimal balance = 0;
                if (accountTypesDict.TryGetValue(account.AccountTypeId, out var accountType))
                {
                    var typeName = accountType.TypeName.ToLower();
                    
                    if (typeName.Contains("Ø£ØµÙ„") || typeName.Contains("Ù…ØµØ±ÙˆÙ"))
                    {
                        balance = totalDebits - totalCredits;
                        if (balance != 0)
                        {
                            data.Assets.Add(new BalanceSheetItem
                            {
                                AccountName = account.AccountName,
                                Balance = balance
                            });
                        }
                    }
                    else if (typeName.Contains("Ø®ØµÙ…") || typeName.Contains("Ø§Ù„ØªØ²Ø§Ù…"))
                    {
                        balance = totalCredits - totalDebits;
                        if (balance != 0)
                        {
                            data.Liabilities.Add(new BalanceSheetItem
                            {
                                AccountName = account.AccountName,
                                Balance = balance
                            });
                        }
                    }
                    else if (typeName.Contains("Ù…Ù„ÙƒÙŠØ©") || typeName.Contains("Ø±Ø£Ø³ Ù…Ø§Ù„") || typeName.Contains("Ø¥ÙŠØ±Ø§Ø¯"))
                    {
                        balance = totalCredits - totalDebits;
                        if (balance != 0)
                        {
                            data.Equity.Add(new BalanceSheetItem
                            {
                                AccountName = account.AccountName,
                                Balance = balance
                            });
                        }
                    }
                }
            }

            return data;
        }

        private void DisplayBalanceSheetData(BalanceSheetData data)
        {
            assetsDataGridView.DataSource = data.Assets.OrderBy(a => a.AccountName).ToList();
            liabilitiesDataGridView.DataSource = data.Liabilities.OrderBy(l => l.AccountName).ToList();
            equityDataGridView.DataSource = data.Equity.OrderBy(e => e.AccountName).ToList();

            var totalAssets = data.Assets.Sum(a => a.Balance);
            var totalLiabilities = data.Liabilities.Sum(l => l.Balance);
            var totalEquity = data.Equity.Sum(e => e.Balance);

            totalAssetsLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø£ØµÙˆÙ„: {totalAssets:C}";
            totalLiabilitiesLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø®ØµÙˆÙ…: {totalLiabilities:C}";
            totalEquityLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø­Ù‚ÙˆÙ‚ Ø§Ù„Ù…Ù„ÙƒÙŠØ©: {totalEquity:C}";

            // Check balance
            var difference = Math.Abs(totalAssets - (totalLiabilities + totalEquity));
            if (difference < 0.01m) // Allow for small rounding differences
            {
                balanceCheckLabel.Text = "Ø­Ø§Ù„Ø© Ø§Ù„ØªÙˆØ§Ø²Ù†: Ù…ØªÙˆØ§Ø²Ù† âœ“";
                balanceCheckLabel.ForeColor = Color.Green;
            }
            else
            {
                balanceCheckLabel.Text = $"Ø­Ø§Ù„Ø© Ø§Ù„ØªÙˆØ§Ø²Ù†: ØºÙŠØ± Ù…ØªÙˆØ§Ø²Ù† ({difference:C})";
                balanceCheckLabel.ForeColor = Color.Red;
            }
        }

        private void ExportToExcel_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("ØªØµØ¯ÙŠØ± Ø¥Ù„Ù‰ Excel - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Print_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("Ø§Ù„Ø·Ø¨Ø§Ø¹Ø© - Ù‚ÙŠØ¯ Ø§Ù„ØªØ·ÙˆÙŠØ±", "Ù…Ø¹Ù„ÙˆÙ…Ø§Øª",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    public class BalanceSheetData
    {
        public List<BalanceSheetItem> Assets { get; set; } = new List<BalanceSheetItem>();
        public List<BalanceSheetItem> Liabilities { get; set; } = new List<BalanceSheetItem>();
        public List<BalanceSheetItem> Equity { get; set; } = new List<BalanceSheetItem>();
    }

    public class BalanceSheetItem
    {
        public string AccountName { get; set; }
        public decimal Balance { get; set; }
    }
}



