namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// خدمة النسخ الاحتياطي والاستعادة المحسنة
    /// Enhanced backup and restore service interface
    /// </summary>
    public interface IBackupService
    {
        /// <summary>
        /// إنشاء نسخة احتياطية كاملة
        /// Create full backup
        /// </summary>
        Task<BackupResult> CreateFullBackupAsync(string backupPath, string description = "");

        /// <summary>
        /// إنشاء نسخة احتياطية تزايدية
        /// Create incremental backup
        /// </summary>
        Task<BackupResult> CreateIncrementalBackupAsync(string backupPath, string description = "");

        /// <summary>
        /// إنشاء نسخة احتياطية مضغوطة
        /// Create compressed backup
        /// </summary>
        Task<BackupResult> CreateCompressedBackupAsync(string backupPath, string description = "", CompressionLevel compressionLevel = CompressionLevel.Optimal);

        /// <summary>
        /// إنشاء نسخة احتياطية مشفرة
        /// Create encrypted backup
        /// </summary>
        Task<BackupResult> CreateEncryptedBackupAsync(string backupPath, string password, string description = "");

        /// <summary>
        /// استعادة النسخة الاحتياطية
        /// Restore backup
        /// </summary>
        Task<RestoreResult> RestoreBackupAsync(string backupPath, string password = "");

        /// <summary>
        /// التحقق من صحة النسخة الاحتياطية
        /// Verify backup integrity
        /// </summary>
        Task<VerificationResult> VerifyBackupAsync(string backupPath, string password = "");

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية
        /// Get backup list
        /// </summary>
        Task<List<BackupInfo>> GetBackupListAsync(string backupDirectory);

        /// <summary>
        /// حذف النسخة الاحتياطية
        /// Delete backup
        /// </summary>
        Task<bool> DeleteBackupAsync(string backupPath);

        /// <summary>
        /// تنظيف النسخ الاحتياطية القديمة
        /// Clean old backups
        /// </summary>
        Task<CleanupResult> CleanupOldBackupsAsync(string backupDirectory, int keepDays = 30);

        /// <summary>
        /// جدولة النسخ الاحتياطي التلقائي
        /// Schedule automatic backup
        /// </summary>
        Task<bool> ScheduleAutomaticBackupAsync(BackupSchedule schedule);

        /// <summary>
        /// إلغاء جدولة النسخ الاحتياطي التلقائي
        /// Cancel automatic backup schedule
        /// </summary>
        Task<bool> CancelAutomaticBackupAsync();

        /// <summary>
        /// الحصول على حالة النسخ الاحتياطي التلقائي
        /// Get automatic backup status
        /// </summary>
        Task<BackupScheduleStatus> GetAutomaticBackupStatusAsync();

        /// <summary>
        /// تصدير البيانات إلى تنسيقات مختلفة
        /// Export data to different formats
        /// </summary>
        Task<ExportResult> ExportDataAsync(string exportPath, ExportFormat format, ExportOptions options);

        /// <summary>
        /// استيراد البيانات من تنسيقات مختلفة
        /// Import data from different formats
        /// </summary>
        Task<ImportResult> ImportDataAsync(string importPath, ImportOptions options);

        /// <summary>
        /// الحصول على إحصائيات قاعدة البيانات
        /// Get database statistics
        /// </summary>
        Task<DatabaseStatistics> GetDatabaseStatisticsAsync();

        /// <summary>
        /// تحسين قاعدة البيانات
        /// Optimize database
        /// </summary>
        Task<OptimizationResult> OptimizeDatabaseAsync();
    }

    /// <summary>
    /// نتيجة النسخ الاحتياطي
    /// Backup result
    /// </summary>
    public class BackupResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string BackupPath { get; set; } = string.Empty;
        public long BackupSize { get; set; }
        public TimeSpan Duration { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Checksum { get; set; } = string.Empty;
        public BackupType Type { get; set; }
        public bool IsEncrypted { get; set; }
        public bool IsCompressed { get; set; }
    }

    /// <summary>
    /// نتيجة الاستعادة
    /// Restore result
    /// </summary>
    public class RestoreResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public DateTime RestoredAt { get; set; }
        public int TablesRestored { get; set; }
        public int RecordsRestored { get; set; }
    }

    /// <summary>
    /// نتيجة التحقق
    /// Verification result
    /// </summary>
    public class VerificationResult
    {
        public bool IsValid { get; set; }
        public string Message { get; set; } = string.Empty;
        public string ExpectedChecksum { get; set; } = string.Empty;
        public string ActualChecksum { get; set; } = string.Empty;
        public bool ChecksumMatch { get; set; }
        public bool StructureValid { get; set; }
        public List<string> Issues { get; set; } = new();
    }

    /// <summary>
    /// معلومات النسخة الاحتياطية
    /// Backup information
    /// </summary>
    public class BackupInfo
    {
        public string FileName { get; set; } = string.Empty;
        public string FullPath { get; set; } = string.Empty;
        public long Size { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Description { get; set; } = string.Empty;
        public BackupType Type { get; set; }
        public bool IsEncrypted { get; set; }
        public bool IsCompressed { get; set; }
        public string Checksum { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
    }

    /// <summary>
    /// نتيجة التنظيف
    /// Cleanup result
    /// </summary>
    public class CleanupResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int FilesDeleted { get; set; }
        public long SpaceFreed { get; set; }
        public List<string> DeletedFiles { get; set; } = new();
    }

    /// <summary>
    /// جدولة النسخ الاحتياطي
    /// Backup schedule
    /// </summary>
    public class BackupSchedule
    {
        public bool Enabled { get; set; }
        public BackupFrequency Frequency { get; set; }
        public TimeSpan Time { get; set; }
        public DayOfWeek? DayOfWeek { get; set; }
        public int? DayOfMonth { get; set; }
        public string BackupDirectory { get; set; } = string.Empty;
        public BackupType Type { get; set; }
        public bool UseCompression { get; set; }
        public bool UseEncryption { get; set; }
        public string Password { get; set; } = string.Empty;
        public int RetentionDays { get; set; } = 30;
    }

    /// <summary>
    /// حالة جدولة النسخ الاحتياطي
    /// Backup schedule status
    /// </summary>
    public class BackupScheduleStatus
    {
        public bool IsScheduled { get; set; }
        public BackupSchedule? Schedule { get; set; }
        public DateTime? LastBackup { get; set; }
        public DateTime? NextBackup { get; set; }
        public bool LastBackupSuccess { get; set; }
        public string LastBackupMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// نتيجة التصدير
    /// Export result
    /// </summary>
    public class ExportResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string ExportPath { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public int RecordsExported { get; set; }
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// نتيجة الاستيراد
    /// Import result
    /// </summary>
    public class ImportResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int RecordsImported { get; set; }
        public int RecordsSkipped { get; set; }
        public int RecordsWithErrors { get; set; }
        public TimeSpan Duration { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    /// <summary>
    /// إحصائيات قاعدة البيانات
    /// Database statistics
    /// </summary>
    public class DatabaseStatistics
    {
        public long DatabaseSize { get; set; }
        public int TableCount { get; set; }
        public long TotalRecords { get; set; }
        public Dictionary<string, long> TableSizes { get; set; } = new();
        public Dictionary<string, long> TableRecords { get; set; } = new();
        public DateTime LastOptimized { get; set; }
        public double FragmentationLevel { get; set; }
    }

    /// <summary>
    /// نتيجة التحسين
    /// Optimization result
    /// </summary>
    public class OptimizationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public long SizeBefore { get; set; }
        public long SizeAfter { get; set; }
        public long SpaceSaved { get; set; }
        public TimeSpan Duration { get; set; }
        public List<string> OptimizationSteps { get; set; } = new();
    }

    /// <summary>
    /// خيارات التصدير
    /// Export options
    /// </summary>
    public class ExportOptions
    {
        public List<string> Tables { get; set; } = new();
        public bool IncludeSchema { get; set; } = true;
        public bool IncludeData { get; set; } = true;
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? WhereClause { get; set; }
    }

    /// <summary>
    /// خيارات الاستيراد
    /// Import options
    /// </summary>
    public class ImportOptions
    {
        public bool OverwriteExisting { get; set; } = false;
        public bool SkipErrors { get; set; } = true;
        public bool ValidateData { get; set; } = true;
        public bool CreateBackupBeforeImport { get; set; } = true;
        public List<string> IgnoreTables { get; set; } = new();
    }

    /// <summary>
    /// نوع النسخة الاحتياطية
    /// Backup type
    /// </summary>
    public enum BackupType
    {
        Full = 1,        // كاملة
        Incremental = 2, // تزايدية
        Differential = 3 // تفاضلية
    }

    /// <summary>
    /// تكرار النسخ الاحتياطي
    /// Backup frequency
    /// </summary>
    public enum BackupFrequency
    {
        Daily = 1,   // يومي
        Weekly = 2,  // أسبوعي
        Monthly = 3  // شهري
    }

    /// <summary>
    /// تنسيق التصدير
    /// Export format
    /// </summary>
    public enum ExportFormat
    {
        Sql = 1,    // SQL
        Csv = 2,    // CSV
        Excel = 3,  // Excel
        Json = 4,   // JSON
        Xml = 5     // XML
    }

    /// <summary>
    /// مستوى الضغط
    /// Compression level
    /// </summary>
    public enum CompressionLevel
    {
        None = 0,
        Fastest = 1,
        Optimal = 2,
        SmallestSize = 3
    }
}
