﻿using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ ØªØ³Ø¬ÙŠÙ„ Ø§Ù„ØªØºØ°ÙŠØ©
    /// Feeding Record Form
    /// </summary>
    public partial class FeedingRecordForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<FeedingRecordForm> _logger;

        // UI Controls
        private ComboBox pondComboBox;
        private DateTimePicker feedingDatePicker;
        // private TimeSpan feedingTime; // ØºÙŠØ± Ù…Ø³ØªØ®Ø¯Ù…
        private ComboBox feedTypeComboBox;
        private NumericUpDown quantityNumericUpDown;
        private ComboBox unitComboBox;
        private NumericUpDown costPerUnitNumericUpDown;
        private Label totalCostLabel;
        private TextBox notesTextBox;
        private Button saveButton;
        private Button cancelButton;
        private DataGridView feedingHistoryDataGridView;

        public FeedingRecordForm(IUnitOfWork unitOfWork, ILogger<FeedingRecordForm> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            InitializeComponent();
            LoadInitialData();
        }

        private void InitializeComponent()
        {
            this.Text = "ØªØ³Ø¬ÙŠÙ„ Ø§Ù„ØªØºØ°ÙŠØ©";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 2,
                ColumnCount = 1,
                Padding = new Padding(20)
            };

            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 350));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            // Input Panel
            var inputPanel = CreateInputPanel();
            mainPanel.Controls.Add(inputPanel, 0, 0);

            // History Panel
            var historyPanel = CreateHistoryPanel();
            mainPanel.Controls.Add(historyPanel, 0, 1);

            this.Controls.Add(mainPanel);
        }

        private GroupBox CreateInputPanel()
        {
            var panel = new GroupBox
            {
                Text = "ØªØ³Ø¬ÙŠÙ„ ØªØºØ°ÙŠØ© Ø¬Ø¯ÙŠØ¯Ø©",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Padding = new Padding(15)
            };

            int y = 30;
            int spacing = 35;
            int labelWidth = 120;
            int controlWidth = 200;

            // Pond selection
            var pondLabel = CreateLabel("Ø§Ù„Ø­ÙˆØ¶:", new Point(600, y), labelWidth);
            pondComboBox = new ComboBox
            {
                Location = new Point(380, y),
                Size = new Size(controlWidth, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            y += spacing;

            // Feeding date and time
            var dateLabel = CreateLabel("ØªØ§Ø±ÙŠØ® Ø§Ù„ØªØºØ°ÙŠØ©:", new Point(600, y), labelWidth);
            feedingDatePicker = new DateTimePicker
            {
                Location = new Point(380, y),
                Size = new Size(controlWidth, 23),
                Value = DateTime.Now
            };
            y += spacing;

            var timeLabel = CreateLabel("ÙˆÙ‚Øª Ø§Ù„ØªØºØ°ÙŠØ©:", new Point(600, y), labelWidth);
            var timePanel = new Panel
            {
                Location = new Point(380, y),
                Size = new Size(controlWidth, 23)
            };

            var hourComboBox = new ComboBox
            {
                Location = new Point(0, 0),
                Size = new Size(60, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            for (int i = 0; i < 24; i++)
                hourComboBox.Items.Add(i.ToString("00"));
            hourComboBox.SelectedIndex = DateTime.Now.Hour;

            var minuteComboBox = new ComboBox
            {
                Location = new Point(70, 0),
                Size = new Size(60, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            for (int i = 0; i < 60; i += 15)
                minuteComboBox.Items.Add(i.ToString("00"));
            minuteComboBox.SelectedIndex = 0;

            var timeFormatLabel = new Label
            {
                Text = "Ø³Ø§Ø¹Ø© : Ø¯Ù‚ÙŠÙ‚Ø©",
                Location = new Point(140, 3),
                Size = new Size(60, 20),
                Font = new Font("Segoe UI", 8F)
            };

            timePanel.Controls.AddRange(new Control[] { hourComboBox, minuteComboBox, timeFormatLabel });
            y += spacing;

            // Feed type
            var feedTypeLabel = CreateLabel("Ù†ÙˆØ¹ Ø§Ù„Ø¹Ù„Ù:", new Point(600, y), labelWidth);
            feedTypeComboBox = new ComboBox
            {
                Location = new Point(380, y),
                Size = new Size(controlWidth, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            y += spacing;

            // Quantity
            var quantityLabel = CreateLabel("Ø§Ù„ÙƒÙ…ÙŠØ©:", new Point(600, y), labelWidth);
            quantityNumericUpDown = new NumericUpDown
            {
                Location = new Point(380, y),
                Size = new Size(100, 23),
                DecimalPlaces = 2,
                Maximum = 10000,
                Minimum = 0.01m,
                Value = 1
            };
            quantityNumericUpDown.ValueChanged += CalculateTotalCost;

            unitComboBox = new ComboBox
            {
                Location = new Point(490, y),
                Size = new Size(90, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            unitComboBox.Items.AddRange(new[] { "ÙƒÙŠÙ„ÙˆØ¬Ø±Ø§Ù…", "Ø¬Ø±Ø§Ù…", "Ø·Ù†" });
            unitComboBox.SelectedIndex = 0;
            y += spacing;

            // Cost per unit
            var costLabel = CreateLabel("ØªÙƒÙ„ÙØ© Ø§Ù„ÙˆØ­Ø¯Ø©:", new Point(600, y), labelWidth);
            costPerUnitNumericUpDown = new NumericUpDown
            {
                Location = new Point(380, y),
                Size = new Size(controlWidth, 23),
                DecimalPlaces = 2,
                Maximum = 100000,
                Minimum = 0,
                Value = 0
            };
            costPerUnitNumericUpDown.ValueChanged += CalculateTotalCost;
            y += spacing;

            // Total cost
            var totalCostTitleLabel = CreateLabel("Ø§Ù„ØªÙƒÙ„ÙØ© Ø§Ù„Ø¥Ø¬Ù…Ø§Ù„ÙŠØ©:", new Point(600, y), labelWidth);
            totalCostLabel = new Label
            {
                Location = new Point(380, y),
                Size = new Size(controlWidth, 23),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(240, 240, 240),
                TextAlign = ContentAlignment.MiddleLeft,
                Text = "0.00"
            };
            y += spacing;

            // Notes
            var notesLabel = CreateLabel("Ù…Ù„Ø§Ø­Ø¸Ø§Øª:", new Point(600, y), labelWidth);
            notesTextBox = new TextBox
            {
                Location = new Point(380, y),
                Size = new Size(controlWidth, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            y += 70;

            // Buttons
            saveButton = new Button
            {
                Text = "Ø­ÙØ¸",
                Location = new Point(500, y),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "Ø¥Ù„ØºØ§Ø¡",
                Location = new Point(400, y),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            cancelButton.Click += (s, e) => this.Close();

            panel.Controls.AddRange(new Control[]
            {
                pondLabel, pondComboBox,
                dateLabel, feedingDatePicker,
                timeLabel, timePanel,
                feedTypeLabel, feedTypeComboBox,
                quantityLabel, quantityNumericUpDown, unitComboBox,
                costLabel, costPerUnitNumericUpDown,
                totalCostTitleLabel, totalCostLabel,
                notesLabel, notesTextBox,
                saveButton, cancelButton
            });

            return panel;
        }

        private GroupBox CreateHistoryPanel()
        {
            var panel = new GroupBox
            {
                Text = "Ø³Ø¬Ù„ Ø§Ù„ØªØºØ°ÙŠØ©",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Padding = new Padding(15)
            };

            feedingHistoryDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F)
            };

            SetupHistoryColumns();
            panel.Controls.Add(feedingHistoryDataGridView);

            return panel;
        }

        private void SetupHistoryColumns()
        {
            feedingHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PondNumber",
                HeaderText = "Ø±Ù‚Ù… Ø§Ù„Ø­ÙˆØ¶",
                DataPropertyName = "PondNumber",
                Width = 80
            });

            feedingHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FeedingDate",
                HeaderText = "Ø§Ù„ØªØ§Ø±ÙŠØ®",
                DataPropertyName = "FeedingDate",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            feedingHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FeedingTime",
                HeaderText = "Ø§Ù„ÙˆÙ‚Øª",
                DataPropertyName = "FeedingTime",
                Width = 80
            });

            feedingHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FeedType",
                HeaderText = "Ù†ÙˆØ¹ Ø§Ù„Ø¹Ù„Ù",
                DataPropertyName = "FeedType",
                Width = 120
            });

            feedingHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                HeaderText = "Ø§Ù„ÙƒÙ…ÙŠØ©",
                DataPropertyName = "Quantity",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            feedingHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Unit",
                HeaderText = "Ø§Ù„ÙˆØ­Ø¯Ø©",
                DataPropertyName = "Unit",
                Width = 80
            });

            feedingHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalCost",
                HeaderText = "Ø§Ù„ØªÙƒÙ„ÙØ©",
                DataPropertyName = "TotalCost",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            feedingHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Notes",
                HeaderText = "Ù…Ù„Ø§Ø­Ø¸Ø§Øª",
                DataPropertyName = "Notes",
                Width = 150
            });
        }

        private Label CreateLabel(string text, Point location, int width)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(width, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
        }

        private async void LoadInitialData()
        {
            try
            {
                // Load ponds
                var ponds = await _unitOfWork.Ponds.GetAllAsync();
                var pondList = ponds.Select(p => new { Value = p.Id, Text = $"Ø­ÙˆØ¶ {p.PondNumber}" }).ToList();
                
                pondComboBox.DataSource = pondList;
                pondComboBox.DisplayMember = "Text";
                pondComboBox.ValueMember = "Value";

                // Load feed types
                var feedTypes = new[]
                {
                    "Ø¹Ù„Ù Ù†Ù…Ùˆ",
                    "Ø¹Ù„Ù Ø¨Ø§Ø¯Ø¦",
                    "Ø¹Ù„Ù ØªØ³Ù…ÙŠÙ†",
                    "Ø¹Ù„Ù ØµÙŠØ§Ù†Ø©",
                    "Ø¹Ù„Ù Ø·Ø¨ÙŠØ¹ÙŠ",
                    "Ø¹Ù„Ù Ù…Ø±ÙƒØ²"
                };
                feedTypeComboBox.Items.AddRange(feedTypes);
                feedTypeComboBox.SelectedIndex = 0;

                // Load recent feeding records
                await LoadFeedingHistory();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø£ÙˆÙ„ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadFeedingHistory()
        {
            try
            {
                // For now, we'll create a placeholder for feeding records
                // In a real implementation, you would have a FeedingRecord entity
                var historyData = new List<object>();
                
                // This is placeholder data - replace with actual database query
                feedingHistoryDataGridView.DataSource = historyData;

                await Task.CompletedTask; // Ù„ØªØ¬Ù†Ø¨ ØªØ­Ø°ÙŠØ± async
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø³Ø¬Ù„ Ø§Ù„ØªØºØ°ÙŠØ©");
            }
        }

        private void CalculateTotalCost(object sender, EventArgs e)
        {
            var quantity = quantityNumericUpDown.Value;
            var costPerUnit = costPerUnitNumericUpDown.Value;
            var totalCost = quantity * costPerUnit;
            
            totalCostLabel.Text = totalCost.ToString("C");
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                // Create feeding record
                // Note: You'll need to create a FeedingRecord entity and repository
                var feedingRecord = new
                {
                    PondId = (int)pondComboBox.SelectedValue,
                    FeedingDate = feedingDatePicker.Value.Date,
                    FeedingTime = GetSelectedTime(),
                    FeedType = feedTypeComboBox.SelectedItem.ToString(),
                    Quantity = quantityNumericUpDown.Value,
                    Unit = unitComboBox.SelectedItem.ToString(),
                    CostPerUnit = costPerUnitNumericUpDown.Value,
                    TotalCost = quantityNumericUpDown.Value * costPerUnitNumericUpDown.Value,
                    Notes = notesTextBox.Text,
                    RecordedDate = DateTime.Now
                };

                // Save to database (implement when FeedingRecord entity is created)
                // await _unitOfWork.FeedingRecords.AddAsync(feedingRecord);
                // await _unitOfWork.SaveChangesAsync();

                MessageBox.Show("ØªÙ… Ø­ÙØ¸ Ø³Ø¬Ù„ Ø§Ù„ØªØºØ°ÙŠØ© Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ClearForm();
                await LoadFeedingHistory();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­ÙØ¸ Ø³Ø¬Ù„ Ø§Ù„ØªØºØ°ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø­ÙØ¸: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (pondComboBox.SelectedValue == null)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ø§Ù„Ø­ÙˆØ¶", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (feedTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ù†ÙˆØ¹ Ø§Ù„Ø¹Ù„Ù", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (quantityNumericUpDown.Value <= 0)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø¥Ø¯Ø®Ø§Ù„ ÙƒÙ…ÙŠØ© ØµØ­ÙŠØ­Ø©", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private TimeSpan GetSelectedTime()
        {
            // This would get the time from the hour and minute combo boxes
            // For now, return current time
            return DateTime.Now.TimeOfDay;
        }

        private void ClearForm()
        {
            feedingDatePicker.Value = DateTime.Now;
            feedTypeComboBox.SelectedIndex = 0;
            quantityNumericUpDown.Value = 1;
            costPerUnitNumericUpDown.Value = 0;
            notesTextBox.Clear();
            totalCostLabel.Text = "0.00";
        }
    }
}



