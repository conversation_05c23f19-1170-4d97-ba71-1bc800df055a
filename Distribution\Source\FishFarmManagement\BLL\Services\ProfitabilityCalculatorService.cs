using Microsoft.Extensions.Logging;
using FishFarmManagement.Models;
using FishFarmManagement.DAL.Interfaces;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة حساب الربحية المحسنة
    /// Enhanced profitability calculator service
    /// </summary>
    public class ProfitabilityCalculatorService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProfitabilityCalculatorService> _logger;

        public ProfitabilityCalculatorService(IUnitOfWork unitOfWork, ILogger<ProfitabilityCalculatorService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// حساب ربحية الدورة الإنتاجية
        /// Calculate production cycle profitability
        /// </summary>
        public async Task<CycleProfitabilityResult> CalculateCycleProfitabilityAsync(int cycleId)
        {
            try
            {
                _logger.LogInformation("حساب ربحية الدورة الإنتاجية {CycleId}", cycleId);

                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                if (cycle == null)
                    throw new ArgumentException($"الدورة الإنتاجية {cycleId} غير موجودة");

                var result = new CycleProfitabilityResult
                {
                    CycleId = cycleId,
                    CycleName = cycle.CycleName,
                    StartDate = cycle.StartDate,
                    EndDate = cycle.EndDate,
                    Status = cycle.Status.ToString()
                };

                // حساب الإيرادات
                result.TotalRevenue = await CalculateCycleRevenueAsync(cycleId);

                // حساب التكاليف
                var costs = await CalculateCycleCostsAsync(cycleId);
                result.FeedCosts = costs.FeedCosts;
                result.LaborCosts = costs.LaborCosts;
                result.MedicationCosts = costs.MedicationCosts;
                result.UtilityCosts = costs.UtilityCosts;
                result.OtherCosts = costs.OtherCosts;
                result.TotalCosts = costs.TotalCosts;

                // حساب الربح
                result.GrossProfit = result.TotalRevenue - result.TotalCosts;
                result.ProfitMargin = result.TotalRevenue > 0 
                    ? (result.GrossProfit / result.TotalRevenue) * 100 
                    : 0;

                // حساب مؤشرات الأداء
                await CalculatePerformanceIndicatorsAsync(result, cycle);

                _logger.LogInformation("تم حساب ربحية الدورة الإنتاجية بنجاح");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب ربحية الدورة الإنتاجية {CycleId}", cycleId);
                throw;
            }
        }

        /// <summary>
        /// حساب ربحية الحوض
        /// Calculate pond profitability
        /// </summary>
        public async Task<PondProfitabilityResult> CalculatePondProfitabilityAsync(int pondId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                _logger.LogInformation("حساب ربحية الحوض {PondId}", pondId);

                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                if (pond == null)
                    throw new ArgumentException($"الحوض {pondId} غير موجود");

                fromDate ??= DateTime.Now.AddMonths(-6);
                toDate ??= DateTime.Now;

                var result = new PondProfitabilityResult
                {
                    PondId = pondId,
                    PondNumber = pond.PondNumber,
                    FromDate = fromDate.Value,
                    ToDate = toDate.Value,
                    CurrentFishCount = pond.FishCount,
                    AverageWeight = pond.AverageWeight
                };

                // حساب الإنتاج المتوقع
                result.EstimatedProduction = pond.FishCount * pond.AverageWeight;

                // حساب التكاليف
                var feedCosts = await CalculatePondFeedCostsAsync(pondId, fromDate.Value, toDate.Value);
                var medicationCosts = await CalculatePondMedicationCostsAsync(pondId, fromDate.Value, toDate.Value);
                var laborCosts = await CalculatePondLaborCostsAsync(pondId, fromDate.Value, toDate.Value);

                result.FeedCosts = feedCosts;
                result.MedicationCosts = medicationCosts;
                result.LaborCosts = laborCosts;
                result.TotalCosts = feedCosts + medicationCosts + laborCosts;

                // حساب الإيرادات المتوقعة (بناءً على سعر السوق)
                var marketPrice = await GetCurrentMarketPriceAsync();
                result.EstimatedRevenue = result.EstimatedProduction * marketPrice;

                // حساب الربح
                result.EstimatedProfit = result.EstimatedRevenue - result.TotalCosts;
                result.ProfitMargin = result.EstimatedRevenue > 0 
                    ? (result.EstimatedProfit / result.EstimatedRevenue) * 100 
                    : 0;

                // حساب مؤشرات الكفاءة
                result.FeedConversionRatio = await CalculateFeedConversionRatioAsync(pondId, fromDate.Value, toDate.Value);
                result.MortalityRate = await CalculateMortalityRateAsync(pondId, fromDate.Value, toDate.Value);
                result.GrowthRate = await CalculateGrowthRateAsync(pondId, fromDate.Value, toDate.Value);

                _logger.LogInformation("تم حساب ربحية الحوض بنجاح");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب ربحية الحوض {PondId}", pondId);
                throw;
            }
        }

        /// <summary>
        /// حساب العائد على الاستثمار
        /// Calculate return on investment
        /// </summary>
        public async Task<ROIResult> CalculateROIAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                _logger.LogInformation("حساب العائد على الاستثمار من {FromDate} إلى {ToDate}", fromDate, toDate);

                var result = new ROIResult
                {
                    FromDate = fromDate,
                    ToDate = toDate
                };

                // حساب إجمالي الاستثمار
                var investments = await _unitOfWork.Transactions.FindAsync(t =>
                    t.TransactionDate >= fromDate && t.TransactionDate <= toDate &&
                    (t.TransactionType == "استثمار" || t.TransactionType == "رأس مال"));
                
                result.TotalInvestment = investments.Sum(i => i.TotalAmount);

                // حساب إجمالي الإيرادات
                var revenues = await _unitOfWork.Transactions.FindAsync(t =>
                    t.TransactionDate >= fromDate && t.TransactionDate <= toDate &&
                    t.TransactionType == "إيراد");
                
                result.TotalRevenue = revenues.Sum(r => r.TotalAmount);

                // حساب إجمالي التكاليف
                var expenses = await _unitOfWork.Transactions.FindAsync(t =>
                    t.TransactionDate >= fromDate && t.TransactionDate <= toDate &&
                    t.TransactionType == "مصروف");
                
                result.TotalExpenses = expenses.Sum(e => e.TotalAmount);

                // حساب صافي الربح
                result.NetProfit = result.TotalRevenue - result.TotalExpenses;

                // حساب العائد على الاستثمار
                result.ROIPercentage = result.TotalInvestment > 0 
                    ? (result.NetProfit / result.TotalInvestment) * 100 
                    : 0;

                // حساب فترة الاسترداد
                result.PaybackPeriodMonths = await CalculatePaybackPeriodAsync(result.TotalInvestment, fromDate, toDate);

                // تقييم الأداء
                result.PerformanceRating = EvaluateROIPerformance(result.ROIPercentage);

                _logger.LogInformation("تم حساب العائد على الاستثمار بنجاح");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب العائد على الاستثمار");
                throw;
            }
        }

        /// <summary>
        /// تحليل التكاليف التفصيلي
        /// Detailed cost analysis
        /// </summary>
        public async Task<CostAnalysisResult> AnalyzeCostsAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                _logger.LogInformation("تحليل التكاليف من {FromDate} إلى {ToDate}", fromDate, toDate);

                var result = new CostAnalysisResult
                {
                    FromDate = fromDate,
                    ToDate = toDate
                };

                // تحليل تكاليف العلف
                var feedCosts = await AnalyzeFeedCostsAsync(fromDate, toDate);
                result.FeedCostAnalysis = feedCosts;

                // تحليل تكاليف العمالة
                var laborCosts = await AnalyzeLaborCostsAsync(fromDate, toDate);
                result.LaborCostAnalysis = laborCosts;

                // تحليل تكاليف الأدوية
                var medicationCosts = await AnalyzeMedicationCostsAsync(fromDate, toDate);
                result.MedicationCostAnalysis = medicationCosts;

                // تحليل التكاليف الأخرى
                var otherCosts = await AnalyzeOtherCostsAsync(fromDate, toDate);
                result.OtherCostAnalysis = otherCosts;

                // حساب الإجماليات
                result.TotalCosts = feedCosts.TotalAmount + laborCosts.TotalAmount + 
                                   medicationCosts.TotalAmount + otherCosts.TotalAmount;

                // حساب النسب المئوية
                if (result.TotalCosts > 0)
                {
                    result.FeedCostPercentage = (feedCosts.TotalAmount / result.TotalCosts) * 100;
                    result.LaborCostPercentage = (laborCosts.TotalAmount / result.TotalCosts) * 100;
                    result.MedicationCostPercentage = (medicationCosts.TotalAmount / result.TotalCosts) * 100;
                    result.OtherCostPercentage = (otherCosts.TotalAmount / result.TotalCosts) * 100;
                }

                // تحليل الاتجاهات
                result.CostTrends = await AnalyzeCostTrendsAsync(fromDate, toDate);

                // التوصيات
                result.Recommendations = GenerateCostOptimizationRecommendations(result);

                _logger.LogInformation("تم تحليل التكاليف بنجاح");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحليل التكاليف");
                throw;
            }
        }

        // Private helper methods
        private async Task<decimal> CalculateCycleRevenueAsync(int cycleId)
        {
            var sales = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionType == "إيراد" && t.Description.Contains($"دورة {cycleId}"));
            return sales.Sum(s => s.TotalAmount);
        }

        private async Task<CycleCosts> CalculateCycleCostsAsync(int cycleId)
        {
            var costs = new CycleCosts();
            
            // تكاليف العلف
            var feedTransactions = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionType == "مصروف" && t.Description.Contains("علف") &&
                t.Description.Contains($"دورة {cycleId}"));
            costs.FeedCosts = feedTransactions.Sum(t => t.TotalAmount);

            // تكاليف العمالة
            var laborTransactions = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionType == "مصروف" && t.Description.Contains("راتب") &&
                t.Description.Contains($"دورة {cycleId}"));
            costs.LaborCosts = laborTransactions.Sum(t => t.TotalAmount);

            // تكاليف الأدوية
            var medicationTransactions = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionType == "مصروف" && t.Description.Contains("دواء") &&
                t.Description.Contains($"دورة {cycleId}"));
            costs.MedicationCosts = medicationTransactions.Sum(t => t.TotalAmount);

            // التكاليف الأخرى
            var otherTransactions = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionType == "مصروف" &&
                !t.Description.Contains("علف") && !t.Description.Contains("راتب") && !t.Description.Contains("دواء") &&
                t.Description.Contains($"دورة {cycleId}"));
            costs.OtherCosts = otherTransactions.Sum(t => t.TotalAmount);

            costs.TotalCosts = costs.FeedCosts + costs.LaborCosts + costs.MedicationCosts + costs.OtherCosts;
            return costs;
        }

        private async Task CalculatePerformanceIndicatorsAsync(CycleProfitabilityResult result, ProductionCycle cycle)
        {
            // حساب معدل التحويل الغذائي
            // TODO: تحتاج إلى تحديث النماذج لتشمل CycleId
            result.FeedConversionRatio = 1.5m; // قيمة افتراضية

            // حساب معدل النفوق
            // TODO: تحتاج إلى تحديث النماذج لتشمل CycleId
            result.MortalityRate = 2.0m; // قيمة افتراضية

            // حساب معدل النمو
            var cycleDuration = (cycle.EndDate ?? DateTime.Now) - cycle.StartDate;
            result.GrowthRate = cycleDuration.Days > 0 ? (10m / cycleDuration.Days) * 30 : 0; // نمو شهري افتراضي

            await Task.CompletedTask; // لتجنب تحذير async
        }

        private async Task<decimal> CalculatePondFeedCostsAsync(int pondId, DateTime fromDate, DateTime toDate)
        {
            var feedConsumptions = await _unitOfWork.FeedConsumptions.FindAsync(f =>
                f.PondId == pondId && f.FeedingDate >= fromDate && f.FeedingDate <= toDate);
            return feedConsumptions.Sum(f => f.Quantity * 10m); // سعر افتراضي 10 ريال للكيلو
        }

        private async Task<decimal> CalculatePondMedicationCostsAsync(int pondId, DateTime fromDate, DateTime toDate)
        {
            // TODO: تحتاج إلى إضافة جدول MedicationRecords
            await Task.CompletedTask;
            return 500m; // تكلفة افتراضية للأدوية
        }

        private async Task<decimal> CalculatePondLaborCostsAsync(int pondId, DateTime fromDate, DateTime toDate)
        {
            // تقدير تكلفة العمالة بناءً على عدد الأيام والراتب اليومي المتوسط
            var days = (toDate - fromDate).Days;
            var averageDailyWage = 100m; // يمكن تخصيصه من الإعدادات
            return days * averageDailyWage * 0.1m; // 10% من تكلفة العمالة للحوض الواحد
        }

        private async Task<decimal> GetCurrentMarketPriceAsync()
        {
            // يمكن الحصول على السعر من جدول الأسعار أو API خارجي
            // هنا نستخدم سعر افتراضي
            return 25m; // 25 ريال للكيلو
        }

        private async Task<decimal> CalculateFeedConversionRatioAsync(int pondId, DateTime fromDate, DateTime toDate)
        {
            var feedConsumptions = await _unitOfWork.FeedConsumptions.FindAsync(f =>
                f.PondId == pondId && f.FeedingDate >= fromDate && f.FeedingDate <= toDate);
            var totalFeed = feedConsumptions.Sum(f => f.Quantity);

            var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
            var estimatedProduction = pond.FishCount * pond.AverageWeight;

            return estimatedProduction > 0 ? totalFeed / estimatedProduction : 0;
        }

        private async Task<decimal> CalculateMortalityRateAsync(int pondId, DateTime fromDate, DateTime toDate)
        {
            var mortalities = await _unitOfWork.FishMortalities.FindAsync(m =>
                m.PondId == pondId && m.MortalityDate >= fromDate && m.MortalityDate <= toDate);
            var totalMortality = mortalities.Sum(m => m.DeadFishCount);

            var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
            return pond.FishCount > 0 ? (decimal)totalMortality / pond.FishCount * 100 : 0;
        }

        private async Task<decimal> CalculateGrowthRateAsync(int pondId, DateTime fromDate, DateTime toDate)
        {
            // TODO: تحتاج إلى إضافة جدول WeightRecords
            await Task.CompletedTask;
            return 0.5m; // معدل نمو افتراضي 0.5 جرام يومياً
        }

        private async Task<decimal> CalculatePaybackPeriodAsync(decimal investment, DateTime fromDate, DateTime toDate)
        {
            if (investment <= 0) return 0;

            var monthlyProfits = new List<decimal>();
            var currentDate = fromDate;

            while (currentDate < toDate)
            {
                var monthEnd = currentDate.AddMonths(1);
                if (monthEnd > toDate) monthEnd = toDate;

                var monthlyRevenue = await GetMonthlyRevenueAsync(currentDate, monthEnd);
                var monthlyExpenses = await GetMonthlyExpensesAsync(currentDate, monthEnd);
                var monthlyProfit = monthlyRevenue - monthlyExpenses;

                monthlyProfits.Add(monthlyProfit);
                currentDate = monthEnd;
            }

            var averageMonthlyProfit = monthlyProfits.Any() ? monthlyProfits.Average() : 0;
            return averageMonthlyProfit > 0 ? investment / averageMonthlyProfit : 0;
        }

        private async Task<decimal> GetMonthlyRevenueAsync(DateTime fromDate, DateTime toDate)
        {
            var revenues = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionDate >= fromDate && t.TransactionDate <= toDate &&
                t.TransactionType == "إيراد");
            return revenues.Sum(r => r.TotalAmount);
        }

        private async Task<decimal> GetMonthlyExpensesAsync(DateTime fromDate, DateTime toDate)
        {
            var expenses = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionDate >= fromDate && t.TransactionDate <= toDate &&
                t.TransactionType == "مصروف");
            return expenses.Sum(e => e.TotalAmount);
        }

        private string EvaluateROIPerformance(decimal roiPercentage)
        {
            return roiPercentage switch
            {
                >= 30 => "ممتاز",
                >= 20 => "جيد جداً",
                >= 15 => "جيد",
                >= 10 => "مقبول",
                >= 5 => "ضعيف",
                _ => "غير مربح"
            };
        }

        private async Task<CostCategoryAnalysis> AnalyzeFeedCostsAsync(DateTime fromDate, DateTime toDate)
        {
            var feedTransactions = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionDate >= fromDate && t.TransactionDate <= toDate &&
                t.TransactionType == "مصروف" && t.Description.Contains("علف"));

            return new CostCategoryAnalysis
            {
                CategoryName = "علف",
                TotalAmount = feedTransactions.Sum(t => t.TotalAmount),
                TransactionCount = feedTransactions.Count(),
                AverageTransactionAmount = feedTransactions.Any() ? feedTransactions.Average(t => t.TotalAmount) : 0,
                MinAmount = feedTransactions.Any() ? feedTransactions.Min(t => t.TotalAmount) : 0,
                MaxAmount = feedTransactions.Any() ? feedTransactions.Max(t => t.TotalAmount) : 0
            };
        }

        private async Task<CostCategoryAnalysis> AnalyzeLaborCostsAsync(DateTime fromDate, DateTime toDate)
        {
            var laborTransactions = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionDate >= fromDate && t.TransactionDate <= toDate &&
                t.TransactionType == "مصروف" && t.Description.Contains("راتب"));

            return new CostCategoryAnalysis
            {
                CategoryName = "عمالة",
                TotalAmount = laborTransactions.Sum(t => t.TotalAmount),
                TransactionCount = laborTransactions.Count(),
                AverageTransactionAmount = laborTransactions.Any() ? laborTransactions.Average(t => t.TotalAmount) : 0,
                MinAmount = laborTransactions.Any() ? laborTransactions.Min(t => t.TotalAmount) : 0,
                MaxAmount = laborTransactions.Any() ? laborTransactions.Max(t => t.TotalAmount) : 0
            };
        }

        private async Task<CostCategoryAnalysis> AnalyzeMedicationCostsAsync(DateTime fromDate, DateTime toDate)
        {
            var medicationTransactions = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionDate >= fromDate && t.TransactionDate <= toDate &&
                t.TransactionType == "مصروف" && t.Description.Contains("دواء"));

            return new CostCategoryAnalysis
            {
                CategoryName = "أدوية",
                TotalAmount = medicationTransactions.Sum(t => t.TotalAmount),
                TransactionCount = medicationTransactions.Count(),
                AverageTransactionAmount = medicationTransactions.Any() ? medicationTransactions.Average(t => t.TotalAmount) : 0,
                MinAmount = medicationTransactions.Any() ? medicationTransactions.Min(t => t.TotalAmount) : 0,
                MaxAmount = medicationTransactions.Any() ? medicationTransactions.Max(t => t.TotalAmount) : 0
            };
        }

        private async Task<CostCategoryAnalysis> AnalyzeOtherCostsAsync(DateTime fromDate, DateTime toDate)
        {
            var otherTransactions = await _unitOfWork.Transactions.FindAsync(t =>
                t.TransactionDate >= fromDate && t.TransactionDate <= toDate &&
                t.TransactionType == "مصروف" &&
                !t.Description.Contains("علف") && !t.Description.Contains("راتب") && !t.Description.Contains("دواء"));

            return new CostCategoryAnalysis
            {
                CategoryName = "أخرى",
                TotalAmount = otherTransactions.Sum(t => t.TotalAmount),
                TransactionCount = otherTransactions.Count(),
                AverageTransactionAmount = otherTransactions.Any() ? otherTransactions.Average(t => t.TotalAmount) : 0,
                MinAmount = otherTransactions.Any() ? otherTransactions.Min(t => t.TotalAmount) : 0,
                MaxAmount = otherTransactions.Any() ? otherTransactions.Max(t => t.TotalAmount) : 0
            };
        }

        private async Task<List<CostTrend>> AnalyzeCostTrendsAsync(DateTime fromDate, DateTime toDate)
        {
            var trends = new List<CostTrend>();
            var currentDate = fromDate;

            while (currentDate < toDate)
            {
                var monthEnd = currentDate.AddMonths(1);
                if (monthEnd > toDate) monthEnd = toDate;

                var monthlyExpenses = await GetMonthlyExpensesAsync(currentDate, monthEnd);

                trends.Add(new CostTrend
                {
                    Period = currentDate.ToString("yyyy/MM"),
                    TotalCost = monthlyExpenses
                });

                currentDate = monthEnd;
            }

            return trends;
        }

        private List<string> GenerateCostOptimizationRecommendations(CostAnalysisResult analysis)
        {
            var recommendations = new List<string>();

            if (analysis.FeedCostPercentage > 60)
            {
                recommendations.Add("تكلفة العلف مرتفعة - يُنصح بمراجعة مصادر العلف والبحث عن بدائل أقل تكلفة");
                recommendations.Add("تحسين معدل التحويل الغذائي من خلال تحسين جودة العلف وأوقات التغذية");
            }

            if (analysis.LaborCostPercentage > 30)
            {
                recommendations.Add("تكلفة العمالة مرتفعة - يُنصح بتحسين كفاءة العمليات والأتمتة حيث أمكن");
            }

            if (analysis.MedicationCostPercentage > 15)
            {
                recommendations.Add("تكلفة الأدوية مرتفعة - يُنصح بتحسين الوقاية وجودة المياه لتقليل الحاجة للأدوية");
            }

            if (analysis.CostTrends.Count > 1)
            {
                var lastTrend = analysis.CostTrends.Last();
                var previousTrend = analysis.CostTrends[analysis.CostTrends.Count - 2];

                if (lastTrend.TotalCost > previousTrend.TotalCost * 1.1m)
                {
                    recommendations.Add("التكاليف في ارتفاع - يُنصح بمراجعة الميزانية ووضع خطة للتحكم في التكاليف");
                }
            }

            if (!recommendations.Any())
            {
                recommendations.Add("التكاليف ضمن المعدلات الطبيعية - استمر في المراقبة والتحسين المستمر");
            }

            return recommendations;
        }
    }

    // Data models for profitability calculations
    public class CycleProfitabilityResult
    {
        public int CycleId { get; set; }
        public string CycleName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public decimal TotalRevenue { get; set; }
        public decimal FeedCosts { get; set; }
        public decimal LaborCosts { get; set; }
        public decimal MedicationCosts { get; set; }
        public decimal UtilityCosts { get; set; }
        public decimal OtherCosts { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public decimal FeedConversionRatio { get; set; }
        public decimal MortalityRate { get; set; }
        public decimal GrowthRate { get; set; }
    }

    public class PondProfitabilityResult
    {
        public int PondId { get; set; }
        public string PondNumber { get; set; } = string.Empty;
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int CurrentFishCount { get; set; }
        public decimal AverageWeight { get; set; }
        public decimal EstimatedProduction { get; set; }
        public decimal EstimatedRevenue { get; set; }
        public decimal FeedCosts { get; set; }
        public decimal MedicationCosts { get; set; }
        public decimal LaborCosts { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal EstimatedProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public decimal FeedConversionRatio { get; set; }
        public decimal MortalityRate { get; set; }
        public decimal GrowthRate { get; set; }
    }

    public class ROIResult
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal TotalInvestment { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal ROIPercentage { get; set; }
        public decimal PaybackPeriodMonths { get; set; }
        public string PerformanceRating { get; set; } = string.Empty;
    }

    public class CostAnalysisResult
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal FeedCostPercentage { get; set; }
        public decimal LaborCostPercentage { get; set; }
        public decimal MedicationCostPercentage { get; set; }
        public decimal OtherCostPercentage { get; set; }
        public CostCategoryAnalysis FeedCostAnalysis { get; set; } = new();
        public CostCategoryAnalysis LaborCostAnalysis { get; set; } = new();
        public CostCategoryAnalysis MedicationCostAnalysis { get; set; } = new();
        public CostCategoryAnalysis OtherCostAnalysis { get; set; } = new();
        public List<CostTrend> CostTrends { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    public class CostCategoryAnalysis
    {
        public string CategoryName { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionAmount { get; set; }
        public decimal MinAmount { get; set; }
        public decimal MaxAmount { get; set; }
    }

    public class CostTrend
    {
        public string Period { get; set; } = string.Empty;
        public decimal TotalCost { get; set; }
    }

    public class CycleCosts
    {
        public decimal FeedCosts { get; set; }
        public decimal LaborCosts { get; set; }
        public decimal MedicationCosts { get; set; }
        public decimal UtilityCosts { get; set; }
        public decimal OtherCosts { get; set; }
        public decimal TotalCosts { get; set; }
    }
}
