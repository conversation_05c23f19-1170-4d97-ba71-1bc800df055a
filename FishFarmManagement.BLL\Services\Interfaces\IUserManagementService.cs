using System.Threading.Tasks;
using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    public interface IUserManagementService
    {
        Task InitializeDefaultDataAsync();
        Task<IEnumerable<User>> GetAllUsersAsync();
        Task<User?> GetUserByIdAsync(int id);
        Task<User?> GetUserByUsernameAsync(string username);
        Task<User> CreateUserAsync(User user);
        Task<User> UpdateUserAsync(User user);
        Task<bool> DeleteUserAsync(int id);
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
    }
}