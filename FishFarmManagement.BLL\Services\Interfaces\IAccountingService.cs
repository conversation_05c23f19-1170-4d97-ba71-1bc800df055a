using FishFarmManagement.Models;
using IncomeStatement = FishFarmManagement.Models.IncomeStatement;
using BalanceSheet = FishFarmManagement.Models.BalanceSheet;
using CycleProfitability = FishFarmManagement.Models.CycleProfitability;
using CostCenterAnalysis = FishFarmManagement.Models.CostCenterAnalysis;
using MedicationPurchaseTransaction = FishFarmManagement.Models.MedicationPurchaseTransaction;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// واجهة الخدمة المحاسبية
    /// Accounting service interface
    /// </summary>
    public interface IAccountingService
    {
        /// <summary>
        /// إنشاء قيد محاسبي
        /// Create accounting entry
        /// </summary>
        Task<Transaction> CreateTransactionAsync(TransactionRequest request);

        /// <summary>
        /// ترحيل قيد محاسبي
        /// Post accounting transaction
        /// </summary>
        Task<bool> PostTransactionAsync(int transactionId);

        /// <summary>
        /// إلغاء قيد محاسبي
        /// Cancel accounting transaction
        /// </summary>
        Task<bool> CancelTransactionAsync(int transactionId);

        /// <summary>
        /// الحصول على ميزان المراجعة
        /// Get trial balance
        /// </summary>
        Task<TrialBalance> GetTrialBalanceAsync(DateTime? asOfDate = null);

        /// <summary>
        /// الحصول على قائمة الدخل
        /// Get income statement
        /// </summary>
        Task<IncomeStatement> GetIncomeStatementAsync(DateTime fromDate, DateTime toDate, int? cycleId = null);

        /// <summary>
        /// الحصول على الميزانية العمومية
        /// Get balance sheet
        /// </summary>
        Task<BalanceSheet> GetBalanceSheetAsync(DateTime asOfDate);

        /// <summary>
        /// حساب ربحية الدورة الإنتاجية
        /// Calculate production cycle profitability
        /// </summary>
        Task<CycleProfitability> GetCycleProfitabilityAsync(int cycleId);

        /// <summary>
        /// مقارنة ربحية الدورات
        /// Compare cycles profitability
        /// </summary>
        Task<IEnumerable<CycleProfitability>> CompareCyclesProfitabilityAsync(IEnumerable<int> cycleIds);

        /// <summary>
        /// تحليل التكاليف حسب مركز التكلفة
        /// Analyze costs by cost center
        /// </summary>
        Task<CostCenterAnalysis> GetCostCenterAnalysisAsync(int costCenterId);

        /// <summary>
        /// إنشاء قيد راتب تلقائي
        /// Create automatic payroll entry
        /// </summary>
        Task<Transaction> CreatePayrollTransactionAsync(int payrollId);

        /// <summary>
        /// إنشاء قيد شراء علف تلقائي
        /// Create automatic feed purchase entry
        /// </summary>
        Task<Transaction> CreateFeedPurchaseTransactionAsync(FeedPurchaseTransaction request);

        /// <summary>
        /// إنشاء قيد شراء دواء تلقائي
        /// Create automatic medication purchase entry
        /// </summary>
        Task<Transaction> CreateMedicationPurchaseTransactionAsync(MedicationPurchaseTransaction request);

        /// <summary>
        /// التحقق من توازن الحسابات
        /// Check accounts balance
        /// </summary>
        Task<bool> CheckAccountsBalanceAsync();

        /// <summary>
        /// إقفال الدورة المحاسبية
        /// Close accounting period
        /// </summary>
        Task<bool> CloseAccountingPeriodAsync(int cycleId);
    }

    /// <summary>
    /// طلب إنشاء معاملة
    /// Transaction creation request
    /// </summary>
    public class TransactionRequest
    {
        public int CycleId { get; set; }
        public string TransactionType { get; set; } = string.Empty;
        public DateTime TransactionDate { get; set; }
        public string Description { get; set; } = string.Empty;
        public List<TransactionDetailRequest> Details { get; set; } = new List<TransactionDetailRequest>();
    }

    public class TransactionDetailRequest
    {
        public int AccountId { get; set; }
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public string Description { get; set; } = string.Empty;
        public int? CostCenterId { get; set; }
    }

    /// <summary>
    /// ميزان المراجعة
    /// Trial balance
    /// </summary>
    public class TrialBalance
    {
        public DateTime AsOfDate { get; set; }
        public List<TrialBalanceItem> Items { get; set; } = new List<TrialBalanceItem>();
        public decimal TotalDebits { get; set; }
        public decimal TotalCredits { get; set; }
        public bool IsBalanced => Math.Abs(TotalDebits - TotalCredits) < 0.01m;
    }

    public class TrialBalanceItem
    {
        public string AccountCode { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public decimal DebitBalance { get; set; }
        public decimal CreditBalance { get; set; }
    }

    /// <summary>
    /// معاملة شراء العلف
    /// Feed purchase transaction
    /// </summary>
    public class FeedPurchaseTransaction
    {
        public int CycleId { get; set; }
        public int FeedTypeId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalAmount { get; set; }
        public DateTime PurchaseDate { get; set; }
        public string SupplierName { get; set; } = string.Empty;
        public string InvoiceNumber { get; set; } = string.Empty;
    }
}
