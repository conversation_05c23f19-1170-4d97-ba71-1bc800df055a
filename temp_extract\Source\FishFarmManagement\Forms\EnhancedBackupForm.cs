﻿using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Drawing;
using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ø§ÙØ°Ø© Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠ ÙˆØ§Ù„Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø§Ù„Ù…Ø­Ø³Ù†Ø©
    /// Enhanced backup and restore form
    /// </summary>
    public partial class EnhancedBackupForm : Form
    {
        private readonly ILogger<EnhancedBackupForm> _logger;
        private readonly IBackupService _backupService;

        // Controls
        private TabControl tabControl;
        private TabPage backupTab;
        private TabPage restoreTab;
        private TabPage scheduleTab;
        private TabPage statisticsTab;

        // Backup controls
        private TextBox backupPathTextBox;
        private Button browseBackupButton;
        private TextBox descriptionTextBox;
        private ComboBox backupTypeComboBox;
        private CheckBox useCompressionCheckBox;
        private CheckBox useEncryptionCheckBox;
        private TextBox passwordTextBox;
        private Button createBackupButton;
        private ProgressBar backupProgressBar;
        private Label backupStatusLabel;

        // Restore controls
        private TextBox restorePathTextBox;
        private Button browseRestoreButton;
        private TextBox restorePasswordTextBox;
        private Button verifyBackupButton;
        private Button restoreButton;
        private ProgressBar restoreProgressBar;
        private Label restoreStatusLabel;
        private DataGridView backupListGridView;

        // Schedule controls
        private CheckBox enableScheduleCheckBox;
        private ComboBox frequencyComboBox;
        private DateTimePicker timePickerSchedule;
        private ComboBox dayOfWeekComboBox;
        private NumericUpDown dayOfMonthNumeric;
        private TextBox scheduleDirectoryTextBox;
        private Button browseScheduleButton;
        private NumericUpDown retentionDaysNumeric;
        private Button saveScheduleButton;
        private Label scheduleStatusLabel;

        // Statistics controls
        private Label dbSizeLabel;
        private Label tableCountLabel;
        private Label totalRecordsLabel;
        private Button optimizeButton;
        private Button refreshStatsButton;
        private DataGridView tableStatsGridView;

        public EnhancedBackupForm(ILogger<EnhancedBackupForm> logger, IBackupService backupService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
            InitializeComponent();
            _ = LoadInitialDataAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠ ÙˆØ§Ù„Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø§Ù„Ù…Ø­Ø³Ù†";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 600);

            CreateControls();
            SetupEventHandlers();
        }

        private void CreateControls()
        {
            // Create main tab control
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            // Create tabs
            CreateBackupTab();
            CreateRestoreTab();
            CreateScheduleTab();
            CreateStatisticsTab();

            // Add tabs to control
            tabControl.TabPages.Add(backupTab);
            tabControl.TabPages.Add(restoreTab);
            tabControl.TabPages.Add(scheduleTab);
            tabControl.TabPages.Add(statisticsTab);

            this.Controls.Add(tabControl);
        }

        private void CreateBackupTab()
        {
            backupTab = new TabPage("Ø¥Ù†Ø´Ø§Ø¡ Ù†Ø³Ø®Ø© Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©");
            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 10,
                Padding = new Padding(10)
            };

            // Backup path
            panel.Controls.Add(new Label { Text = "Ù…Ø³Ø§Ø± Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©:", Anchor = AnchorStyles.Right }, 0, 0);
            backupPathTextBox = new TextBox { Dock = DockStyle.Fill };
            panel.Controls.Add(backupPathTextBox, 1, 0);
            browseBackupButton = new Button { Text = "Ø§Ø³ØªØ¹Ø±Ø§Ø¶...", Width = 80 };
            panel.Controls.Add(browseBackupButton, 2, 0);

            // Description
            panel.Controls.Add(new Label { Text = "Ø§Ù„ÙˆØµÙ:", Anchor = AnchorStyles.Right }, 0, 1);
            descriptionTextBox = new TextBox { Dock = DockStyle.Fill };
            panel.Controls.Add(descriptionTextBox, 1, 1);

            // Backup type
            panel.Controls.Add(new Label { Text = "Ù†ÙˆØ¹ Ø§Ù„Ù†Ø³Ø®Ø©:", Anchor = AnchorStyles.Right }, 0, 2);
            backupTypeComboBox = new ComboBox 
            { 
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            backupTypeComboBox.Items.AddRange(new[] { "ÙƒØ§Ù…Ù„Ø©", "ØªØ²Ø§ÙŠØ¯ÙŠØ©", "ØªÙØ§Ø¶Ù„ÙŠØ©" });
            backupTypeComboBox.SelectedIndex = 0;
            panel.Controls.Add(backupTypeComboBox, 1, 2);

            // Options
            useCompressionCheckBox = new CheckBox { Text = "Ø§Ø³ØªØ®Ø¯Ø§Ù… Ø§Ù„Ø¶ØºØ·", Dock = DockStyle.Fill };
            panel.Controls.Add(useCompressionCheckBox, 1, 3);

            useEncryptionCheckBox = new CheckBox { Text = "Ø§Ø³ØªØ®Ø¯Ø§Ù… Ø§Ù„ØªØ´ÙÙŠØ±", Dock = DockStyle.Fill };
            panel.Controls.Add(useEncryptionCheckBox, 1, 4);

            // Password
            panel.Controls.Add(new Label { Text = "ÙƒÙ„Ù…Ø© Ø§Ù„Ù…Ø±ÙˆØ±:", Anchor = AnchorStyles.Right }, 0, 5);
            passwordTextBox = new TextBox { Dock = DockStyle.Fill, UseSystemPasswordChar = true, Enabled = false };
            panel.Controls.Add(passwordTextBox, 1, 5);

            // Create backup button
            createBackupButton = new Button 
            { 
                Text = "Ø¥Ù†Ø´Ø§Ø¡ Ù†Ø³Ø®Ø© Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©", 
                Height = 35,
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            panel.Controls.Add(createBackupButton, 1, 6);

            // Progress bar
            backupProgressBar = new ProgressBar { Dock = DockStyle.Fill, Visible = false };
            panel.Controls.Add(backupProgressBar, 1, 7);

            // Status label
            backupStatusLabel = new Label { Text = "Ø¬Ø§Ù‡Ø²", Dock = DockStyle.Fill };
            panel.Controls.Add(backupStatusLabel, 1, 8);

            // Set column styles
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 150));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));

            backupTab.Controls.Add(panel);
        }

        private void CreateRestoreTab()
        {
            restoreTab = new TabPage("Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©");
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(10)
            };

            // Top panel for restore controls
            var topPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 6
            };

            // Restore path
            topPanel.Controls.Add(new Label { Text = "Ù…Ù„Ù Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©:", Anchor = AnchorStyles.Right }, 0, 0);
            restorePathTextBox = new TextBox { Dock = DockStyle.Fill };
            topPanel.Controls.Add(restorePathTextBox, 1, 0);
            browseRestoreButton = new Button { Text = "Ø§Ø³ØªØ¹Ø±Ø§Ø¶...", Width = 80 };
            topPanel.Controls.Add(browseRestoreButton, 2, 0);

            // Password
            topPanel.Controls.Add(new Label { Text = "ÙƒÙ„Ù…Ø© Ø§Ù„Ù…Ø±ÙˆØ±:", Anchor = AnchorStyles.Right }, 0, 1);
            restorePasswordTextBox = new TextBox { Dock = DockStyle.Fill, UseSystemPasswordChar = true };
            topPanel.Controls.Add(restorePasswordTextBox, 1, 1);

            // Buttons
            var buttonPanel = new FlowLayoutPanel { Dock = DockStyle.Fill, FlowDirection = FlowDirection.LeftToRight };
            verifyBackupButton = new Button { Text = "Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† Ø§Ù„Ù†Ø³Ø®Ø©", Height = 35, Width = 120 };
            restoreButton = new Button 
            { 
                Text = "Ø§Ø³ØªØ¹Ø§Ø¯Ø©", 
                Height = 35, 
                Width = 100,
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            buttonPanel.Controls.AddRange(new Control[] { verifyBackupButton, restoreButton });
            topPanel.Controls.Add(buttonPanel, 1, 2);

            // Progress bar
            restoreProgressBar = new ProgressBar { Dock = DockStyle.Fill, Visible = false };
            topPanel.Controls.Add(restoreProgressBar, 1, 3);

            // Status label
            restoreStatusLabel = new Label { Text = "Ø¬Ø§Ù‡Ø²", Dock = DockStyle.Fill };
            topPanel.Controls.Add(restoreStatusLabel, 1, 4);

            // Set column styles for top panel
            topPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 150));
            topPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            topPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));

            // Backup list
            var listLabel = new Label { Text = "Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© Ø§Ù„Ù…ØªØ§Ø­Ø©:", Dock = DockStyle.Top, Height = 25 };
            backupListGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect
            };

            // Setup columns
            backupListGridView.Columns.Add(new DataGridViewTextBoxColumn { Name = "FileName", HeaderText = "Ø§Ø³Ù… Ø§Ù„Ù…Ù„Ù", DataPropertyName = "FileName" });
            backupListGridView.Columns.Add(new DataGridViewTextBoxColumn { Name = "Size", HeaderText = "Ø§Ù„Ø­Ø¬Ù…", DataPropertyName = "Size" });
            backupListGridView.Columns.Add(new DataGridViewTextBoxColumn { Name = "CreatedAt", HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø¥Ù†Ø´Ø§Ø¡", DataPropertyName = "CreatedAt" });
            backupListGridView.Columns.Add(new DataGridViewTextBoxColumn { Name = "Type", HeaderText = "Ø§Ù„Ù†ÙˆØ¹", DataPropertyName = "Type" });
            backupListGridView.Columns.Add(new DataGridViewCheckBoxColumn { Name = "IsEncrypted", HeaderText = "Ù…Ø´ÙØ±", DataPropertyName = "IsEncrypted" });

            var bottomPanel = new Panel { Dock = DockStyle.Fill };
            bottomPanel.Controls.Add(backupListGridView);
            bottomPanel.Controls.Add(listLabel);

            mainPanel.Controls.Add(topPanel, 0, 0);
            mainPanel.Controls.Add(bottomPanel, 0, 1);

            // Set row styles
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 200));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            restoreTab.Controls.Add(mainPanel);
        }

        private void CreateScheduleTab()
        {
            scheduleTab = new TabPage("Ø§Ù„Ø¬Ø¯ÙˆÙ„Ø© Ø§Ù„ØªÙ„Ù‚Ø§Ø¦ÙŠØ©");
            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 10,
                Padding = new Padding(10)
            };

            // Enable schedule
            enableScheduleCheckBox = new CheckBox { Text = "ØªÙØ¹ÙŠÙ„ Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠ Ø§Ù„ØªÙ„Ù‚Ø§Ø¦ÙŠ", Dock = DockStyle.Fill };
            panel.Controls.Add(enableScheduleCheckBox, 0, 0);
            panel.SetColumnSpan(enableScheduleCheckBox, 2);

            // Frequency
            panel.Controls.Add(new Label { Text = "Ø§Ù„ØªÙƒØ±Ø§Ø±:", Anchor = AnchorStyles.Right }, 0, 1);
            frequencyComboBox = new ComboBox { Dock = DockStyle.Fill, DropDownStyle = ComboBoxStyle.DropDownList };
            frequencyComboBox.Items.AddRange(new[] { "ÙŠÙˆÙ…ÙŠ", "Ø£Ø³Ø¨ÙˆØ¹ÙŠ", "Ø´Ù‡Ø±ÙŠ" });
            panel.Controls.Add(frequencyComboBox, 1, 1);

            // Time
            panel.Controls.Add(new Label { Text = "Ø§Ù„ÙˆÙ‚Øª:", Anchor = AnchorStyles.Right }, 0, 2);
            timePickerSchedule = new DateTimePicker { Format = DateTimePickerFormat.Time, ShowUpDown = true, Dock = DockStyle.Fill };
            panel.Controls.Add(timePickerSchedule, 1, 2);

            // Day of week
            panel.Controls.Add(new Label { Text = "ÙŠÙˆÙ… Ø§Ù„Ø£Ø³Ø¨ÙˆØ¹:", Anchor = AnchorStyles.Right }, 0, 3);
            dayOfWeekComboBox = new ComboBox { Dock = DockStyle.Fill, DropDownStyle = ComboBoxStyle.DropDownList };
            dayOfWeekComboBox.Items.AddRange(new[] { "Ø§Ù„Ø£Ø­Ø¯", "Ø§Ù„Ø§Ø«Ù†ÙŠÙ†", "Ø§Ù„Ø«Ù„Ø§Ø«Ø§Ø¡", "Ø§Ù„Ø£Ø±Ø¨Ø¹Ø§Ø¡", "Ø§Ù„Ø®Ù…ÙŠØ³", "Ø§Ù„Ø¬Ù…Ø¹Ø©", "Ø§Ù„Ø³Ø¨Øª" });
            panel.Controls.Add(dayOfWeekComboBox, 1, 3);

            // Day of month
            panel.Controls.Add(new Label { Text = "ÙŠÙˆÙ… Ø§Ù„Ø´Ù‡Ø±:", Anchor = AnchorStyles.Right }, 0, 4);
            dayOfMonthNumeric = new NumericUpDown { Minimum = 1, Maximum = 31, Dock = DockStyle.Fill };
            panel.Controls.Add(dayOfMonthNumeric, 1, 4);

            // Directory
            panel.Controls.Add(new Label { Text = "Ù…Ø¬Ù„Ø¯ Ø§Ù„Ù†Ø³Ø®:", Anchor = AnchorStyles.Right }, 0, 5);
            var dirPanel = new TableLayoutPanel { Dock = DockStyle.Fill, ColumnCount = 2 };
            scheduleDirectoryTextBox = new TextBox { Dock = DockStyle.Fill };
            browseScheduleButton = new Button { Text = "Ø§Ø³ØªØ¹Ø±Ø§Ø¶...", Width = 80 };
            dirPanel.Controls.Add(scheduleDirectoryTextBox, 0, 0);
            dirPanel.Controls.Add(browseScheduleButton, 1, 0);
            dirPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            dirPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            panel.Controls.Add(dirPanel, 1, 5);

            // Retention days
            panel.Controls.Add(new Label { Text = "Ø§Ù„Ø§Ø­ØªÙØ§Ø¸ (Ø£ÙŠØ§Ù…):", Anchor = AnchorStyles.Right }, 0, 6);
            retentionDaysNumeric = new NumericUpDown { Minimum = 1, Maximum = 365, Value = 30, Dock = DockStyle.Fill };
            panel.Controls.Add(retentionDaysNumeric, 1, 6);

            // Save button
            saveScheduleButton = new Button 
            { 
                Text = "Ø­ÙØ¸ Ø§Ù„Ø¬Ø¯ÙˆÙ„Ø©", 
                Height = 35,
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            panel.Controls.Add(saveScheduleButton, 1, 7);

            // Status
            scheduleStatusLabel = new Label { Text = "ØºÙŠØ± Ù…Ø¬Ø¯ÙˆÙ„", Dock = DockStyle.Fill };
            panel.Controls.Add(scheduleStatusLabel, 1, 8);

            // Set column styles
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 150));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            scheduleTab.Controls.Add(panel);
        }

        private void CreateStatisticsTab()
        {
            statisticsTab = new TabPage("Ø§Ù„Ø¥Ø­ØµØ§Ø¦ÙŠØ§Øª ÙˆØ§Ù„ØªØ­Ø³ÙŠÙ†");
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(10)
            };

            // Statistics panel
            var statsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 2
            };

            // Database statistics
            statsPanel.Controls.Add(new Label { Text = "Ø­Ø¬Ù… Ù‚Ø§Ø¹Ø¯Ø© Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª:", Anchor = AnchorStyles.Right }, 0, 0);
            dbSizeLabel = new Label { Text = "0 Ø¨Ø§ÙŠØª", Anchor = AnchorStyles.Left };
            statsPanel.Controls.Add(dbSizeLabel, 1, 0);

            statsPanel.Controls.Add(new Label { Text = "Ø¹Ø¯Ø¯ Ø§Ù„Ø¬Ø¯Ø§ÙˆÙ„:", Anchor = AnchorStyles.Right }, 2, 0);
            tableCountLabel = new Label { Text = "0", Anchor = AnchorStyles.Left };
            statsPanel.Controls.Add(tableCountLabel, 3, 0);

            statsPanel.Controls.Add(new Label { Text = "Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ø³Ø¬Ù„Ø§Øª:", Anchor = AnchorStyles.Right }, 0, 1);
            totalRecordsLabel = new Label { Text = "0", Anchor = AnchorStyles.Left };
            statsPanel.Controls.Add(totalRecordsLabel, 1, 1);

            // Buttons
            var buttonPanel = new FlowLayoutPanel { Dock = DockStyle.Fill, FlowDirection = FlowDirection.LeftToRight };
            optimizeButton = new Button { Text = "ØªØ­Ø³ÙŠÙ† Ù‚Ø§Ø¹Ø¯Ø© Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª", Height = 35, Width = 150 };
            refreshStatsButton = new Button { Text = "ØªØ­Ø¯ÙŠØ« Ø§Ù„Ø¥Ø­ØµØ§Ø¦ÙŠØ§Øª", Height = 35, Width = 150 };
            buttonPanel.Controls.AddRange(new Control[] { optimizeButton, refreshStatsButton });
            statsPanel.Controls.Add(buttonPanel, 2, 1);
            statsPanel.SetColumnSpan(buttonPanel, 2);

            // Table statistics grid
            tableStatsGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true
            };

            // Setup columns
            tableStatsGridView.Columns.Add(new DataGridViewTextBoxColumn { Name = "TableName", HeaderText = "Ø§Ø³Ù… Ø§Ù„Ø¬Ø¯ÙˆÙ„", DataPropertyName = "Key" });
            tableStatsGridView.Columns.Add(new DataGridViewTextBoxColumn { Name = "RecordCount", HeaderText = "Ø¹Ø¯Ø¯ Ø§Ù„Ø³Ø¬Ù„Ø§Øª", DataPropertyName = "Value" });

            mainPanel.Controls.Add(statsPanel, 0, 0);
            mainPanel.Controls.Add(new Label { Text = "Ø¥Ø­ØµØ§Ø¦ÙŠØ§Øª Ø§Ù„Ø¬Ø¯Ø§ÙˆÙ„:", Dock = DockStyle.Top, Height = 25 }, 0, 1);
            mainPanel.Controls.Add(tableStatsGridView, 0, 2);

            // Set row styles
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 100));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 25));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            statisticsTab.Controls.Add(mainPanel);
        }

        private void SetupEventHandlers()
        {
            // Backup tab events
            browseBackupButton.Click += BrowseBackupButton_Click;
            useEncryptionCheckBox.CheckedChanged += UseEncryptionCheckBox_CheckedChanged;
            createBackupButton.Click += CreateBackupButton_Click;

            // Restore tab events
            browseRestoreButton.Click += BrowseRestoreButton_Click;
            verifyBackupButton.Click += VerifyBackupButton_Click;
            restoreButton.Click += RestoreButton_Click;
            backupListGridView.SelectionChanged += BackupListGridView_SelectionChanged;

            // Schedule tab events
            enableScheduleCheckBox.CheckedChanged += EnableScheduleCheckBox_CheckedChanged;
            frequencyComboBox.SelectedIndexChanged += FrequencyComboBox_SelectedIndexChanged;
            browseScheduleButton.Click += BrowseScheduleButton_Click;
            saveScheduleButton.Click += SaveScheduleButton_Click;

            // Statistics tab events
            optimizeButton.Click += OptimizeButton_Click;
            refreshStatsButton.Click += RefreshStatsButton_Click;

            // Form events
            this.Load += EnhancedBackupForm_Load;
        }

        private async Task LoadInitialDataAsync()
        {
            try
            {
                // Load backup list
                await LoadBackupListAsync();

                // Load schedule status
                await LoadScheduleStatusAsync();

                // Load statistics
                await LoadStatisticsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø£ÙˆÙ„ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BrowseBackupButton_Click(object? sender, EventArgs e)
        {
            using var dialog = new SaveFileDialog
            {
                Filter = "Ù…Ù„ÙØ§Øª Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© (*.bak)|*.bak|Ù…Ù„ÙØ§Øª Ù…Ø¶ØºÙˆØ·Ø© (*.gz)|*.gz|Ø¬Ù…ÙŠØ¹ Ø§Ù„Ù…Ù„ÙØ§Øª (*.*)|*.*",
                DefaultExt = "bak",
                FileName = $"FishFarm_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.bak"
            };

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                backupPathTextBox.Text = dialog.FileName;
            }
        }

        private void UseEncryptionCheckBox_CheckedChanged(object? sender, EventArgs e)
        {
            passwordTextBox.Enabled = useEncryptionCheckBox.Checked;
            if (!useEncryptionCheckBox.Checked)
            {
                passwordTextBox.Clear();
            }
        }

        private async void CreateBackupButton_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(backupPathTextBox.Text))
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ ØªØ­Ø¯ÙŠØ¯ Ù…Ø³Ø§Ø± Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (useEncryptionCheckBox.Checked && string.IsNullOrWhiteSpace(passwordTextBox.Text))
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø¥Ø¯Ø®Ø§Ù„ ÙƒÙ„Ù…Ø© Ø§Ù„Ù…Ø±ÙˆØ± Ù„Ù„ØªØ´ÙÙŠØ±", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                createBackupButton.Enabled = false;
                backupProgressBar.Visible = true;
                backupProgressBar.Style = ProgressBarStyle.Marquee;
                backupStatusLabel.Text = "Ø¬Ø§Ø±ÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©...";

                var backupPath = backupPathTextBox.Text;
                var description = descriptionTextBox.Text;
                var password = useEncryptionCheckBox.Checked ? passwordTextBox.Text : "";

                BackupResult result;

                if (useEncryptionCheckBox.Checked)
                {
                    result = await _backupService.CreateEncryptedBackupAsync(backupPath, password, description);
                }
                else if (useCompressionCheckBox.Checked)
                {
                    result = await _backupService.CreateCompressedBackupAsync(backupPath, description);
                }
                else
                {
                    result = await _backupService.CreateFullBackupAsync(backupPath, description);
                }

                if (result.Success)
                {
                    backupStatusLabel.Text = $"ØªÙ… Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© Ø¨Ù†Ø¬Ø§Ø­ - Ø§Ù„Ø­Ø¬Ù…: {FormatFileSize(result.BackupSize)}";
                    MessageBox.Show("ØªÙ… Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    await LoadBackupListAsync();
                }
                else
                {
                    backupStatusLabel.Text = "ÙØ´Ù„ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©";
                    MessageBox.Show($"ÙØ´Ù„ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©: {result.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©");
                backupStatusLabel.Text = "Ø­Ø¯Ø« Ø®Ø·Ø£";
                MessageBox.Show($"Ø­Ø¯Ø« Ø®Ø·Ø£: {ex.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                createBackupButton.Enabled = true;
                backupProgressBar.Visible = false;
                backupProgressBar.Style = ProgressBarStyle.Continuous;
            }
        }

        private void BrowseRestoreButton_Click(object? sender, EventArgs e)
        {
            using var dialog = new OpenFileDialog
            {
                Filter = "Ù…Ù„ÙØ§Øª Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© (*.bak;*.gz;*.db)|*.bak;*.gz;*.db|Ø¬Ù…ÙŠØ¹ Ø§Ù„Ù…Ù„ÙØ§Øª (*.*)|*.*",
                Title = "Ø§Ø®ØªØ± Ù…Ù„Ù Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©"
            };

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                restorePathTextBox.Text = dialog.FileName;
            }
        }

        private async void VerifyBackupButton_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(restorePathTextBox.Text))
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ ØªØ­Ø¯ÙŠØ¯ Ù…Ù„Ù Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                verifyBackupButton.Enabled = false;
                restoreStatusLabel.Text = "Ø¬Ø§Ø±ÙŠ Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©...";

                var result = await _backupService.VerifyBackupAsync(restorePathTextBox.Text, restorePasswordTextBox.Text);

                if (result.IsValid)
                {
                    restoreStatusLabel.Text = "Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© ØµØ­ÙŠØ­Ø©";
                    MessageBox.Show("Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© ØµØ­ÙŠØ­Ø© ÙˆÙŠÙ…ÙƒÙ† Ø§Ø³ØªØ¹Ø§Ø¯ØªÙ‡Ø§", "Ø§Ù„ØªØ­Ù‚Ù‚ Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    restoreStatusLabel.Text = "Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© ØªØ­ØªÙˆÙŠ Ø¹Ù„Ù‰ Ù…Ø´Ø§ÙƒÙ„";
                    var issues = string.Join("\n", result.Issues);
                    MessageBox.Show($"ØªÙ… Ø§Ù„Ø¹Ø«ÙˆØ± Ø¹Ù„Ù‰ Ù…Ø´Ø§ÙƒÙ„ ÙÙŠ Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©:\n{issues}", "Ø§Ù„ØªØ­Ù‚Ù‚ ÙØ´Ù„", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©");
                restoreStatusLabel.Text = "Ø­Ø¯Ø« Ø®Ø·Ø£ ÙÙŠ Ø§Ù„ØªØ­Ù‚Ù‚";
                MessageBox.Show($"Ø­Ø¯Ø« Ø®Ø·Ø£ ÙÙŠ Ø§Ù„ØªØ­Ù‚Ù‚: {ex.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                verifyBackupButton.Enabled = true;
            }
        }

        private async void RestoreButton_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(restorePathTextBox.Text))
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ ØªØ­Ø¯ÙŠØ¯ Ù…Ù„Ù Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var confirmResult = MessageBox.Show(
                "Ù‡Ù„ Ø£Ù†Øª Ù…ØªØ£ÙƒØ¯ Ù…Ù† Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©ØŸ Ø³ÙŠØªÙ… Ø§Ø³ØªØ¨Ø¯Ø§Ù„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø­Ø§Ù„ÙŠØ©.",
                "ØªØ£ÙƒÙŠØ¯ Ø§Ù„Ø§Ø³ØªØ¹Ø§Ø¯Ø©",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (confirmResult != DialogResult.Yes)
                return;

            try
            {
                restoreButton.Enabled = false;
                restoreProgressBar.Visible = true;
                restoreProgressBar.Style = ProgressBarStyle.Marquee;
                restoreStatusLabel.Text = "Ø¬Ø§Ø±ÙŠ Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©...";

                var result = await _backupService.RestoreBackupAsync(restorePathTextBox.Text, restorePasswordTextBox.Text);

                if (result.Success)
                {
                    restoreStatusLabel.Text = "ØªÙ… Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© Ø¨Ù†Ø¬Ø§Ø­";
                    MessageBox.Show("ØªÙ… Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© Ø¨Ù†Ø¬Ø§Ø­. ÙŠØ±Ø¬Ù‰ Ø¥Ø¹Ø§Ø¯Ø© ØªØ´ØºÙŠÙ„ Ø§Ù„ØªØ·Ø¨ÙŠÙ‚.", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    restoreStatusLabel.Text = "ÙØ´Ù„ ÙÙŠ Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©";
                    MessageBox.Show($"ÙØ´Ù„ ÙÙŠ Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©: {result.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©");
                restoreStatusLabel.Text = "Ø­Ø¯Ø« Ø®Ø·Ø£";
                MessageBox.Show($"Ø­Ø¯Ø« Ø®Ø·Ø£: {ex.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                restoreButton.Enabled = true;
                restoreProgressBar.Visible = false;
                restoreProgressBar.Style = ProgressBarStyle.Continuous;
            }
        }

        private void BackupListGridView_SelectionChanged(object? sender, EventArgs e)
        {
            if (backupListGridView.SelectedRows.Count > 0)
            {
                var selectedRow = backupListGridView.SelectedRows[0];
                var backupInfo = selectedRow.DataBoundItem as BackupInfo;
                if (backupInfo != null)
                {
                    restorePathTextBox.Text = backupInfo.FullPath;
                }
            }
        }

        private void EnableScheduleCheckBox_CheckedChanged(object? sender, EventArgs e)
        {
            var enabled = enableScheduleCheckBox.Checked;
            frequencyComboBox.Enabled = enabled;
            timePickerSchedule.Enabled = enabled;
            dayOfWeekComboBox.Enabled = enabled;
            dayOfMonthNumeric.Enabled = enabled;
            scheduleDirectoryTextBox.Enabled = enabled;
            browseScheduleButton.Enabled = enabled;
            retentionDaysNumeric.Enabled = enabled;
            saveScheduleButton.Enabled = enabled;
        }

        private void FrequencyComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            var frequency = frequencyComboBox.SelectedIndex;
            dayOfWeekComboBox.Enabled = frequency == 1; // Weekly
            dayOfMonthNumeric.Enabled = frequency == 2; // Monthly
        }

        private void BrowseScheduleButton_Click(object? sender, EventArgs e)
        {
            using var dialog = new FolderBrowserDialog
            {
                Description = "Ø§Ø®ØªØ± Ù…Ø¬Ù„Ø¯ Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© Ø§Ù„ØªÙ„Ù‚Ø§Ø¦ÙŠØ©"
            };

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                scheduleDirectoryTextBox.Text = dialog.SelectedPath;
            }
        }

        private async void SaveScheduleButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (enableScheduleCheckBox.Checked)
                {
                    if (string.IsNullOrWhiteSpace(scheduleDirectoryTextBox.Text))
                    {
                        MessageBox.Show("ÙŠØ±Ø¬Ù‰ ØªØ­Ø¯ÙŠØ¯ Ù…Ø¬Ù„Ø¯ Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    if (frequencyComboBox.SelectedIndex == -1)
                    {
                        MessageBox.Show("ÙŠØ±Ø¬Ù‰ ØªØ­Ø¯ÙŠØ¯ ØªÙƒØ±Ø§Ø± Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠ", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    var schedule = new BackupSchedule
                    {
                        Enabled = true,
                        Frequency = (BackupFrequency)(frequencyComboBox.SelectedIndex + 1),
                        Time = timePickerSchedule.Value.TimeOfDay,
                        DayOfWeek = frequencyComboBox.SelectedIndex == 1 ? (DayOfWeek)dayOfWeekComboBox.SelectedIndex : null,
                        DayOfMonth = frequencyComboBox.SelectedIndex == 2 ? (int)dayOfMonthNumeric.Value : null,
                        BackupDirectory = scheduleDirectoryTextBox.Text,
                        Type = BackupType.Full,
                        UseCompression = true,
                        UseEncryption = false,
                        RetentionDays = (int)retentionDaysNumeric.Value
                    };

                    var success = await _backupService.ScheduleAutomaticBackupAsync(schedule);
                    if (success)
                    {
                        scheduleStatusLabel.Text = "ØªÙ… Ø­ÙØ¸ Ø§Ù„Ø¬Ø¯ÙˆÙ„Ø© Ø¨Ù†Ø¬Ø§Ø­";
                        MessageBox.Show("ØªÙ… Ø­ÙØ¸ Ø¬Ø¯ÙˆÙ„Ø© Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠ Ø§Ù„ØªÙ„Ù‚Ø§Ø¦ÙŠ", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        await LoadScheduleStatusAsync();
                    }
                    else
                    {
                        MessageBox.Show("ÙØ´Ù„ ÙÙŠ Ø­ÙØ¸ Ø§Ù„Ø¬Ø¯ÙˆÙ„Ø©", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    var success = await _backupService.CancelAutomaticBackupAsync();
                    if (success)
                    {
                        scheduleStatusLabel.Text = "ØªÙ… Ø¥Ù„ØºØ§Ø¡ Ø§Ù„Ø¬Ø¯ÙˆÙ„Ø©";
                        MessageBox.Show("ØªÙ… Ø¥Ù„ØºØ§Ø¡ Ø¬Ø¯ÙˆÙ„Ø© Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠ Ø§Ù„ØªÙ„Ù‚Ø§Ø¦ÙŠ", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        await LoadScheduleStatusAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­ÙØ¸ Ø¬Ø¯ÙˆÙ„Ø© Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠ");
                MessageBox.Show($"Ø­Ø¯Ø« Ø®Ø·Ø£: {ex.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void OptimizeButton_Click(object? sender, EventArgs e)
        {
            var confirmResult = MessageBox.Show(
                "Ù‡Ù„ ØªØ±ÙŠØ¯ ØªØ­Ø³ÙŠÙ† Ù‚Ø§Ø¹Ø¯Ø© Ø§Ù„Ø¨ÙŠØ§Ù†Ø§ØªØŸ Ù‚Ø¯ ÙŠØ³ØªØºØ±Ù‚ Ù‡Ø°Ø§ Ø¨Ø¹Ø¶ Ø§Ù„ÙˆÙ‚Øª.",
                "ØªØ£ÙƒÙŠØ¯ Ø§Ù„ØªØ­Ø³ÙŠÙ†",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (confirmResult != DialogResult.Yes)
                return;

            try
            {
                optimizeButton.Enabled = false;
                optimizeButton.Text = "Ø¬Ø§Ø±ÙŠ Ø§Ù„ØªØ­Ø³ÙŠÙ†...";

                var result = await _backupService.OptimizeDatabaseAsync();

                if (result.Success)
                {
                    MessageBox.Show($"ØªÙ… ØªØ­Ø³ÙŠÙ† Ù‚Ø§Ø¹Ø¯Ø© Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø¨Ù†Ø¬Ø§Ø­\nØªÙ… ØªÙˆÙÙŠØ±: {FormatFileSize(result.SpaceSaved)}", "Ù†Ø¬Ø­", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    await LoadStatisticsAsync();
                }
                else
                {
                    MessageBox.Show($"ÙØ´Ù„ ÙÙŠ ØªØ­Ø³ÙŠÙ† Ù‚Ø§Ø¹Ø¯Ø© Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {result.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ø³ÙŠÙ† Ù‚Ø§Ø¹Ø¯Ø© Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª");
                MessageBox.Show($"Ø­Ø¯Ø« Ø®Ø·Ø£: {ex.Message}", "Ø®Ø·Ø£", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                optimizeButton.Enabled = true;
                optimizeButton.Text = "ØªØ­Ø³ÙŠÙ† Ù‚Ø§Ø¹Ø¯Ø© Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª";
            }
        }

        private async void private async void private async void RefreshStatsButton_Click(object? sender, EventArgs e)
        {
            await LoadStatisticsAsync();
        }

        private async void private async void private async void EnhancedBackupForm_Load(object? sender, EventArgs e)
        {
            await LoadInitialDataAsync();
        }

        private async Task LoadBackupListAsync()
        {
            try
            {
                var backupDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
                var backups = await _backupService.GetBackupListAsync(backupDirectory);

                backupListGridView.DataSource = backups.Select(b => new
                {
                    b.FileName,
                    Size = FormatFileSize(b.Size),
                    CreatedAt = b.CreatedAt.ToString("yyyy-MM-dd HH:mm"),
                    Type = GetBackupTypeText(b.Type),
                    b.IsEncrypted
                }).ToList();

                // Store the original BackupInfo in Tag
                for (int i = 0; i < backupListGridView.Rows.Count && i < backups.Count; i++)
                {
                    backupListGridView.Rows[i].Tag = backups[i];
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù†Ø³Ø® Ø§Ù„Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©");
            }
        }

        private async Task LoadScheduleStatusAsync()
        {
            try
            {
                var status = await _backupService.GetAutomaticBackupStatusAsync();

                enableScheduleCheckBox.Checked = status.IsScheduled;

                if (status.Schedule != null)
                {
                    frequencyComboBox.SelectedIndex = (int)status.Schedule.Frequency - 1;
                    timePickerSchedule.Value = DateTime.Today.Add(status.Schedule.Time);

                    if (status.Schedule.DayOfWeek.HasValue)
                        dayOfWeekComboBox.SelectedIndex = (int)status.Schedule.DayOfWeek.Value;

                    if (status.Schedule.DayOfMonth.HasValue)
                        dayOfMonthNumeric.Value = status.Schedule.DayOfMonth.Value;

                    scheduleDirectoryTextBox.Text = status.Schedule.BackupDirectory;
                    retentionDaysNumeric.Value = status.Schedule.RetentionDays;
                }

                if (status.IsScheduled)
                {
                    var nextBackupText = status.NextBackup?.ToString("yyyy-MM-dd HH:mm") ?? "ØºÙŠØ± Ù…Ø­Ø¯Ø¯";
                    scheduleStatusLabel.Text = $"Ù…Ø¬Ø¯ÙˆÙ„ - Ø§Ù„Ù†Ø³Ø®Ø© Ø§Ù„ØªØ§Ù„ÙŠØ©: {nextBackupText}";
                }
                else
                {
                    scheduleStatusLabel.Text = "ØºÙŠØ± Ù…Ø¬Ø¯ÙˆÙ„";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø­Ø§Ù„Ø© Ø§Ù„Ø¬Ø¯ÙˆÙ„Ø©");
            }
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                var stats = await _backupService.GetDatabaseStatisticsAsync();

                dbSizeLabel.Text = FormatFileSize(stats.DatabaseSize);
                tableCountLabel.Text = stats.TableCount.ToString();
                totalRecordsLabel.Text = stats.TotalRecords.ToString();

                tableStatsGridView.DataSource = stats.TableRecords.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¥Ø­ØµØ§Ø¦ÙŠØ§Øª");
            }
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes < 1024)
                return $"{bytes} Ø¨Ø§ÙŠØª";
            else if (bytes < 1024 * 1024)
                return $"{bytes / 1024.0:F1} ÙƒÙŠÙ„ÙˆØ¨Ø§ÙŠØª";
            else if (bytes < 1024 * 1024 * 1024)
                return $"{bytes / (1024.0 * 1024.0):F1} Ù…ÙŠØ¬Ø§Ø¨Ø§ÙŠØª";
            else
                return $"{bytes / (1024.0 * 1024.0 * 1024.0):F1} Ø¬ÙŠØ¬Ø§Ø¨Ø§ÙŠØª";
        }

        private string GetBackupTypeText(BackupType type)
        {
            return type switch
            {
                BackupType.Full => "ÙƒØ§Ù…Ù„Ø©",
                BackupType.Incremental => "ØªØ²Ø§ÙŠØ¯ÙŠØ©",
                BackupType.Differential => "ØªÙØ§Ø¶Ù„ÙŠØ©",
                _ => "ØºÙŠØ± Ù…Ø¹Ø±ÙˆÙ"
            };
        }
    }
}



