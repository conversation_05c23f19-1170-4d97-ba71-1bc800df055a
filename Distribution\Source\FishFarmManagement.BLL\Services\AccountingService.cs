using Microsoft.Extensions.Logging;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;


namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة المحاسبة المبسطة
    /// Simplified accounting service
    /// </summary>
    public class AccountingService
    {
        private readonly ILogger<AccountingService> _logger;
        private readonly IUnitOfWork _unitOfWork;

        public AccountingService(ILogger<AccountingService> logger, IUnitOfWork unitOfWork)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// إنشاء معاملة جديدة
        /// Create new transaction
        /// </summary>
        public async Task<Transaction> CreateTransactionAsync(Transaction transaction)
        {
            try
            {
                _logger.LogInformation("إنشاء معاملة جديدة: {Description}", transaction.Description);
                
                transaction.CreatedDate = DateTime.Now;
                transaction.Status = TransactionStatus.Draft.ToString();
                
                await _unitOfWork.Transactions.AddAsync(transaction);
                await _unitOfWork.SaveChangesAsync();
                
                _logger.LogInformation("تم إنشاء المعاملة بنجاح: {Id}", transaction.Id);
                return transaction;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء المعاملة");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع المعاملات
        /// Get all transactions
        /// </summary>
        public async Task<IEnumerable<Transaction>> GetAllTransactionsAsync()
        {
            try
            {
                var transactions = await _unitOfWork.Transactions.GetAllAsync();
                return transactions.OrderByDescending(t => t.CreatedDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المعاملات");
                throw;
            }
        }

        /// <summary>
        /// الحصول على معاملة بالمعرف
        /// Get transaction by ID
        /// </summary>
        public async Task<Transaction?> GetTransactionByIdAsync(int id)
        {
            try
            {
                return await _unitOfWork.Transactions.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المعاملة: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// تحديث معاملة
        /// Update transaction
        /// </summary>
        public async Task<Transaction> UpdateTransactionAsync(Transaction transaction)
        {
            try
            {
                _logger.LogInformation("تحديث المعاملة: {Id}", transaction.Id);
                
                // transaction.ModifiedDate = DateTime.Now; // خاصية غير موجودة

                // _unitOfWork.Transactions.Update(transaction); // طريقة غير موجودة
                // استخدام طريقة بديلة
                var existingTransaction = await _unitOfWork.Transactions.GetByIdAsync(transaction.Id);
                if (existingTransaction != null)
                {
                    existingTransaction.Description = transaction.Description;
                    existingTransaction.TotalAmount = transaction.TotalAmount;
                    existingTransaction.Status = transaction.Status;
                }
                await _unitOfWork.SaveChangesAsync();
                
                _logger.LogInformation("تم تحديث المعاملة بنجاح: {Id}", transaction.Id);
                return transaction;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المعاملة: {Id}", transaction.Id);
                throw;
            }
        }

        /// <summary>
        /// حذف معاملة
        /// Delete transaction
        /// </summary>
        public async Task<bool> DeleteTransactionAsync(int id)
        {
            try
            {
                _logger.LogInformation("حذف المعاملة: {Id}", id);
                
                var transaction = await _unitOfWork.Transactions.GetByIdAsync(id);
                if (transaction == null)
                {
                    _logger.LogWarning("المعاملة غير موجودة: {Id}", id);
                    return false;
                }
                
                // _unitOfWork.Transactions.Delete(transaction); // طريقة غير موجودة
                // استخدام طريقة بديلة - تحديث الحالة بدلاً من الحذف
                transaction.Status = TransactionStatus.Cancelled.ToString();
                await _unitOfWork.SaveChangesAsync();
                
                _logger.LogInformation("تم حذف المعاملة بنجاح: {Id}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المعاملة: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// الحصول على المعاملات في فترة زمنية
        /// Get transactions in date range
        /// </summary>
        public async Task<IEnumerable<Transaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var transactions = await _unitOfWork.Transactions.GetAllAsync();
                return transactions.Where(t => t.CreatedDate >= startDate && t.CreatedDate <= endDate)
                                 .OrderByDescending(t => t.CreatedDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المعاملات للفترة: {Start} - {End}", startDate, endDate);
                throw;
            }
        }

        /// <summary>
        /// حساب إجمالي المبلغ للمعاملات
        /// Calculate total amount for transactions
        /// </summary>
        public async Task<decimal> CalculateTotalAmountAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var transactions = await _unitOfWork.Transactions.GetAllAsync();
                
                if (startDate.HasValue && endDate.HasValue)
                {
                    transactions = transactions.Where(t => t.CreatedDate >= startDate && t.CreatedDate <= endDate);
                }
                
                return transactions.Sum(t => t.TotalAmount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إجمالي المبلغ");
                throw;
            }
        }

        /// <summary>
        /// الحصول على تقرير مالي مبسط
        /// Get simplified financial report
        /// </summary>
        public async Task<object> GetFinancialSummaryAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var transactions = await GetTransactionsByDateRangeAsync(startDate, endDate);
                
                var income = transactions.Where(t => t.TotalAmount > 0).Sum(t => t.TotalAmount);
                var expenses = transactions.Where(t => t.TotalAmount < 0).Sum(t => Math.Abs(t.TotalAmount));
                var netProfit = income - expenses;
                
                return new
                {
                    Period = $"{startDate:yyyy-MM-dd} إلى {endDate:yyyy-MM-dd}",
                    TotalIncome = income,
                    TotalExpenses = expenses,
                    NetProfit = netProfit,
                    TransactionCount = transactions.Count()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء التقرير المالي");
                throw;
            }
        }

        /// <summary>
        /// تسجيل معاملة شراء علف
        /// Record feed purchase transaction
        /// </summary>
        public async Task<Transaction> RecordFeedPurchaseAsync(decimal amount, string description, int? feedTypeId = null)
        {
            try
            {
                var transaction = new Transaction
                {
                    TotalAmount = -Math.Abs(amount), // مصروف
                    Description = $"شراء علف: {description}",
                    Status = TransactionStatus.Posted.ToString(),
                    CreatedDate = DateTime.Now
                };
                
                return await CreateTransactionAsync(transaction);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل معاملة شراء العلف");
                throw;
            }
        }

        /// <summary>
        /// تسجيل معاملة بيع أسماك
        /// Record fish sale transaction
        /// </summary>
        public async Task<Transaction> RecordFishSaleAsync(decimal amount, string description, int? productionCycleId = null)
        {
            try
            {
                var transaction = new Transaction
                {
                    TotalAmount = Math.Abs(amount), // دخل
                    Description = $"بيع أسماك: {description}",
                    Status = TransactionStatus.Posted.ToString(),
                    CreatedDate = DateTime.Now
                };
                
                return await CreateTransactionAsync(transaction);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل معاملة بيع الأسماك");
                throw;
            }
        }
    }
}
