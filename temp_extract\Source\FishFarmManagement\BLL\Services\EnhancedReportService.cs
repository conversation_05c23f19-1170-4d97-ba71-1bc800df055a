using Microsoft.Extensions.Logging;
using FishFarmManagement.Models;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Helpers;
using System.Data;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة التقارير المحسنة
    /// Enhanced report service
    /// </summary>
    public class EnhancedReportService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<EnhancedReportService> _logger;
        private readonly ReportExporter _reportExporter;

        public EnhancedReportService(
            IUnitOfWork unitOfWork, 
            ILogger<EnhancedReportService> logger,
            ReportExporter reportExporter)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _reportExporter = reportExporter ?? throw new ArgumentNullException(nameof(reportExporter));
        }

        /// <summary>
        /// إنشاء تقرير الإنتاج الشامل
        /// Generate comprehensive production report
        /// </summary>
        public async Task<ProductionReportData> GenerateProductionReportAsync(DateTime fromDate, DateTime toDate, int? cycleId = null)
        {
            try
            {
                _logger.LogInformation("إنشاء تقرير الإنتاج من {FromDate} إلى {ToDate}", fromDate, toDate);

                var ponds = await _unitOfWork.Ponds.GetAllAsync();
                if (cycleId.HasValue)
                {
                    ponds = ponds.Where(p => p.CycleId == cycleId.Value);
                }

                var reportData = new ProductionReportData
                {
                    ReportDate = DateTime.Now,
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalPonds = ponds.Count(),
                    ActivePonds = ponds.Count(p => p.Status == "نشط"),
                    TotalFishCount = ponds.Sum(p => p.FishCount),
                    AverageWeight = ponds.Any() ? ponds.Average(p => p.AverageWeight) : 0,
                    EstimatedProduction = ponds.Sum(p => p.FishCount * p.AverageWeight)
                };

                // Get feed consumption data
                var feedConsumptions = await _unitOfWork.FeedConsumptions.FindAsync(f => 
                    f.FeedingDate >= fromDate && f.FeedingDate <= toDate);
                
                reportData.TotalFeedConsumption = feedConsumptions.Sum(f => f.Quantity);
                reportData.TotalFeedCost = feedConsumptions.Sum(f => f.Quantity * 10m); // سعر افتراضي

                // Get mortality data
                var mortalities = await _unitOfWork.FishMortalities.FindAsync(m => 
                    m.MortalityDate >= fromDate && m.MortalityDate <= toDate);
                
                reportData.TotalMortality = mortalities.Sum(m => m.DeadFishCount);
                reportData.MortalityRate = reportData.TotalFishCount > 0 
                    ? (decimal)reportData.TotalMortality / reportData.TotalFishCount * 100 
                    : 0;

                // Pond details
                reportData.PondDetails = ponds.Select(p => new PondProductionDetail
                {
                    PondNumber = p.PondNumber,
                    FishCount = p.FishCount,
                    AverageWeight = p.AverageWeight,
                    EstimatedProduction = p.FishCount * p.AverageWeight,
                    Status = p.Status,
                    FeedConsumption = feedConsumptions.Where(f => f.PondId == p.Id).Sum(f => f.Quantity),
                    Mortality = mortalities.Where(m => m.PondId == p.Id).Sum(m => m.DeadFishCount)
                }).ToList();

                _logger.LogInformation("تم إنشاء تقرير الإنتاج بنجاح");
                return reportData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير الإنتاج");
                throw;
            }
        }

        /// <summary>
        /// إنشاء تقرير مالي شامل
        /// Generate comprehensive financial report
        /// </summary>
        public async Task<FinancialReportData> GenerateFinancialReportAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                _logger.LogInformation("إنشاء التقرير المالي من {FromDate} إلى {ToDate}", fromDate, toDate);

                var transactions = await _unitOfWork.Transactions.FindAsync(t => 
                    t.TransactionDate >= fromDate && t.TransactionDate <= toDate);

                var reportData = new FinancialReportData
                {
                    ReportDate = DateTime.Now,
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalRevenue = transactions.Where(t => t.TransactionType == "إيراد").Sum(t => t.TotalAmount),
                    TotalExpenses = transactions.Where(t => t.TransactionType == "مصروف").Sum(t => t.TotalAmount),
                    TransactionCount = transactions.Count()
                };

                reportData.NetProfit = reportData.TotalRevenue - reportData.TotalExpenses;
                reportData.ProfitMargin = reportData.TotalRevenue > 0 
                    ? (reportData.NetProfit / reportData.TotalRevenue) * 100 
                    : 0;

                // Expense breakdown
                reportData.ExpenseBreakdown = transactions
                    .Where(t => t.TransactionType == "مصروف")
                    .GroupBy(t => t.Description)
                    .Select(g => new ExpenseCategory
                    {
                        Category = g.Key,
                        Amount = g.Sum(t => t.TotalAmount),
                        Percentage = reportData.TotalExpenses > 0
                            ? (g.Sum(t => t.TotalAmount) / reportData.TotalExpenses) * 100
                            : 0
                    }).ToList();

                // Revenue breakdown
                reportData.RevenueBreakdown = transactions
                    .Where(t => t.TransactionType == "إيراد")
                    .GroupBy(t => t.Description)
                    .Select(g => new RevenueCategory
                    {
                        Category = g.Key,
                        Amount = g.Sum(t => t.TotalAmount),
                        Percentage = reportData.TotalRevenue > 0
                            ? (g.Sum(t => t.TotalAmount) / reportData.TotalRevenue) * 100
                            : 0
                    }).ToList();

                _logger.LogInformation("تم إنشاء التقرير المالي بنجاح");
                return reportData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء التقرير المالي");
                throw;
            }
        }

        /// <summary>
        /// إنشاء تقرير الموظفين والرواتب
        /// Generate employee and payroll report
        /// </summary>
        public async Task<EmployeeReportData> GenerateEmployeeReportAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                _logger.LogInformation("إنشاء تقرير الموظفين من {FromDate} إلى {ToDate}", fromDate, toDate);

                var employees = await _unitOfWork.Employees.GetAllAsync();
                var payrolls = await _unitOfWork.Payrolls.FindAsync(p =>
                    p.CreatedDate >= fromDate && p.CreatedDate <= toDate);

                var reportData = new EmployeeReportData
                {
                    ReportDate = DateTime.Now,
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalEmployees = employees.Count(),
                    ActiveEmployees = employees.Count(e => e.Status == EmployeeStatus.Active),
                    TotalPayrollAmount = payrolls.Sum(p => p.NetSalary),
                    AverageSalary = payrolls.Any() ? payrolls.Average(p => p.NetSalary) : 0
                };

                // Employee details
                reportData.EmployeeDetails = employees.Select(e => new EmployeeDetail
                {
                    FullName = e.FullName,
                    Position = e.Position,
                    BaseSalary = e.BaseSalary,
                    Status = e.Status.ToString(),
                    JoinDate = e.JoinDate,
                    TotalPaid = payrolls.Where(p => p.EmployeeId == e.Id).Sum(p => p.NetSalary)
                }).ToList();

                // Payroll summary by position
                reportData.PayrollByPosition = employees
                    .GroupBy(e => e.Position)
                    .Select(g => new PayrollByPosition
                    {
                        Position = g.Key,
                        EmployeeCount = g.Count(),
                        TotalSalary = payrolls.Where(p => g.Select(e => e.Id).Contains(p.EmployeeId)).Sum(p => p.NetSalary),
                        AverageSalary = g.Average(e => e.BaseSalary)
                    }).ToList();

                _logger.LogInformation("تم إنشاء تقرير الموظفين بنجاح");
                return reportData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير الموظفين");
                throw;
            }
        }

        /// <summary>
        /// إنشاء تقرير المخزون
        /// Generate inventory report
        /// </summary>
        public async Task<InventoryReportData> GenerateInventoryReportAsync()
        {
            try
            {
                _logger.LogInformation("إنشاء تقرير المخزون");

                var inventoryItems = await _unitOfWork.Inventories.GetAllAsync();
                var activeItems = inventoryItems.Where(i => i.Status == InventoryStatus.Active);

                var reportData = new InventoryReportData
                {
                    ReportDate = DateTime.Now,
                    TotalItems = inventoryItems.Count(),
                    ActiveItems = activeItems.Count(),
                    TotalValue = activeItems.Sum(i => i.Quantity * i.UnitPrice),
                    LowStockItems = activeItems.Count(i => i.Quantity <= i.MinimumStock),
                    ExpiredItems = activeItems.Count(i => i.ExpiryDate.HasValue && i.ExpiryDate.Value < DateTime.Now)
                };

                // Item details
                reportData.ItemDetails = activeItems.Select(i => new InventoryItemDetail
                {
                    ItemName = i.ItemName,
                    ItemType = i.ItemType,
                    Quantity = i.Quantity,
                    Unit = i.Unit,
                    UnitPrice = i.UnitPrice,
                    TotalValue = i.Quantity * i.UnitPrice,
                    MinimumStock = i.MinimumStock,
                    ExpiryDate = i.ExpiryDate,
                    Status = i.Status.ToString()
                }).ToList();

                // Category breakdown
                reportData.CategoryBreakdown = activeItems
                    .GroupBy(i => i.ItemType)
                    .Select(g => new InventoryCategory
                    {
                        Category = g.Key,
                        ItemCount = g.Count(),
                        TotalValue = g.Sum(i => i.Quantity * i.UnitPrice),
                        Percentage = reportData.TotalValue > 0 
                            ? (g.Sum(i => i.Quantity * i.UnitPrice) / reportData.TotalValue) * 100 
                            : 0
                    }).ToList();

                _logger.LogInformation("تم إنشاء تقرير المخزون بنجاح");
                return reportData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير المخزون");
                throw;
            }
        }

        /// <summary>
        /// تصدير التقرير إلى ملف
        /// Export report to file
        /// </summary>
        public async Task<bool> ExportReportAsync<T>(T reportData, string filePath, ReportFormat format, string title = "تقرير")
        {
            try
            {
                _logger.LogInformation("تصدير التقرير إلى {FilePath} بتنسيق {Format}", filePath, format);

                var dataTable = ConvertToDataTable(reportData);
                var summary = ExtractSummary(reportData);

                switch (format)
                {
                    case ReportFormat.Excel:
                        return await _reportExporter.ExportToExcelAsync(dataTable, filePath);
                    
                    case ReportFormat.PDF:
                        return await _reportExporter.ExportToPdfAsync(dataTable, filePath, title);
                    
                    case ReportFormat.CSV:
                        return await _reportExporter.ExportToCsvAsync(dataTable, filePath);
                    
                    case ReportFormat.HTML:
                        return await _reportExporter.CreateCustomHtmlReportAsync(filePath, title, summary, dataTable);
                    
                    default:
                        throw new ArgumentException($"تنسيق غير مدعوم: {format}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير التقرير");
                return false;
            }
        }

        private DataTable ConvertToDataTable<T>(T data)
        {
            var dataTable = new DataTable();
            
            // This is a simplified implementation
            // In a real application, you would use reflection or a library like AutoMapper
            // to convert the object to DataTable based on its properties
            
            if (data is ProductionReportData productionData)
            {
                dataTable.Columns.Add("رقم الحوض");
                dataTable.Columns.Add("عدد الأسماك");
                dataTable.Columns.Add("متوسط الوزن");
                dataTable.Columns.Add("الإنتاج المتوقع");
                dataTable.Columns.Add("الحالة");

                foreach (var pond in productionData.PondDetails)
                {
                    dataTable.Rows.Add(pond.PondNumber, pond.FishCount, pond.AverageWeight, 
                        pond.EstimatedProduction, pond.Status);
                }
            }
            
            return dataTable;
        }

        private Dictionary<string, object> ExtractSummary<T>(T data)
        {
            var summary = new Dictionary<string, object>();
            
            if (data is ProductionReportData productionData)
            {
                summary["إجمالي الأحواض"] = productionData.TotalPonds;
                summary["الأحواض النشطة"] = productionData.ActivePonds;
                summary["إجمالي عدد الأسماك"] = productionData.TotalFishCount;
                summary["متوسط الوزن"] = $"{productionData.AverageWeight:F2} كجم";
                summary["الإنتاج المتوقع"] = $"{productionData.EstimatedProduction:F2} كجم";
                summary["معدل النفوق"] = $"{productionData.MortalityRate:F2}%";
            }
            
            return summary;
        }
    }

    // Report data models
    public class ProductionReportData
    {
        public DateTime ReportDate { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalPonds { get; set; }
        public int ActivePonds { get; set; }
        public int TotalFishCount { get; set; }
        public decimal AverageWeight { get; set; }
        public decimal EstimatedProduction { get; set; }
        public decimal TotalFeedConsumption { get; set; }
        public decimal TotalFeedCost { get; set; }
        public int TotalMortality { get; set; }
        public decimal MortalityRate { get; set; }
        public List<PondProductionDetail> PondDetails { get; set; } = new List<PondProductionDetail>();
    }

    public class PondProductionDetail
    {
        public string PondNumber { get; set; }
        public int FishCount { get; set; }
        public decimal AverageWeight { get; set; }
        public decimal EstimatedProduction { get; set; }
        public string Status { get; set; }
        public decimal FeedConsumption { get; set; }
        public int Mortality { get; set; }
    }

    public class FinancialReportData
    {
        public DateTime ReportDate { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public int TransactionCount { get; set; }
        public List<ExpenseCategory> ExpenseBreakdown { get; set; } = new List<ExpenseCategory>();
        public List<RevenueCategory> RevenueBreakdown { get; set; } = new List<RevenueCategory>();
    }

    public class ExpenseCategory
    {
        public string Category { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
    }

    public class RevenueCategory
    {
        public string Category { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
    }

    public class EmployeeReportData
    {
        public DateTime ReportDate { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalEmployees { get; set; }
        public int ActiveEmployees { get; set; }
        public decimal TotalPayrollAmount { get; set; }
        public decimal AverageSalary { get; set; }
        public List<EmployeeDetail> EmployeeDetails { get; set; } = new List<EmployeeDetail>();
        public List<PayrollByPosition> PayrollByPosition { get; set; } = new List<PayrollByPosition>();
    }

    public class EmployeeDetail
    {
        public string FullName { get; set; }
        public string Position { get; set; }
        public decimal BaseSalary { get; set; }
        public string Status { get; set; }
        public DateTime JoinDate { get; set; }
        public decimal TotalPaid { get; set; }
    }

    public class PayrollByPosition
    {
        public string Position { get; set; }
        public int EmployeeCount { get; set; }
        public decimal TotalSalary { get; set; }
        public decimal AverageSalary { get; set; }
    }

    public class InventoryReportData
    {
        public DateTime ReportDate { get; set; }
        public int TotalItems { get; set; }
        public int ActiveItems { get; set; }
        public decimal TotalValue { get; set; }
        public int LowStockItems { get; set; }
        public int ExpiredItems { get; set; }
        public List<InventoryItemDetail> ItemDetails { get; set; } = new List<InventoryItemDetail>();
        public List<InventoryCategory> CategoryBreakdown { get; set; } = new List<InventoryCategory>();
    }

    public class InventoryItemDetail
    {
        public string ItemName { get; set; }
        public string ItemType { get; set; }
        public decimal Quantity { get; set; }
        public string Unit { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue { get; set; }
        public decimal MinimumStock { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string Status { get; set; }
    }

    public class InventoryCategory
    {
        public string Category { get; set; }
        public int ItemCount { get; set; }
        public decimal TotalValue { get; set; }
        public decimal Percentage { get; set; }
    }

    public enum ReportFormat
    {
        Excel,
        PDF,
        CSV,
        HTML
    }
}
