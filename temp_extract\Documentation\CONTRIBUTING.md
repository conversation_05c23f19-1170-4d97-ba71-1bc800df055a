# دليل المساهمة | Contributing Guide

نرحب بمساهماتكم في تطوير نظام إدارة مزارع الأسماك! هذا الدليل يوضح كيفية المساهمة في المشروع.

We welcome contributions to the Fish Farm Management System! This guide explains how to contribute to the project.

## كيفية المساهمة | How to Contribute

### 1. الإبلاغ عن الأخطاء | Reporting Bugs

قبل الإبلاغ عن خطأ، تأكد من:
- البحث في Issues الموجودة للتأكد من عدم الإبلاغ عن الخطأ مسبقاً
- استخدام أحدث إصدار من النظام
- جمع معلومات كافية عن الخطأ

#### معلومات مطلوبة للإبلاغ عن الأخطاء | Required Bug Report Information
- **وصف الخطأ**: وصف واضح ومختصر للمشكلة
- **خطوات إعادة الإنتاج**: خطوات مفصلة لإعادة إنتاج الخطأ
- **السلوك المتوقع**: ما كان يجب أن يحدث
- **السلوك الفعلي**: ما حدث بالفعل
- **لقطات الشاشة**: إذا كانت مفيدة
- **بيئة النظام**: نظام التشغيل، إصدار .NET، إلخ
- **معلومات إضافية**: أي معلومات أخرى مفيدة

### 2. اقتراح ميزات جديدة | Suggesting Features

لاقتراح ميزة جديدة:
1. تحقق من عدم وجود اقتراح مشابه
2. اشرح الحاجة للميزة
3. وضح كيف ستعمل الميزة
4. قدم أمثلة للاستخدام

### 3. المساهمة في الكود | Code Contributions

#### إعداد بيئة التطوير | Development Environment Setup

```bash
# 1. Fork المشروع على GitHub
# 2. استنساخ Fork الخاص بك
git clone https://github.com/your-username/fish-farm-management.git
cd fish-farm-management

# 3. إضافة المستودع الأصلي كـ upstream
git remote add upstream https://github.com/original-repo/fish-farm-management.git

# 4. إنشاء فرع جديد للميزة
git checkout -b feature/new-feature-name

# 5. تثبيت المتطلبات
dotnet restore
```

#### معايير الكود | Code Standards

##### تسمية المتغيرات والدوال | Naming Conventions
```csharp
// Classes - PascalCase
public class UserManagementService

// Methods - PascalCase
public async Task<User> GetUserByIdAsync(int id)

// Properties - PascalCase
public string FullName { get; set; }

// Private fields - camelCase with underscore
private readonly ILogger _logger;

// Local variables - camelCase
var userName = "admin";

// Constants - PascalCase
public const string DefaultPassword = "123456";
```

##### تعليقات الكود | Code Comments
```csharp
/// <summary>
/// الحصول على المستخدم بواسطة المعرف
/// Get user by ID
/// </summary>
/// <param name="id">معرف المستخدم | User ID</param>
/// <returns>المستخدم أو null | User or null</returns>
public async Task<User?> GetUserByIdAsync(int id)
{
    // تحقق من صحة المعرف
    // Validate ID
    if (id <= 0)
        return null;
        
    return await _context.Users.FindAsync(id);
}
```

##### معالجة الأخطاء | Error Handling
```csharp
public async Task<UserResult> CreateUserAsync(CreateUserRequest request)
{
    try
    {
        // منطق إنشاء المستخدم
        // User creation logic
        
        return new UserResult { Success = true };
    }
    catch (ValidationException ex)
    {
        _logger.LogWarning(ex, "خطأ في التحقق من البيانات: {Message}", ex.Message);
        return new UserResult 
        { 
            Success = false, 
            Message = ex.Message 
        };
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "خطأ في إنشاء المستخدم");
        return new UserResult 
        { 
            Success = false, 
            Message = "حدث خطأ غير متوقع" 
        };
    }
}
```

#### كتابة الاختبارات | Writing Tests

##### اختبارات الوحدة | Unit Tests
```csharp
[Fact]
public async Task GetUserByIdAsync_WithValidId_ShouldReturnUser()
{
    // Arrange
    var userId = 1;
    var expectedUser = new User { Id = userId, Username = "testuser" };
    
    // Act
    var result = await _userService.GetUserByIdAsync(userId);
    
    // Assert
    result.Should().NotBeNull();
    result.Id.Should().Be(userId);
    result.Username.Should().Be("testuser");
}

[Fact]
public async Task GetUserByIdAsync_WithInvalidId_ShouldReturnNull()
{
    // Arrange
    var invalidId = -1;
    
    // Act
    var result = await _userService.GetUserByIdAsync(invalidId);
    
    // Assert
    result.Should().BeNull();
}
```

##### اختبارات التكامل | Integration Tests
```csharp
[Fact]
public async Task CreateUser_EndToEnd_ShouldWork()
{
    // Arrange
    using var scope = _factory.Services.CreateScope();
    var context = scope.ServiceProvider.GetRequiredService<FishFarmDbContext>();
    
    var request = new CreateUserRequest
    {
        Username = "newuser",
        Email = "<EMAIL>",
        Password = "Password123!"
    };
    
    // Act
    var result = await _userService.CreateUserAsync(request);
    
    // Assert
    result.Success.Should().BeTrue();
    
    var userInDb = await context.Users
        .FirstOrDefaultAsync(u => u.Username == "newuser");
    userInDb.Should().NotBeNull();
}
```

### 4. عملية المراجعة | Review Process

#### قبل إرسال Pull Request | Before Submitting PR
1. تأكد من تشغيل جميع الاختبارات
2. تأكد من عدم وجود تحذيرات في البناء
3. اكتب اختبارات للكود الجديد
4. حدث التوثيق إذا لزم الأمر
5. تأكد من اتباع معايير الكود

#### إرسال Pull Request | Submitting PR
```bash
# 1. تحديث الفرع من upstream
git fetch upstream
git rebase upstream/main

# 2. دفع التغييرات
git push origin feature/new-feature-name

# 3. إنشاء Pull Request على GitHub
```

#### معلومات Pull Request | PR Information
- **العنوان**: عنوان واضح ومختصر
- **الوصف**: شرح مفصل للتغييرات
- **نوع التغيير**: ميزة جديدة، إصلاح خطأ، تحسين، إلخ
- **الاختبارات**: وصف الاختبارات المضافة أو المحدثة
- **لقطات الشاشة**: إذا كانت التغييرات تؤثر على الواجهة

### 5. معايير المراجعة | Review Criteria

#### الكود | Code
- ✅ يتبع معايير المشروع
- ✅ مكتوب بوضوح ومفهوم
- ✅ يحتوي على تعليقات مناسبة
- ✅ يعالج الأخطاء بشكل صحيح
- ✅ محسن للأداء

#### الاختبارات | Tests
- ✅ تغطية كافية للكود الجديد
- ✅ اختبارات تمر بنجاح
- ✅ تختبر الحالات الحدية
- ✅ أسماء اختبارات واضحة

#### التوثيق | Documentation
- ✅ التوثيق محدث
- ✅ أمثلة واضحة
- ✅ شرح للميزات الجديدة

### 6. أنواع المساهمات | Types of Contributions

#### الكود | Code
- إصلاح الأخطاء
- ميزات جديدة
- تحسينات الأداء
- إعادة هيكلة الكود

#### التوثيق | Documentation
- تحسين التوثيق الموجود
- إضافة أمثلة جديدة
- ترجمة التوثيق
- إنشاء فيديوهات تعليمية

#### الاختبارات | Testing
- إضافة اختبارات جديدة
- تحسين التغطية
- اختبارات الأداء
- اختبارات الأمان

#### التصميم | Design
- تحسين واجهة المستخدم
- تصميم أيقونات جديدة
- تحسين تجربة المستخدم

### 7. الحصول على المساعدة | Getting Help

إذا كنت بحاجة للمساعدة:
- اطرح سؤالاً في Issues
- راسلنا على البريد الإلكتروني
- انضم لمجتمع المطورين

### 8. قواعد السلوك | Code of Conduct

نتوقع من جميع المساهمين:
- الاحترام المتبادل
- التواصل البناء
- قبول النقد البناء
- التركيز على ما هو أفضل للمجتمع

### 9. الترخيص | License

بمساهمتك في هذا المشروع، توافق على أن مساهماتك ستكون مرخصة تحت نفس ترخيص المشروع (MIT License).

## شكراً لمساهمتكم! | Thank You for Contributing!

مساهماتكم تجعل هذا المشروع أفضل للجميع. نقدر وقتكم وجهدكم في تحسين نظام إدارة مزارع الأسماك.

Your contributions make this project better for everyone. We appreciate your time and effort in improving the Fish Farm Management System.
