using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// نوع العلف
    /// Feed type
    /// </summary>
    public class FeedType : BaseEntity
    {
        [Required(ErrorMessage = "اسم العلف مطلوب")]
        [StringLength(100, ErrorMessage = "اسم العلف يجب أن يكون أقل من 100 حرف")]
        public string FeedName { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "اسم الماركة يجب أن يكون أقل من 100 حرف")]
        public string Brand { get; set; } = string.Empty;

        [Range(0, double.MaxValue, ErrorMessage = "سعر الكيلو يجب أن يكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal PricePerKg { get; set; }

        [StringLength(500, ErrorMessage = "المواصفات يجب أن تكون أقل من 500 حرف")]
        public string Specifications { get; set; } = string.Empty;

        [StringLength(20)]
        public string Status { get; set; } = "نشط"; // نشط، متوقف

        // Navigation Properties
        public virtual ICollection<FeedConsumption> FeedConsumptions { get; set; } = new List<FeedConsumption>();

        /// <summary>
        /// حساب إجمالي الكمية المستهلكة من هذا النوع
        /// Calculate total consumed quantity of this feed type
        /// </summary>
        public decimal GetTotalConsumedQuantity()
        {
            return FeedConsumptions.Sum(f => f.Quantity);
        }

        /// <summary>
        /// حساب إجمالي قيمة الاستهلاك
        /// Calculate total consumption value
        /// </summary>
        public decimal GetTotalConsumptionValue()
        {
            return FeedConsumptions.Sum(f => f.Cost);
        }
    }
}
