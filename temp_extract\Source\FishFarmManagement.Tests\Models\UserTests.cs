using Xunit;
using FluentAssertions;
using FishFarmManagement.Models;

namespace FishFarmManagement.Tests.Models
{
    /// <summary>
    /// اختبارات نموذج المستخدم
    /// User model tests
    /// </summary>
    public class UserTests
    {
        [Fact]
        public void SetPassword_ShouldHashPassword()
        {
            // Arrange
            var user = new User();
            var password = "TestPassword123!";

            // Act
            user.SetPassword(password);

            // Assert
            user.PasswordHash.Should().NotBeNullOrEmpty();
            user.PasswordHash.Should().NotBe(password); // Should be hashed, not plain text
        }

        [Fact]
        public void VerifyPassword_WithCorrectPassword_ShouldReturnTrue()
        {
            // Arrange
            var user = new User();
            var password = "TestPassword123!";
            user.SetPassword(password);

            // Act
            var result = user.VerifyPassword(password);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public void VerifyPassword_WithIncorrectPassword_ShouldReturnFalse()
        {
            // Arrange
            var user = new User();
            var correctPassword = "TestPassword123!";
            var incorrectPassword = "WrongPassword";
            user.SetPassword(correctPassword);

            // Act
            var result = user.VerifyPassword(incorrectPassword);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void IsAccountLocked_WithNoLockDate_ShouldReturnFalse()
        {
            // Arrange
            var user = new User();

            // Act
            var result = user.IsAccountLocked();

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void IsAccountLocked_WithFutureLockDate_ShouldReturnTrue()
        {
            // Arrange
            var user = new User
            {
                LockedUntil = DateTime.Now.AddHours(1)
            };

            // Act
            var result = user.IsAccountLocked();

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public void IsAccountLocked_WithPastLockDate_ShouldReturnFalse()
        {
            // Arrange
            var user = new User
            {
                LockedUntil = DateTime.Now.AddHours(-1)
            };

            // Act
            var result = user.IsAccountLocked();

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void RecordFailedLogin_ShouldIncrementFailedAttempts()
        {
            // Arrange
            var user = new User();
            var initialAttempts = user.FailedLoginAttempts;

            // Act
            user.RecordFailedLogin();

            // Assert
            user.FailedLoginAttempts.Should().Be(initialAttempts + 1);
        }

        [Fact]
        public void RecordFailedLogin_WithMaxAttempts_ShouldLockAccount()
        {
            // Arrange
            var user = new User
            {
                FailedLoginAttempts = 2 // One less than max (3)
            };

            // Act
            user.RecordFailedLogin();

            // Assert
            user.FailedLoginAttempts.Should().Be(3);
            user.LockedUntil.Should().NotBeNull();
            user.LockedUntil.Should().BeAfter(DateTime.Now);
        }

        [Fact]
        public void UnlockAccount_ShouldClearLockAndResetAttempts()
        {
            // Arrange
            var user = new User
            {
                FailedLoginAttempts = 5,
                LockedUntil = DateTime.Now.AddHours(1)
            };

            // Act
            user.UnlockAccount();

            // Assert
            user.FailedLoginAttempts.Should().Be(0);
            user.LockedUntil.Should().BeNull();
        }

        [Fact]
        public void SetPassword_WithNullPassword_ShouldThrowException()
        {
            // Arrange
            var user = new User();

            // Act & Assert
            var action = () => user.SetPassword(null!);
            action.Should().Throw<ArgumentNullException>();
        }

        [Fact]
        public void User_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange & Act
            var user = new User();

            // Assert
            user.Status.Should().Be(UserStatus.Active);
            user.FailedLoginAttempts.Should().Be(0);
            user.IsSystemAdmin.Should().BeFalse();
            user.MustChangePassword.Should().BeFalse();
            user.LockedUntil.Should().BeNull();
        }

        [Fact]
        public void User_WithValidData_ShouldSetPropertiesCorrectly()
        {
            // Arrange & Act
            var user = new User
            {
                Username = "testuser",
                FullName = "Test User",
                Email = "<EMAIL>",
                PhoneNumber = "123456789",
                Status = UserStatus.Active,
                IsSystemAdmin = true
            };

            // Assert
            user.Username.Should().Be("testuser");
            user.FullName.Should().Be("Test User");
            user.Email.Should().Be("<EMAIL>");
            user.PhoneNumber.Should().Be("123456789");
            user.Status.Should().Be(UserStatus.Active);
            user.IsSystemAdmin.Should().BeTrue();
        }

        [Fact]
        public void User_PasswordOperations_ShouldWorkCorrectly()
        {
            // Arrange
            var user = new User();
            var password1 = "Password123!";
            var password2 = "NewPassword456!";

            // Act & Assert - Set first password
            user.SetPassword(password1);
            user.VerifyPassword(password1).Should().BeTrue();
            user.VerifyPassword(password2).Should().BeFalse();

            // Act & Assert - Change password
            user.SetPassword(password2);
            user.VerifyPassword(password1).Should().BeFalse();
            user.VerifyPassword(password2).Should().BeTrue();
        }

        [Fact]
        public void User_AccountLocking_ShouldWorkCorrectly()
        {
            // Arrange
            var user = new User();

            // Initially not locked
            user.IsAccountLocked().Should().BeFalse();

            // Record failed attempts
            for (int i = 0; i < 2; i++)
            {
                user.RecordFailedLogin();
                user.IsAccountLocked().Should().BeFalse();
            }

            // Third attempt should lock the account
            user.RecordFailedLogin();
            user.IsAccountLocked().Should().BeTrue();

            // Unlock should clear everything
            user.UnlockAccount();
            user.IsAccountLocked().Should().BeFalse();
            user.FailedLoginAttempts.Should().Be(0);
        }
    }
}
