2025-07-24 10:49:34.913 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-24 10:49:35.293 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-24 10:49:35.297 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-24 10:49:35.297 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-24 10:49:35.297 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-24 10:49:35.298 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-24 10:49:35.298 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-24 10:49:35.298 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-24 10:49:35.299 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-24 10:49:35.299 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-24 10:49:35.299 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-24 10:49:35.299 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-24 10:49:35.300 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-24 10:49:35.300 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-24 10:49:35.300 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-24 10:49:35.301 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-24 10:49:35.301 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-24 10:49:35.302 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-24 10:49:35.0210038', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-24 10:49:35.0210039', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-24 10:49:35.0210039', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-24 10:49:35.021004', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-24 10:49:35.021004', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-24 10:49:35.302 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-24 10:49:35.0210183', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-24 10:49:35.303 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-24 10:49:35.021012', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-24 10:49:35.021012', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-24 10:49:35.0210121', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-24 10:49:35.0210121', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-24 10:49:35.0210121', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-24 10:49:35.0210122', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-24 10:49:35.0210122', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-24 10:49:35.0210123', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-24 10:49:35.0210123', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-24 10:49:35.0210123', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-24 10:49:35.0210124', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-24 10:49:35.0210124', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-24 10:49:35.0210124', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-24 10:49:35.304 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-24 10:49:35.304 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-24 10:49:35.304 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-24 10:49:35.304 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-24 10:49:35.304 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-24 10:49:35.304 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-24 10:49:35.305 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-24 10:49:35.305 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-24 10:49:35.305 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-24 10:49:35.305 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-24 10:49:35.305 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-24 10:49:35.305 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-24 10:49:35.305 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-24 10:49:35.306 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-24 10:49:35.306 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-24 10:49:35.306 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-24 10:49:35.306 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-24 10:49:35.306 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-24 10:49:35.306 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-24 10:49:35.307 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-24 10:49:35.307 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-24 10:49:35.352 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-24 10:49:35.373 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-24 10:49:35.376 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-24 10:49:35.384 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-24 10:49:35.408 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-24 10:49:35.412 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-24 10:49:36.002 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-07-24 11:21:56.530 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."AccountCode", "a"."AccountName", "a"."AccountNameEn", "a"."AccountTypeId", "a"."Balance", "a"."CreatedDate", "a"."Description", "a"."IsPostable", "a"."Level", "a"."ParentAccountId", "a"."Status", "a"."UpdatedDate"
FROM "Accounts" AS "a"
2025-07-24 11:21:56.652 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__fromDatePicker_Value_Date_0='?' (DbType = DateTime), @__toDatePicker_Value_Date_1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
WHERE "t"."TransactionDate" >= @__fromDatePicker_Value_Date_0 AND "t"."TransactionDate" <= @__toDatePicker_Value_Date_1
2025-07-24 11:22:29.816 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "e"."Id", "e"."Address", "e"."BaseSalary", "e"."BirthDate", "e"."ContactInfo", "e"."CreatedDate", "e"."Email", "e"."FullName", "e"."JoinDate", "e"."LeaveDate", "e"."MaritalStatus", "e"."NationalId", "e"."Nationality", "e"."NumberOfChildren", "e"."Phone", "e"."Position", "e"."ResidenceNumber", "e"."Status", "e"."UpdatedDate"
FROM "Employees" AS "e"
2025-07-24 11:24:39.158 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (Size = 9), @p1='?' (DbType = Decimal), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (DbType = DateTime), @p5='?' (Size = 20), @p6='?' (Size = 14), @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?' (Size = 5), @p10='?' (Size = 10), @p11='?' (Size = 7), @p12='?' (DbType = Int32), @p13='?' (Size = 10), @p14='?' (Size = 5), @p15='?' (Size = 10), @p16='?' (Size = 3), @p17='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Employees" ("Address", "BaseSalary", "BirthDate", "ContactInfo", "CreatedDate", "Email", "FullName", "JoinDate", "LeaveDate", "MaritalStatus", "NationalId", "Nationality", "NumberOfChildren", "Phone", "Position", "ResidenceNumber", "Status", "UpdatedDate")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17)
RETURNING "Id";
2025-07-24 11:24:39.206 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "e"."Id", "e"."Address", "e"."BaseSalary", "e"."BirthDate", "e"."ContactInfo", "e"."CreatedDate", "e"."Email", "e"."FullName", "e"."JoinDate", "e"."LeaveDate", "e"."MaritalStatus", "e"."NationalId", "e"."Nationality", "e"."NumberOfChildren", "e"."Phone", "e"."Position", "e"."ResidenceNumber", "e"."Status", "e"."UpdatedDate"
FROM "Employees" AS "e"
2025-07-24 11:24:47.208 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@___employee_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."AbsenceDays", "p"."Allowances", "p"."BaseSalary", "p"."CreatedDate", "p"."CycleId", "p"."Deductions", "p"."EmployeeId", "p"."Month", "p"."NetSalary", "p"."Notes", "p"."OvertimeHours", "p"."OvertimeRate", "p"."PaymentDate", "p"."PaymentStatus", "p"."UpdatedDate", "p"."WorkingDays", "p"."Year"
FROM "Payrolls" AS "p"
WHERE "p"."EmployeeId" = @___employee_Id_0
2025-07-24 11:25:04.524 +03:00 [ERR] Failed executing DbCommand (17ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Decimal), @p2='?' (DbType = Decimal), @p3='?' (DbType = DateTime), @p4='?' (DbType = Int32), @p5='?' (DbType = Decimal), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (DbType = Decimal), @p9='?', @p10='?' (DbType = Decimal), @p11='?' (DbType = Decimal), @p12='?' (DbType = DateTime), @p13='?' (Size = 4), @p14='?' (DbType = DateTime), @p15='?' (DbType = Int32), @p16='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Payrolls" ("AbsenceDays", "Allowances", "BaseSalary", "CreatedDate", "CycleId", "Deductions", "EmployeeId", "Month", "NetSalary", "Notes", "OvertimeHours", "OvertimeRate", "PaymentDate", "PaymentStatus", "UpdatedDate", "WorkingDays", "Year")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING "Id";
2025-07-24 11:25:04.554 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'FishFarmManagement.DAL.FishFarmDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 19: 'FOREIGN KEY constraint failed'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 19: 'FOREIGN KEY constraint failed'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-24 11:25:04.555 +03:00 [ERR] خطأ في حفظ بيانات الراتب
System.InvalidOperationException: خطأ في حفظ البيانات
 ---> Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 19: 'FOREIGN KEY constraint failed'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.UnitOfWork.SaveChangesAsync() in D:\account pro\fish accounting & management\FishFarmManagement.DAL\UnitOfWork.cs:line 95
   --- End of inner exception stack trace ---
   at FishFarmManagement.DAL.UnitOfWork.SaveChangesAsync() in D:\account pro\fish accounting & management\FishFarmManagement.DAL\UnitOfWork.cs:line 100
   at FishFarmManagement.Forms.PayrollAddEditForm.SaveButton_Click(Object sender, EventArgs e) in D:\account pro\fish accounting & management\FishFarmManagement\Forms\PayrollAddEditForm.cs:line 299
2025-07-24 11:25:27.184 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@___employee_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."AbsenceDays", "p"."Allowances", "p"."BaseSalary", "p"."CreatedDate", "p"."CycleId", "p"."Deductions", "p"."EmployeeId", "p"."Month", "p"."NetSalary", "p"."Notes", "p"."OvertimeHours", "p"."OvertimeRate", "p"."PaymentDate", "p"."PaymentStatus", "p"."UpdatedDate", "p"."WorkingDays", "p"."Year"
FROM "Payrolls" AS "p"
WHERE "p"."EmployeeId" = @___employee_Id_0
2025-07-24 14:32:34.691 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-24 14:32:34.746 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-24 14:32:34.752 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-24 14:32:34.773 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-24 14:32:34.786 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-24 14:32:34.788 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-24 14:32:35.099 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-07-24 14:32:53.071 +03:00 [INF] تم تحميل بيانات المخزون بنجاح
2025-07-24 14:33:56.076 +03:00 [INF] تم تحميل بيانات دليل الحسابات بنجاح
2025-07-24 14:34:47.981 +03:00 [INF] تم تحميل بيانات مراكز التكلفة بنجاح
2025-07-24 14:35:33.376 +03:00 [INF] تم إنشاء تقرير ملخص الإنتاج للفترة من 24/06/2025 إلى 24/07/2025
2025-07-24 14:36:10.308 +03:00 [INF] تم إنشاء قائمة الدخل بنجاح
2025-07-24 14:36:29.953 +03:00 [INF] تم إنشاء تقرير الحضور بنجاح
2025-07-24 14:38:14.246 +03:00 [INF] تم تحميل معلومات المزرعة بنجاح
2025-07-24 14:39:26.982 +03:00 [INF] تم حفظ معلومات المزرعة بنجاح
2025-07-24 14:39:40.962 +03:00 [INF] تم تحميل محتوى دليل المستخدم بنجاح
2025-07-24 14:52:04.764 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-24 14:52:04.869 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-24 14:52:04.875 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-24 14:52:04.904 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-24 14:52:04.938 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-24 14:52:04.941 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-24 14:52:05.511 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-07-24 14:52:14.250 +03:00 [INF] تم تحميل بيانات مراكز التكلفة بنجاح
2025-07-24 23:57:58.395 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 43
