# Fix UIStrings references in MainForm.cs
$file = "FishFarmManagement\Forms\MainForm.cs"

if (Test-Path $file) {
    Write-Host "Fixing UIStrings in $file"
    $content = Get-Content $file -Raw
    
    # Replace UIStrings with Arabic text
    $content = $content -replace 'UIStrings\.AccountingMenu', '"المحاسبة"'
    $content = $content -replace 'UIStrings\.TransactionsMenuItem', '"المعاملات"'
    $content = $content -replace 'UIStrings\.AccountsMenuItem', '"الحسابات"'
    $content = $content -replace 'UIStrings\.ReportsMenuItem', '"التقارير"'
    $content = $content -replace 'UIStrings\.ReportsMenu', '"التقارير"'
    $content = $content -replace 'UIStrings\.IncomeStatementMenuItem', '"قائمة الدخل"'
    $content = $content -replace 'UIStrings\.BalanceSheetMenuItem', '"الميزانية العمومية"'
    $content = $content -replace 'UIStrings\.ProductionReportsMenuItem', '"تقارير الإنتاج"'
    $content = $content -replace 'UIStrings\.ToolsMenu', '"الأدوات"'
    $content = $content -replace 'UIStrings\.SettingsMenuItem', '"الإعدادات"'
    $content = $content -replace 'UIStrings\.BackupMenuItem', '"نسخ احتياطي"'
    $content = $content -replace 'UIStrings\.DatabaseOptimizationMenuItem', '"تحسين قاعدة البيانات"'
    $content = $content -replace 'UIStrings\.HelpMenu', '"المساعدة"'
    $content = $content -replace 'UIStrings\.UserGuideMenuItem', '"دليل المستخدم"'
    $content = $content -replace 'UIStrings\.AboutMenuItem', '"حول البرنامج"'
    
    # Replace Permissions with simple true
    $content = $content -replace 'Permissions\.[A-Za-z_]+', 'true'
    
    Set-Content -Path $file -Value $content -NoNewline
    Write-Host "Fixed UIStrings and Permissions in $file"
}

Write-Host "Done"
