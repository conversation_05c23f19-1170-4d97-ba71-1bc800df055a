using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة الدورات الإنتاجية
    /// Production cycle management service interface
    /// </summary>
    public interface IProductionCycleService : IBusinessService<ProductionCycle, int>
    {
        /// <summary>
        /// الحصول على الدورات النشطة
        /// Get active cycles
        /// </summary>
        Task<IEnumerable<ProductionCycle>> GetActiveCyclesAsync();

        /// <summary>
        /// الحصول على الدورات المكتملة
        /// Get completed cycles
        /// </summary>
        Task<IEnumerable<ProductionCycle>> GetCompletedCyclesAsync();

        /// <summary>
        /// إنشاء دورة إنتاجية جديدة
        /// Create new production cycle
        /// </summary>
        Task<ProductionCycle> CreateCycleAsync(string cycleName, DateTime startDate, 
            DateTime expectedEndDate, decimal budgetAmount = 0, string? notes = null);

        /// <summary>
        /// إنهاء دورة إنتاجية
        /// Complete production cycle
        /// </summary>
        Task<bool> CompleteCycleAsync(int cycleId, DateTime endDate);

        /// <summary>
        /// تمديد دورة إنتاجية
        /// Extend production cycle
        /// </summary>
        Task<bool> ExtendCycleAsync(int cycleId, DateTime newExpectedEndDate, string reason);

        /// <summary>
        /// الحصول على أحواض الدورة
        /// Get cycle ponds
        /// </summary>
        Task<IEnumerable<Pond>> GetCyclePondsAsync(int cycleId);

        /// <summary>
        /// حساب إجمالي تكلفة الدورة
        /// Calculate total cycle cost
        /// </summary>
        Task<decimal> CalculateCycleTotalCostAsync(int cycleId);

        /// <summary>
        /// حساب إجمالي إنتاج الدورة
        /// Calculate total cycle production
        /// </summary>
        Task<decimal> CalculateCycleTotalProductionAsync(int cycleId);

        /// <summary>
        /// حساب ربحية الدورة
        /// Calculate cycle profitability
        /// </summary>
        Task<decimal> CalculateCycleProfitabilityAsync(int cycleId);

        /// <summary>
        /// الحصول على إحصائيات الدورة
        /// Get cycle statistics
        /// </summary>
        Task<CycleStatistics> GetCycleStatisticsAsync(int cycleId);

        /// <summary>
        /// البحث في الدورات
        /// Search cycles
        /// </summary>
        Task<IEnumerable<ProductionCycle>> SearchCyclesAsync(string searchTerm);

        /// <summary>
        /// الحصول على الدورات في فترة زمنية
        /// Get cycles in date range
        /// </summary>
        Task<IEnumerable<ProductionCycle>> GetCyclesByDateRangeAsync(DateTime startDate, DateTime endDate);
    }

}
