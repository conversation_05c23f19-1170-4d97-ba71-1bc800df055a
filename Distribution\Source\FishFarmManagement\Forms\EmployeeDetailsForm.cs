﻿using FishFarmManagement.Models;
using FishFarmManagement.DAL.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج عرض تفاصيل الموظف
    /// Employee details form
    /// </summary>
    public partial class EmployeeDetailsForm : Form
    {
        private readonly Employee _employee;
        private readonly IUnitOfWork _unitOfWork;

        public EmployeeDetailsForm(Employee employee, IUnitOfWork unitOfWork)
        {
            _employee = employee ?? throw new ArgumentNullException(nameof(employee));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = $"تفاصيل الموظف - {_employee.FullName}";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                AutoScroll = true
            };

            int y = 20;
            int spacing = 30;

            // Personal Information
            var personalGroupBox = new GroupBox
            {
                Text = "المعلومات الشخصية",
                Location = new Point(20, y),
                Size = new Size(520, 200),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            int groupY = 25;
            personalGroupBox.Controls.Add(CreateInfoLabel("الاسم الكامل:", _employee.FullName, new Point(20, groupY)));
            groupY += 25;
            personalGroupBox.Controls.Add(CreateInfoLabel("الجنسية:", _employee.Nationality, new Point(20, groupY)));
            groupY += 25;
            personalGroupBox.Controls.Add(CreateInfoLabel("رقم الهوية:", _employee.NationalId, new Point(20, groupY)));
            groupY += 25;
            personalGroupBox.Controls.Add(CreateInfoLabel("تاريخ الميلاد:", _employee.BirthDate?.ToString("yyyy/MM/dd") ?? "غير محدد", new Point(20, groupY)));
            groupY += 25;
            personalGroupBox.Controls.Add(CreateInfoLabel("العمر:", _employee.GetAge().ToString() + " سنة", new Point(20, groupY)));
            groupY += 25;
            personalGroupBox.Controls.Add(CreateInfoLabel("الحالة الاجتماعية:", _employee.MaritalStatus, new Point(20, groupY)));

            panel.Controls.Add(personalGroupBox);
            y += 220;

            // Work Information
            var workGroupBox = new GroupBox
            {
                Text = "معلومات العمل",
                Location = new Point(20, y),
                Size = new Size(520, 150),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            groupY = 25;
            workGroupBox.Controls.Add(CreateInfoLabel("المنصب:", _employee.Position, new Point(20, groupY)));
            groupY += 25;
            workGroupBox.Controls.Add(CreateInfoLabel("تاريخ الانضمام:", _employee.JoinDate.ToString("yyyy/MM/dd"), new Point(20, groupY)));
            groupY += 25;
            workGroupBox.Controls.Add(CreateInfoLabel("سنوات الخدمة:", _employee.GetYearsOfService().ToString() + " سنة", new Point(20, groupY)));
            groupY += 25;
            workGroupBox.Controls.Add(CreateInfoLabel("الراتب الأساسي:", _employee.BaseSalary.ToString("C"), new Point(20, groupY)));
            groupY += 25;
            workGroupBox.Controls.Add(CreateInfoLabel("الحالة:", _employee.Status, new Point(20, groupY)));

            panel.Controls.Add(workGroupBox);
            y += 170;

            // Contact Information
            var contactGroupBox = new GroupBox
            {
                Text = "معلومات الاتصال",
                Location = new Point(20, y),
                Size = new Size(520, 100),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            groupY = 25;
            contactGroupBox.Controls.Add(CreateInfoLabel("الهاتف:", _employee.Phone, new Point(20, groupY)));
            groupY += 25;
            contactGroupBox.Controls.Add(CreateInfoLabel("البريد الإلكتروني:", _employee.Email, new Point(20, groupY)));
            groupY += 25;
            contactGroupBox.Controls.Add(CreateInfoLabel("العنوان:", _employee.Address, new Point(20, groupY)));

            panel.Controls.Add(contactGroupBox);

            this.Controls.Add(panel);
        }

        private Panel CreateInfoLabel(string label, string value, Point location)
        {
            var panel = new Panel
            {
                Location = location,
                Size = new Size(480, 20)
            };

            var labelControl = new Label
            {
                Text = label,
                Location = new Point(350, 0),
                Size = new Size(120, 20),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            var valueControl = new Label
            {
                Text = value,
                Location = new Point(20, 0),
                Size = new Size(320, 20),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 9F),
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            panel.Controls.Add(labelControl);
            panel.Controls.Add(valueControl);

            return panel;
        }

        private void LoadData()
        {
            // Data is loaded in CreateControls method
        }
    }
}

