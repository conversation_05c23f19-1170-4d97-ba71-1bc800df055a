using System.ComponentModel.DataAnnotations;

namespace FishFarmManagement.Models.License
{
    /// <summary>
    /// معلومات الترخيص التجاري
    /// Commercial license information
    /// </summary>
    public class LicenseInfo
    {
        public int Id { get; set; }

        [Required]
        public string LicenseKey { get; set; } = string.Empty;

        [Required]
        public LicenseType Type { get; set; }

        [Required]
        public string CustomerName { get; set; } = string.Empty;

        [Required]
        public string CustomerEmail { get; set; } = string.Empty;

        public string? CompanyName { get; set; }

        [Required]
        public DateTime IssueDate { get; set; }

        [Required]
        public DateTime ExpirationDate { get; set; }

        public int MaxUsers { get; set; } = 1;

        public int MaxPonds { get; set; } = 10;

        public bool IsActive { get; set; } = true;

        public string? HardwareId { get; set; }

        public DateTime? LastActivationDate { get; set; }

        public int ActivationCount { get; set; } = 0;

        public string? Notes { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        /// <summary>
        /// التحقق من صلاحية الترخيص
        /// Check if license is valid
        /// </summary>
        public bool IsValid()
        {
            return IsActive && 
                   ExpirationDate > DateTime.Now && 
                   ActivationCount <= GetMaxActivations();
        }

        /// <summary>
        /// التحقق من صلاحية الترخيص للمزرعة
        /// Check if license is valid for farm
        /// </summary>
        public bool IsValidForFarm(int pondCount, int userCount)
        {
            return IsValid() && 
                   pondCount <= MaxPonds && 
                   userCount <= MaxUsers;
        }

        /// <summary>
        /// الحصول على الحد الأقصى لعدد التفعيلات
        /// Get maximum number of activations
        /// </summary>
        public int GetMaxActivations()
        {
            return Type switch
            {
                LicenseType.Trial => 1,
                LicenseType.Basic => 2,
                LicenseType.Professional => 5,
                LicenseType.Enterprise => 10,
                _ => 1
            };
        }

        /// <summary>
        /// الحصول على اسم نوع الترخيص
        /// Get license type name
        /// </summary>
        public string GetTypeName()
        {
            return Type switch
            {
                LicenseType.Trial => "نسخة تجريبية",
                LicenseType.Basic => "أساسي",
                LicenseType.Professional => "احترافي",
                LicenseType.Enterprise => "مؤسسات",
                _ => "غير محدد"
            };
        }

        /// <summary>
        /// الحصول على الأيام المتبقية
        /// Get remaining days
        /// </summary>
        public int GetRemainingDays()
        {
            var remaining = ExpirationDate - DateTime.Now;
            return Math.Max(0, (int)remaining.TotalDays);
        }

        /// <summary>
        /// التحقق من انتهاء صلاحية الترخيص قريباً
        /// Check if license expires soon
        /// </summary>
        public bool ExpiresSoon(int daysThreshold = 30)
        {
            return GetRemainingDays() <= daysThreshold;
        }
    }

    /// <summary>
    /// أنواع التراخيص
    /// License types
    /// </summary>
    public enum LicenseType
    {
        Trial = 0,        // نسخة تجريبية - 30 يوم
        Basic = 1,        // أساسي - مزرعة صغيرة
        Professional = 2, // احترافي - مزرعة متوسطة
        Enterprise = 3    // مؤسسات - مزرعة كبيرة
    }
} 