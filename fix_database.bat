@echo off
echo ======================================================
echo  إصلاح وإعادة إنشاء قاعدة بيانات نظام إدارة مزرعة الأسماك
echo ======================================================
echo.

REM التأكد من وجود مجلد النسخ الاحتياطية
if not exist "Backups" (
    mkdir "Backups"
    echo تم إنشاء مجلد النسخ الاحتياطية
)

REM عمل نسخة احتياطية من قاعدة البيانات الحالية إذا وجدت
if exist "FishFarmDatabase.db" (
    echo إنشاء نسخة احتياطية من قاعدة البيانات الحالية...
    copy "FishFarmDatabase.db" "Backups\FishFarmDatabase_backup_%date:~-4%%date:~3,2%%date:~0,2%_%time:~0,2%%time:~3,2%.db" >nul
    echo تم إنشاء نسخة احتياطية بنجاح

    echo حذف قاعدة البيانات الحالية...
    del "FishFarmDatabase.db"
    if exist "FishFarmManagementin\Debug
et8.0-windows\FishFarmDatabase.db" (
        del "FishFarmManagementin\Debug
et8.0-windows\FishFarmDatabase.db"
    )
)

REM إنشاء قاعدة البيانات الجديدة
echo إنشاء قاعدة بيانات جديدة باستخدام النص البرمجي SQL...

REM اختيار الطريقة المناسبة لإنشاء قاعدة البيانات
echo اختر طريقة إنشاء قاعدة البيانات:
echo 1. استخدام SQLite3 (يجب أن تكون مثبتة على النظام)
echo 2. إنشاء قاعدة البيانات تلقائياً عند تشغيل التطبيق
echo.
set /p choice="اختر رقم الخيار (1 أو 2): "

if "%choice%"=="1" (
    echo تنفيذ النص البرمجي SQL...
    sqlite3 "FishFarmDatabase.db" < "Database\CreateDatabase.sql"
    echo تم إنشاء قاعدة البيانات بنجاح!

    REM نسخ قاعدة البيانات إلى مجلد التنفيذ
    if not exist "FishFarmManagementin\Debug
et8.0-windows" (
        mkdir "FishFarmManagementin\Debug
et8.0-windows"
    )
    copy "FishFarmDatabase.db" "FishFarmManagementin\Debug
et8.0-windows\FishFarmDatabase.db" >nul
    echo تم نسخ قاعدة البيانات إلى مجلد التنفيذ
) else (
    echo تم اختيار إنشاء قاعدة البيانات تلقائياً عند تشغيل التطبيق

    REM تعديل ملف الإعدادات لاستخدام التهجيرات
    echo تعديل ملف الإعدادات لاستخدام التهجيرات...
    powershell -Command "(Get-Content FishFarmManagementppsettings.json) -replace '"UseManualScript": true', '"UseManualScript": false' -replace '"AutoMigrate": false', '"AutoMigrate": true' | Set-Content FishFarmManagementppsettings.json"
    echo تم تعديل ملف الإعدادات بنجاح
)

echo.
echo ===============================
echo  تم الانتهاء من العملية بنجاح
echo ===============================
echo يمكنك الآن تشغيل التطبيق وسيتم استخدام قاعدة البيانات الجديدة

pause
