using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة التقارير
    /// Report service interface
    /// </summary>
    public interface IReportService
    {
        /// <summary>
        /// تقرير الإنتاج اليومي
        /// Daily production report
        /// </summary>
        Task<DailyProductionReport> GetDailyProductionReportAsync(DateTime date);

        /// <summary>
        /// تقرير الإنتاج الشهري
        /// Monthly production report
        /// </summary>
        Task<MonthlyProductionReport> GetMonthlyProductionReportAsync(int month, int year);

        /// <summary>
        /// تقرير الأحواض
        /// Ponds report
        /// </summary>
        Task<PondsReport> GetPondsReportAsync(int? cycleId = null);

        /// <summary>
        /// تقرير استهلاك العلف
        /// Feed consumption report
        /// </summary>
        Task<FeedConsumptionReport> GetFeedConsumptionReportAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// تقرير نفوق الأسماك
        /// Fish mortality report
        /// </summary>
        Task<FishMortalityReport> GetFishMortalityReportAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// تقرير الرواتب
        /// Payroll report
        /// </summary>
        Task<PayrollReport> GetPayrollReportAsync(int month, int year);

        /// <summary>
        /// تقرير المخزون
        /// Inventory report
        /// </summary>
        Task<InventoryReport> GetInventoryReportAsync();

        /// <summary>
        /// تقرير الربحية
        /// Profitability report
        /// </summary>
        Task<ProfitabilityReport> GetProfitabilityReportAsync(int cycleId);

        /// <summary>
        /// تقرير مقارنة الدورات
        /// Cycles comparison report
        /// </summary>
        Task<CyclesComparisonReport> GetCyclesComparisonReportAsync(List<int> cycleIds);

        /// <summary>
        /// تقرير التدفق النقدي
        /// Cash flow report
        /// </summary>
        Task<CashFlowReport> GetCashFlowReportAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// تصدير تقرير إلى PDF
        /// Export report to PDF
        /// </summary>
        Task<byte[]> ExportReportToPdfAsync<T>(T report, string reportTitle) where T : class;

        /// <summary>
        /// تصدير تقرير إلى Excel
        /// Export report to Excel
        /// </summary>
        Task<byte[]> ExportReportToExcelAsync<T>(T report, string reportTitle) where T : class;
    }

    /// <summary>
    /// تقرير الإنتاج اليومي
    /// Daily production report
    /// </summary>
    public class DailyProductionReport
    {
        public DateTime ReportDate { get; set; }
        public int TotalActivePonds { get; set; }
        public int TotalFishCount { get; set; }
        public decimal TotalBiomass { get; set; }
        public decimal TotalFeedConsumed { get; set; }
        public decimal TotalFeedCost { get; set; }
        public int TotalMortality { get; set; }
        public decimal MortalityRate { get; set; }
        public List<PondDailyData> PondData { get; set; } = new();
    }

    /// <summary>
    /// بيانات الحوض اليومية
    /// Pond daily data
    /// </summary>
    public class PondDailyData
    {
        public int PondId { get; set; }
        public string PondNumber { get; set; } = string.Empty;
        public int FishCount { get; set; }
        public decimal AverageWeight { get; set; }
        public decimal FeedConsumed { get; set; }
        public int Mortality { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// تقرير الإنتاج الشهري
    /// Monthly production report
    /// </summary>
    public class MonthlyProductionReport
    {
        public int Month { get; set; }
        public int Year { get; set; }
        public decimal TotalProduction { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal NetProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public List<DailyProductionReport> DailyReports { get; set; } = new();
    }

    /// <summary>
    /// تقرير الأحواض
    /// Ponds report
    /// </summary>
    public class PondsReport
    {
        public int? CycleId { get; set; }
        public string? CycleName { get; set; }
        public int TotalPonds { get; set; }
        public int ActivePonds { get; set; }
        public int EmptyPonds { get; set; }
        public int MaintenancePonds { get; set; }
        public List<PondSummary> PondSummaries { get; set; } = new();
    }

    /// <summary>
    /// ملخص الحوض
    /// Pond summary
    /// </summary>
    public class PondSummary
    {
        public int PondId { get; set; }
        public string PondNumber { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public int FishCount { get; set; }
        public decimal AverageWeight { get; set; }
        public decimal TotalBiomass { get; set; }
        public DateTime StockingDate { get; set; }
        public DateTime? ExpectedHarvestDate { get; set; }
        public int DaysInProduction { get; set; }
        public decimal TotalFeedConsumed { get; set; }
        public decimal TotalFeedCost { get; set; }
        public int TotalMortality { get; set; }
        public decimal MortalityRate { get; set; }
        public decimal FCR { get; set; }
    }

    /// <summary>
    /// تقرير استهلاك العلف
    /// Feed consumption report
    /// </summary>
    public class FeedConsumptionReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalFeedConsumed { get; set; }
        public decimal TotalFeedCost { get; set; }
        public decimal AverageDailyConsumption { get; set; }
        public List<FeedTypeConsumption> FeedTypeConsumptions { get; set; } = new();
        public List<PondFeedConsumption> PondConsumptions { get; set; } = new();
    }

    /// <summary>
    /// استهلاك نوع العلف
    /// Feed type consumption
    /// </summary>
    public class FeedTypeConsumption
    {
        public int FeedTypeId { get; set; }
        public string FeedName { get; set; } = string.Empty;
        public decimal TotalQuantity { get; set; }
        public decimal TotalCost { get; set; }
        public decimal AveragePrice { get; set; }
    }

    /// <summary>
    /// استهلاك علف الحوض
    /// Pond feed consumption
    /// </summary>
    public class PondFeedConsumption
    {
        public int PondId { get; set; }
        public string PondNumber { get; set; } = string.Empty;
        public decimal TotalFeedConsumed { get; set; }
        public decimal TotalFeedCost { get; set; }
        public decimal FCR { get; set; }
    }

    /// <summary>
    /// تقرير نفوق الأسماك
    /// Fish mortality report
    /// </summary>
    public class FishMortalityReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalMortality { get; set; }
        public decimal TotalEstimatedLoss { get; set; }
        public decimal AverageMortalityRate { get; set; }
        public List<MortalityCause> MortalityCauses { get; set; } = new();
        public List<PondMortality> PondMortalities { get; set; } = new();
    }

    /// <summary>
    /// سبب النفوق
    /// Mortality cause
    /// </summary>
    public class MortalityCause
    {
        public string Cause { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Percentage { get; set; }
        public decimal EstimatedLoss { get; set; }
    }

    /// <summary>
    /// نفوق الحوض
    /// Pond mortality
    /// </summary>
    public class PondMortality
    {
        public int PondId { get; set; }
        public string PondNumber { get; set; } = string.Empty;
        public int TotalMortality { get; set; }
        public decimal MortalityRate { get; set; }
        public decimal EstimatedLoss { get; set; }
    }

    /// <summary>
    /// تقرير الرواتب
    /// Payroll report
    /// </summary>
    public class PayrollReport
    {
        public int Month { get; set; }
        public int Year { get; set; }
        public int TotalEmployees { get; set; }
        public decimal TotalBaseSalaries { get; set; }
        public decimal TotalAllowances { get; set; }
        public decimal TotalDeductions { get; set; }
        public decimal TotalNetSalaries { get; set; }
        public List<EmployeePayrollSummary> EmployeePayrolls { get; set; } = new();
    }

    /// <summary>
    /// ملخص راتب الموظف
    /// Employee payroll summary
    /// </summary>
    public class EmployeePayrollSummary
    {
        public int EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public decimal BaseSalary { get; set; }
        public decimal Allowances { get; set; }
        public decimal Deductions { get; set; }
        public decimal NetSalary { get; set; }
        public int WorkingDays { get; set; }
        public decimal OvertimeHours { get; set; }
        public string PaymentStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// تقرير المخزون
    /// Inventory report
    /// </summary>
    public class InventoryReport
    {
        public DateTime ReportDate { get; set; }
        public int TotalItems { get; set; }
        public int LowStockItems { get; set; }
        public int ExpiredItems { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public List<InventoryItemSummary> ItemSummaries { get; set; } = new();
    }

    /// <summary>
    /// ملخص صنف المخزون
    /// Inventory item summary
    /// </summary>
    public class InventoryItemSummary
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemType { get; set; } = string.Empty;
        public decimal CurrentQuantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue { get; set; }
        public decimal MinimumStock { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime? ExpiryDate { get; set; }
    }

    /// <summary>
    /// تقرير الربحية
    /// Profitability report
    /// </summary>
    public class ProfitabilityReport
    {
        public int CycleId { get; set; }
        public string CycleName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal NetProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public decimal ROI { get; set; }
        public CostBreakdown CostBreakdown { get; set; } = new();
    }

    /// <summary>
    /// تفصيل التكاليف
    /// Cost breakdown
    /// </summary>
    public class CostBreakdown
    {
        public decimal FeedCosts { get; set; }
        public decimal MedicationCosts { get; set; }
        public decimal SalaryCosts { get; set; }
        public decimal UtilityCosts { get; set; }
        public decimal MaintenanceCosts { get; set; }
        public decimal OtherCosts { get; set; }
    }

    /// <summary>
    /// تقرير مقارنة الدورات
    /// Cycles comparison report
    /// </summary>
    public class CyclesComparisonReport
    {
        public List<CycleComparison> CycleComparisons { get; set; } = new();
        public CycleComparison AveragePerformance { get; set; } = new();
        public CycleComparison BestPerformance { get; set; } = new();
    }

    /// <summary>
    /// مقارنة الدورة
    /// Cycle comparison
    /// </summary>
    public class CycleComparison
    {
        public int CycleId { get; set; }
        public string CycleName { get; set; } = string.Empty;
        public int Duration { get; set; }
        public decimal TotalProduction { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal NetProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public decimal FCR { get; set; }
        public decimal MortalityRate { get; set; }
        public decimal ProductivityPerPond { get; set; }
    }

    /// <summary>
    /// تقرير التدفق النقدي
    /// Cash flow report
    /// </summary>
    public class CashFlowReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal OpeningBalance { get; set; }
        public decimal TotalInflows { get; set; }
        public decimal TotalOutflows { get; set; }
        public decimal ClosingBalance { get; set; }
        public decimal NetCashFlow { get; set; }
        public List<CashFlowItem> Inflows { get; set; } = new();
        public List<CashFlowItem> Outflows { get; set; } = new();
    }

    /// <summary>
    /// عنصر التدفق النقدي
    /// Cash flow item
    /// </summary>
    public class CashFlowItem
    {
        public DateTime Date { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Reference { get; set; } = string.Empty;
    }
}
