{"ConnectionStrings": {"DefaultConnection": "Data Source=FishFarmDatabase.db;Cache=Shared"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "Application": {"Name": "نظام إدارة مزرعة الأسماك", "Version": "1.0.1", "Language": "ar", "Theme": "<PERSON><PERSON><PERSON>", "AutoBackup": {"Enabled": true, "IntervalHours": 24, "BackupPath": "Backups", "MaxBackupFiles": 30, "CompressBackups": true}, "BusinessSettings": {"DefaultFishSellPricePerKg": 15.0, "LowStockWarningDays": 30, "DefaultCurrency": "ريال سعودي", "FiscalYearStartMonth": 1, "DefaultTaxRate": 0.15}, "Performance": {"EnableMonitoring": true, "OptimizeMemory": true, "CacheEnabled": true, "CacheExpirationMinutes": 15}}, "Database": {"Provider": "SQLite", "AutoMigrate": true, "UseManualScript": false, "SeedDefaultData": true, "BackupOnStartup": false, "BackupPath": "Backups", "ConnectionRetryCount": 3, "ConnectionRetryDelay": 5}, "Security": {"RequireAuthentication": true, "SessionTimeoutMinutes": 60, "MaxLoginAttempts": 5}, "Reports": {"DefaultFormat": "PDF", "OutputPath": "Reports", "IncludeLogo": true, "ShowWatermark": false}, "Notifications": {"ShowAlerts": true, "CheckIntervalMinutes": 30, "AlertTypes": {"HighMortality": true, "LowFeedStock": true, "ExpiredMedications": true, "OverdueTasks": true, "LicenseExpiring": true, "SystemHealth": true}}, "UpdateService": {"Enabled": true, "ServerUrl": "https://updates.fishfarm.com/api", "CheckIntervalHours": 24, "AutoDownload": false, "AutoInstall": false}, "License": {"ServerUrl": "https://license.fishfarm.com/api", "CheckIntervalHours": 1, "OfflineGracePeriodDays": 7}}