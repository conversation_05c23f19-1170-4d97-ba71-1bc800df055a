using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.BLL.Services.DTOs;
using FishFarmManagement.DAL.Interfaces;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة إدارة المحتوى
    /// Content management service
    /// </summary>
    public class ContentService : IContentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ContentService> _logger;

        public ContentService(IUnitOfWork unitOfWork, ILogger<ContentService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<ContentIndexDTO?> GetContentIndexAsync()
        {
            try
            {
                // إنشاء فهرس المحتوى الافتراضي
                var contentIndex = new ContentIndexDTO
                {
                    Topics = new List<ContentTopicDTO>
                    {
                        new ContentTopicDTO
                        {
                            Id = "getting-started",
                            Title = "البدء السريع",
                            Order = 1,
                            Children = new List<ContentTopicDTO>
                            {
                                new ContentTopicDTO { Id = "installation", Title = "التثبيت", Order = 1 },
                                new ContentTopicDTO { Id = "first-login", Title = "أول تسجيل دخول", Order = 2 }
                            }
                        },
                        new ContentTopicDTO
                        {
                            Id = "pond-management",
                            Title = "إدارة الأحواض",
                            Order = 2,
                            Children = new List<ContentTopicDTO>
                            {
                                new ContentTopicDTO { Id = "add-pond", Title = "إضافة حوض جديد", Order = 1 },
                                new ContentTopicDTO { Id = "pond-monitoring", Title = "مراقبة الأحواض", Order = 2 }
                            }
                        },
                        new ContentTopicDTO
                        {
                            Id = "accounting",
                            Title = "المحاسبة",
                            Order = 3,
                            Children = new List<ContentTopicDTO>
                            {
                                new ContentTopicDTO { Id = "transactions", Title = "المعاملات المالية", Order = 1 },
                                new ContentTopicDTO { Id = "reports", Title = "التقارير المالية", Order = 2 }
                            }
                        }
                    }
                };

                return await Task.FromResult(contentIndex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على فهرس المحتوى");
                return null;
            }
        }

        public async Task<ContentDTO?> GetTopicContentAsync(string topicId)
        {
            try
            {
                // إنشاء محتوى افتراضي بناءً على معرف الموضوع
                var content = topicId switch
                {
                    "getting-started" => new ContentDTO
                    {
                        Id = topicId,
                        Title = "البدء السريع",
                        Content = "مرحباً بك في نظام إدارة مزرعة الأسماك. هذا الدليل سيساعدك على البدء بسرعة."
                    },
                    "installation" => new ContentDTO
                    {
                        Id = topicId,
                        Title = "التثبيت",
                        Content = "خطوات تثبيت النظام:\n1. تشغيل ملف التثبيت\n2. اتباع التعليمات\n3. إعداد قاعدة البيانات"
                    },
                    "first-login" => new ContentDTO
                    {
                        Id = topicId,
                        Title = "أول تسجيل دخول",
                        Content = "لتسجيل الدخول لأول مرة:\nاسم المستخدم: admin\nكلمة المرور: Admin123!"
                    },
                    _ => new ContentDTO
                    {
                        Id = topicId,
                        Title = "موضوع غير متوفر",
                        Content = "المحتوى غير متوفر حالياً."
                    }
                };

                return await Task.FromResult(content);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على محتوى الموضوع {TopicId}", topicId);
                return null;
            }
        }

        public async Task<IEnumerable<ContentSearchResultDTO>> SearchContentAsync(string searchTerm)
        {
            try
            {
                var results = new List<ContentSearchResultDTO>();

                // بحث بسيط في المحتوى الافتراضي
                if (searchTerm.Contains("حوض") || searchTerm.Contains("pond"))
                {
                    results.Add(new ContentSearchResultDTO
                    {
                        TopicId = "pond-management",
                        Title = "إدارة الأحواض",
                        Excerpt = "دليل شامل لإدارة أحواض الأسماك"
                    });
                }

                if (searchTerm.Contains("محاسبة") || searchTerm.Contains("accounting"))
                {
                    results.Add(new ContentSearchResultDTO
                    {
                        TopicId = "accounting",
                        Title = "المحاسبة",
                        Excerpt = "نظام المحاسبة والتقارير المالية"
                    });
                }

                return await Task.FromResult(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث في المحتوى: {SearchTerm}", searchTerm);
                return Enumerable.Empty<ContentSearchResultDTO>();
            }
        }
    }
}