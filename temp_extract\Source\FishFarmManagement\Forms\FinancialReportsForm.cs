﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.Forms
{
    public partial class FinancialReportsForm : Form
    {
        private readonly ILogger<FinancialReportsForm> _logger;
        private TabControl reportsTabControl;
        private TabPage incomeStatementTab;
        private TabPage balanceSheetTab;
        private TabPage cashFlowTab;
        private TabPage profitabilityTab;
        private TabPage costAnalysisTab;

        public FinancialReportsForm(ILogger<FinancialReportsForm> logger)
        {
            _logger = logger;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "التقارير المالية";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            CreateControls();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(230, 126, 34),
                Padding = new Padding(20, 10, 20, 10)
            };

            var titleLabel = new Label
            {
                Text = "التقارير المالية",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 25)
            };

            headerPanel.Controls.Add(titleLabel);

            // Tab control
            reportsTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            // Create tabs
            CreateIncomeStatementTab();
            CreateBalanceSheetTab();
            CreateCashFlowTab();
            CreateProfitabilityTab();
            CreateCostAnalysisTab();

            reportsTabControl.TabPages.AddRange(new TabPage[] 
            { 
                incomeStatementTab, balanceSheetTab, cashFlowTab, 
                profitabilityTab, costAnalysisTab 
            });

            this.Controls.AddRange(new Control[] { reportsTabControl, headerPanel });
        }

        private void CreateIncomeStatementTab()
        {
            incomeStatementTab = new TabPage("قائمة الدخل");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // Period selection controls
            var controlPanel = new Panel { Height = 60, Dock = DockStyle.Top };
            
            var periodLabel = new Label
            {
                Text = "الفترة:",
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var periodComboBox = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(70, 17),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            periodComboBox.Items.AddRange(new[] { "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة مخصصة" });
            periodComboBox.SelectedIndex = 0;

            var fromDatePicker = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(240, 17),
                Value = DateTime.Now.AddMonths(-1)
            };

            var toDatePicker = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(370, 17),
                Value = DateTime.Now
            };

            var generateButton = new Button
            {
                Text = "إنشاء التقرير",
                Size = new Size(120, 30),
                Location = new Point(500, 15),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            generateButton.Click += (s, e) => GenerateIncomeStatement();

            var exportButton = new Button
            {
                Text = "تصدير",
                Size = new Size(80, 30),
                Location = new Point(630, 15),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            controlPanel.Controls.AddRange(new Control[] 
            { 
                periodLabel, periodComboBox, fromDatePicker, toDatePicker, generateButton, exportButton 
            });

            // Income statement grid
            var incomeGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                ReadOnly = true,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                Font = new Font("Segoe UI", 10F),
                RowHeadersVisible = false
            };

            // Setup income statement columns
            incomeGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "AccountName", HeaderText = "البند", Width = 300 },
                new DataGridViewTextBoxColumn { Name = "CurrentPeriod", HeaderText = "الفترة الحالية", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "PreviousPeriod", HeaderText = "الفترة السابقة", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Variance", HeaderText = "الفرق", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "VariancePercent", HeaderText = "نسبة التغير (%)", Width = 120 }
            });

            // Format currency columns
            incomeGrid.Columns["CurrentPeriod"].DefaultCellStyle.Format = "C2";
            incomeGrid.Columns["PreviousPeriod"].DefaultCellStyle.Format = "C2";
            incomeGrid.Columns["Variance"].DefaultCellStyle.Format = "C2";
            incomeGrid.Columns["VariancePercent"].DefaultCellStyle.Format = "N2";

            panel.Controls.AddRange(new Control[] { incomeGrid, controlPanel });
            incomeStatementTab.Controls.Add(panel);
        }

        private void CreateBalanceSheetTab()
        {
            balanceSheetTab = new TabPage("الميزانية العمومية");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // Date selection controls
            var controlPanel = new Panel { Height = 60, Dock = DockStyle.Top };
            
            var dateLabel = new Label
            {
                Text = "كما في تاريخ:",
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var asOfDatePicker = new DateTimePicker
            {
                Size = new Size(150, 25),
                Location = new Point(100, 17),
                Value = DateTime.Now
            };

            var generateButton = new Button
            {
                Text = "إنشاء الميزانية",
                Size = new Size(120, 30),
                Location = new Point(270, 15),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            controlPanel.Controls.AddRange(new Control[] { dateLabel, asOfDatePicker, generateButton });

            // Balance sheet content
            var contentPanel = new Panel { Dock = DockStyle.Fill };
            
            var placeholderLabel = new Label
            {
                Text = "الميزانية العمومية\n\nالأصول:\n• الأصول المتداولة\n• الأصول الثابتة\n\nالخصوم وحقوق الملكية:\n• الخصوم المتداولة\n• الخصوم طويلة الأجل\n• حقوق الملكية",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            contentPanel.Controls.Add(placeholderLabel);
            panel.Controls.AddRange(new Control[] { contentPanel, controlPanel });
            balanceSheetTab.Controls.Add(panel);
        }

        private void CreateCashFlowTab()
        {
            cashFlowTab = new TabPage("التدفق النقدي");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            
            var placeholderLabel = new Label
            {
                Text = "قائمة التدفق النقدي\n\n• التدفقات النقدية من الأنشطة التشغيلية\n• التدفقات النقدية من الأنشطة الاستثمارية\n• التدفقات النقدية من الأنشطة التمويلية\n• صافي التغير في النقدية",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            panel.Controls.Add(placeholderLabel);
            cashFlowTab.Controls.Add(panel);
        }

        private void CreateProfitabilityTab()
        {
            profitabilityTab = new TabPage("تحليل الربحية");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // KPI cards panel
            var kpiPanel = new Panel { Height = 120, Dock = DockStyle.Top };
            
            // Create KPI cards
            var grossMarginCard = CreateKPICard("هامش الربح الإجمالي", "25.5%", Color.FromArgb(46, 204, 113));
            grossMarginCard.Location = new Point(20, 20);

            var netMarginCard = CreateKPICard("هامش الربح الصافي", "15.2%", Color.FromArgb(52, 152, 219));
            netMarginCard.Location = new Point(220, 20);

            var roiCard = CreateKPICard("العائد على الاستثمار", "18.7%", Color.FromArgb(155, 89, 182));
            roiCard.Location = new Point(420, 20);

            var roeCard = CreateKPICard("العائد على حقوق الملكية", "22.3%", Color.FromArgb(230, 126, 34));
            roeCard.Location = new Point(620, 20);

            kpiPanel.Controls.AddRange(new Control[] { grossMarginCard, netMarginCard, roiCard, roeCard });

            // Charts panel
            var chartsPanel = new Panel { Dock = DockStyle.Fill };
            
            var chartsLabel = new Label
            {
                Text = "مخططات الربحية\n\n• اتجاه الربحية الشهرية\n• مقارنة الهوامش\n• تحليل نقطة التعادل\n• ربحية المنتجات",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            chartsPanel.Controls.Add(chartsLabel);
            panel.Controls.AddRange(new Control[] { chartsPanel, kpiPanel });
            profitabilityTab.Controls.Add(panel);
        }

        private void CreateCostAnalysisTab()
        {
            costAnalysisTab = new TabPage("تحليل التكاليف");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // Cost breakdown controls
            var controlPanel = new Panel { Height = 60, Dock = DockStyle.Top };
            
            var analysisTypeLabel = new Label
            {
                Text = "نوع التحليل:",
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var analysisTypeComboBox = new ComboBox
            {
                Size = new Size(200, 25),
                Location = new Point(100, 17),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            analysisTypeComboBox.Items.AddRange(new[] { "تكاليف حسب المركز", "تكاليف حسب النشاط", "تكاليف حسب المنتج", "تحليل التكاليف المتغيرة والثابتة" });
            analysisTypeComboBox.SelectedIndex = 0;

            var analyzeButton = new Button
            {
                Text = "تحليل",
                Size = new Size(100, 30),
                Location = new Point(320, 15),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            controlPanel.Controls.AddRange(new Control[] { analysisTypeLabel, analysisTypeComboBox, analyzeButton });

            // Cost analysis content
            var contentPanel = new Panel { Dock = DockStyle.Fill };
            
            var placeholderLabel = new Label
            {
                Text = "تحليل التكاليف\n\n• تكاليف الأعلاف (60%)\n• تكاليف العمالة (20%)\n• تكاليف المرافق (10%)\n• تكاليف أخرى (10%)",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            contentPanel.Controls.Add(placeholderLabel);
            panel.Controls.AddRange(new Control[] { contentPanel, controlPanel });
            costAnalysisTab.Controls.Add(panel);
        }

        private Panel CreateKPICard(string title, string value, Color color)
        {
            var card = new Panel
            {
                Size = new Size(180, 80),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.Gray,
                Location = new Point(10, 10),
                Size = new Size(160, 20)
            };

            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = color,
                Location = new Point(10, 35),
                Size = new Size(160, 30)
            };

            card.Controls.AddRange(new Control[] { titleLabel, valueLabel });
            return card;
        }

        private void GenerateIncomeStatement()
        {
            try
            {
                // Sample income statement data
                var sampleData = new List<dynamic>
                {
                    new { AccountName = "الإيرادات", CurrentPeriod = 500000.00m, PreviousPeriod = 450000.00m, Variance = 50000.00m, VariancePercent = 11.11 },
                    new { AccountName = "  مبيعات الأسماك", CurrentPeriod = 480000.00m, PreviousPeriod = 430000.00m, Variance = 50000.00m, VariancePercent = 11.63 },
                    new { AccountName = "  إيرادات أخرى", CurrentPeriod = 20000.00m, PreviousPeriod = 20000.00m, Variance = 0.00m, VariancePercent = 0.00 },
                    new { AccountName = "تكلفة البضاعة المباعة", CurrentPeriod = 300000.00m, PreviousPeriod = 280000.00m, Variance = 20000.00m, VariancePercent = 7.14 },
                    new { AccountName = "إجمالي الربح", CurrentPeriod = 200000.00m, PreviousPeriod = 170000.00m, Variance = 30000.00m, VariancePercent = 17.65 },
                    new { AccountName = "المصروفات التشغيلية", CurrentPeriod = 120000.00m, PreviousPeriod = 110000.00m, Variance = 10000.00m, VariancePercent = 9.09 },
                    new { AccountName = "  مصروفات الأعلاف", CurrentPeriod = 80000.00m, PreviousPeriod = 75000.00m, Variance = 5000.00m, VariancePercent = 6.67 },
                    new { AccountName = "  مصروفات العمالة", CurrentPeriod = 25000.00m, PreviousPeriod = 22000.00m, Variance = 3000.00m, VariancePercent = 13.64 },
                    new { AccountName = "  مصروفات أخرى", CurrentPeriod = 15000.00m, PreviousPeriod = 13000.00m, Variance = 2000.00m, VariancePercent = 15.38 },
                    new { AccountName = "صافي الربح", CurrentPeriod = 80000.00m, PreviousPeriod = 60000.00m, Variance = 20000.00m, VariancePercent = 33.33 }
                };

                var incomeGrid = (DataGridView)incomeStatementTab.Controls[0].Controls[0];
                incomeGrid.DataSource = sampleData;

                // Apply formatting for totals
                foreach (DataGridViewRow row in incomeGrid.Rows)
                {
                    var accountName = row.Cells["AccountName"].Value?.ToString();
                    if (accountName == "إجمالي الربح" || accountName == "صافي الربح" || accountName == "الإيرادات")
                    {
                        row.DefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                        row.DefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
                    }
                }

                _logger.LogInformation("تم إنشاء قائمة الدخل بنجاح");
                MessageBox.Show("تم إنشاء قائمة الدخل بنجاح", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء قائمة الدخل");
                MessageBox.Show($"خطأ في إنشاء قائمة الدخل: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}

