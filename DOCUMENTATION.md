# التوثيق الشامل لنظام إدارة مزارع الأسماك
## Comprehensive Documentation for Fish Farm Management System

## فهرس المحتويات | Table of Contents

1. [نظرة عامة | Overview](#نظرة-عامة--overview)
2. [دليل التثبيت | Installation Guide](#دليل-التثبيت--installation-guide)
3. [دليل المستخدم | User Guide](#دليل-المستخدم--user-guide)
4. [دليل المطور | Developer Guide](#دليل-المطور--developer-guide)
5. [API Documentation](#api-documentation)
6. [قاعدة البيانات | Database Schema](#قاعدة-البيانات--database-schema)
7. [الأمان | Security](#الأمان--security)
8. [الاختبارات | Testing](#الاختبارات--testing)
9. [النشر | Deployment](#النشر--deployment)
10. [الصيانة | Maintenance](#الصيانة--maintenance)

## نظرة عامة | Overview

### الهدف من النظام | System Purpose
نظام إدارة مزارع الأسماك هو حل متكامل مصمم لتبسيط وأتمتة عمليات إدارة مزارع الأسماك، بما في ذلك:
- إدارة الدورات الإنتاجية
- متابعة الأحواض والأسماك
- إدارة الموظفين والرواتب
- النظام المحاسبي المتكامل
- التقارير والإحصائيات

### المستخدمون المستهدفون | Target Users
- مالكو مزارع الأسماك
- مديرو الإنتاج
- المحاسبون
- موظفو المزرعة
- المشرفون الفنيون

### المتطلبات الوظيفية | Functional Requirements
1. **إدارة الأحواض**: تسجيل ومتابعة حالة الأحواض
2. **إدارة الإنتاج**: تتبع الدورات الإنتاجية والتغذية
3. **إدارة الموظفين**: بيانات الموظفين والرواتب
4. **المحاسبة**: تسجيل المعاملات المالية والتقارير
5. **الأمان**: نظام المستخدمين والصلاحيات

## دليل التثبيت | Installation Guide

### متطلبات النظام | System Requirements

#### الحد الأدنى | Minimum Requirements
- **نظام التشغيل**: Windows 10 (64-bit)
- **المعالج**: Intel Core i3 أو AMD Ryzen 3
- **الذاكرة**: 4 GB RAM
- **التخزين**: 1 GB مساحة فارغة
- **الشاشة**: 1024x768 دقة
- **.NET Runtime**: 8.0 أو أحدث

#### المستحسن | Recommended Requirements
- **نظام التشغيل**: Windows 11 (64-bit)
- **المعالج**: Intel Core i5 أو AMD Ryzen 5
- **الذاكرة**: 8 GB RAM
- **التخزين**: 5 GB مساحة فارغة
- **الشاشة**: 1920x1080 دقة أو أعلى

### خطوات التثبيت | Installation Steps

#### 1. تحميل المتطلبات | Download Prerequisites
```bash
# تحميل .NET 8.0 Runtime
https://dotnet.microsoft.com/download/dotnet/8.0

# تحميل Visual C++ Redistributable (إذا لزم الأمر)
https://aka.ms/vs/17/release/vc_redist.x64.exe
```

#### 2. تحميل النظام | Download System
```bash
# من GitHub
git clone https://github.com/your-repo/fish-farm-management.git

# أو تحميل ZIP
https://github.com/your-repo/fish-farm-management/archive/main.zip
```

#### 3. بناء المشروع | Build Project
```bash
cd fish-farm-management
dotnet restore
dotnet build --configuration Release
```

#### 4. إعداد قاعدة البيانات | Database Setup
```bash
# إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
dotnet run --project FishFarmManagement
```

### التحقق من التثبيت | Installation Verification
1. تشغيل النظام
2. تسجيل الدخول بحساب المدير الافتراضي
3. التحقق من إنشاء قاعدة البيانات
4. اختبار الوظائف الأساسية

## دليل المستخدم | User Guide

### تسجيل الدخول | Login
1. تشغيل النظام
2. إدخال اسم المستخدم وكلمة المرور
3. اختيار "تذكرني" (اختياري)
4. النقر على "تسجيل الدخول"

### الواجهة الرئيسية | Main Interface

#### شريط القوائم | Menu Bar
- **ملف**: النسخ الاحتياطي والاستعادة
- **الإدارة**: إدارة الأحواض والموظفين
- **المحاسبة**: القيود والحسابات
- **التقارير**: جميع أنواع التقارير
- **أدوات**: الإعدادات والتحسين
- **مساعدة**: دليل المستخدم والدعم

#### لوحة المعلومات | Dashboard
- إحصائيات سريعة
- التنبيهات والإشعارات
- الأنشطة الحديثة
- الرسوم البيانية

### إدارة الأحواض | Pond Management

#### إضافة حوض جديد | Add New Pond
1. الذهاب إلى "الإدارة" > "الأحواض"
2. النقر على "إضافة حوض"
3. ملء البيانات المطلوبة:
   - رقم الحوض
   - معرف الدورة الإنتاجية
   - عدد الأسماك
   - متوسط الوزن
   - تاريخ التخزين
4. حفظ البيانات

#### تحديث بيانات الحوض | Update Pond Data
1. اختيار الحوض من القائمة
2. النقر على "تعديل"
3. تحديث البيانات المطلوبة
4. حفظ التغييرات

### إدارة الدورات الإنتاجية | Production Cycle Management

#### إنشاء دورة جديدة | Create New Cycle
1. الذهاب إلى "الإدارة" > "الدورات الإنتاجية"
2. النقر على "دورة جديدة"
3. ملء البيانات:
   - اسم الدورة
   - تاريخ البداية
   - التاريخ المتوقع للانتهاء
   - الميزانية المخصصة
4. حفظ الدورة

### إدارة الموظفين | Employee Management

#### إضافة موظف | Add Employee
1. الذهاب إلى "الإدارة" > "الموظفين"
2. النقر على "موظف جديد"
3. ملء البيانات الشخصية والوظيفية
4. تحديد الراتب والمزايا
5. حفظ البيانات

#### إدارة الرواتب | Payroll Management
1. الذهاب إلى "الإدارة" > "الرواتب"
2. اختيار الشهر والسنة
3. مراجعة رواتب الموظفين
4. إضافة الخصومات أو المكافآت
5. اعتماد الرواتب

### النظام المحاسبي | Accounting System

#### إنشاء قيد محاسبي | Create Journal Entry
1. الذهاب إلى "المحاسبة" > "القيود"
2. النقر على "قيد جديد"
3. اختيار تاريخ القيد
4. إضافة بنود القيد (مدين/دائن)
5. التأكد من توازن القيد
6. حفظ القيد

#### إدارة الحسابات | Account Management
1. الذهاب إلى "المحاسبة" > "دليل الحسابات"
2. عرض شجرة الحسابات
3. إضافة حسابات جديدة
4. تعديل الحسابات الموجودة

### التقارير | Reports

#### تقارير الإنتاج | Production Reports
1. الذهاب إلى "التقارير" > "تقارير الإنتاج"
2. اختيار نوع التقرير
3. تحديد الفترة الزمنية
4. تطبيق المرشحات
5. إنشاء التقرير
6. طباعة أو تصدير

#### التقارير المالية | Financial Reports
1. الذهاب إلى "التقارير" > "التقارير المالية"
2. اختيار نوع التقرير (قائمة دخل، ميزانية، إلخ)
3. تحديد الفترة المحاسبية
4. إنشاء التقرير

## دليل المطور | Developer Guide

### هيكل المشروع | Project Structure

```
FishFarmManagement/
├── FishFarmManagement/              # التطبيق الرئيسي
│   ├── Forms/                       # نوافذ التطبيق
│   ├── Controls/                    # عناصر التحكم المخصصة
│   ├── Helpers/                     # فئات مساعدة
│   └── Program.cs                   # نقطة البداية
├── FishFarmManagement.Models/       # النماذج
│   ├── Entities/                    # كائنات قاعدة البيانات
│   ├── DTOs/                        # كائنات نقل البيانات
│   └── Enums/                       # التعدادات
├── FishFarmManagement.DAL/          # طبقة الوصول للبيانات
│   ├── Repositories/                # المستودعات
│   ├── Interfaces/                  # الواجهات
│   └── FishFarmDbContext.cs         # سياق قاعدة البيانات
├── FishFarmManagement.BLL/          # طبقة منطق الأعمال
│   ├── Services/                    # الخدمات
│   └── Interfaces/                  # واجهات الخدمات
└── FishFarmManagement.Tests/        # الاختبارات
    ├── Unit/                        # اختبارات الوحدة
    └── Integration/                 # اختبارات التكامل
```

### إعداد بيئة التطوير | Development Environment Setup

#### المتطلبات | Requirements
- Visual Studio 2022 أو VS Code
- .NET 8.0 SDK
- Git
- SQL Server Management Studio (اختياري)

#### خطوات الإعداد | Setup Steps
1. استنساخ المشروع
2. فتح الحل في Visual Studio
3. استعادة حزم NuGet
4. بناء الحل
5. تشغيل الاختبارات

### إضافة ميزة جديدة | Adding New Feature

#### 1. إنشاء النموذج | Create Model
```csharp
// في FishFarmManagement.Models
public class NewEntity : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    // خصائص أخرى
}
```

#### 2. إضافة إلى DbContext | Add to DbContext
```csharp
// في FishFarmDbContext.cs
public DbSet<NewEntity> NewEntities { get; set; }
```

#### 3. إنشاء المستودع | Create Repository
```csharp
// في DAL/Interfaces
public interface INewEntityRepository : IRepository<NewEntity>
{
    // دوال إضافية
}

// في DAL/Repositories
public class NewEntityRepository : Repository<NewEntity>, INewEntityRepository
{
    // تنفيذ الدوال
}
```

#### 4. إنشاء الخدمة | Create Service
```csharp
// في BLL/Interfaces
public interface INewEntityService
{
    Task<List<NewEntity>> GetAllAsync();
    // دوال أخرى
}

// في BLL/Services
public class NewEntityService : INewEntityService
{
    // تنفيذ الخدمة
}
```

#### 5. إنشاء النافذة | Create Form
```csharp
// في Forms
public partial class NewEntityForm : Form
{
    private readonly INewEntityService _service;
    
    public NewEntityForm(INewEntityService service)
    {
        _service = service;
        InitializeComponent();
    }
}
```

### أفضل الممارسات | Best Practices

#### كتابة الكود | Code Writing
1. استخدام أسماء واضحة ومعبرة
2. كتابة تعليقات باللغتين العربية والإنجليزية
3. اتباع معايير C# Coding Standards
4. استخدام async/await للعمليات غير المتزامنة

#### إدارة الأخطاء | Error Handling
```csharp
try
{
    // العملية
}
catch (SpecificException ex)
{
    _logger.LogError(ex, "رسالة خطأ محددة");
    // معالجة الخطأ
}
catch (Exception ex)
{
    _logger.LogError(ex, "خطأ عام");
    throw; // إعادة رمي الخطأ إذا لزم الأمر
}
```

#### الاختبارات | Testing
```csharp
[Fact]
public async Task MethodName_Condition_ExpectedResult()
{
    // Arrange
    var service = new Service();
    
    // Act
    var result = await service.Method();
    
    // Assert
    result.Should().NotBeNull();
}
```
