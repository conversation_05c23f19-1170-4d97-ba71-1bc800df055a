using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL;
using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة إدارة التنبيهات والإشعارات
    /// Notification and alert management service
    /// </summary>
    public class NotificationService : INotificationService
    {
        private readonly FishFarmDbContext _context;
        private readonly ILogger<NotificationService> _logger;

        public NotificationService(FishFarmDbContext context, ILogger<NotificationService> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<List<Notification>> GetActiveNotificationsAsync()
        {
            try
            {
                return await _context.Notifications
                    .Where(n => n.Status != NotificationStatus.Archived)
                    .OrderByDescending(n => n.Priority)
                    .ThenByDescending(n => n.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على التنبيهات النشطة");
                return new List<Notification>();
            }
        }

        public async Task<List<Notification>> GetNotificationsByTypeAsync(NotificationType type)
        {
            try
            {
                return await _context.Notifications
                    .Where(n => n.Type == type && n.Status != NotificationStatus.Archived)
                    .OrderByDescending(n => n.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على التنبيهات حسب النوع {Type}", type);
                return new List<Notification>();
            }
        }

        public async Task<List<Notification>> GetNotificationsByPriorityAsync(NotificationPriority priority)
        {
            try
            {
                return await _context.Notifications
                    .Where(n => n.Priority == priority && n.Status != NotificationStatus.Archived)
                    .OrderByDescending(n => n.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على التنبيهات حسب الأولوية {Priority}", priority);
                return new List<Notification>();
            }
        }

        public async Task<bool> CreateNotificationAsync(Notification notification)
        {
            try
            {
                notification.CreatedDate = DateTime.Now;
                notification.Status = NotificationStatus.Pending;

                _context.Notifications.Add(notification);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء تنبيه جديد: {Title}", notification.Title);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء التنبيه: {Title}", notification.Title);
                return false;
            }
        }

        public async Task<bool> UpdateNotificationStatusAsync(int notificationId, NotificationStatus status)
        {
            try
            {
                var notification = await _context.Notifications.FindAsync(notificationId);
                if (notification == null)
                {
                    _logger.LogWarning("التنبيه غير موجود: {Id}", notificationId);
                    return false;
                }

                notification.Status = status;
                if (status == NotificationStatus.Read && notification.ReadAt == null)
                {
                    notification.ReadAt = DateTime.Now;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث حالة التنبيه {Id}", notificationId);
                return false;
            }
        }

        public async Task<bool> MarkAsReadAsync(int notificationId)
        {
            return await UpdateNotificationStatusAsync(notificationId, NotificationStatus.Read);
        }

        public async Task<bool> MarkAllAsReadAsync()
        {
            try
            {
                var unreadNotifications = await _context.Notifications
                    .Where(n => n.Status == NotificationStatus.Pending || n.Status == NotificationStatus.Sent)
                    .ToListAsync();

                foreach (var notification in unreadNotifications)
                {
                    notification.Status = NotificationStatus.Read;
                    notification.ReadAt = DateTime.Now;
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation("تم وضع علامة مقروء على {Count} تنبيه", unreadNotifications.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في وضع علامة مقروء على جميع التنبيهات");
                return false;
            }
        }

        public async Task<bool> DeleteNotificationAsync(int notificationId)
        {
            try
            {
                var notification = await _context.Notifications.FindAsync(notificationId);
                if (notification == null)
                {
                    return false;
                }

                _context.Notifications.Remove(notification);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف التنبيه {Id}", notificationId);
                return false;
            }
        }

        public async Task<bool> DeleteOldNotificationsAsync(DateTime olderThan)
        {
            try
            {
                var oldNotifications = await _context.Notifications
                    .Where(n => n.CreatedDate < olderThan && n.Status == NotificationStatus.Read)
                    .ToListAsync();

                _context.Notifications.RemoveRange(oldNotifications);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم حذف {Count} تنبيه قديم", oldNotifications.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف التنبيهات القديمة");
                return false;
            }
        }

        public async Task CheckAutomaticNotificationsAsync()
        {
            try
            {
                await CheckWaterQualityAlertsAsync();
                await CheckFeedingAlertsAsync();
                await CheckHealthAlertsAsync();
                await CheckInventoryAlertsAsync();
                await CheckProductionCycleAlertsAsync();

                _logger.LogInformation("تم فحص التنبيهات التلقائية");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص التنبيهات التلقائية");
            }
        }

        public async Task CheckWaterQualityAlertsAsync()
        {
            try
            {
                // Note: Update this when WaterQualityRecord model is available
                // var recentTests = await _context.WaterQualityRecords
                //     .Where(w => w.TestDate >= DateTime.Now.AddDays(-1))
                //     .Include(w => w.Pond)
                //     .ToListAsync();
                var recentTests = new List<dynamic>(); // Placeholder

                // TODO: Implement when WaterQualityRecord model is available
                // foreach (var test in recentTests)
                // {
                //     var alerts = new List<string>();
                //     // ... water quality checks
                // }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص تنبيهات جودة المياه");
            }
        }

        public async Task CheckFeedingAlertsAsync()
        {
            try
            {
                var activePonds = await _context.Ponds
                    .Where(p => p.Status == "نشط")
                    .ToListAsync();

                foreach (var pond in activePonds)
                {
                    var lastFeeding = await _context.FeedConsumptions
                        .Where(f => f.PondId == pond.Id)
                        .OrderByDescending(f => f.FeedingDate)
                        .FirstOrDefaultAsync();

                    if (lastFeeding == null || lastFeeding.FeedingDate.Date < DateTime.Now.Date)
                    {
                        await SendImmediateNotificationAsync(
                            "تذكير التغذية",
                            $"لم يتم تسجيل تغذية للحوض {pond.PondNumber} اليوم",
                            NotificationType.Feeding,
                            NotificationPriority.Normal);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص تنبيهات التغذية");
            }
        }

        public async Task CheckHealthAlertsAsync()
        {
            try
            {
                var recentMortalities = await _context.FishMortalities
                    .Where(m => m.MortalityDate >= DateTime.Now.AddDays(-7))
                    .GroupBy(m => m.PondId)
                    .Select(g => new { PondId = g.Key, TotalDeaths = g.Sum(m => m.DeadFishCount) })
                    .ToListAsync();

                foreach (var mortality in recentMortalities)
                {
                    if (mortality.TotalDeaths > 50) // عتبة تحذيرية
                    {
                        var pond = await _context.Ponds.FindAsync(mortality.PondId);
                        await SendImmediateNotificationAsync(
                            "تحذير معدل الوفيات",
                            $"معدل وفيات عالي في الحوض {pond?.PondNumber}: {mortality.TotalDeaths} سمكة في الأسبوع الماضي",
                            NotificationType.Health,
                            NotificationPriority.High);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص تنبيهات الصحة");
            }
        }

        public async Task CheckInventoryAlertsAsync()
        {
            try
            {
                var lowStockItems = await _context.Inventories
                    .Where(i => i.Quantity <= 10) // عتبة تحذيرية مؤقتة
                    .ToListAsync();

                foreach (var item in lowStockItems)
                {
                    await SendImmediateNotificationAsync(
                        "تنبيه نقص المخزون",
                        $"المخزون منخفض للصنف {item.ItemName}: {item.Quantity} {item.Unit}",
                        NotificationType.Inventory,
                        NotificationPriority.Normal);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص تنبيهات المخزون");
            }
        }

        public async Task CheckProductionCycleAlertsAsync()
        {
            try
            {
                var nearEndCycles = await _context.ProductionCycles
                    .Where(c => c.Status == "نشط" && c.ExpectedEndDate <= DateTime.Now.AddDays(7))
                    .ToListAsync();

                foreach (var cycle in nearEndCycles)
                {
                    await SendImmediateNotificationAsync(
                        "تذكير انتهاء الدورة",
                        $"الدورة الإنتاجية {cycle.CycleName} ستنتهي خلال أسبوع",
                        NotificationType.Production,
                        NotificationPriority.Normal);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص تنبيهات الدورات الإنتاجية");
            }
        }

        public async Task<int> GetUnreadCountAsync()
        {
            try
            {
                return await _context.Notifications
                    .CountAsync(n => n.Status == NotificationStatus.Pending || n.Status == NotificationStatus.Sent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على عدد التنبيهات غير المقروءة");
                return 0;
            }
        }

        public async Task<bool> SendImmediateNotificationAsync(string title, string message, NotificationType type, NotificationPriority priority)
        {
            var notification = new Notification
            {
                Title = title,
                Message = message,
                Type = type,
                Priority = priority,
                Status = NotificationStatus.Sent,
                CreatedDate = DateTime.Now,
                IsAutoGenerated = true
            };

            return await CreateNotificationAsync(notification);
        }

        public async Task<bool> ScheduleNotificationAsync(string title, string message, NotificationType type, NotificationPriority priority, DateTime scheduledTime)
        {
            var notification = new Notification
            {
                Title = title,
                Message = message,
                Type = type,
                Priority = priority,
                Status = NotificationStatus.Pending,
                CreatedDate = DateTime.Now,
                ScheduledAt = scheduledTime,
                IsAutoGenerated = false
            };

            return await CreateNotificationAsync(notification);
        }
    }
}
