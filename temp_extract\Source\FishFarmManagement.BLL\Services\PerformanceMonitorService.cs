using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Management;
using System.Timers;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة مراقبة الأداء
    /// Performance monitoring service
    /// </summary>
    public class PerformanceMonitorService
    {
        private readonly ILogger<PerformanceMonitorService> _logger;
        private readonly Timer _monitoringTimer;
        private readonly PerformanceCounter _cpuCounter;
        private readonly PerformanceCounter _memoryCounter;
        private readonly Process _currentProcess;

        public PerformanceMonitorService(ILogger<PerformanceMonitorService> logger)
        {
            _logger = logger;
            _currentProcess = Process.GetCurrentProcess();
            
            // تهيئة عدادات الأداء
            _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
            _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
            
            // تهيئة المراقبة الدورية
            _monitoringTimer = new Timer(60000); // كل دقيقة
            _monitoringTimer.Elapsed += OnMonitoringTimerElapsed;
            _monitoringTimer.AutoReset = true;
            _monitoringTimer.Start();
            
            _logger.LogInformation("تم تهيئة خدمة مراقبة الأداء");
        }

        /// <summary>
        /// الحصول على معلومات الأداء الحالية
        /// Get current performance information
        /// </summary>
        public PerformanceInfo GetCurrentPerformance()
        {
            try
            {
                var performanceInfo = new PerformanceInfo
                {
                    Timestamp = DateTime.Now,
                    CpuUsagePercent = GetCpuUsage(),
                    MemoryUsageMB = GetMemoryUsage(),
                    AvailableMemoryMB = GetAvailableMemory(),
                    ProcessMemoryMB = GetProcessMemoryUsage(),
                    ThreadCount = _currentProcess.Threads.Count,
                    HandleCount = _currentProcess.HandleCount,
                    DatabaseConnections = GetDatabaseConnectionCount(),
                    ResponseTimeMs = MeasureResponseTime()
                };

                return performanceInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معلومات الأداء");
                return new PerformanceInfo { Timestamp = DateTime.Now };
            }
        }

        /// <summary>
        /// الحصول على استخدام المعالج
        /// Get CPU usage
        /// </summary>
        private float GetCpuUsage()
        {
            try
            {
                return _cpuCounter.NextValue();
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// الحصول على استخدام الذاكرة
        /// Get memory usage
        /// </summary>
        private long GetMemoryUsage()
        {
            try
            {
                return GC.GetTotalMemory(false) / (1024 * 1024); // تحويل إلى MB
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// الحصول على الذاكرة المتاحة
        /// Get available memory
        /// </summary>
        private float GetAvailableMemory()
        {
            try
            {
                return _memoryCounter.NextValue();
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// الحصول على استخدام ذاكرة العملية
        /// Get process memory usage
        /// </summary>
        private long GetProcessMemoryUsage()
        {
            try
            {
                _currentProcess.Refresh();
                return _currentProcess.WorkingSet64 / (1024 * 1024); // تحويل إلى MB
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// الحصول على عدد اتصالات قاعدة البيانات
        /// Get database connection count
        /// </summary>
        private int GetDatabaseConnectionCount()
        {
            try
            {
                // هذا يتطلب الوصول لـ DbContext
                // يمكن تنفيذه لاحقاً
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// قياس وقت الاستجابة
        /// Measure response time
        /// </summary>
        private long MeasureResponseTime()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                
                // محاكاة عملية بسيطة لقياس الاستجابة
                Thread.Sleep(1);
                
                stopwatch.Stop();
                return stopwatch.ElapsedMilliseconds;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// التحقق من حالة النظام
        /// Check system health
        /// </summary>
        public SystemHealthStatus CheckSystemHealth()
        {
            try
            {
                var performance = GetCurrentPerformance();
                var status = new SystemHealthStatus
                {
                    Timestamp = DateTime.Now,
                    OverallHealth = HealthLevel.Good
                };

                // تحقق من استخدام المعالج
                if (performance.CpuUsagePercent > 80)
                {
                    status.OverallHealth = HealthLevel.Warning;
                    status.Issues.Add("استخدام المعالج مرتفع");
                }

                // تحقق من استخدام الذاكرة
                if (performance.ProcessMemoryMB > 1000) // أكثر من 1GB
                {
                    status.OverallHealth = HealthLevel.Warning;
                    status.Issues.Add("استخدام الذاكرة مرتفع");
                }

                // تحقق من الذاكرة المتاحة
                if (performance.AvailableMemoryMB < 500) // أقل من 500MB
                {
                    status.OverallHealth = HealthLevel.Critical;
                    status.Issues.Add("الذاكرة المتاحة منخفضة");
                }

                // تحقق من وقت الاستجابة
                if (performance.ResponseTimeMs > 1000)
                {
                    status.OverallHealth = HealthLevel.Warning;
                    status.Issues.Add("وقت الاستجابة بطيء");
                }

                return status;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص حالة النظام");
                return new SystemHealthStatus
                {
                    Timestamp = DateTime.Now,
                    OverallHealth = HealthLevel.Unknown,
                    Issues = { "خطأ في فحص النظام" }
                };
            }
        }

        /// <summary>
        /// تحسين الأداء التلقائي
        /// Automatic performance optimization
        /// </summary>
        public void OptimizePerformance()
        {
            try
            {
                _logger.LogInformation("بدء تحسين الأداء التلقائي");

                // تنظيف الذاكرة
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                // تحسين أولوية العملية
                try
                {
                    _currentProcess.PriorityClass = ProcessPriorityClass.High;
                }
                catch
                {
                    // قد لا تكون متاحة في بعض البيئات
                }

                _logger.LogInformation("تم تحسين الأداء بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين الأداء");
            }
        }

        /// <summary>
        /// معالج المراقبة الدورية
        /// Periodic monitoring handler
        /// </summary>
        private void OnMonitoringTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            try
            {
                var performance = GetCurrentPerformance();
                var health = CheckSystemHealth();

                // تسجيل معلومات الأداء
                _logger.LogDebug("أداء النظام - CPU: {Cpu}%, Memory: {Memory}MB, Health: {Health}",
                    performance.CpuUsagePercent, performance.ProcessMemoryMB, health.OverallHealth);

                // تحسين تلقائي إذا لزم الأمر
                if (health.OverallHealth == HealthLevel.Warning || health.OverallHealth == HealthLevel.Critical)
                {
                    OptimizePerformance();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في المراقبة الدورية");
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _monitoringTimer?.Stop();
            _monitoringTimer?.Dispose();
            _cpuCounter?.Dispose();
            _memoryCounter?.Dispose();
            _currentProcess?.Dispose();
        }
    }

    /// <summary>
    /// معلومات الأداء
    /// Performance information
    /// </summary>
    public class PerformanceInfo
    {
        public DateTime Timestamp { get; set; }
        public float CpuUsagePercent { get; set; }
        public long MemoryUsageMB { get; set; }
        public float AvailableMemoryMB { get; set; }
        public long ProcessMemoryMB { get; set; }
        public int ThreadCount { get; set; }
        public int HandleCount { get; set; }
        public int DatabaseConnections { get; set; }
        public long ResponseTimeMs { get; set; }
    }

    /// <summary>
    /// حالة صحة النظام
    /// System health status
    /// </summary>
    public class SystemHealthStatus
    {
        public DateTime Timestamp { get; set; }
        public HealthLevel OverallHealth { get; set; }
        public List<string> Issues { get; set; } = new();
    }

    /// <summary>
    /// مستويات الصحة
    /// Health levels
    /// </summary>
    public enum HealthLevel
    {
        Unknown,
        Good,
        Warning,
        Critical
    }
}
