using System.ComponentModel.DataAnnotations;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// معلومات المزرعة الأساسية
    /// Basic farm information
    /// </summary>
    public class FarmInfo : BaseEntity
    {
        [Required(ErrorMessage = "اسم المزرعة مطلوب")]
        [StringLength(200, ErrorMessage = "اسم المزرعة يجب أن يكون أقل من 200 حرف")]
        public string FarmName { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الموقع يجب أن يكون أقل من 500 حرف")]
        public string Location { get; set; } = string.Empty;

        [StringLength(1000, ErrorMessage = "معلومات الاتصال يجب أن تكون أقل من 1000 حرف")]
        public string ContactInfo { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        public string Email { get; set; } = string.Empty;

        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 رقم")]
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// شعار المزرعة كبيانات ثنائية
        /// Farm logo as binary data
        /// </summary>
        public byte[]? Logo { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// Additional notes
        /// </summary>
        [StringLength(2000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 2000 حرف")]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// معرف المشرف على النظام
        /// System supervisor identifier
        /// </summary>
        [StringLength(100)]
        public string SupervisorName { get; set; } = "طارق حسين صالح";

        [EmailAddress]
        [StringLength(100)]
        public string SupervisorEmail { get; set; } = "<EMAIL>";
    }
}
