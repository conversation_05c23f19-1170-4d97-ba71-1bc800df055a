using System.ComponentModel.DataAnnotations;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// ربحية الدورة الإنتاجية
    /// Production cycle profitability
    /// </summary>
    public class CycleProfitability
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// معرف الدورة الإنتاجية
        /// Production cycle ID
        /// </summary>
        public int CycleId { get; set; }

        /// <summary>
        /// الدورة الإنتاجية
        /// Production cycle
        /// </summary>
        public ProductionCycle Cycle { get; set; } = null!;

        /// <summary>
        /// إجمالي الإيرادات
        /// Total revenue
        /// </summary>
        public decimal TotalRevenue { get; set; }

        /// <summary>
        /// إجمالي المصروفات
        /// Total expenses
        /// </summary>
        public decimal TotalExpense { get; set; }

        /// <summary>
        /// صافي الربح
        /// Net profit
        /// </summary>
        public decimal NetProfit => TotalRevenue - TotalExpense;

        /// <summary>
        /// هامش الربح
        /// Profit margin
        /// </summary>
        public decimal ProfitMargin => TotalRevenue > 0 ? (NetProfit / TotalRevenue) * 100 : 0;

        /// <summary>
        /// تكلفة العلف
        /// Feed cost
        /// </summary>
        public decimal FeedCost { get; set; }

        /// <summary>
        /// تكلفة الأدوية
        /// Medication cost
        /// </summary>
        public decimal MedicationCost { get; set; }

        /// <summary>
        /// تكلفة العمالة
        /// Labor cost
        /// </summary>
        public decimal LaborCost { get; set; }

        /// <summary>
        /// تكاليف أخرى
        /// Other costs
        /// </summary>
        public decimal OtherCosts { get; set; }

        /// <summary>
        /// إيرادات البيع
        /// Sales revenue
        /// </summary>
        public decimal SalesRevenue { get; set; }

        /// <summary>
        /// إيرادات أخرى
        /// Other revenue
        /// </summary>
        public decimal OtherRevenue { get; set; }

        /// <summary>
        /// عدد الأسماك المباعة
        /// Number of fish sold
        /// </summary>
        public int FishSold { get; set; }

        /// <summary>
        /// متوسط وزن السمكة
        /// Average fish weight
        /// </summary>
        public decimal AverageFishWeight { get; set; }

        /// <summary>
        /// إجمالي الوزن المباع
        /// Total weight sold
        /// </summary>
        public decimal TotalWeightSold { get; set; }

        /// <summary>
        /// سعر الكيلو
        /// Price per kg
        /// </summary>
        public decimal PricePerKg { get; set; }

        /// <summary>
        /// معدل التحويل الغذائي
        /// Feed conversion ratio
        /// </summary>
        public decimal FeedConversionRatio { get; set; }

        /// <summary>
        /// معدل البقاء
        /// Survival rate
        /// </summary>
        public decimal SurvivalRate { get; set; }

        /// <summary>
        /// تاريخ الحساب
        /// Calculation date
        /// </summary>
        public DateTime CalculationDate { get; set; } = DateTime.Now;

        /// <summary>
        /// ملاحظات
        /// Notes
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// Creation date
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// المستخدم المنشئ
        /// Created by user
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;
    }
}
