using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace FishFarmManagement.DAL.Extensions
{
    /// <summary>
    /// امتدادات للمستودعات
    /// Repository extensions
    /// </summary>
    public static class RepositoryExtensions
    {
        /// <summary>
        /// الحصول على المعاملات المرحلة في فترة زمنية
        /// Get posted transactions in date range
        /// </summary>
        public static async Task<IEnumerable<Transaction>> GetPostedTransactionsInDateRangeAsync(
            this IRepository<Transaction> repository, 
            DateTime startDate, 
            DateTime endDate)
        {
            return await repository.FindAsync(t => 
                t.TransactionDate >= startDate && 
                t.TransactionDate <= endDate && 
                t.Status == "مرحل");
        }

        /// <summary>
        /// الحصول على تفاصيل المعاملات بمعرفات المعاملات
        /// Get transaction details by transaction IDs
        /// </summary>
        public static async Task<IEnumerable<TransactionDetail>> GetByTransactionIdsAsync(
            this IRepository<TransactionDetail> repository, 
            IEnumerable<int> transactionIds)
        {
            return await repository.FindAsync(td => transactionIds.Contains(td.TransactionId));
        }

        /// <summary>
        /// الحصول على تفاصيل المعاملات بمركز التكلفة
        /// Get transaction details by cost center
        /// </summary>
        public static async Task<IEnumerable<TransactionDetail>> GetByCostCenterAsync(
            this IRepository<TransactionDetail> repository,
            int costCenterId)
        {
            return await repository.FindAsync(td => td.CostCenterId == costCenterId);
        }

        /// <summary>
        /// الحصول على الحسابات النشطة
        /// Get active accounts
        /// </summary>
        public static async Task<IEnumerable<Account>> GetActiveAccountsAsync(
            this IRepository<Account> repository)
        {
            return await repository.FindAsync(a => a.Status == "نشط");
        }

        /// <summary>
        /// الحصول على الحسابات بالنوع
        /// Get accounts by type
        /// </summary>
        public static async Task<IEnumerable<Account>> GetAccountsByTypeAsync(
            this IRepository<Account> repository, 
            string accountType)
        {
            return await repository.FindAsync(a => a.AccountType != null && a.AccountType.TypeName == accountType);
        }

        /// <summary>
        /// الحصول على الموظفين النشطين
        /// Get active employees
        /// </summary>
        public static async Task<IEnumerable<Employee>> GetActiveEmployeesAsync(
            this IRepository<Employee> repository)
        {
            return await repository.FindAsync(e => e.Status == "نشط");
        }

        /// <summary>
        /// الحصول على المخزون النشط
        /// Get active inventory
        /// </summary>
        public static async Task<IEnumerable<Inventory>> GetActiveInventoryAsync(
            this IRepository<Inventory> repository)
        {
            return await repository.FindAsync(i => i.Quantity > 0);
        }

        /// <summary>
        /// الحصول على الأحواض النشطة
        /// Get active ponds
        /// </summary>
        public static async Task<IEnumerable<Pond>> GetActivePondsAsync(
            this IRepository<Pond> repository)
        {
            return await repository.FindAsync(p => p.Status == "نشط");
        }

        /// <summary>
        /// الحصول على أنواع العلف النشطة
        /// Get active feed types
        /// </summary>
        public static async Task<IEnumerable<FeedType>> GetActiveFeedTypesAsync(
            this IRepository<FeedType> repository)
        {
            return await repository.FindAsync(f => f.Status == "نشط");
        }

        /// <summary>
        /// الحصول على الأدوية غير المنتهية الصلاحية
        /// Get non-expired medications
        /// </summary>
        public static async Task<IEnumerable<Medication>> GetNonExpiredMedicationsAsync(
            this IRepository<Medication> repository)
        {
            var today = DateTime.Today;
            return await repository.FindAsync(m => !m.ExpiryDate.HasValue || m.ExpiryDate.Value > today);
        }

        /// <summary>
        /// الحصول على الدورات الإنتاجية النشطة
        /// Get active production cycles
        /// </summary>
        public static async Task<IEnumerable<ProductionCycle>> GetActiveProductionCyclesAsync(
            this IRepository<ProductionCycle> repository)
        {
            return await repository.FindAsync(pc => pc.Status == "نشط");
        }

        /// <summary>
        /// البحث في النص
        /// Search in text
        /// </summary>
        public static async Task<IEnumerable<T>> SearchAsync<T>(
            this IRepository<T> repository,
            Func<T, bool> predicate) where T : class
        {
            var allItems = await repository.GetAllAsync();
            return allItems.Where(predicate);
        }

        /// <summary>
        /// الحصول على العناصر مع الترقيم
        /// Get items with pagination
        /// </summary>
        public static async Task<IEnumerable<T>> GetPagedAsync<T>(
            this IRepository<T> repository,
            int pageNumber,
            int pageSize) where T : class
        {
            var allItems = await repository.GetAllAsync();
            return allItems.Skip((pageNumber - 1) * pageSize).Take(pageSize);
        }

        /// <summary>
        /// عد العناصر
        /// Count items
        /// </summary>
        public static async Task<int> CountAsync<T>(
            this IRepository<T> repository,
            Func<T, bool>? predicate = null) where T : class
        {
            var allItems = await repository.GetAllAsync();
            return predicate != null ? allItems.Count(predicate) : allItems.Count();
        }
    }
}
