using System.ComponentModel.DataAnnotations;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// قائمة الدخل
    /// Income Statement
    /// </summary>
    public class IncomeStatement
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// تاريخ البداية
        /// Start date
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// تاريخ النهاية
        /// End date
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// إجمالي الإيرادات
        /// Total revenue
        /// </summary>
        public decimal TotalRevenue { get; set; }

        /// <summary>
        /// إجمالي المصروفات
        /// Total expenses
        /// </summary>
        public decimal TotalExpense { get; set; }

        /// <summary>
        /// صافي الربح
        /// Net profit
        /// </summary>
        public decimal NetProfit => TotalRevenue - TotalExpense;

        /// <summary>
        /// تفاصيل الإيرادات
        /// Revenue details
        /// </summary>
        public List<IncomeStatementItem> RevenueItems { get; set; } = new List<IncomeStatementItem>();

        /// <summary>
        /// تفاصيل المصروفات
        /// Expense details
        /// </summary>
        public List<IncomeStatementItem> ExpenseItems { get; set; } = new List<IncomeStatementItem>();

        /// <summary>
        /// تاريخ الإنشاء
        /// Creation date
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// المستخدم المنشئ
        /// Created by user
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// عنصر قائمة الدخل
    /// Income statement item
    /// </summary>
    public class IncomeStatementItem
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// معرف قائمة الدخل
        /// Income statement ID
        /// </summary>
        public int IncomeStatementId { get; set; }

        /// <summary>
        /// قائمة الدخل
        /// Income statement
        /// </summary>
        public IncomeStatement IncomeStatement { get; set; } = null!;

        /// <summary>
        /// اسم العنصر
        /// Item name
        /// </summary>
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// المبلغ
        /// Amount
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// النوع (إيراد/مصروف)
        /// Type (Revenue/Expense)
        /// </summary>
        public string ItemType { get; set; } = string.Empty;

        /// <summary>
        /// الفئة
        /// Category
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// الوصف
        /// Description
        /// </summary>
        public string? Description { get; set; }
    }
}
