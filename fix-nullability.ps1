# سكريبت إصلاح أخطاء nullability
# Fix nullability errors script

$files = @(
    "FishFarmManagement/Forms/BalanceSheetReportForm.cs",
    "FishFarmManagement/Forms/EmployeeManagementForm.cs",
    "FishFarmManagement/Forms/EnhancedDashboardForm.cs",
    "FishFarmManagement/Forms/WeightRecordForm.cs",
    "FishFarmManagement/Forms/UserGuideForm.cs",
    "FishFarmManagement/Forms/SettingsForm.cs",
    "FishFarmManagement/Forms/ProductionReportForm.cs",
    "FishFarmManagement/Forms/ProductionCycleForm.cs",
    "FishFarmManagement/Forms/ProductionCycleAddEditForm.cs",
    "FishFarmManagement/Forms/PondsReportForm.cs",
    "FishFarmManagement/Forms/PondManagementForm.cs",
    "FishFarmManagement/Forms/PondAddEditForm.cs",
    "FishFarmManagement/Forms/PayrollReportForm.cs",
    "FishFarmManagement/Forms/PayrollManagementForm.cs",
    "FishFarmManagement/Forms/PayrollAddEditForm.cs",
    "FishFarmManagement/Forms/NotificationForm.cs",
    "FishFarmManagement/Forms/MortalityRecordForm.cs",
    "FishFarmManagement/Forms/LicenseActivationForm.cs",
    "FishFarmManagement/Forms/InventoryManagementForm.cs",
    "FishFarmManagement/Forms/IncomeStatementReportForm.cs",
    "FishFarmManagement/Forms/FeedingRecordForm.cs",
    "FishFarmManagement/Forms/WaterQualityRecordForm.cs",
    "FishFarmManagement/Forms/EnhancedInventoryManagementForm.cs",
    "FishFarmManagement/Forms/EnhancedBackupForm.cs",
    "FishFarmManagement/Forms/EnhancedAccountingForm.cs"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Processing: $file"
        
        # قراءة محتوى الملف
        $content = Get-Content $file -Raw
        
        # إصلاح أخطاء nullability للأحداث
        $content = $content -replace 'private.*void.*Click.*object sender', 'private void $&?'
        $content = $content -replace 'private.*async.*void.*Click.*object sender', 'private async void $&?'
        
        # إصلاح أخطاء nullability للخلايا
        $content = $content -replace 'private.*void.*CellDoubleClick.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للفلاتر
        $content = $content -replace 'private.*async.*void.*Filter.*object sender', 'private async void $&?'
        
        # إصلاح أخطاء nullability للحفظ
        $content = $content -replace 'private.*async.*void.*Save.*object sender', 'private async void $&?'
        
        # إصلاح أخطاء nullability للتصدير
        $content = $content -replace 'private.*void.*Export.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للطباعة
        $content = $content -replace 'private.*void.*Print.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للإضافة
        $content = $content -replace 'private.*void.*Add.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للتعديل
        $content = $content -replace 'private.*void.*Edit.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للحذف
        $content = $content -replace 'private.*async.*void.*Delete.*object sender', 'private async void $&?'
        
        # إصلاح أخطاء nullability للتحديث
        $content = $content -replace 'private.*void.*Refresh.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للعرض
        $content = $content -replace 'private.*void.*View.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للتحكم
        $content = $content -replace 'private.*void.*Manage.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للإنشاء
        $content = $content -replace 'private.*async.*void.*Create.*object sender', 'private async void $&?'
        
        # إصلاح أخطاء nullability للاستعادة
        $content = $content -replace 'private.*async.*void.*Restore.*object sender', 'private async void $&?'
        
        # إصلاح أخطاء nullability للتحسين
        $content = $content -replace 'private.*async.*void.*Optimize.*object sender', 'private async void $&?'
        
        # إصلاح أخطاء nullability للتحقق
        $content = $content -replace 'private.*async.*void.*Verify.*object sender', 'private async void $&?'
        
        # إصلاح أخطاء nullability للحفظ
        $content = $content -replace 'private.*async.*void.*Save.*object sender', 'private async void $&?'
        
        # إصلاح أخطاء nullability للتحميل
        $content = $content -replace 'private.*async.*void.*Load.*object sender', 'private async void $&?'
        
        # إصلاح أخطاء nullability للإنشاء
        $content = $content -replace 'private.*void.*Generate.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للعرض
        $content = $content -replace 'private.*void.*Show.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للتحقق
        $content = $content -replace 'private.*void.*Check.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للنسخ الاحتياطي
        $content = $content -replace 'private.*void.*Backup.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للبحث
        $content = $content -replace 'private.*void.*Browse.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للتنفيذ
        $content = $content -replace 'private.*void.*Execute.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للتحكم
        $content = $content -replace 'private.*void.*Control.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للتحكم
        $content = $content -replace 'private.*void.*Manage.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للتحكم
        $content = $content -replace 'private.*void.*Mark.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للتحكم
        $content = $content -replace 'private.*void.*Complete.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للتحكم
        $content = $content -replace 'private.*void.*Record.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للتحكم
        $content = $content -replace 'private.*void.*Format.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للتحكم
        $content = $content -replace 'private.*void.*Activate.*object sender', 'private void $&?'
        
        # إصلاح أخطاء nullability للتحكم
        $content = $content -replace 'private.*void.*Trial.*object sender', 'private void $&?'
        
        # حفظ الملف المعدل
        Set-Content $file $content -Encoding UTF8
        
        Write-Host "Fixed: $file" -ForegroundColor Green
    } else {
        Write-Host "File not found: $file" -ForegroundColor Red
    }
}

Write-Host "All files processed!" -ForegroundColor Green 