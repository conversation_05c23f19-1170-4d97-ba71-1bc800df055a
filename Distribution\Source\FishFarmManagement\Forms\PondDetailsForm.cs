﻿using FishFarmManagement.BLL.Services.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج عرض تفاصيل الحوض
    /// Pond details form
    /// </summary>
    public partial class PondDetailsForm : Form
    {
        private readonly PondStatusReport _statusReport;

        // UI Controls
        private TabControl tabControl;
        private TabPage generalTabPage;
        private TabPage statisticsTabPage;
        private TabPage alertsTabPage;

        public PondDetailsForm(PondStatusReport statusReport)
        {
            _statusReport = statusReport ?? throw new ArgumentNullException(nameof(statusReport));
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = $"تفاصيل الحوض - {_statusReport.PondNumber}";
            this.Size = new Size(700, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            CreateTabControl();
            CreateGeneralTab();
            CreateStatisticsTab();
            CreateAlertsTab();
        }

        private void CreateTabControl()
        {
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            generalTabPage = new TabPage("المعلومات العامة");
            statisticsTabPage = new TabPage("الإحصائيات");
            alertsTabPage = new TabPage("التنبيهات");

            tabControl.TabPages.AddRange(new TabPage[] { generalTabPage, statisticsTabPage, alertsTabPage });
            this.Controls.Add(tabControl);
        }

        private void CreateGeneralTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            int y = 20;
            int spacing = 35;

            // Pond Number
            var pondNumberLabel = CreateLabel("رقم الحوض:", new Point(20, y));
            var pondNumberValue = CreateValueLabel(_statusReport.PondNumber, new Point(150, y));
            y += spacing;

            // Initial Fish Count
            var initialFishLabel = CreateLabel("العدد الأولي للأسماك:", new Point(20, y));
            var initialFishValue = CreateValueLabel(_statusReport.InitialFishCount.ToString("N0"), new Point(150, y));
            y += spacing;

            // Current Fish Count
            var currentFishLabel = CreateLabel("العدد الحالي للأسماك:", new Point(20, y));
            var currentFishValue = CreateValueLabel(_statusReport.CurrentFishCount.ToString("N0"), new Point(150, y));
            y += spacing;

            // Dead Fish Count
            var deadFishLabel = CreateLabel("عدد الأسماك النافقة:", new Point(20, y));
            var deadFishValue = CreateValueLabel(_statusReport.DeadFishCount.ToString("N0"), new Point(150, y));
            y += spacing;

            // Average Weight
            var avgWeightLabel = CreateLabel("متوسط الوزن (كجم):", new Point(20, y));
            var avgWeightValue = CreateValueLabel(_statusReport.AverageWeight.ToString("N3"), new Point(150, y));
            y += spacing;

            // Estimated Total Weight
            var totalWeightLabel = CreateLabel("الوزن الإجمالي المتوقع (كجم):", new Point(20, y));
            var totalWeightValue = CreateValueLabel(_statusReport.EstimatedTotalWeight.ToString("N2"), new Point(150, y));
            y += spacing;

            // Days in Production
            var daysLabel = CreateLabel("أيام الإنتاج:", new Point(20, y));
            var daysValue = CreateValueLabel(_statusReport.DaysInProduction.ToString("N0"), new Point(150, y));
            y += spacing;

            // Expected Harvest Date
            var harvestDateLabel = CreateLabel("تاريخ الحصاد المتوقع:", new Point(20, y));
            var harvestDateValue = CreateValueLabel(
                _statusReport.ExpectedHarvestDate?.ToString("yyyy/MM/dd") ?? "غير محدد", 
                new Point(150, y));
            y += spacing;

            // Ready for Harvest
            var readyLabel = CreateLabel("جاهز للحصاد:", new Point(20, y));
            var readyValue = CreateValueLabel(
                _statusReport.IsReadyForHarvest ? "نعم" : "لا", 
                new Point(150, y));
            if (_statusReport.IsReadyForHarvest)
            {
                readyValue.ForeColor = Color.Green;
                readyValue.Font = new Font(readyValue.Font, FontStyle.Bold);
            }

            panel.Controls.AddRange(new Control[]
            {
                pondNumberLabel, pondNumberValue,
                initialFishLabel, initialFishValue,
                currentFishLabel, currentFishValue,
                deadFishLabel, deadFishValue,
                avgWeightLabel, avgWeightValue,
                totalWeightLabel, totalWeightValue,
                daysLabel, daysValue,
                harvestDateLabel, harvestDateValue,
                readyLabel, readyValue
            });

            generalTabPage.Controls.Add(panel);
        }

        private void CreateStatisticsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            int y = 20;
            int spacing = 35;

            // Mortality Rate
            var mortalityLabel = CreateLabel("معدل النفوق (%):", new Point(20, y));
            var mortalityValue = CreateValueLabel(_statusReport.MortalityRate.ToString("N2"), new Point(150, y));
            if (_statusReport.MortalityRate > 5)
            {
                mortalityValue.ForeColor = Color.Red;
                mortalityValue.Font = new Font(mortalityValue.Font, FontStyle.Bold);
            }
            y += spacing;

            // Total Feed Consumption
            var feedConsumptionLabel = CreateLabel("إجمالي استهلاك العلف (كجم):", new Point(20, y));
            var feedConsumptionValue = CreateValueLabel(_statusReport.TotalFeedConsumption.ToString("N2"), new Point(150, y));
            y += spacing;

            // Total Feed Cost
            var feedCostLabel = CreateLabel("إجمالي تكلفة العلف:", new Point(20, y));
            var feedCostValue = CreateValueLabel(_statusReport.TotalFeedCost.ToString("C"), new Point(150, y));
            y += spacing;

            // Feed Conversion Ratio (FCR)
            var fcr = _statusReport.EstimatedTotalWeight > 0 ? 
                _statusReport.TotalFeedConsumption / _statusReport.EstimatedTotalWeight : 0;
            var fcrLabel = CreateLabel("معدل تحويل العلف (FCR):", new Point(20, y));
            var fcrValue = CreateValueLabel(fcr.ToString("N2"), new Point(150, y));
            y += spacing;

            // Cost per kg
            var costPerKgLabel = CreateLabel("التكلفة لكل كيلو:", new Point(20, y));
            var costPerKg = _statusReport.EstimatedTotalWeight > 0 ? 
                _statusReport.TotalFeedCost / _statusReport.EstimatedTotalWeight : 0;
            var costPerKgValue = CreateValueLabel(costPerKg.ToString("C"), new Point(150, y));
            y += spacing;

            // Survival Rate
            var survivalRate = _statusReport.InitialFishCount > 0 ? 
                (decimal)_statusReport.CurrentFishCount / _statusReport.InitialFishCount * 100 : 0;
            var survivalLabel = CreateLabel("معدل البقاء (%):", new Point(20, y));
            var survivalValue = CreateValueLabel(survivalRate.ToString("N2"), new Point(150, y));
            if (survivalRate >= 95)
            {
                survivalValue.ForeColor = Color.Green;
            }
            else if (survivalRate < 90)
            {
                survivalValue.ForeColor = Color.Red;
            }
            survivalValue.Font = new Font(survivalValue.Font, FontStyle.Bold);

            panel.Controls.AddRange(new Control[]
            {
                mortalityLabel, mortalityValue,
                feedConsumptionLabel, feedConsumptionValue,
                feedCostLabel, feedCostValue,
                fcrLabel, fcrValue,
                costPerKgLabel, costPerKgValue,
                survivalLabel, survivalValue
            });

            statisticsTabPage.Controls.Add(panel);
        }

        private void CreateAlertsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            if (_statusReport.Alerts.Any())
            {
                var alertsListBox = new ListBox
                {
                    Dock = DockStyle.Fill,
                    Font = new Font("Segoe UI", 10F),
                    BackColor = Color.FromArgb(255, 248, 220),
                    ForeColor = Color.DarkRed
                };

                foreach (var alert in _statusReport.Alerts)
                {
                    alertsListBox.Items.Add($"⚠ {alert}");
                }

                panel.Controls.Add(alertsListBox);
            }
            else
            {
                var noAlertsLabel = new Label
                {
                    Text = "لا توجد تنبيهات",
                    Font = new Font("Segoe UI", 12F),
                    ForeColor = Color.Green,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Dock = DockStyle.Fill
                };

                panel.Controls.Add(noAlertsLabel);
            }

            alertsTabPage.Controls.Add(panel);
        }

        private Label CreateLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
        }

        private Label CreateValueLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(200, 23),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 10F),
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };
        }

        private void LoadData()
        {
            // Data is loaded in the CreateXXXTab methods
        }
    }
}

