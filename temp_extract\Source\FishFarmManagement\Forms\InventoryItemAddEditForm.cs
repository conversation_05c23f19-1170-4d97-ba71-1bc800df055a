﻿using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    public partial class InventoryItemAddEditForm : Form
    {
        private readonly ILogger _logger;
        private InventoryItem? _item;
        private bool _isEditMode;

        // Controls
        private TextBox itemCodeTextBox = null!;
        private TextBox itemNameTextBox = null!;
        private TextBox itemNameEnTextBox = null!;
        private ComboBox categoryComboBox = null!;
        private ComboBox unitComboBox = null!;
        private NumericUpDown quantityNumericUpDown = null!;
        private NumericUpDown unitCostNumericUpDown = null!;
        private NumericUpDown reorderLevelNumericUpDown = null!;
        private NumericUpDown maxStockLevelNumericUpDown = null!;
        private ComboBox statusComboBox = null!;
        private TextBox supplierTextBox = null!;
        private DateTimePicker expiryDatePicker = null!;
        private CheckBox hasExpiryDateCheckBox = null!;
        private TextBox locationTextBox = null!;
        private TextBox descriptionTextBox = null!;
        private Button saveButton = null!;
        private Button cancelButton = null!;

        public InventoryItemAddEditForm(ILogger logger)
        {
            _logger = logger;
            InitializeComponent();
        }

        public InventoryItemAddEditForm(ILogger logger, InventoryItem item) : this(logger)
        {
            _item = item;
            _isEditMode = true;
            LoadItemData();
        }

        private void InitializeComponent()
        {
            this.Text = _isEditMode ? "تعديل صنف" : "إضافة صنف جديد";
            this.Size = new Size(600, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(52, 73, 94),
                Padding = new Padding(20, 15, 20, 15)
            };

            var titleLabel = new Label
            {
                Text = _isEditMode ? "تعديل بيانات الصنف" : "إضافة صنف جديد",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 18)
            };

            headerPanel.Controls.Add(titleLabel);

            // Main panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White
            };

            // Create form fields
            int yPos = 20;
            // const int fieldHeight = 35; // غير مستخدم
            const int spacing = 45;

            // Item Code
            var itemCodeLabel = new Label
            {
                Text = "كود الصنف:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            itemCodeTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(200, 23),
                Font = new Font("Segoe UI", 10F)
            };

            yPos += spacing;

            // Item Name (Arabic)
            var itemNameLabel = new Label
            {
                Text = "اسم الصنف:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            itemNameTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(300, 23),
                Font = new Font("Segoe UI", 10F)
            };

            yPos += spacing;

            // Item Name (English)
            var itemNameEnLabel = new Label
            {
                Text = "الاسم بالإنجليزية:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            itemNameEnTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(300, 23),
                Font = new Font("Segoe UI", 10F)
            };

            yPos += spacing;

            // Category
            var categoryLabel = new Label
            {
                Text = "الفئة:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            categoryComboBox = new ComboBox
            {
                Location = new Point(130, yPos),
                Size = new Size(200, 23),
                Font = new Font("Segoe UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            categoryComboBox.Items.AddRange(new[] { "أعلاف", "أدوية", "معدات", "مواد كيميائية", "أخرى" });

            yPos += spacing;

            // Unit
            var unitLabel = new Label
            {
                Text = "الوحدة:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            unitComboBox = new ComboBox
            {
                Location = new Point(130, yPos),
                Size = new Size(150, 23),
                Font = new Font("Segoe UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            unitComboBox.Items.AddRange(new[] { "كيلو", "جرام", "طن", "لتر", "قطعة", "عبوة", "كيس" });

            yPos += spacing;

            // Quantity
            var quantityLabel = new Label
            {
                Text = "الكمية الحالية:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            quantityNumericUpDown = new NumericUpDown
            {
                Location = new Point(130, yPos),
                Size = new Size(150, 23),
                Font = new Font("Segoe UI", 10F),
                DecimalPlaces = 2,
                Maximum = 999999,
                Minimum = 0
            };

            yPos += spacing;

            // Unit Cost
            var unitCostLabel = new Label
            {
                Text = "تكلفة الوحدة:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            unitCostNumericUpDown = new NumericUpDown
            {
                Location = new Point(130, yPos),
                Size = new Size(150, 23),
                Font = new Font("Segoe UI", 10F),
                DecimalPlaces = 2,
                Maximum = 999999,
                Minimum = 0
            };

            yPos += spacing;

            // Reorder Level
            var reorderLevelLabel = new Label
            {
                Text = "حد إعادة الطلب:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            reorderLevelNumericUpDown = new NumericUpDown
            {
                Location = new Point(130, yPos),
                Size = new Size(150, 23),
                Font = new Font("Segoe UI", 10F),
                DecimalPlaces = 2,
                Maximum = 999999,
                Minimum = 0
            };

            yPos += spacing;

            // Max Stock Level
            var maxStockLabel = new Label
            {
                Text = "الحد الأقصى للمخزون:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            maxStockLevelNumericUpDown = new NumericUpDown
            {
                Location = new Point(130, yPos),
                Size = new Size(150, 23),
                Font = new Font("Segoe UI", 10F),
                DecimalPlaces = 2,
                Maximum = 999999,
                Minimum = 0
            };

            yPos += spacing;

            // Status
            var statusLabel = new Label
            {
                Text = "الحالة:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            statusComboBox = new ComboBox
            {
                Location = new Point(130, yPos),
                Size = new Size(150, 23),
                Font = new Font("Segoe UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            statusComboBox.Items.AddRange(new[] { "نشط", "غير نشط", "متوقف" });

            yPos += spacing;

            // Supplier
            var supplierLabel = new Label
            {
                Text = "المورد:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            supplierTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(300, 23),
                Font = new Font("Segoe UI", 10F)
            };

            yPos += spacing;

            // Has Expiry Date
            hasExpiryDateCheckBox = new CheckBox
            {
                Text = "له تاريخ انتهاء صلاحية",
                Location = new Point(20, yPos),
                Size = new Size(200, 23),
                Font = new Font("Segoe UI", 10F)
            };
            hasExpiryDateCheckBox.CheckedChanged += HasExpiryDateCheckBox_CheckedChanged;

            yPos += spacing;

            // Expiry Date
            var expiryDateLabel = new Label
            {
                Text = "تاريخ الانتهاء:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            expiryDatePicker = new DateTimePicker
            {
                Location = new Point(130, yPos),
                Size = new Size(200, 23),
                Font = new Font("Segoe UI", 10F),
                Format = DateTimePickerFormat.Short,
                Enabled = false
            };

            yPos += spacing;

            // Location
            var locationLabel = new Label
            {
                Text = "الموقع:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            locationTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(300, 23),
                Font = new Font("Segoe UI", 10F)
            };

            yPos += spacing;

            // Description
            var descriptionLabel = new Label
            {
                Text = "الوصف:",
                Location = new Point(20, yPos),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10F)
            };

            descriptionTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(300, 60),
                Font = new Font("Segoe UI", 10F),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            yPos += 80;

            // Buttons
            var buttonPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(236, 240, 241),
                Padding = new Padding(20, 10, 20, 10)
            };

            saveButton = new Button
            {
                Text = "حفظ",
                Size = new Size(100, 30),
                Location = new Point(20, 10),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "إلغاء",
                Size = new Size(100, 30),
                Location = new Point(130, 10),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            cancelButton.Click += (s, e) => this.Close();

            buttonPanel.Controls.AddRange(new Control[] { saveButton, cancelButton });

            // Add all controls to main panel
            mainPanel.Controls.AddRange(new Control[] 
            {
                itemCodeLabel, itemCodeTextBox,
                itemNameLabel, itemNameTextBox,
                itemNameEnLabel, itemNameEnTextBox,
                categoryLabel, categoryComboBox,
                unitLabel, unitComboBox,
                quantityLabel, quantityNumericUpDown,
                unitCostLabel, unitCostNumericUpDown,
                reorderLevelLabel, reorderLevelNumericUpDown,
                maxStockLabel, maxStockLevelNumericUpDown,
                statusLabel, statusComboBox,
                supplierLabel, supplierTextBox,
                hasExpiryDateCheckBox,
                expiryDateLabel, expiryDatePicker,
                locationLabel, locationTextBox,
                descriptionLabel, descriptionTextBox
            });

            this.Controls.AddRange(new Control[] { mainPanel, buttonPanel, headerPanel });
        }

        private void HasExpiryDateCheckBox_CheckedChanged(object? sender, EventArgs e)
        {
            expiryDatePicker.Enabled = hasExpiryDateCheckBox.Checked;
        }

        private void LoadItemData()
        {
            if (_item == null) return;

            try
            {
                itemCodeTextBox.Text = _item.ItemCode;
                itemNameTextBox.Text = _item.ItemName;
                itemNameEnTextBox.Text = _item.ItemNameEn ?? "";
                categoryComboBox.Text = _item.Category;
                unitComboBox.Text = _item.Unit;
                quantityNumericUpDown.Value = _item.CurrentQuantity;
                unitCostNumericUpDown.Value = _item.UnitCost;
                reorderLevelNumericUpDown.Value = _item.ReorderLevel;
                maxStockLevelNumericUpDown.Value = _item.MaxStockLevel;
                statusComboBox.Text = _item.Status;
                supplierTextBox.Text = _item.Supplier ?? "";
                locationTextBox.Text = _item.Location ?? "";
                descriptionTextBox.Text = _item.Description ?? "";

                if (_item.ExpiryDate.HasValue)
                {
                    hasExpiryDateCheckBox.Checked = true;
                    expiryDatePicker.Value = _item.ExpiryDate.Value;
                }

                _logger.LogInformation($"تم تحميل بيانات الصنف: {_item.ItemName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل بيانات الصنف");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                // Create or update item
                if (_item == null)
                {
                    _item = new InventoryItem();
                }

                _item.ItemCode = itemCodeTextBox.Text.Trim();
                _item.ItemName = itemNameTextBox.Text.Trim();
                _item.ItemNameEn = itemNameEnTextBox.Text.Trim();
                _item.Category = categoryComboBox.Text;
                _item.Unit = unitComboBox.Text;
                _item.CurrentQuantity = quantityNumericUpDown.Value;
                _item.UnitCost = unitCostNumericUpDown.Value;
                _item.ReorderLevel = reorderLevelNumericUpDown.Value;
                _item.MaxStockLevel = maxStockLevelNumericUpDown.Value;
                _item.Status = statusComboBox.Text;
                _item.Supplier = supplierTextBox.Text.Trim();
                _item.Location = locationTextBox.Text.Trim();
                _item.Description = descriptionTextBox.Text.Trim();

                if (hasExpiryDateCheckBox.Checked)
                {
                    _item.ExpiryDate = expiryDatePicker.Value;
                }
                else
                {
                    _item.ExpiryDate = null;
                }

                _item.LastUpdated = DateTime.Now;

                // TODO: Save to database using service
                
                MessageBox.Show(_isEditMode ? "تم تحديث الصنف بنجاح" : "تم إضافة الصنف بنجاح", 
                    "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();

                _logger.LogInformation($"تم حفظ الصنف: {_item.ItemName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ الصنف");
                MessageBox.Show($"خطأ في الحفظ: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(itemCodeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال كود الصنف", "تحذير", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                itemCodeTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(itemNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الصنف", "تحذير", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                itemNameTextBox.Focus();
                return false;
            }

            if (categoryComboBox.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار فئة الصنف", "تحذير", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                categoryComboBox.Focus();
                return false;
            }

            if (unitComboBox.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار وحدة القياس", "تحذير", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                unitComboBox.Focus();
                return false;
            }

            if (statusComboBox.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار حالة الصنف", "تحذير", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                statusComboBox.Focus();
                return false;
            }

            return true;
        }
    }
}

