using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة إدارة المخزون
    /// Inventory management service
    /// </summary>
    public class InventoryService : IInventoryService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<InventoryService> _logger;

        public InventoryService(IUnitOfWork unitOfWork, ILogger<InventoryService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<Inventory>> GetAllAsync()
        {
            try
            {
                return await _unitOfWork.Inventories.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على جميع أصناف المخزون");
                throw;
            }
        }

        public async Task<Inventory?> GetByIdAsync(int id)
        {
            try
            {
                return await _unitOfWork.Inventories.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على صنف المخزون {ItemId}", id);
                throw;
            }
        }

        public async Task<Inventory> AddAsync(Inventory entity)
        {
            try
            {
                var validationResult = await ValidateAsync(entity);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"بيانات الصنف غير صحيحة: {string.Join(", ", validationResult.Errors)}");
                }

                entity.CreatedDate = DateTime.Now;
                entity.Status = InventoryStatus.Active;

                var result = await _unitOfWork.Inventories.AddAsync(entity);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إضافة صنف جديد للمخزون: {ItemName}", entity.ItemName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة صنف المخزون {ItemName}", entity.ItemName);
                throw;
            }
        }

        public async Task<Inventory> UpdateAsync(Inventory entity)
        {
            try
            {
                var validationResult = await ValidateAsync(entity);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"بيانات الصنف غير صحيحة: {string.Join(", ", validationResult.Errors)}");
                }

                entity.UpdatedDate = DateTime.Now;

                var result = await _unitOfWork.Inventories.UpdateAsync(entity);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث صنف المخزون: {ItemName}", entity.ItemName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث صنف المخزون {ItemId}", entity.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var item = await _unitOfWork.Inventories.GetByIdAsync(id);
                if (item == null)
                {
                    return false;
                }

                await _unitOfWork.Inventories.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم حذف صنف المخزون: {ItemName}", item.ItemName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف صنف المخزون {ItemId}", id);
                throw;
            }
        }

        public async Task<ValidationResult> ValidateAsync(Inventory entity)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(entity.ItemName))
            {
                result.AddError("اسم الصنف مطلوب");
            }

            if (string.IsNullOrWhiteSpace(entity.ItemType))
            {
                result.AddError("نوع الصنف مطلوب");
            }

            if (entity.Quantity < 0)
            {
                result.AddError("الكمية الحالية لا يمكن أن تكون سالبة");
            }

            if (entity.UnitPrice < 0)
            {
                result.AddError("سعر الوحدة لا يمكن أن يكون سالباً");
            }

            if (entity.MinimumStock < 0)
            {
                result.AddError("الحد الأدنى للمخزون لا يمكن أن يكون سالباً");
            }

            // Check for duplicate item name
            var existingItem = await _unitOfWork.Inventories
                .FindAsync(i => i.ItemName == entity.ItemName && i.Id != entity.Id);
            if (existingItem.Any())
            {
                result.AddError("اسم الصنف مستخدم من قبل");
            }

            return result;
        }

        public async Task<IEnumerable<Inventory>> GetActiveItemsAsync()
        {
            try
            {
                return await _unitOfWork.Inventories.FindAsync(i => i.Status == InventoryStatus.Active);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأصناف النشطة");
                throw;
            }
        }

        public async Task<IEnumerable<Inventory>> GetLowStockItemsAsync()
        {
            try
            {
                return await _unitOfWork.Inventories.FindAsync(i =>
                    i.Quantity <= i.MinimumStock && i.Status == InventoryStatus.Active);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأصناف منخفضة المخزون");
                throw;
            }
        }

        public async Task<IEnumerable<Inventory>> GetExpiredItemsAsync()
        {
            try
            {
                return await _unitOfWork.Inventories.FindAsync(i =>
                    i.ExpiryDate.HasValue && i.ExpiryDate.Value < DateTime.Now && i.Status == InventoryStatus.Active);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأصناف منتهية الصلاحية");
                throw;
            }
        }

        public async Task<IEnumerable<Inventory>> GetItemsExpiringSoonAsync(int daysAhead = 30)
        {
            try
            {
                var futureDate = DateTime.Now.AddDays(daysAhead);
                return await _unitOfWork.Inventories.FindAsync(i =>
                    i.ExpiryDate.HasValue &&
                    i.ExpiryDate.Value >= DateTime.Now &&
                    i.ExpiryDate.Value <= futureDate &&
                    i.Status == InventoryStatus.Active);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأصناف قريبة انتهاء الصلاحية");
                throw;
            }
        }

        public async Task<IEnumerable<Inventory>> SearchInventoryAsync(string searchTerm)
        {
            try
            {
                return await _unitOfWork.Inventories.FindAsync(i =>
                    i.ItemName.Contains(searchTerm) ||
                    i.ItemType.Contains(searchTerm) ||
                    (i.Notes != null && i.Notes.Contains(searchTerm)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث في المخزون بالكلمة {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<IEnumerable<Inventory>> GetItemsByTypeAsync(string itemType)
        {
            try
            {
                return await _unitOfWork.Inventories.FindAsync(i => i.ItemType == itemType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأصناف بالنوع {ItemType}", itemType);
                throw;
            }
        }

        public async Task<bool> AddQuantityAsync(int itemId, decimal quantity, decimal unitPrice,
            string? batchNumber = null, DateTime? expiryDate = null, string? supplier = null)
        {
            try
            {
                var item = await _unitOfWork.Inventories.GetByIdAsync(itemId);
                if (item == null)
                {
                    return false;
                }

                var quantityBefore = item.Quantity;

                item.Quantity += quantity;
                item.UnitPrice = unitPrice; // Update with latest price
                item.UpdatedDate = DateTime.Now;

                if (expiryDate.HasValue)
                {
                    item.ExpiryDate = expiryDate;
                }

                if (!string.IsNullOrWhiteSpace(supplier))
                {
                    item.Supplier = supplier;
                }

                // Record inventory movement
                var movement = new InventoryMovement
                {
                    ItemId = itemId,
                    MovementDate = DateTime.Now,
                    MovementType = InventoryMovementType.Purchase,
                    Quantity = quantity,
                    UnitPrice = unitPrice,
                    QuantityBefore = quantityBefore,
                    QuantityAfter = item.Quantity,
                    Reason = "إضافة مخزون",
                    BatchNumber = batchNumber,
                    CreatedBy = "النظام" // TODO: Get from current user context
                };
                movement.CalculateTotalValue();

                await _unitOfWork.Inventories.UpdateAsync(item);
                await _unitOfWork.InventoryMovements.AddAsync(movement);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إضافة كمية {Quantity} من الصنف {ItemName}", quantity, item.ItemName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة كمية للصنف {ItemId}", itemId);
                throw;
            }
        }

        public async Task<bool> ConsumeQuantityAsync(int itemId, decimal quantity, string reason)
        {
            try
            {
                var item = await _unitOfWork.Inventories.GetByIdAsync(itemId);
                if (item == null)
                {
                    return false;
                }

                if (item.Quantity < quantity)
                {
                    throw new InvalidOperationException("الكمية المطلوبة أكبر من المتوفر في المخزون");
                }

                var quantityBefore = item.Quantity;

                item.Quantity -= quantity;
                item.UpdatedDate = DateTime.Now;

                // Record inventory movement
                var movement = new InventoryMovement
                {
                    ItemId = itemId,
                    MovementDate = DateTime.Now,
                    MovementType = InventoryMovementType.Consumption,
                    Quantity = -quantity, // Negative for consumption
                    UnitPrice = item.UnitPrice,
                    QuantityBefore = quantityBefore,
                    QuantityAfter = item.Quantity,
                    Reason = reason,
                    CreatedBy = "النظام" // TODO: Get from current user context
                };
                movement.CalculateTotalValue();

                await _unitOfWork.Inventories.UpdateAsync(item);
                await _unitOfWork.InventoryMovements.AddAsync(movement);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم استهلاك كمية {Quantity} من الصنف {ItemName} - السبب: {Reason}",
                    quantity, item.ItemName, reason);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استهلاك كمية من الصنف {ItemId}", itemId);
                throw;
            }
        }

        public async Task<bool> UpdateItemPriceAsync(int itemId, decimal newPrice)
        {
            try
            {
                var item = await _unitOfWork.Inventories.GetByIdAsync(itemId);
                if (item == null)
                {
                    return false;
                }

                item.UnitPrice = newPrice;
                item.UpdatedDate = DateTime.Now;

                await _unitOfWork.Inventories.UpdateAsync(item);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث سعر الصنف {ItemName} إلى {NewPrice}", item.ItemName, newPrice);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث سعر الصنف {ItemId}", itemId);
                throw;
            }
        }

        public async Task<decimal> CalculateTotalInventoryValueAsync()
        {
            try
            {
                var items = await _unitOfWork.Inventories.FindAsync(i => i.Status == InventoryStatus.Active);
                return items.Sum(i => i.Quantity * i.UnitPrice);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إجمالي قيمة المخزون");
                throw;
            }
        }

        public async Task<decimal> CalculateInventoryValueByTypeAsync(string itemType)
        {
            try
            {
                var items = await _unitOfWork.Inventories.FindAsync(i =>
                    i.ItemType == itemType && i.Status == InventoryStatus.Active);
                return items.Sum(i => i.Quantity * i.UnitPrice);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب قيمة المخزون بالنوع {ItemType}", itemType);
                throw;
            }
        }

        public async Task<PurchaseOrder> CreateAutomaticPurchaseOrderAsync()
        {
            try
            {
                var lowStockItems = await GetLowStockItemsAsync();

                var purchaseOrder = new PurchaseOrder
                {
                    OrderNumber = $"PO-{DateTime.Now:yyyyMMdd}-{DateTime.Now.Ticks}",
                    OrderDate = DateTime.Now,
                    Status = "معلق",
                    Items = new List<PurchaseOrderItem>()
                };

                foreach (var item in lowStockItems)
                {
                    var orderQuantity = Math.Max(item.ReorderPoint - item.Quantity,
                        item.MaximumStock - item.Quantity);

                    purchaseOrder.Items.Add(new PurchaseOrderItem
                    {
                        ItemId = item.Id,
                        ItemName = item.ItemName,
                        Quantity = orderQuantity,
                        UnitPrice = item.UnitPrice,
                        TotalPrice = orderQuantity * item.UnitPrice
                    });
                }

                purchaseOrder.TotalAmount = purchaseOrder.Items.Sum(i => i.TotalPrice);

                _logger.LogInformation("تم إنشاء طلب شراء تلقائي برقم {OrderNumber} بقيمة {TotalAmount}",
                    purchaseOrder.OrderNumber, purchaseOrder.TotalAmount);

                return purchaseOrder;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء طلب شراء تلقائي");
                throw;
            }
        }

        public async Task<IEnumerable<Models.InventoryMovement>> GetInventoryMovementReportAsync(
            DateTime startDate, DateTime endDate, int? itemId = null)
        {
            try
            {
                var movements = await _unitOfWork.InventoryMovements.FindAsync(m =>
                    m.MovementDate >= startDate &&
                    m.MovementDate <= endDate &&
                    (!itemId.HasValue || m.ItemId == itemId.Value));

                // Include item information
                var result = new List<Models.InventoryMovement>();
                foreach (var movement in movements)
                {
                    var item = await _unitOfWork.Inventories.GetByIdAsync(movement.ItemId);
                    if (item != null)
                    {
                        movement.Item = item;
                        result.Add(movement);
                    }
                }

                return result.OrderByDescending(m => m.MovementDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تقرير حركة المخزون");
                throw;
            }
        }

        public async Task<bool> UpdateInventoryLimitsAsync(int itemId, decimal minimumStock,
            decimal maximumStock, decimal reorderPoint)
        {
            try
            {
                var item = await _unitOfWork.Inventories.GetByIdAsync(itemId);
                if (item == null)
                {
                    return false;
                }

                item.MinimumStock = minimumStock;
                item.MaximumStock = maximumStock;
                item.ReorderPoint = reorderPoint;
                item.UpdatedDate = DateTime.Now;

                await _unitOfWork.Inventories.UpdateAsync(item);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث حدود المخزون للصنف {ItemName}", item.ItemName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث حدود المخزون للصنف {ItemId}", itemId);
                throw;
            }
        }

        public async Task<InventoryCountResult> PerformInventoryCountAsync(
            Dictionary<int, decimal> actualQuantities, string countedBy)
        {
            try
            {
                var result = new InventoryCountResult
                {
                    CountDate = DateTime.Now,
                    CountedBy = countedBy,
                    TotalItemsCounted = actualQuantities.Count,
                    Differences = new List<InventoryCountDifference>()
                };

                foreach (var kvp in actualQuantities)
                {
                    var item = await _unitOfWork.Inventories.GetByIdAsync(kvp.Key);
                    if (item != null)
                    {
                        var difference = kvp.Value - item.Quantity;
                        if (Math.Abs(difference) > 0.01m) // Allow for small rounding differences
                        {
                            result.Differences.Add(new InventoryCountDifference
                            {
                                ItemId = item.Id,
                                ItemName = item.ItemName,
                                SystemQuantity = item.Quantity,
                                ActualQuantity = kvp.Value,
                                Difference = difference,
                                ValueDifference = difference * item.UnitPrice
                            });

                            // Record inventory movement for count adjustment
                            var movement = new InventoryMovement
                            {
                                ItemId = item.Id,
                                MovementDate = DateTime.Now,
                                MovementType = InventoryMovementType.Count,
                                Quantity = difference,
                                UnitPrice = item.UnitPrice,
                                QuantityBefore = item.Quantity,
                                QuantityAfter = kvp.Value,
                                Reason = $"تعديل جرد - الفرق: {difference:F3}",
                                CreatedBy = countedBy
                            };
                            movement.CalculateTotalValue();

                            // Update system quantity to match actual count
                            item.Quantity = kvp.Value;
                            item.UpdatedDate = DateTime.Now;

                            await _unitOfWork.Inventories.UpdateAsync(item);
                            await _unitOfWork.InventoryMovements.AddAsync(movement);
                        }
                    }
                }

                result.ItemsWithDifferences = result.Differences.Count;
                result.TotalValueDifference = result.Differences.Sum(d => d.ValueDifference);

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إجراء جرد المخزون بواسطة {CountedBy} - عدد الفروقات: {DifferencesCount}",
                    countedBy, result.ItemsWithDifferences);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إجراء جرد المخزون");
                throw;
            }
        }
    }
}
