using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using FluentAssertions;
using FishFarmManagement.DAL;
using FishFarmManagement.Models;

namespace FishFarmManagement.Tests.Integration
{
    /// <summary>
    /// اختبارات التكامل مع قاعدة البيانات
    /// Database integration tests
    /// </summary>
    public class DatabaseIntegrationTests : IDisposable
    {
        private readonly FishFarmDbContext _context;
        private readonly ServiceProvider _serviceProvider;

        public DatabaseIntegrationTests()
        {
            var services = new ServiceCollection();
            
            // Configure in-memory database
            services.AddDbContext<FishFarmDbContext>(options =>
                options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));
            
            services.AddLogging(builder => builder.AddConsole());

            _serviceProvider = services.BuildServiceProvider();
            _context = _serviceProvider.GetRequiredService<FishFarmDbContext>();
        }

        [Fact]
        public async Task Database_CanCreateAndRetrieveUser()
        {
            // Arrange
            var user = new User
            {
                Username = "testuser",
                FullName = "Test User",
                Email = "<EMAIL>",
                Status = UserStatus.Active
            };
            user.SetPassword("TestPassword123!");

            // Act
            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            var retrievedUser = await _context.Users
                .FirstOrDefaultAsync(u => u.Username == "testuser");

            // Assert
            retrievedUser.Should().NotBeNull();
            retrievedUser!.Username.Should().Be("testuser");
            retrievedUser.FullName.Should().Be("Test User");
            retrievedUser.Email.Should().Be("<EMAIL>");
            retrievedUser.VerifyPassword("TestPassword123!").Should().BeTrue();
        }

        [Fact]
        public async Task Database_CanCreateAndRetrievePond()
        {
            // Arrange
            var pond = new Pond
            {
                PondNumber = "P001",
                CycleId = 1,
                FishCount = 1000,
                AverageWeight = 0.5m,
                StockingDate = DateTime.Now,
                Status = "نشط"
            };

            // Act
            _context.Ponds.Add(pond);
            await _context.SaveChangesAsync();

            var retrievedPond = await _context.Ponds
                .FirstOrDefaultAsync(p => p.PondNumber == "P001");

            // Assert
            retrievedPond.Should().NotBeNull();
            retrievedPond!.PondNumber.Should().Be("P001");
            retrievedPond.FishCount.Should().Be(1000);
            retrievedPond.Status.Should().Be("نشط");
        }

        [Fact]
        public async Task Database_CanCreateBasicEntities()
        {
            // Arrange
            var user = new User
            {
                Username = "testuser",
                FullName = "Test User",
                Email = "<EMAIL>",
                Status = UserStatus.Active
            };

            var pond = new Pond
            {
                PondNumber = "P002",
                CycleId = 1,
                FishCount = 500,
                AverageWeight = 0.3m,
                StockingDate = DateTime.Now,
                Status = "نشط"
            };

            // Act
            _context.Users.Add(user);
            _context.Ponds.Add(pond);
            await _context.SaveChangesAsync();

            // Assert
            var userCount = await _context.Users.CountAsync();
            var pondCount = await _context.Ponds.CountAsync();

            userCount.Should().BeGreaterThan(0);
            pondCount.Should().BeGreaterThan(0);
        }



        public void Dispose()
        {
            _context.Dispose();
            _serviceProvider.Dispose();
        }
    }
}
