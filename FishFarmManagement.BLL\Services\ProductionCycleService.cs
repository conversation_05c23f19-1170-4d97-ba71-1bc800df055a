using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة إدارة الدورات الإنتاجية
    /// Production cycle management service
    /// </summary>
    public class ProductionCycleService : IProductionCycleService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProductionCycleService> _logger;
        private readonly ISettingsService _settingsService;
        private readonly IProductionCycleCalculatorService _calculatorService;

        public ProductionCycleService(
            IUnitOfWork unitOfWork,
            ILogger<ProductionCycleService> logger,
            ISettingsService settingsService,
            IProductionCycleCalculatorService calculatorService)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _settingsService = settingsService ?? throw new ArgumentNullException(nameof(settingsService));
            _calculatorService = calculatorService ?? throw new ArgumentNullException(nameof(calculatorService));
        }

        public async Task<IEnumerable<ProductionCycle>> GetAllAsync()
        {
            try
            {
                return await _unitOfWork.ProductionCycles.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على جميع الدورات الإنتاجية");
                throw;
            }
        }

        public async Task<ProductionCycle?> GetByIdAsync(int id)
        {
            try
            {
                return await _unitOfWork.ProductionCycles.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الدورة الإنتاجية {CycleId}", id);
                throw;
            }
        }

        public async Task<ProductionCycle> AddAsync(ProductionCycle entity)
        {
            try
            {
                var validationResult = await ValidateAsync(entity);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"بيانات الدورة غير صحيحة: {string.Join(", ", validationResult.Errors)}");
                }

                entity.CreatedDate = DateTime.Now;
                entity.Status = CycleStatus.Active;

                var result = await _unitOfWork.ProductionCycles.AddAsync(entity);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء دورة إنتاجية جديدة: {CycleName}", entity.CycleName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة الدورة الإنتاجية {CycleName}", entity.CycleName);
                throw;
            }
        }

        public async Task<ProductionCycle> UpdateAsync(ProductionCycle entity)
        {
            try
            {
                var validationResult = await ValidateAsync(entity);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"بيانات الدورة غير صحيحة: {string.Join(", ", validationResult.Errors)}");
                }

                entity.UpdatedDate = DateTime.Now;

                var result = await _unitOfWork.ProductionCycles.UpdateAsync(entity);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث الدورة الإنتاجية: {CycleName}", entity.CycleName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الدورة الإنتاجية {CycleId}", entity.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(id);
                if (cycle == null)
                {
                    return false;
                }

                // Check if cycle has ponds
                var hasPonds = await _unitOfWork.Ponds.ExistsAsync(p => p.CycleId == id);
                if (hasPonds)
                {
                    throw new InvalidOperationException("لا يمكن حذف الدورة لوجود أحواض مرتبطة بها");
                }

                await _unitOfWork.ProductionCycles.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم حذف الدورة الإنتاجية: {CycleName}", cycle.CycleName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الدورة الإنتاجية {CycleId}", id);
                throw;
            }
        }

        public async Task<ValidationResult> ValidateAsync(ProductionCycle entity)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(entity.CycleName))
            {
                result.AddError("اسم الدورة مطلوب");
            }

            if (entity.StartDate > entity.ExpectedEndDate)
            {
                result.AddError("تاريخ البداية لا يمكن أن يكون بعد تاريخ النهاية المتوقع");
            }

            if (entity.BudgetAmount < 0)
            {
                result.AddError("مبلغ الميزانية لا يمكن أن يكون سالباً");
            }

            // Check for duplicate cycle name
            var existingCycle = await _unitOfWork.ProductionCycles
                .FindAsync(c => c.CycleName == entity.CycleName && c.Id != entity.Id);
            if (existingCycle.Any())
            {
                result.AddError("اسم الدورة مستخدم من قبل");
            }

            return result;
        }

        public async Task<IEnumerable<ProductionCycle>> GetActiveCyclesAsync()
        {
            try
            {
                return await _unitOfWork.ProductionCycles.FindAsync(c => c.Status == CycleStatus.Active);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الدورات النشطة");
                throw;
            }
        }

        public async Task<IEnumerable<ProductionCycle>> GetCompletedCyclesAsync()
        {
            try
            {
                return await _unitOfWork.ProductionCycles.FindAsync(c => c.Status == CycleStatus.Completed);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الدورات المكتملة");
                throw;
            }
        }

        public async Task<ProductionCycle> CreateCycleAsync(string cycleName, DateTime startDate, 
            DateTime expectedEndDate, decimal budgetAmount = 0, string? notes = null)
        {
            try
            {
                var cycle = new ProductionCycle
                {
                    CycleName = cycleName,
                    StartDate = startDate,
                    ExpectedEndDate = expectedEndDate,
                    BudgetAmount = budgetAmount,
                    Notes = notes,
                    Status = CycleStatus.Active,
                    CreatedDate = DateTime.Now
                };

                return await AddAsync(cycle);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء دورة إنتاجية جديدة {CycleName}", cycleName);
                throw;
            }
        }

        public async Task<bool> CompleteCycleAsync(int cycleId, DateTime endDate)
        {
            try
            {
                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                if (cycle == null)
                {
                    return false;
                }

                cycle.EndDate = endDate;
                cycle.Status = CycleStatus.Completed;
                cycle.UpdatedDate = DateTime.Now;

                await _unitOfWork.ProductionCycles.UpdateAsync(cycle);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إنهاء الدورة الإنتاجية {CycleName}", cycle.CycleName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنهاء الدورة الإنتاجية {CycleId}", cycleId);
                throw;
            }
        }

        public async Task<bool> ExtendCycleAsync(int cycleId, DateTime newExpectedEndDate, string reason)
        {
            try
            {
                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                if (cycle == null)
                {
                    return false;
                }

                cycle.ExpectedEndDate = newExpectedEndDate;
                cycle.Notes = $"{cycle.Notes}\nتم تمديد الدورة حتى {newExpectedEndDate:yyyy-MM-dd} - السبب: {reason}";
                cycle.UpdatedDate = DateTime.Now;

                await _unitOfWork.ProductionCycles.UpdateAsync(cycle);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تمديد الدورة الإنتاجية {CycleName} حتى {NewDate}",
                    cycle.CycleName, newExpectedEndDate);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تمديد الدورة الإنتاجية {CycleId}", cycleId);
                throw;
            }
        }

        public async Task<IEnumerable<Pond>> GetCyclePondsAsync(int cycleId)
        {
            try
            {
                return await _unitOfWork.Ponds.FindAsync(p => p.CycleId == cycleId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على أحواض الدورة {CycleId}", cycleId);
                throw;
            }
        }

        public async Task<decimal> CalculateCycleTotalCostAsync(int cycleId)
        {
            return await _calculatorService.CalculateTotalCostAsync(cycleId);
        }

        public async Task<decimal> CalculateCycleTotalProductionAsync(int cycleId)
        {
            return await _calculatorService.CalculateTotalProductionAsync(cycleId);
        }

        public async Task<decimal> CalculateCycleProfitabilityAsync(int cycleId)
        {
            return await _calculatorService.CalculateExpectedProfitabilityAsync(cycleId);
        }

        public async Task<CycleStatistics> GetCycleStatisticsAsync(int cycleId)
        {
            return await _calculatorService.CalculateStatisticsAsync(cycleId);
        }

        public async Task<IEnumerable<ProductionCycle>> SearchCyclesAsync(string searchTerm)
        {
            try
            {
                return await _unitOfWork.ProductionCycles.FindAsync(c =>
                    c.CycleName.Contains(searchTerm) ||
                    (c.Notes != null && c.Notes.Contains(searchTerm)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن الدورات بالكلمة {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<IEnumerable<ProductionCycle>> GetCyclesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _unitOfWork.ProductionCycles.FindAsync(c =>
                    c.StartDate >= startDate && c.StartDate <= endDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الدورات في الفترة {StartDate} - {EndDate}",
                    startDate, endDate);
                throw;
            }
        }
    }
}
