using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// كشف الراتب
    /// Payroll
    /// </summary>
    public class Payroll : BaseEntity
    {
        [Required(ErrorMessage = "معرف الموظف مطلوب")]
        public int EmployeeId { get; set; }

        [Required(ErrorMessage = "معرف الدورة الإنتاجية مطلوب")]
        public int CycleId { get; set; }

        [Required(ErrorMessage = "الشهر مطلوب")]
        [Range(1, 12, ErrorMessage = "الشهر يجب أن يكون بين 1 و 12")]
        public int Month { get; set; }

        [Required(ErrorMessage = "السنة مطلوبة")]
        [Range(2020, 2050, ErrorMessage = "السنة يجب أن تكون بين 2020 و 2050")]
        public int Year { get; set; }

        [Required(ErrorMessage = "الراتب الأساسي مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "الراتب الأساسي يجب أن يكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal BaseSalary { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "البدلات يجب أن تكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal Allowances { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الخصومات يجب أن تكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal Deductions { get; set; }

        [Required(ErrorMessage = "صافي الراتب مطلوب")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal NetSalary { get; set; }

        public DateTime? PaymentDate { get; set; }

        [StringLength(1000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 1000 حرف")]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// عدد أيام العمل
        /// Working days
        /// </summary>
        [Range(0, 31, ErrorMessage = "عدد أيام العمل يجب أن يكون بين 0 و 31")]
        public int WorkingDays { get; set; } = 30;

        /// <summary>
        /// عدد أيام الغياب
        /// Absence days
        /// </summary>
        [Range(0, 31, ErrorMessage = "عدد أيام الغياب يجب أن يكون بين 0 و 31")]
        public int AbsenceDays { get; set; }

        /// <summary>
        /// ساعات العمل الإضافية
        /// Overtime hours
        /// </summary>
        [Range(0, 200, ErrorMessage = "ساعات العمل الإضافية يجب أن تكون بين 0 و 200")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal OvertimeHours { get; set; }

        /// <summary>
        /// معدل الساعة الإضافية
        /// Overtime rate per hour
        /// </summary>
        [Range(0, 1000, ErrorMessage = "معدل الساعة الإضافية يجب أن يكون بين 0 و 1000")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal OvertimeRate { get; set; }

        /// <summary>
        /// حالة الدفع
        /// Payment status
        /// </summary>
        [Required]
        [StringLength(20)]
        public string PaymentStatus { get; set; } = "معلق"; // معلق، مدفوع، ملغي

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("CycleId")]
        public virtual ProductionCycle ProductionCycle { get; set; } = null!;

        /// <summary>
        /// حساب إجمالي البدلات
        /// Calculate total allowances
        /// </summary>
        public decimal CalculateTotalAllowances()
        {
            var overtimeAllowance = OvertimeHours * OvertimeRate;
            return Allowances + overtimeAllowance;
        }

        /// <summary>
        /// حساب خصم الغياب
        /// Calculate absence deduction
        /// </summary>
        public decimal CalculateAbsenceDeduction()
        {
            if (WorkingDays <= 0) return 0;
            var dailyRate = BaseSalary / WorkingDays;
            return AbsenceDays * dailyRate;
        }

        /// <summary>
        /// حساب صافي الراتب تلقائياً
        /// Calculate net salary automatically
        /// </summary>
        public void CalculateNetSalary()
        {
            var totalAllowances = CalculateTotalAllowances();
            var totalDeductions = Deductions + CalculateAbsenceDeduction();
            NetSalary = BaseSalary + totalAllowances - totalDeductions;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data consistency
        /// </summary>
        public bool IsDataConsistent()
        {
            var calculatedNet = BaseSalary + CalculateTotalAllowances() - (Deductions + CalculateAbsenceDeduction());
            return Math.Abs(NetSalary - calculatedNet) < 0.01m;
        }

        /// <summary>
        /// التحقق من أن الراتب مدفوع
        /// Check if salary is paid
        /// </summary>
        public bool IsPaid => PaymentStatus == "مدفوع" && PaymentDate.HasValue;

        /// <summary>
        /// الحصول على اسم الشهر
        /// Get month name
        /// </summary>
        public string GetMonthName()
        {
            var monthNames = new[] { "", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", 
                                   "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر" };
            return Month >= 1 && Month <= 12 ? monthNames[Month] : "";
        }
    }
}
