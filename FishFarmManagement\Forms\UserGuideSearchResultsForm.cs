using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج نتائج البحث في دليل المستخدم
    /// User guide search results form
    /// </summary>
    public partial class UserGuideSearchResultsForm : Form
    {
        private readonly ILogger<UserGuideSearchResultsForm> _logger;
        private readonly IContentService _contentService;
        private readonly string _searchQuery;

        private ListView _resultsListView = null!;
        private Label _resultsCountLabel = null!;
        private Button _closeButton = null!;

        public UserGuideSearchResultsForm(ILogger<UserGuideSearchResultsForm> logger, IContentService contentService, string searchQuery)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _contentService = contentService ?? throw new ArgumentNullException(nameof(contentService));
            _searchQuery = searchQuery ?? throw new ArgumentNullException(nameof(searchQuery));

            InitializeComponent();
            LoadSearchResults();
        }

        private void InitializeComponent()
        {
            this.Text = $"نتائج البحث: {_searchQuery}";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Results count label
            _resultsCountLabel = new Label
            {
                Text = "جاري البحث...",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Location = new Point(20, 20),
                AutoSize = true
            };

            // Results list view
            _resultsListView = new ListView
            {
                Location = new Point(20, 50),
                Size = new Size(740, 480),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = false
            };

            _resultsListView.Columns.Add("العنوان", 300);
            _resultsListView.Columns.Add("المحتوى", 400);
            _resultsListView.Columns.Add("القسم", 100);

            // Close button
            _closeButton = new Button
            {
                Text = "إغلاق",
                Size = new Size(100, 30),
                Location = new Point(680, 540),
                DialogResult = DialogResult.OK
            };

            _closeButton.Click += (s, e) => this.Close();

            // Add controls
            this.Controls.AddRange(new Control[]
            {
                _resultsCountLabel,
                _resultsListView,
                _closeButton
            });

            // Handle double click to view content
            _resultsListView.DoubleClick += ResultsListView_DoubleClick;
        }

        private async void LoadSearchResults()
        {
            try
            {
                _resultsCountLabel.Text = "جاري البحث...";
                _resultsListView.Items.Clear();

                // Simulate search results since IContentService.SearchContentAsync doesn't exist
                var searchResults = await SimulateSearchAsync(_searchQuery);

                _resultsCountLabel.Text = $"تم العثور على {searchResults.Count} نتيجة";

                foreach (var result in searchResults)
                {
                    var item = new ListViewItem(result.Title);
                    item.SubItems.Add(result.Content.Length > 100 ? 
                        result.Content.Substring(0, 100) + "..." : 
                        result.Content);
                    item.SubItems.Add(result.Section);
                    item.Tag = result;

                    _resultsListView.Items.Add(item);
                }

                _logger.LogInformation($"تم تحميل {searchResults.Count} نتيجة بحث للاستعلام: {_searchQuery}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في البحث عن: {_searchQuery}");
                _resultsCountLabel.Text = "حدث خطأ أثناء البحث";
                MessageBox.Show("حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task<List<SearchResult>> SimulateSearchAsync(string query)
        {
            // Simulate async search operation
            await Task.Delay(500);

            var results = new List<SearchResult>();

            // Sample search results based on query
            if (query.Contains("حوض") || query.Contains("أحواض"))
            {
                results.Add(new SearchResult
                {
                    Title = "إدارة الأحواض",
                    Content = "يمكنك إدارة الأحواض من خلال قائمة الإدارة > إدارة الأحواض. يمكنك إضافة أحواض جديدة وتعديل الموجودة ومراقبة حالتها.",
                    Section = "الإدارة"
                });

                results.Add(new SearchResult
                {
                    Title = "مراقبة جودة المياه",
                    Content = "من المهم مراقبة جودة المياه في الأحواض بانتظام. يمكنك تسجيل قراءات جودة المياه من خلال نموذج مراقبة جودة المياه.",
                    Section = "المراقبة"
                });
            }

            if (query.Contains("موظف") || query.Contains("موظفين"))
            {
                results.Add(new SearchResult
                {
                    Title = "إدارة الموظفين",
                    Content = "يمكنك إدارة بيانات الموظفين وحساب الرواتب من خلال قائمة الإدارة > إدارة الموظفين.",
                    Section = "الموظفين"
                });
            }

            if (query.Contains("تقرير") || query.Contains("تقارير"))
            {
                results.Add(new SearchResult
                {
                    Title = "التقارير المالية",
                    Content = "يمكنك إنشاء التقارير المالية المختلفة مثل قائمة الدخل والميزانية العمومية من قائمة التقارير.",
                    Section = "التقارير"
                });
            }

            // Add default results if no specific matches
            if (results.Count == 0)
            {
                results.Add(new SearchResult
                {
                    Title = "البدء السريع",
                    Content = "مرحباً بك في نظام إدارة مزرعة الأسماك. يمكنك البدء بإعداد الأحواض والدورات الإنتاجية.",
                    Section = "عام"
                });

                results.Add(new SearchResult
                {
                    Title = "الدعم الفني",
                    Content = "للحصول على الدعم الفني، يرجى التواصل معنا عبر البريد الإلكتروني: <EMAIL>",
                    Section = "الدعم"
                });
            }

            return results;
        }

        private void ResultsListView_DoubleClick(object? sender, EventArgs e)
        {
            if (_resultsListView.SelectedItems.Count > 0)
            {
                var selectedItem = _resultsListView.SelectedItems[0];
                var result = selectedItem.Tag as SearchResult;

                if (result != null)
                {
                    // Show detailed content
                    var detailForm = new Form
                    {
                        Text = result.Title,
                        Size = new Size(600, 400),
                        StartPosition = FormStartPosition.CenterParent,
                        RightToLeft = RightToLeft.Yes,
                        RightToLeftLayout = true
                    };

                    var textBox = new TextBox
                    {
                        Text = result.Content,
                        Multiline = true,
                        ReadOnly = true,
                        ScrollBars = ScrollBars.Vertical,
                        Dock = DockStyle.Fill,
                        Font = new Font("Segoe UI", 10F)
                    };

                    detailForm.Controls.Add(textBox);
                    detailForm.ShowDialog();
                }
            }
        }

        private class SearchResult
        {
            public string Title { get; set; } = string.Empty;
            public string Content { get; set; } = string.Empty;
            public string Section { get; set; } = string.Empty;
        }
    }
}
