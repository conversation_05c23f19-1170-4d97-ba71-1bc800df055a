using System.ComponentModel.DataAnnotations;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// الميزانية العمومية
    /// Balance Sheet
    /// </summary>
    public class BalanceSheet
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// تاريخ الميزانية
        /// Balance sheet date
        /// </summary>
        public DateTime BalanceDate { get; set; }

        /// <summary>
        /// إجمالي الأصول
        /// Total assets
        /// </summary>
        public decimal TotalAssets { get; set; }

        /// <summary>
        /// إجمالي الخصوم
        /// Total liabilities
        /// </summary>
        public decimal TotalLiabilities { get; set; }

        /// <summary>
        /// إجمالي حقوق الملكية
        /// Total equity
        /// </summary>
        public decimal TotalEquity { get; set; }

        /// <summary>
        /// عناصر الميزانية
        /// Balance sheet items
        /// </summary>
        public List<BalanceSheetItem> Items { get; set; } = new List<BalanceSheetItem>();

        /// <summary>
        /// تاريخ الإنشاء
        /// Creation date
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// المستخدم المنشئ
        /// Created by user
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// عنصر الميزانية العمومية
    /// Balance sheet item
    /// </summary>
    public class BalanceSheetItem
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// معرف الميزانية
        /// Balance sheet ID
        /// </summary>
        public int BalanceSheetId { get; set; }

        /// <summary>
        /// الميزانية العمومية
        /// Balance sheet
        /// </summary>
        public BalanceSheet BalanceSheet { get; set; } = null!;

        /// <summary>
        /// اسم العنصر
        /// Item name
        /// </summary>
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// الرصيد
        /// Balance
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// النوع (أصل/خصم/حقوق ملكية)
        /// Type (Asset/Liability/Equity)
        /// </summary>
        public string ItemType { get; set; } = string.Empty;

        /// <summary>
        /// الفئة
        /// Category
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// معرف الحساب
        /// Account ID
        /// </summary>
        public int? AccountId { get; set; }

        /// <summary>
        /// الحساب
        /// Account
        /// </summary>
        public Account? Account { get; set; }

        /// <summary>
        /// الوصف
        /// Description
        /// </summary>
        public string? Description { get; set; }
    }
}
