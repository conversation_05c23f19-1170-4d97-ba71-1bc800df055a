2025-07-25 00:11:41.920 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 43
2025-07-25 00:21:28.456 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 43
2025-07-25 00:21:30.029 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:21:30.038 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:21:30.052 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:21:30.065 +03:00 [INF] Applying migration '20250724205802_InitialCreate'.
2025-07-25 00:21:30.185 +03:00 [ERR] Failed executing DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:22:23.599 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 43
2025-07-25 00:22:24.995 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:22:25.012 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:22:40.207 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 43
2025-07-25 00:23:04.903 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 43
2025-07-25 00:23:06.306 +03:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-25 00:23:06.398 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-25 00:23:06.404 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:23:06.406 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:23:06.420 +03:00 [INF] Applying migration '20250724205802_InitialCreate'.
2025-07-25 00:23:06.527 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:23:06.527 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,JsonDocument jsonDocument = JsonDocument.Parse(JsonSerializer.Serialize(permissions));
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:23:06.527 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (JsonDocument jsonDocument = JsonDocument.Parse(JsonSerializer.Serialize(permissions));
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:23:06.528 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:23:06.528 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:23:06.528 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:23:06.528 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:23:06.528 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:23:06.528 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE CASCADE
);
2025-07-25 00:23:06.528 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:23:06.529 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:23:06.529 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:23:06.529 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:23:06.529 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-25 00:23:06.529 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-25 00:23:06.530 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-25 00:23:06.530 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-25 00:23:06.531 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-24 23:58:00.3543764', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-24 23:58:00.3543765', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-24 23:58:00.3543765', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-24 23:58:00.3543766', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-24 23:58:00.3543766', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-25 00:23:06.531 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-24 23:58:00.3543917', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-25 00:23:06.533 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-24 23:58:00.3543858', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-24 23:58:00.3543858', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-24 23:58:00.3543859', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-24 23:58:00.3543859', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-24 23:58:00.354386', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-24 23:58:00.3543861', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-24 23:58:00.3543861', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-24 23:58:00.3543862', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-24 23:58:00.3543863', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-25 00:23:06.534 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-25 00:23:06.535 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-25 00:23:06.535 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-25 00:23:06.535 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-25 00:23:06.536 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-25 00:23:06.536 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-25 00:23:06.536 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-25 00:23:06.536 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-25 00:23:06.536 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-25 00:23:06.536 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-25 00:23:06.537 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId" ON "InventoryMovements" ("ItemId");
2025-07-25 00:23:06.537 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-25 00:23:06.537 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-25 00:23:06.537 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-25 00:23:06.537 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-25 00:23:06.537 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-25 00:23:06.537 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-25 00:23:06.537 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-25 00:23:06.538 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-25 00:23:06.538 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-25 00:23:06.538 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-25 00:23:06.538 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-25 00:23:06.538 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724205802_InitialCreate', '8.0.0');
2025-07-25 00:23:06.547 +03:00 [INF] Applying migration '20250724211144_AddUserManagement'.
2025-07-25 00:23:06.611 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP INDEX "IX_InventoryMovements_ItemId";
2025-07-25 00:23:06.611 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:23:06.612 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:23:06.612 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-25 00:23:06.612 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100123'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 00:23:06.612 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100129'
WHERE "Id" = 2;
SELECT changes();
2025-07-25 00:23:06.612 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.810013'
WHERE "Id" = 3;
SELECT changes();
2025-07-25 00:23:06.612 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.810013'
WHERE "Id" = 4;
SELECT changes();
2025-07-25 00:23:06.612 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:11:43.8100131'
WHERE "Id" = 5;
SELECT changes();
2025-07-25 00:23:06.612 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100494'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 00:23:06.613 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100496'
WHERE "Id" = 2;
SELECT changes();
2025-07-25 00:23:06.613 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100506'
WHERE "Id" = 3;
SELECT changes();
2025-07-25 00:23:06.613 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100544'
WHERE "Id" = 4;
SELECT changes();
2025-07-25 00:23:06.613 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100545'
WHERE "Id" = 5;
SELECT changes();
2025-07-25 00:23:06.613 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100546'
WHERE "Id" = 6;
SELECT changes();
2025-07-25 00:23:06.613 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100547'
WHERE "Id" = 7;
SELECT changes();
2025-07-25 00:23:06.613 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100547'
WHERE "Id" = 8;
SELECT changes();
2025-07-25 00:23:06.613 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100548'
WHERE "Id" = 9;
SELECT changes();
2025-07-25 00:23:06.613 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100549'
WHERE "Id" = 10;
SELECT changes();
2025-07-25 00:23:06.613 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100549'
WHERE "Id" = 11;
SELECT changes();
2025-07-25 00:23:06.613 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.810055'
WHERE "Id" = 12;
SELECT changes();
2025-07-25 00:23:06.613 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:11:43.8100551'
WHERE "Id" = 13;
SELECT changes();
2025-07-25 00:23:06.614 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "FarmInfos" SET "CreatedDate" = '2025-07-25 00:11:43.8100734'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 00:23:06.614 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-25 00:11:43.8102126', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-25 00:11:43.8102335', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-25 00:11:43.8102376', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-25 00:11:43.810241', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-25 00:11:43.8102437', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-25 00:23:06.614 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-25 00:11:43.8102634', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, NULL, '$2a$11$xajPljvN0VMOVwgwxmR0vetvCHYroT46IZNhyYQDBHYuv1GqGxzKS', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-25 00:23:06.614 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-25 00:11:43.960912', NULL, 'System', '2025-07-25 00:11:43.9609117', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-25 00:23:06.614 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-25 00:23:06.614 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-25 00:23:06.614 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-25 00:23:06.614 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-25 00:23:06.615 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-25 00:23:06.615 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-25 00:23:06.615 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-25 00:23:06.615 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ef_temp_InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "BatchNumber" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "Reference" TEXT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:23:06.615 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "ef_temp_InventoryMovements" ("Id", "BatchNumber", "CreatedBy", "CreatedDate", "ItemId", "MovementDate", "MovementType", "Notes", "Quantity", "QuantityAfter", "QuantityBefore", "Reason", "Reference", "TotalValue", "UnitPrice", "UpdatedDate")
SELECT "Id", "BatchNumber", "CreatedBy", "CreatedDate", "ItemId", "MovementDate", "MovementType", "Notes", "Quantity", "QuantityAfter", "QuantityBefore", "Reason", "Reference", "TotalValue", "UnitPrice", "UpdatedDate"
FROM "InventoryMovements";
2025-07-25 00:23:06.620 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA foreign_keys = 0;
2025-07-25 00:23:06.620 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP TABLE "InventoryMovements";
2025-07-25 00:23:06.623 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "ef_temp_InventoryMovements" RENAME TO "InventoryMovements";
2025-07-25 00:23:06.626 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA foreign_keys = 1;
2025-07-25 00:23:06.626 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-25 00:23:06.627 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-25 00:23:06.627 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724211144_AddUserManagement', '8.0.0');
2025-07-25 00:48:10.706 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 44
2025-07-25 00:49:46.191 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 44
2025-07-25 00:50:07.830 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 44
2025-07-25 00:51:52.674 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-25 00:51:52.778 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-25 00:51:52.791 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:51:52.795 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:51:52.815 +03:00 [INF] Applying migration '20250724214950_InitialCreate'.
2025-07-25 00:51:52.938 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:51:52.938 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:51:52.938 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:51:52.938 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:51:52.938 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:51:52.939 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:51:52.940 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:51:52.940 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:51:52.941 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 00:51:52.941 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:51:52.941 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:51:52.941 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:51:52.941 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:51:52.941 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:51:52.941 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 00:51:52.942 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-25 00:51:52.942 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-25 00:51:52.942 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-25 00:51:52.942 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-25 00:51:52.942 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-25 00:51:52.943 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-25 00:49:47.658323', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-25 00:49:47.6583231', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-25 00:49:47.6583232', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-25 00:49:47.6583232', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-25 00:49:47.6583233', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-25 00:51:52.944 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-25 00:49:47.658358', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-25 00:51:52.944 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-25 00:49:47.6584142', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-25 00:49:47.6584229', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-25 00:49:47.6584251', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-25 00:49:47.658427', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-25 00:49:47.6584295', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-25 00:51:52.944 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-25 00:49:47.6584359', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, NULL, '$2a$11$TQ7CvSqMXdT4TX1n7P/o5egPyX2beGWMWqUavG3tCEqh2CvcKaNhK', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-25 00:51:52.945 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-25 00:49:47.6583355', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-25 00:49:47.6583356', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-25 00:49:47.6583359', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-25 00:49:47.658337', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-25 00:49:47.6583371', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-25 00:49:47.6583371', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-25 00:49:47.6583372', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-25 00:49:47.6583372', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-25 00:49:47.6583373', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-25 00:49:47.6583373', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-25 00:49:47.6583374', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-25 00:49:47.6583374', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-25 00:49:47.6583375', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-25 00:51:52.946 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-25 00:49:48.0307072', NULL, 'System', '2025-07-25 00:49:48.0307071', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-25 00:51:52.947 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-25 00:51:52.947 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-25 00:51:52.947 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-25 00:51:52.947 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-25 00:51:52.948 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-25 00:51:52.948 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-25 00:51:52.948 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-25 00:51:52.948 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-25 00:51:52.948 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-25 00:51:52.948 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-25 00:51:52.948 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-25 00:51:52.949 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-25 00:51:52.949 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-25 00:51:52.949 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-25 00:51:52.949 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-25 00:51:52.949 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-25 00:51:52.949 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-25 00:51:52.949 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-25 00:51:52.950 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-25 00:51:52.950 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-25 00:51:52.950 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-25 00:51:52.950 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-25 00:51:52.950 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-25 00:51:52.950 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-25 00:51:52.950 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-25 00:51:52.951 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-25 00:51:52.951 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-25 00:51:52.951 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-25 00:51:52.951 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724214950_InitialCreate', '8.0.0');
2025-07-25 00:51:52.963 +03:00 [INF] Applying migration '20250724215010_AddUserManagement'.
2025-07-25 00:51:53.006 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:50:09.4642939'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 00:51:53.006 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:50:09.4642941'
WHERE "Id" = 2;
SELECT changes();
2025-07-25 00:51:53.006 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:50:09.4642941'
WHERE "Id" = 3;
SELECT changes();
2025-07-25 00:51:53.006 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:50:09.4642942'
WHERE "Id" = 4;
SELECT changes();
2025-07-25 00:51:53.007 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 00:50:09.4642942'
WHERE "Id" = 5;
SELECT changes();
2025-07-25 00:51:53.007 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643088'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 00:51:53.007 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643089'
WHERE "Id" = 2;
SELECT changes();
2025-07-25 00:51:53.007 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643092'
WHERE "Id" = 3;
SELECT changes();
2025-07-25 00:51:53.007 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643102'
WHERE "Id" = 4;
SELECT changes();
2025-07-25 00:51:53.007 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643103'
WHERE "Id" = 5;
SELECT changes();
2025-07-25 00:51:53.007 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643103'
WHERE "Id" = 6;
SELECT changes();
2025-07-25 00:51:53.007 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643104'
WHERE "Id" = 7;
SELECT changes();
2025-07-25 00:51:53.007 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643104'
WHERE "Id" = 8;
SELECT changes();
2025-07-25 00:51:53.007 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643105'
WHERE "Id" = 9;
SELECT changes();
2025-07-25 00:51:53.007 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643105'
WHERE "Id" = 10;
SELECT changes();
2025-07-25 00:51:53.007 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643106'
WHERE "Id" = 11;
SELECT changes();
2025-07-25 00:51:53.008 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643107'
WHERE "Id" = 12;
SELECT changes();
2025-07-25 00:51:53.008 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 00:50:09.4643107'
WHERE "Id" = 13;
SELECT changes();
2025-07-25 00:51:53.008 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "FarmInfos" SET "CreatedDate" = '2025-07-25 00:50:09.4643199'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 00:51:53.008 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-25 00:50:09.4644025'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 00:51:53.008 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-25 00:50:09.4644117'
WHERE "Id" = 2;
SELECT changes();
2025-07-25 00:51:53.008 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-25 00:50:09.4644141'
WHERE "Id" = 3;
SELECT changes();
2025-07-25 00:51:53.008 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-25 00:50:09.4644161'
WHERE "Id" = 4;
SELECT changes();
2025-07-25 00:51:53.008 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-25 00:50:09.4644176'
WHERE "Id" = 5;
SELECT changes();
2025-07-25 00:51:53.008 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedDate" = '2025-07-25 00:50:09.622935', "GrantedDate" = '2025-07-25 00:50:09.6229348'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 00:51:53.008 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = '2025-07-25 00:50:09.4644238', "PasswordHash" = '$2a$11$NMTasAjImY6H.cMQqhmU/e/kgw29JcjF9BIp7km3rsT13AiDND2mi'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 00:51:53.008 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724215010_AddUserManagement', '8.0.0');
2025-07-25 00:51:53.016 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-25 00:51:53.313 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-25 00:51:53.387 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-25 00:51:53.416 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-25 00:51:53.416 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-25 00:51:53.416 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-25 00:52:05.004 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:52:05.204 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:52:05.222 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:52:15.930 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:52:16.086 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:52:16.092 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:52:37.444 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:52:37.586 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:52:37.593 +03:00 [ERR] خطأ في تسجيل الدخول للمستخدم: admin
System.InvalidOperationException: The LINQ expression 'DbSet<UserRole>()
    .Where(u => u.UserId == __user_Id_0 && u.IsActive && !(u.IsExpired()))' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalQueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FishFarmManagement.DAL.Repositories.Repository`1.FindAsync(Expression`1 predicate) in D:\account pro\fish accounting & management\FishFarmManagement.DAL\Repositories\Repository.cs:line 35
   at FishFarmManagement.BLL.Services.AuthenticationService.LoginAsync(String username, String password) in D:\account pro\fish accounting & management\FishFarmManagement.BLL\Services\AuthenticationService.cs:line 94
2025-07-25 00:53:36.339 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:53:36.388 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 00:53:36.413 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 00:53:36.438 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-25 00:53:36.440 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-25 00:53:36.657 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-25 00:53:36.776 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-25 00:53:36.830 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-25 00:53:36.831 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-25 00:53:36.831 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-25 00:53:46.005 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 00:53:46.384 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p13='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (Size = 60), @p9='?', @p10='?' (Size = 3), @p11='?' (DbType = DateTime), @p12='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "Notes" = @p7, "PasswordHash" = @p8, "PhoneNumber" = @p9, "Status" = @p10, "UpdatedDate" = @p11, "Username" = @p12
WHERE "Id" = @p13
RETURNING 1;
2025-07-25 00:53:46.416 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__user_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."ExpiryDate", "u"."GrantedBy", "u"."GrantedDate", "u"."IsActive", "u"."Notes", "u"."RoleId", "u"."UpdatedDate", "u"."UserId"
FROM "UserRoles" AS "u"
WHERE "u"."UserId" = @__user_Id_0 AND "u"."IsActive" AND ("u"."ExpiryDate" IS NULL OR "u"."ExpiryDate" >= rtrim(rtrim(strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime'), '0'), '.'))
2025-07-25 00:53:46.468 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "r"."Id", "r"."CreatedDate", "r"."Description", "r"."IsActive", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate"
FROM "Roles" AS "r"
WHERE "r"."Id" = @__p_0
LIMIT 1
2025-07-25 00:53:46.492 +03:00 [INF] تم تسجيل دخول المستخدم بنجاح: admin
2025-07-25 00:53:46.853 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-07-25 00:53:46.898 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."AverageWeight", "p"."CreatedDate", "p"."CycleId", "p"."ExpectedHarvestDate", "p"."FishCount", "p"."Notes", "p"."PondNumber", "p"."Status", "p"."StockingDate", "p"."UpdatedDate"
FROM "Ponds" AS "p"
2025-07-25 00:53:46.902 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."BudgetAmount", "p"."CreatedDate", "p"."CycleName", "p"."EndDate", "p"."ExpectedEndDate", "p"."Notes", "p"."StartDate", "p"."Status", "p"."UpdatedDate"
FROM "ProductionCycles" AS "p"
2025-07-25 00:53:46.910 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "e"."Id", "e"."Address", "e"."BaseSalary", "e"."BirthDate", "e"."ContactInfo", "e"."CreatedDate", "e"."Email", "e"."FullName", "e"."JoinDate", "e"."LeaveDate", "e"."MaritalStatus", "e"."NationalId", "e"."Nationality", "e"."NumberOfChildren", "e"."Phone", "e"."Position", "e"."ResidenceNumber", "e"."Status", "e"."UpdatedDate"
FROM "Employees" AS "e"
2025-07-25 00:53:46.915 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
2025-07-25 00:53:46.920 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "i"."Id", "i"."BatchNumber", "i"."CreatedDate", "i"."ExpiryDate", "i"."ItemName", "i"."ItemType", "i"."MaximumStock", "i"."MinimumStock", "i"."Notes", "i"."Quantity", "i"."ReorderPoint", "i"."Status", "i"."StorageLocation", "i"."Supplier", "i"."TotalValue", "i"."Unit", "i"."UnitPrice", "i"."UpdatedDate"
FROM "Inventories" AS "i"
2025-07-25 00:53:46.934 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__startOfMonth_0='?' (DbType = DateTime), @__AddMonths_1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
WHERE "t"."TransactionDate" >= @__startOfMonth_0 AND "t"."TransactionDate" < @__AddMonths_1
2025-07-25 00:53:46.940 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__startOfMonth_0='?' (DbType = DateTime), @__AddMonths_1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
WHERE "t"."TransactionDate" >= @__startOfMonth_0 AND "t"."TransactionDate" < @__AddMonths_1
2025-07-25 00:53:46.941 +03:00 [INF] تم تحميل بيانات لوحة المعلومات بنجاح
2025-07-25 00:53:46.946 +03:00 [INF] تم تطبيق الصلاحيات للمستخدم: admin
2025-07-25 00:54:07.776 +03:00 [INF] تم تحميل بيانات المخزون بنجاح
2025-07-25 00:54:14.196 +03:00 [INF] تم تحميل بيانات مراكز التكلفة بنجاح
2025-07-25 01:08:34.687 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 44
2025-07-25 01:09:28.524 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 01:09:28.565 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 01:09:28.581 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 01:09:28.604 +03:00 [INF] Applying migration '20250724220843_AddMustChangePasswordField'.
2025-07-25 01:09:28.703 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "Users" ADD "MustChangePassword" INTEGER NOT NULL DEFAULT 0;
2025-07-25 01:09:28.704 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 01:08:38.3723781'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 01:09:28.704 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 01:08:38.3723782'
WHERE "Id" = 2;
SELECT changes();
2025-07-25 01:09:28.704 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 01:08:38.3723783'
WHERE "Id" = 3;
SELECT changes();
2025-07-25 01:09:28.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 01:08:38.3723784'
WHERE "Id" = 4;
SELECT changes();
2025-07-25 01:09:28.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "AccountTypes" SET "CreatedDate" = '2025-07-25 01:08:38.3723785'
WHERE "Id" = 5;
SELECT changes();
2025-07-25 01:09:28.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.3724038'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 01:09:28.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.372404'
WHERE "Id" = 2;
SELECT changes();
2025-07-25 01:09:28.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.3724056'
WHERE "Id" = 3;
SELECT changes();
2025-07-25 01:09:28.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.3724078'
WHERE "Id" = 4;
SELECT changes();
2025-07-25 01:09:28.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.3724079'
WHERE "Id" = 5;
SELECT changes();
2025-07-25 01:09:28.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.3724081'
WHERE "Id" = 6;
SELECT changes();
2025-07-25 01:09:28.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.3724082'
WHERE "Id" = 7;
SELECT changes();
2025-07-25 01:09:28.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.3724083'
WHERE "Id" = 8;
SELECT changes();
2025-07-25 01:09:28.705 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.3724084'
WHERE "Id" = 9;
SELECT changes();
2025-07-25 01:09:28.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.3724085'
WHERE "Id" = 10;
SELECT changes();
2025-07-25 01:09:28.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.3724086'
WHERE "Id" = 11;
SELECT changes();
2025-07-25 01:09:28.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.3724087'
WHERE "Id" = 12;
SELECT changes();
2025-07-25 01:09:28.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Accounts" SET "CreatedDate" = '2025-07-25 01:08:38.3724089'
WHERE "Id" = 13;
SELECT changes();
2025-07-25 01:09:28.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "FarmInfos" SET "CreatedDate" = '2025-07-25 01:08:38.3724715'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 01:09:28.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-25 01:08:38.3725796'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 01:09:28.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-25 01:08:38.3726055'
WHERE "Id" = 2;
SELECT changes();
2025-07-25 01:09:28.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-25 01:08:38.372612'
WHERE "Id" = 3;
SELECT changes();
2025-07-25 01:09:28.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-25 01:08:38.3726184'
WHERE "Id" = 4;
SELECT changes();
2025-07-25 01:09:28.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Roles" SET "CreatedDate" = '2025-07-25 01:08:38.3726219'
WHERE "Id" = 5;
SELECT changes();
2025-07-25 01:09:28.706 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedDate" = '2025-07-25 01:08:38.6641305', "GrantedDate" = '2025-07-25 01:08:38.6641304'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 01:09:28.707 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = '2025-07-25 01:08:38.37264', "MustChangePassword" = 0, "PasswordHash" = '$2a$11$dqw8vb.YWD01O/9vWhBFmuBAO6f2vRBXrCqpEY3pYGfwGLkbxrA0S'
WHERE "Id" = 1;
SELECT changes();
2025-07-25 01:09:28.707 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250724220843_AddMustChangePasswordField', '8.0.0');
2025-07-25 01:09:28.727 +03:00 [INF] تم تطبيق migrations بنجاح
2025-07-25 01:09:28.988 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = 'admin'
LIMIT 1
2025-07-25 01:09:29.133 +03:00 [INF] المستخدم الافتراضي موجود بالفعل - Username: admin, Email: <EMAIL>
2025-07-25 01:09:29.185 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Users" AS "u"
2025-07-25 01:09:29.186 +03:00 [INF] إجمالي المستخدمين في قاعدة البيانات: 1
2025-07-25 01:09:29.187 +03:00 [INF] تم تهيئة قاعدة البيانات بنجاح
2025-07-25 01:17:41.225 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__username_0='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."Email", "u"."FailedLoginAttempts", "u"."FullName", "u"."IsSystemAdmin", "u"."LastLoginDate", "u"."LockedUntil", "u"."MustChangePassword", "u"."Notes", "u"."PasswordHash", "u"."PhoneNumber", "u"."Status", "u"."UpdatedDate", "u"."Username"
FROM "Users" AS "u"
WHERE "u"."Username" = @__username_0
2025-07-25 01:17:41.442 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p14='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (Size = 18), @p2='?' (DbType = Int32), @p3='?' (Size = 11), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime), @p6='?' (DbType = DateTime), @p7='?' (DbType = Boolean), @p8='?', @p9='?' (Size = 60), @p10='?', @p11='?' (Size = 3), @p12='?' (DbType = DateTime), @p13='?' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "CreatedDate" = @p0, "Email" = @p1, "FailedLoginAttempts" = @p2, "FullName" = @p3, "IsSystemAdmin" = @p4, "LastLoginDate" = @p5, "LockedUntil" = @p6, "MustChangePassword" = @p7, "Notes" = @p8, "PasswordHash" = @p9, "PhoneNumber" = @p10, "Status" = @p11, "UpdatedDate" = @p12, "Username" = @p13
WHERE "Id" = @p14
RETURNING 1;
2025-07-25 01:17:41.475 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__user_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."Id", "u"."CreatedDate", "u"."ExpiryDate", "u"."GrantedBy", "u"."GrantedDate", "u"."IsActive", "u"."Notes", "u"."RoleId", "u"."UpdatedDate", "u"."UserId"
FROM "UserRoles" AS "u"
WHERE "u"."UserId" = @__user_Id_0 AND "u"."IsActive" AND ("u"."ExpiryDate" IS NULL OR "u"."ExpiryDate" >= rtrim(rtrim(strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime'), '0'), '.'))
2025-07-25 01:17:41.521 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "r"."Id", "r"."CreatedDate", "r"."Description", "r"."IsActive", "r"."IsSystemRole", "r"."Name", "r"."Permissions", "r"."UpdatedDate"
FROM "Roles" AS "r"
WHERE "r"."Id" = @__p_0
LIMIT 1
2025-07-25 01:17:41.546 +03:00 [INF] تم تسجيل دخول المستخدم بنجاح: admin
2025-07-25 01:17:41.790 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-07-25 01:17:41.849 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."AverageWeight", "p"."CreatedDate", "p"."CycleId", "p"."ExpectedHarvestDate", "p"."FishCount", "p"."Notes", "p"."PondNumber", "p"."Status", "p"."StockingDate", "p"."UpdatedDate"
FROM "Ponds" AS "p"
2025-07-25 01:17:41.866 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."Id", "p"."BudgetAmount", "p"."CreatedDate", "p"."CycleName", "p"."EndDate", "p"."ExpectedEndDate", "p"."Notes", "p"."StartDate", "p"."Status", "p"."UpdatedDate"
FROM "ProductionCycles" AS "p"
2025-07-25 01:17:41.871 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "e"."Id", "e"."Address", "e"."BaseSalary", "e"."BirthDate", "e"."ContactInfo", "e"."CreatedDate", "e"."Email", "e"."FullName", "e"."JoinDate", "e"."LeaveDate", "e"."MaritalStatus", "e"."NationalId", "e"."Nationality", "e"."NumberOfChildren", "e"."Phone", "e"."Position", "e"."ResidenceNumber", "e"."Status", "e"."UpdatedDate"
FROM "Employees" AS "e"
2025-07-25 01:17:41.882 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
2025-07-25 01:17:41.888 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "i"."Id", "i"."BatchNumber", "i"."CreatedDate", "i"."ExpiryDate", "i"."ItemName", "i"."ItemType", "i"."MaximumStock", "i"."MinimumStock", "i"."Notes", "i"."Quantity", "i"."ReorderPoint", "i"."Status", "i"."StorageLocation", "i"."Supplier", "i"."TotalValue", "i"."Unit", "i"."UnitPrice", "i"."UpdatedDate"
FROM "Inventories" AS "i"
2025-07-25 01:17:41.902 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__startOfMonth_0='?' (DbType = DateTime), @__AddMonths_1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
WHERE "t"."TransactionDate" >= @__startOfMonth_0 AND "t"."TransactionDate" < @__AddMonths_1
2025-07-25 01:17:41.909 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__startOfMonth_0='?' (DbType = DateTime), @__AddMonths_1='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."ApprovalDate", "t"."ApprovedBy", "t"."CreatedBy", "t"."CreatedDate", "t"."CycleId", "t"."Description", "t"."Notes", "t"."ReferenceNumber", "t"."Status", "t"."TotalAmount", "t"."TransactionDate", "t"."TransactionType", "t"."UpdatedDate"
FROM "Transactions" AS "t"
WHERE "t"."TransactionDate" >= @__startOfMonth_0 AND "t"."TransactionDate" < @__AddMonths_1
2025-07-25 01:17:41.911 +03:00 [INF] تم تحميل بيانات لوحة المعلومات بنجاح
2025-07-25 01:17:41.917 +03:00 [INF] تم تطبيق الصلاحيات للمستخدم: admin
2025-07-25 01:17:52.915 +03:00 [INF] تم إنشاء تقرير ملخص الإنتاج للفترة من 25/06/2025 إلى 25/07/2025
2025-07-25 03:23:38.430 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-25 03:24:16.167 +03:00 [FTL] خطأ في بدء تشغيل التطبيق
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at FishFarmManagement.Program.Main() in D:\account pro\fish accounting & management\FishFarmManagement\Program.cs:line 45
2025-07-25 03:24:18.012 +03:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-25 03:24:18.196 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-07-25 03:24:18.208 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-25 03:24:18.211 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-25 03:24:18.237 +03:00 [INF] Applying migration '20250725002348_InitialCreate'.
2025-07-25 03:24:18.392 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AccountTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AccountTypes" PRIMARY KEY AUTOINCREMENT,
    "TypeName" TEXT NOT NULL,
    "TypeCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Nature" TEXT NOT NULL,
    "DisplayOrder" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:24:18.392 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Employees" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Employees" PRIMARY KEY AUTOINCREMENT,
    "FullName" TEXT NOT NULL,
    "Nationality" TEXT NOT NULL,
    "ResidenceNumber" TEXT NOT NULL,
    "Position" TEXT NOT NULL,
    "JoinDate" TEXT NOT NULL,
    "LeaveDate" TEXT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Address" TEXT NOT NULL,
    "NationalId" TEXT NOT NULL,
    "BirthDate" TEXT NULL,
    "MaritalStatus" TEXT NOT NULL,
    "NumberOfChildren" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:24:18.392 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FarmInfos" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FarmInfos" PRIMARY KEY AUTOINCREMENT,
    "FarmName" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "ContactInfo" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "Phone" TEXT NOT NULL,
    "Logo" BLOB NULL,
    "Notes" TEXT NOT NULL,
    "SupervisorName" TEXT NOT NULL,
    "SupervisorEmail" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:24:18.392 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedTypes" PRIMARY KEY AUTOINCREMENT,
    "FeedName" TEXT NOT NULL,
    "Brand" TEXT NOT NULL,
    "PricePerKg" decimal(10,2) NOT NULL,
    "Specifications" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:24:18.393 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Inventories" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Inventories" PRIMARY KEY AUTOINCREMENT,
    "ItemName" TEXT NOT NULL,
    "ItemType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "Unit" TEXT NOT NULL,
    "MinimumStock" decimal(10,3) NOT NULL,
    "MaximumStock" decimal(10,3) NOT NULL,
    "ReorderPoint" decimal(10,3) NOT NULL,
    "StorageLocation" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "BatchNumber" TEXT NOT NULL,
    "Supplier" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:24:18.393 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Medications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Medications" PRIMARY KEY AUTOINCREMENT,
    "MedicationName" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "PricePerUnit" decimal(10,2) NOT NULL,
    "Dosage" TEXT NOT NULL,
    "Unit" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "Manufacturer" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:24:18.393 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Notifications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Notifications" PRIMARY KEY AUTOINCREMENT,
    "Title" TEXT NOT NULL,
    "Message" TEXT NOT NULL,
    "Type" INTEGER NOT NULL,
    "Priority" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL,
    "ReadAt" TEXT NULL,
    "ScheduledAt" TEXT NULL,
    "RelatedEntityType" TEXT NULL,
    "RelatedEntityId" INTEGER NULL,
    "ActionUrl" TEXT NULL,
    "IsAutoGenerated" INTEGER NOT NULL,
    "CreatedBy" TEXT NULL,
    "AdditionalData" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:24:18.393 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ProductionCycles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_ProductionCycles" PRIMARY KEY AUTOINCREMENT,
    "CycleName" TEXT NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NULL,
    "ExpectedEndDate" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "BudgetAmount" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:24:18.393 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Roles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Roles" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Permissions" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "IsSystemRole" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:24:18.393 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Users" PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "PhoneNumber" TEXT NULL,
    "Status" TEXT NOT NULL,
    "IsSystemAdmin" INTEGER NOT NULL,
    "MustChangePassword" INTEGER NOT NULL,
    "LastLoginDate" TEXT NULL,
    "FailedLoginAttempts" INTEGER NOT NULL,
    "LockedUntil" TEXT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL
);
2025-07-25 03:24:18.394 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "AccountTypeId" INTEGER NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "AccountNameEn" TEXT NOT NULL,
    "Balance" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "ParentAccountId" INTEGER NULL,
    "Level" INTEGER NOT NULL,
    "IsPostable" INTEGER NOT NULL,
    "Description" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Accounts_AccountTypes_AccountTypeId" FOREIGN KEY ("AccountTypeId") REFERENCES "AccountTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Accounts_Accounts_ParentAccountId" FOREIGN KEY ("ParentAccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT
);
2025-07-25 03:24:18.394 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryMovements" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_InventoryMovements" PRIMARY KEY AUTOINCREMENT,
    "ItemId" INTEGER NOT NULL,
    "MovementDate" TEXT NOT NULL,
    "MovementType" TEXT NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "UnitPrice" decimal(10,2) NOT NULL,
    "TotalValue" decimal(15,2) NOT NULL,
    "QuantityBefore" decimal(10,3) NOT NULL,
    "QuantityAfter" decimal(10,3) NOT NULL,
    "Reason" TEXT NOT NULL,
    "BatchNumber" TEXT NULL,
    "Reference" TEXT NULL,
    "CreatedBy" TEXT NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_InventoryMovements_Inventories_ItemId" FOREIGN KEY ("ItemId") REFERENCES "Inventories" ("Id") ON DELETE RESTRICT
);
2025-07-25 03:24:18.394 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CostCenters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CostCenters" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "CenterName" TEXT NOT NULL,
    "CenterCode" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "AllocatedBudget" decimal(15,2) NOT NULL,
    "ActualSpending" decimal(15,2) NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_CostCenters_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 03:24:18.394 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Payrolls" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Payrolls" PRIMARY KEY AUTOINCREMENT,
    "EmployeeId" INTEGER NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "Year" INTEGER NOT NULL,
    "BaseSalary" decimal(10,2) NOT NULL,
    "Allowances" decimal(10,2) NOT NULL,
    "Deductions" decimal(10,2) NOT NULL,
    "NetSalary" decimal(10,2) NOT NULL,
    "PaymentDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "WorkingDays" INTEGER NOT NULL,
    "AbsenceDays" INTEGER NOT NULL,
    "OvertimeHours" decimal(5,2) NOT NULL,
    "OvertimeRate" decimal(10,2) NOT NULL,
    "PaymentStatus" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Payrolls_Employees_EmployeeId" FOREIGN KEY ("EmployeeId") REFERENCES "Employees" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_Payrolls_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 03:24:18.394 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Ponds" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Ponds" PRIMARY KEY AUTOINCREMENT,
    "PondNumber" TEXT NOT NULL,
    "CycleId" INTEGER NOT NULL,
    "FishCount" INTEGER NOT NULL,
    "AverageWeight" decimal(10,3) NOT NULL,
    "StockingDate" TEXT NOT NULL,
    "ExpectedHarvestDate" TEXT NULL,
    "Status" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Ponds_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 03:24:18.394 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Transactions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Transactions" PRIMARY KEY AUTOINCREMENT,
    "CycleId" INTEGER NOT NULL,
    "TransactionType" TEXT NOT NULL,
    "ReferenceNumber" TEXT NOT NULL,
    "TransactionDate" TEXT NOT NULL,
    "TotalAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "ApprovedBy" TEXT NOT NULL,
    "ApprovalDate" TEXT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_Transactions_ProductionCycles_CycleId" FOREIGN KEY ("CycleId") REFERENCES "ProductionCycles" ("Id") ON DELETE RESTRICT
);
2025-07-25 03:24:18.395 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserRoles" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_UserRoles" PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "GrantedDate" TEXT NOT NULL,
    "ExpiryDate" TEXT NULL,
    "GrantedBy" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "Notes" TEXT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-07-25 03:24:18.395 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FeedConsumptions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FeedConsumptions" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "FeedTypeId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "FeedingDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FeedConsumptions_FeedTypes_FeedTypeId" FOREIGN KEY ("FeedTypeId") REFERENCES "FeedTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_FeedConsumptions_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-25 03:24:18.395 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "FishMortalities" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_FishMortalities" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "DeadFishCount" INTEGER NOT NULL,
    "MortalityDate" TEXT NOT NULL,
    "Cause" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "EstimatedWeight" decimal(10,3) NOT NULL,
    "EstimatedLoss" decimal(10,2) NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_FishMortalities_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-25 03:24:18.395 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "PondMedications" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_PondMedications" PRIMARY KEY AUTOINCREMENT,
    "PondId" INTEGER NOT NULL,
    "MedicationId" INTEGER NOT NULL,
    "Quantity" decimal(10,3) NOT NULL,
    "ApplicationDate" TEXT NOT NULL,
    "Cost" decimal(10,2) NOT NULL,
    "Notes" TEXT NOT NULL,
    "ReasonForUse" TEXT NOT NULL,
    "VeterinarianName" TEXT NOT NULL,
    "WithdrawalPeriodDays" INTEGER NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_PondMedications_Medications_MedicationId" FOREIGN KEY ("MedicationId") REFERENCES "Medications" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_PondMedications_Ponds_PondId" FOREIGN KEY ("PondId") REFERENCES "Ponds" ("Id") ON DELETE CASCADE
);
2025-07-25 03:24:18.395 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TransactionDetails" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_TransactionDetails" PRIMARY KEY AUTOINCREMENT,
    "TransactionId" INTEGER NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "DebitAmount" decimal(15,2) NOT NULL,
    "CreditAmount" decimal(15,2) NOT NULL,
    "Description" TEXT NOT NULL,
    "LineNumber" INTEGER NOT NULL,
    "CostCenterId" INTEGER NULL,
    "Notes" TEXT NOT NULL,
    "CreatedDate" TEXT NOT NULL,
    "UpdatedDate" TEXT NULL,
    CONSTRAINT "FK_TransactionDetails_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TransactionDetails_CostCenters_CostCenterId" FOREIGN KEY ("CostCenterId") REFERENCES "CostCenters" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TransactionDetails_Transactions_TransactionId" FOREIGN KEY ("TransactionId") REFERENCES "Transactions" ("Id") ON DELETE CASCADE
);
2025-07-25 03:24:18.396 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (1, '2025-07-25 03:23:43.5577613', 'الأصول الثابتة والمتداولة', 1, 'مدين', '1', 'الأصول', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (2, '2025-07-25 03:23:43.5577615', 'الالتزامات والديون', 2, 'دائن', '2', 'الخصوم', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (3, '2025-07-25 03:23:43.5577616', 'رأس المال والأرباح المحتجزة', 3, 'دائن', '3', 'حقوق الملكية', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (4, '2025-07-25 03:23:43.5577617', 'إيرادات المبيعات والخدمات', 4, 'دائن', '4', 'الإيرادات', NULL);
SELECT changes();

INSERT INTO "AccountTypes" ("Id", "CreatedDate", "Description", "DisplayOrder", "Nature", "TypeCode", "TypeName", "UpdatedDate")
VALUES (5, '2025-07-25 03:23:43.5577619', 'مصروفات التشغيل والإدارة', 5, 'مدين', '5', 'المصروفات', NULL);
SELECT changes();
2025-07-25 03:24:18.396 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "FarmInfos" ("Id", "ContactInfo", "CreatedDate", "Email", "FarmName", "Location", "Logo", "Notes", "Phone", "SupervisorEmail", "SupervisorName", "UpdatedDate")
VALUES (1, '', '2025-07-25 03:23:43.5578114', '<EMAIL>', 'مزرعة الأسماك النموذجية', 'المملكة العربية السعودية', NULL, '', '+************', '<EMAIL>', 'طارق حسين صالح', NULL);
SELECT changes();
2025-07-25 03:24:18.396 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (1, '2025-07-25 03:23:43.5579083', 'مدير النظام - صلاحيات كاملة', 1, 1, 'مدير النظام', '["system.management","user.management","role.management","system.settings","database.management","farm.info.management","pond.management","production.cycle.management","employee.management","payroll.management","accounting.management","transaction.management","financial.reports","inventory.management","inventory.reports","production.reports","employee.reports","general.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (2, '2025-07-25 03:23:43.5579259', 'مدير المزرعة - إدارة العمليات اليومية', 1, 1, 'مدير المزرعة', '["farm.info.management","pond.management","production.cycle.management","employee.management","inventory.management","inventory.reports","production.reports","employee.reports","feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (3, '2025-07-25 03:23:43.5579316', 'محاسب - إدارة الحسابات والتقارير المالية', 1, 1, 'محاسب', '["accounting.management","transaction.management","financial.reports","payroll.management","inventory.reports","view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (4, '2025-07-25 03:23:43.5579369', 'عامل أحواض - تسجيل العمليات اليومية', 1, 1, 'عامل أحواض', '["feeding.records","water.quality.records","weight.records","mortality.records","medication.records","view.dashboard","view.data"]', NULL);
SELECT changes();

INSERT INTO "Roles" ("Id", "CreatedDate", "Description", "IsActive", "IsSystemRole", "Name", "Permissions", "UpdatedDate")
VALUES (5, '2025-07-25 03:23:43.5579421', 'مشاهد - عرض البيانات والتقارير فقط', 1, 1, 'مشاهد', '["view.dashboard","view.reports","view.data"]', NULL);
SELECT changes();
2025-07-25 03:24:18.396 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedDate", "Email", "FailedLoginAttempts", "FullName", "IsSystemAdmin", "LastLoginDate", "LockedUntil", "MustChangePassword", "Notes", "PasswordHash", "PhoneNumber", "Status", "UpdatedDate", "Username")
VALUES (1, '2025-07-25 03:23:43.5579563', '<EMAIL>', 0, 'مدير النظام', 1, NULL, NULL, 0, NULL, '$2a$11$7cmnBROsoIIWjpZehiWLX.QdfD7G4EF6345gcRFVgltLsllbmplee', NULL, 'نشط', NULL, 'admin');
SELECT changes();
2025-07-25 03:24:18.396 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (1, '1001', 'النقدية', 'Cash', 1, '0.0', '2025-07-25 03:23:43.5577911', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (2, '1002', 'البنك', 'Bank', 1, '0.0', '2025-07-25 03:23:43.5577913', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (3, '1003', 'المخزون', 'Inventory', 1, '0.0', '2025-07-25 03:23:43.5577914', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (4, '1004', 'الأصول الثابتة', 'Fixed Assets', 1, '0.0', '2025-07-25 03:23:43.5577915', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (5, '2001', 'الموردون', 'Suppliers', 2, '0.0', '2025-07-25 03:23:43.5577917', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (6, '2002', 'رواتب مستحقة', 'Accrued Salaries', 2, '0.0', '2025-07-25 03:23:43.5577918', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (7, '3001', 'رأس المال', 'Capital', 3, '0.0', '2025-07-25 03:23:43.5577919', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (8, '3002', 'الأرباح المحتجزة', 'Retained Earnings', 3, '0.0', '2025-07-25 03:23:43.557792', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (9, '4001', 'مبيعات الأسماك', 'Fish Sales', 4, '0.0', '2025-07-25 03:23:43.5577921', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (10, '5001', 'مصروفات العلف', 'Feed Expenses', 5, '0.0', '2025-07-25 03:23:43.5577922', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (11, '5002', 'مصروفات الرواتب', 'Salary Expenses', 5, '0.0', '2025-07-25 03:23:43.5577923', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (12, '5003', 'مصروفات الأدوية', 'Medicine Expenses', 5, '0.0', '2025-07-25 03:23:43.5577924', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();

INSERT INTO "Accounts" ("Id", "AccountCode", "AccountName", "AccountNameEn", "AccountTypeId", "Balance", "CreatedDate", "Description", "IsPostable", "Level", "ParentAccountId", "Status", "UpdatedDate")
VALUES (13, '5004', 'مصروفات عامة', 'General Expenses', 5, '0.0', '2025-07-25 03:23:43.5577925', '', 1, 1, NULL, 'نشط', NULL);
SELECT changes();
2025-07-25 03:24:18.397 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedDate", "ExpiryDate", "GrantedBy", "GrantedDate", "IsActive", "Notes", "RoleId", "UpdatedDate", "UserId")
VALUES (1, '2025-07-25 03:23:44.2291633', NULL, 'System', '2025-07-25 03:23:44.2291631', 1, NULL, 1, NULL, 1);
SELECT changes();
2025-07-25 03:24:18.397 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_AccountCode" ON "Accounts" ("AccountCode");
2025-07-25 03:24:18.397 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_AccountTypeId" ON "Accounts" ("AccountTypeId");
2025-07-25 03:24:18.397 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Accounts_ParentAccountId" ON "Accounts" ("ParentAccountId");
2025-07-25 03:24:18.398 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CostCenters_CycleId" ON "CostCenters" ("CycleId");
2025-07-25 03:24:18.398 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Employees_NationalId" ON "Employees" ("NationalId");
2025-07-25 03:24:18.398 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedingDate" ON "FeedConsumptions" ("FeedingDate");
2025-07-25 03:24:18.398 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_FeedTypeId" ON "FeedConsumptions" ("FeedTypeId");
2025-07-25 03:24:18.398 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FeedConsumptions_PondId" ON "FeedConsumptions" ("PondId");
2025-07-25 03:24:18.398 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_MortalityDate" ON "FishMortalities" ("MortalityDate");
2025-07-25 03:24:18.399 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_FishMortalities_PondId" ON "FishMortalities" ("PondId");
2025-07-25 03:24:18.399 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_ItemId_MovementDate" ON "InventoryMovements" ("ItemId", "MovementDate");
2025-07-25 03:24:18.399 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_InventoryMovements_MovementDate" ON "InventoryMovements" ("MovementDate");
2025-07-25 03:24:18.399 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Payrolls_CycleId" ON "Payrolls" ("CycleId");
2025-07-25 03:24:18.399 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Payrolls_EmployeeId_Month_Year" ON "Payrolls" ("EmployeeId", "Month", "Year");
2025-07-25 03:24:18.399 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_MedicationId" ON "PondMedications" ("MedicationId");
2025-07-25 03:24:18.399 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_PondMedications_PondId" ON "PondMedications" ("PondId");
2025-07-25 03:24:18.399 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Ponds_CycleId" ON "Ponds" ("CycleId");
2025-07-25 03:24:18.400 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Ponds_PondNumber" ON "Ponds" ("PondNumber");
2025-07-25 03:24:18.400 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Roles_Name" ON "Roles" ("Name");
2025-07-25 03:24:18.400 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_AccountId" ON "TransactionDetails" ("AccountId");
2025-07-25 03:24:18.400 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_CostCenterId" ON "TransactionDetails" ("CostCenterId");
2025-07-25 03:24:18.400 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TransactionDetails_TransactionId" ON "TransactionDetails" ("TransactionId");
2025-07-25 03:24:18.400 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Transactions_CycleId" ON "Transactions" ("CycleId");
2025-07-25 03:24:18.401 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Transactions_ReferenceNumber" ON "Transactions" ("ReferenceNumber");
2025-07-25 03:24:18.401 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserRoles_RoleId" ON "UserRoles" ("RoleId");
2025-07-25 03:24:18.401 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserRoles_UserId_RoleId" ON "UserRoles" ("UserId", "RoleId");
2025-07-25 03:24:18.401 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-07-25 03:24:18.402 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "Users" ("Username");
2025-07-25 03:24:18.402 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250725002348_InitialCreate', '8.0.0');
