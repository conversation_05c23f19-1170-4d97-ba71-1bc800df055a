using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة الإحصائيات
    /// Statistics service
    /// </summary>
    public class StatisticsService : IStatisticsService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<StatisticsService> _logger;

        public StatisticsService(IUnitOfWork unitOfWork, ILogger<StatisticsService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<DashboardStatistics> GetDashboardStatisticsAsync()
        {
            try
            {
                var ponds = await _unitOfWork.Ponds.GetAllAsync();
                var cycles = await _unitOfWork.ProductionCycles.GetAllAsync();
                var employees = await _unitOfWork.Employees.GetAllAsync();
                var transactions = await _unitOfWork.Transactions.GetAllAsync();
                var inventory = await _unitOfWork.Inventories.GetAllAsync();

                var today = DateTime.Today;
                var startOfMonth = new DateTime(today.Year, today.Month, 1);

                var statistics = new DashboardStatistics
                {
                    ActivePonds = ponds.Count(p => p.Status == PondStatus.Active),
                    ActiveCycles = cycles.Count(c => c.Status == CycleStatus.Active),
                    TotalEmployees = employees.Count(e => e.Status == EmployeeStatus.Active),
                    TodayTransactions = transactions.Count(t => t.TransactionDate.Date == today),
                    TotalInventoryValue = inventory.Where(i => i.Status == InventoryStatus.Active)
                                                  .Sum(i => i.Quantity * i.UnitPrice),
                    LowStockItems = inventory.Count(i => i.Quantity <= i.MinimumStock && 
                                                        i.Status == InventoryStatus.Active),
                    MonthlyRevenue = await CalculateMonthlyRevenueAsync(startOfMonth),
                    MonthlyExpenses = await CalculateMonthlyExpensesAsync(startOfMonth)
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على إحصائيات لوحة المعلومات");
                throw;
            }
        }

        public async Task<PondStatistics> GetPondStatisticsAsync()
        {
            try
            {
                var ponds = await _unitOfWork.Ponds.GetAllAsync();

                var statistics = new PondStatistics
                {
                    TotalPonds = ponds.Count(),
                    ActivePonds = ponds.Count(p => p.Status == PondStatus.Active),
                    InactivePonds = ponds.Count(p => p.Status == PondStatus.Inactive),
                    UnderMaintenancePonds = ponds.Count(p => p.Status == PondStatus.UnderMaintenance),
                    AverageCapacity = ponds.Any() ? (decimal)ponds.Average(p => p.FishCount) : 0,
                    TotalCapacity = ponds.Sum(p => p.FishCount)
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على إحصائيات الأحواض");
                throw;
            }
        }

        public async Task<EmployeeStatistics> GetEmployeeStatisticsAsync()
        {
            try
            {
                var employees = await _unitOfWork.Employees.GetAllAsync();

                var statistics = new EmployeeStatistics
                {
                    TotalEmployees = employees.Count(),
                    ActiveEmployees = employees.Count(e => e.Status == EmployeeStatus.Active),
                    InactiveEmployees = employees.Count(e => e.Status != EmployeeStatus.Active),
                    TotalMonthlySalaries = employees.Where(e => e.Status == EmployeeStatus.Active)
                                                  .Sum(e => e.BaseSalary),
                    AverageSalary = employees.Where(e => e.Status == EmployeeStatus.Active).Any() 
                                   ? employees.Where(e => e.Status == EmployeeStatus.Active)
                                            .Average(e => e.BaseSalary) : 0
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على إحصائيات الموظفين");
                throw;
            }
        }

        public async Task<InventoryStatistics> GetInventoryStatisticsAsync()
        {
            try
            {
                var inventory = await _unitOfWork.Inventories.GetAllAsync();

                var statistics = new InventoryStatistics
                {
                    TotalItems = inventory.Count(),
                    ActiveItems = inventory.Count(i => i.Status == InventoryStatus.Active),
                    LowStockItems = inventory.Count(i => i.Quantity <= i.MinimumStock && 
                                                        i.Status == InventoryStatus.Active),
                    ExpiredItems = inventory.Count(i => i.ExpiryDate.HasValue && 
                                                       i.ExpiryDate.Value < DateTime.Now && 
                                                       i.Status == InventoryStatus.Active),
                    TotalValue = inventory.Where(i => i.Status == InventoryStatus.Active)
                                         .Sum(i => i.Quantity * i.UnitPrice),
                    AverageItemValue = inventory.Where(i => i.Status == InventoryStatus.Active).Any()
                                      ? inventory.Where(i => i.Status == InventoryStatus.Active)
                                                .Average(i => i.Quantity * i.UnitPrice) : 0
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على إحصائيات المخزون");
                throw;
            }
        }

        public async Task<FinancialStatistics> GetFinancialStatisticsAsync()
        {
            try
            {
                var today = DateTime.Today;
                var startOfMonth = new DateTime(today.Year, today.Month, 1);
                var startOfYear = new DateTime(today.Year, 1, 1);

                var statistics = new FinancialStatistics
                {
                    MonthlyRevenue = await CalculateMonthlyRevenueAsync(startOfMonth),
                    MonthlyExpenses = await CalculateMonthlyExpensesAsync(startOfMonth),
                    YearlyRevenue = await CalculateYearlyRevenueAsync(startOfYear),
                    YearlyExpenses = await CalculateYearlyExpensesAsync(startOfYear)
                };

                statistics.MonthlyProfit = statistics.MonthlyRevenue - statistics.MonthlyExpenses;
                statistics.YearlyProfit = statistics.YearlyRevenue - statistics.YearlyExpenses;

                // Calculate assets and liabilities from accounts
                var accounts = await _unitOfWork.Accounts.GetAllAsync();
                statistics.TotalAssets = accounts.Where(a => a.AccountType.TypeName.Contains("أصول"))
                                               .Sum(a => a.Balance);
                statistics.TotalLiabilities = accounts.Where(a => a.AccountType.TypeName.Contains("التزامات"))
                                                    .Sum(a => a.Balance);

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الإحصائيات المالية");
                throw;
            }
        }

        private async Task<decimal> CalculateMonthlyRevenueAsync(DateTime startOfMonth)
        {
            var transactions = await _unitOfWork.Transactions.FindAsync(t => 
                t.TransactionDate >= startOfMonth && 
                t.TransactionDate < startOfMonth.AddMonths(1));

            return transactions.Where(t => t.TransactionDetails.Any(td => td.CreditAmount > 0))
                             .SelectMany(t => t.TransactionDetails)
                             .Sum(td => td.CreditAmount);
        }

        private async Task<decimal> CalculateMonthlyExpensesAsync(DateTime startOfMonth)
        {
            var transactions = await _unitOfWork.Transactions.FindAsync(t => 
                t.TransactionDate >= startOfMonth && 
                t.TransactionDate < startOfMonth.AddMonths(1));

            return transactions.Where(t => t.TransactionDetails.Any(td => td.DebitAmount > 0))
                             .SelectMany(t => t.TransactionDetails)
                             .Sum(td => td.DebitAmount);
        }

        private async Task<decimal> CalculateYearlyRevenueAsync(DateTime startOfYear)
        {
            var transactions = await _unitOfWork.Transactions.FindAsync(t => 
                t.TransactionDate >= startOfYear && 
                t.TransactionDate < startOfYear.AddYears(1));

            return transactions.Where(t => t.TransactionDetails.Any(td => td.CreditAmount > 0))
                             .SelectMany(t => t.TransactionDetails)
                             .Sum(td => td.CreditAmount);
        }

        private async Task<decimal> CalculateYearlyExpensesAsync(DateTime startOfYear)
        {
            var transactions = await _unitOfWork.Transactions.FindAsync(t => 
                t.TransactionDate >= startOfYear && 
                t.TransactionDate < startOfYear.AddYears(1));

            return transactions.Where(t => t.TransactionDetails.Any(td => td.DebitAmount > 0))
                             .SelectMany(t => t.TransactionDetails)
                             .Sum(td => td.DebitAmount);
        }
    }
}
