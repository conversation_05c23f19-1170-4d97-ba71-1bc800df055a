using System.ComponentModel.DataAnnotations;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// معاملة شراء دواء
    /// Medication purchase transaction
    /// </summary>
    public class MedicationPurchaseTransaction
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// معرف الدورة الإنتاجية
        /// Production cycle ID
        /// </summary>
        public int CycleId { get; set; }

        /// <summary>
        /// الدورة الإنتاجية
        /// Production cycle
        /// </summary>
        public ProductionCycle Cycle { get; set; } = null!;

        /// <summary>
        /// معرف الدواء
        /// Medication ID
        /// </summary>
        public int MedicationId { get; set; }

        /// <summary>
        /// الدواء
        /// Medication
        /// </summary>
        public Medication Medication { get; set; } = null!;

        /// <summary>
        /// تاريخ الشراء
        /// Purchase date
        /// </summary>
        public DateTime PurchaseDate { get; set; }

        /// <summary>
        /// رقم الفاتورة
        /// Invoice number
        /// </summary>
        public string InvoiceNumber { get; set; } = string.Empty;

        /// <summary>
        /// اسم المورد
        /// Supplier name
        /// </summary>
        public string SupplierName { get; set; } = string.Empty;

        /// <summary>
        /// الكمية المشتراة
        /// Quantity purchased
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// الوحدة
        /// Unit
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// سعر الوحدة
        /// Unit price
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// المبلغ الإجمالي
        /// Total amount
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// هل الدفع نقدي
        /// Is cash payment
        /// </summary>
        public bool IsCashPayment { get; set; } = true;

        /// <summary>
        /// طريقة الدفع
        /// Payment method
        /// </summary>
        public string PaymentMethod { get; set; } = "نقدي";

        /// <summary>
        /// تاريخ الاستحقاق (في حالة الآجل)
        /// Due date (for credit purchases)
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// الحالة
        /// Status
        /// </summary>
        public string Status { get; set; } = "مكتمل";

        /// <summary>
        /// ملاحظات
        /// Notes
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// Creation date
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// المستخدم المنشئ
        /// Created by user
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;
    }
}
