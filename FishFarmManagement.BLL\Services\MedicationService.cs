using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة إدارة الأدوية
    /// Medication management service
    /// </summary>
    public class MedicationService : IMedicationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<MedicationService> _logger;

        public MedicationService(IUnitOfWork unitOfWork, ILogger<MedicationService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<Medication>> GetAllAsync()
        {
            try
            {
                return await _unitOfWork.Medications.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على جميع الأدوية");
                throw;
            }
        }

        public async Task<Medication?> GetByIdAsync(int id)
        {
            try
            {
                return await _unitOfWork.Medications.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الدواء {MedicationId}", id);
                throw;
            }
        }

        public async Task<Medication> AddAsync(Medication entity)
        {
            try
            {
                var validationResult = await ValidateAsync(entity);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"بيانات الدواء غير صحيحة: {string.Join(", ", validationResult.Errors)}");
                }

                entity.CreatedDate = DateTime.Now;

                var result = await _unitOfWork.Medications.AddAsync(entity);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إضافة دواء جديد: {MedicationName}", entity.MedicationName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة الدواء {MedicationName}", entity.MedicationName);
                throw;
            }
        }

        public async Task<Medication> UpdateAsync(Medication entity)
        {
            try
            {
                var validationResult = await ValidateAsync(entity);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"بيانات الدواء غير صحيحة: {string.Join(", ", validationResult.Errors)}");
                }

                entity.UpdatedDate = DateTime.Now;

                var result = await _unitOfWork.Medications.UpdateAsync(entity);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث الدواء: {MedicationName}", entity.MedicationName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الدواء {MedicationId}", entity.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var medication = await _unitOfWork.Medications.GetByIdAsync(id);
                if (medication == null)
                {
                    return false;
                }

                // Check if medication is used in pond medication records
                var hasUsages = await _unitOfWork.PondMedications.ExistsAsync(pm => pm.MedicationId == id);
                if (hasUsages)
                {
                    throw new InvalidOperationException("لا يمكن حذف الدواء لوجود سجلات استخدام مرتبطة به");
                }

                await _unitOfWork.Medications.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم حذف الدواء: {MedicationName}", medication.MedicationName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الدواء {MedicationId}", id);
                throw;
            }
        }

        public async Task<ValidationResult> ValidateAsync(Medication entity)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(entity.MedicationName))
            {
                result.AddError("اسم الدواء مطلوب");
            }

            if (string.IsNullOrWhiteSpace(entity.Type))
            {
                result.AddError("نوع الدواء مطلوب");
            }

            if (entity.PricePerUnit <= 0)
            {
                result.AddError("سعر الوحدة يجب أن يكون أكبر من الصفر");
            }

            // Remove withdrawal period validation as it's not in the Medication model
            // It's in PondMedication model instead

            if (entity.ExpiryDate.HasValue && entity.ExpiryDate.Value <= DateTime.Now)
            {
                result.AddError("تاريخ انتهاء الصلاحية يجب أن يكون في المستقبل");
            }

            // Check for duplicate medication name
            var existingMedication = await _unitOfWork.Medications
                .FindAsync(m => m.MedicationName == entity.MedicationName && m.Id != entity.Id);
            if (existingMedication.Any())
            {
                result.AddError("اسم الدواء مستخدم من قبل");
            }

            return result;
        }

        public async Task<IEnumerable<Medication>> GetActiveMedicationsAsync()
        {
            try
            {
                return await _unitOfWork.Medications.FindAsync(m => 
                    (!m.ExpiryDate.HasValue || m.ExpiryDate.Value > DateTime.Now));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأدوية النشطة");
                throw;
            }
        }

        public async Task<IEnumerable<Medication>> GetExpiredMedicationsAsync()
        {
            try
            {
                return await _unitOfWork.Medications.FindAsync(m => 
                    m.ExpiryDate.HasValue && m.ExpiryDate.Value <= DateTime.Now);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأدوية منتهية الصلاحية");
                throw;
            }
        }

        public async Task<IEnumerable<Medication>> GetMedicationsExpiringSoonAsync(int daysAhead = 30)
        {
            try
            {
                var futureDate = DateTime.Now.AddDays(daysAhead);
                return await _unitOfWork.Medications.FindAsync(m => 
                    m.ExpiryDate.HasValue && 
                    m.ExpiryDate.Value > DateTime.Now && 
                    m.ExpiryDate.Value <= futureDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأدوية قريبة انتهاء الصلاحية");
                throw;
            }
        }

        public async Task<IEnumerable<Medication>> SearchMedicationsAsync(string searchTerm)
        {
            try
            {
                return await _unitOfWork.Medications.FindAsync(m =>
                    m.MedicationName.Contains(searchTerm) ||
                    m.Type.Contains(searchTerm) ||
                    (m.Description != null && m.Description.Contains(searchTerm)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن الأدوية بالكلمة {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<IEnumerable<Medication>> GetMedicationsByTypeAsync(string medicationType)
        {
            try
            {
                return await _unitOfWork.Medications.FindAsync(m => m.Type == medicationType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأدوية بالنوع {MedicationType}", medicationType);
                throw;
            }
        }

        public async Task<PondMedication> AddPondMedicationAsync(int pondId, int medicationId, 
            decimal quantity, DateTime applicationDate, string reasonForUse, 
            string? veterinarianName = null, int withdrawalPeriodDays = 0, string? notes = null)
        {
            try
            {
                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                if (pond == null)
                {
                    throw new InvalidOperationException("الحوض غير موجود");
                }

                var medication = await _unitOfWork.Medications.GetByIdAsync(medicationId);
                if (medication == null)
                {
                    throw new InvalidOperationException("الدواء غير موجود");
                }

                var pondMedication = new PondMedication
                {
                    PondId = pondId,
                    MedicationId = medicationId,
                    Quantity = quantity,
                    ApplicationDate = applicationDate,
                    ReasonForUse = reasonForUse,
                    VeterinarianName = veterinarianName,
                    WithdrawalPeriodDays = withdrawalPeriodDays,
                    Notes = notes,
                    CreatedDate = DateTime.Now
                };

                var result = await _unitOfWork.PondMedications.AddAsync(pondMedication);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إضافة استخدام دواء للحوض {PondNumber} - الدواء: {MedicationName}", 
                    pond.PondNumber, medication.MedicationName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة استخدام الدواء للحوض {PondId}", pondId);
                throw;
            }
        }

        public async Task<IEnumerable<PondMedication>> GetPondMedicationsAsync(int pondId, 
            DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var query = await _unitOfWork.PondMedications.FindAsync(pm => pm.PondId == pondId);
                
                if (startDate.HasValue)
                {
                    query = query.Where(pm => pm.ApplicationDate >= startDate.Value);
                }
                
                if (endDate.HasValue)
                {
                    query = query.Where(pm => pm.ApplicationDate <= endDate.Value);
                }

                return query.OrderByDescending(pm => pm.ApplicationDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على أدوية الحوض {PondId}", pondId);
                throw;
            }
        }

        public async Task<IEnumerable<PondMedication>> GetCycleMedicationsAsync(int cycleId,
            DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var query = await _unitOfWork.PondMedications.FindAsync(pm => pm.Pond.CycleId == cycleId);

                if (startDate.HasValue)
                {
                    query = query.Where(pm => pm.ApplicationDate >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(pm => pm.ApplicationDate <= endDate.Value);
                }

                return query.OrderByDescending(pm => pm.ApplicationDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على أدوية الدورة {CycleId}", cycleId);
                throw;
            }
        }

        public async Task<decimal> CalculateTotalMedicationCostAsync(int? pondId = null,
            int? cycleId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                IEnumerable<PondMedication> medications;

                if (pondId.HasValue)
                {
                    medications = await GetPondMedicationsAsync(pondId.Value, startDate, endDate);
                }
                else if (cycleId.HasValue)
                {
                    medications = await GetCycleMedicationsAsync(cycleId.Value, startDate, endDate);
                }
                else
                {
                    var query = await _unitOfWork.PondMedications.GetAllAsync();
                    medications = query;

                    if (startDate.HasValue)
                    {
                        medications = medications.Where(pm => pm.ApplicationDate >= startDate.Value);
                    }

                    if (endDate.HasValue)
                    {
                        medications = medications.Where(pm => pm.ApplicationDate <= endDate.Value);
                    }
                }

                return medications.Sum(pm => pm.Quantity * pm.Medication.PricePerUnit);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إجمالي تكلفة الأدوية");
                throw;
            }
        }

        public async Task<MedicationUsageReport> GetMedicationUsageReportAsync(
            DateTime startDate, DateTime endDate, int? pondId = null)
        {
            try
            {
                var medications = pondId.HasValue ?
                    await GetPondMedicationsAsync(pondId.Value, startDate, endDate) :
                    await _unitOfWork.PondMedications.FindAsync(pm =>
                        pm.ApplicationDate >= startDate && pm.ApplicationDate <= endDate);

                var medicationsList = medications.ToList();

                var report = new MedicationUsageReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    TotalMedicationCost = medicationsList.Sum(pm => pm.Quantity * pm.Medication.PricePerUnit),
                    TotalApplications = medicationsList.Count,
                    MedicationSummaries = new List<MedicationUsageSummary>(),
                    PondSummaries = new List<PondMedicationSummary>(),
                    ReasonSummaries = new List<MedicationByReason>()
                };

                // Group by medication
                var medicationGroups = medicationsList.GroupBy(pm => pm.MedicationId);
                foreach (var group in medicationGroups)
                {
                    var medication = group.First().Medication;
                    var totalQuantity = group.Sum(pm => pm.Quantity);
                    var totalCost = group.Sum(pm => pm.Quantity * pm.Medication.PricePerUnit);

                    report.MedicationSummaries.Add(new MedicationUsageSummary
                    {
                        MedicationId = group.Key,
                        MedicationName = medication.MedicationName,
                        Type = medication.Type,
                        TotalQuantityUsed = totalQuantity,
                        TotalCost = totalCost,
                        ApplicationCount = group.Count(),
                        AverageQuantityPerApplication = group.Count() > 0 ? totalQuantity / group.Count() : 0
                    });
                }

                // Group by pond
                var pondGroups = medicationsList.GroupBy(pm => pm.PondId);
                foreach (var group in pondGroups)
                {
                    var pond = group.First().Pond;
                    var totalCost = group.Sum(pm => pm.Quantity * pm.Medication.PricePerUnit);
                    var medicationsUsed = group.Select(pm => pm.Medication.MedicationName).Distinct().ToList();

                    report.PondSummaries.Add(new PondMedicationSummary
                    {
                        PondId = group.Key,
                        PondNumber = pond.PondNumber,
                        TotalMedicationCost = totalCost,
                        TotalApplications = group.Count(),
                        MedicationsUsed = medicationsUsed
                    });
                }

                // Group by reason
                var reasonGroups = medicationsList.GroupBy(pm => pm.ReasonForUse);
                foreach (var group in reasonGroups)
                {
                    var totalCost = group.Sum(pm => pm.Quantity * pm.Medication.PricePerUnit);

                    report.ReasonSummaries.Add(new MedicationByReason
                    {
                        Reason = group.Key,
                        TotalCost = totalCost,
                        ApplicationCount = group.Count(),
                        Percentage = report.TotalApplications > 0 ?
                            (decimal)group.Count() / report.TotalApplications * 100 : 0
                    });
                }

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير استخدام الأدوية");
                throw;
            }
        }

        public async Task<WithdrawalPeriodCheck> CheckWithdrawalPeriodAsync(int pondId, DateTime harvestDate)
        {
            try
            {
                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                if (pond == null)
                {
                    throw new InvalidOperationException("الحوض غير موجود");
                }

                var medications = await GetPondMedicationsAsync(pondId);

                var check = new WithdrawalPeriodCheck
                {
                    PondId = pondId,
                    PondNumber = pond.PondNumber,
                    ProposedHarvestDate = harvestDate,
                    CanHarvest = true,
                    Violations = new List<WithdrawalPeriodViolation>()
                };

                foreach (var medication in medications)
                {
                    var safeHarvestDate = medication.ApplicationDate.AddDays(medication.WithdrawalPeriodDays);

                    if (harvestDate < safeHarvestDate)
                    {
                        check.CanHarvest = false;
                        check.Violations.Add(new WithdrawalPeriodViolation
                        {
                            MedicationId = medication.MedicationId,
                            MedicationName = medication.Medication.MedicationName,
                            ApplicationDate = medication.ApplicationDate,
                            WithdrawalPeriodDays = medication.WithdrawalPeriodDays,
                            SafeHarvestDate = safeHarvestDate,
                            DaysRemaining = (safeHarvestDate - harvestDate).Days
                        });
                    }
                }

                if (check.Violations.Any())
                {
                    check.EarliestSafeHarvestDate = check.Violations.Max(v => v.SafeHarvestDate);
                }

                return check;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص فترة السحب للحوض {PondId}", pondId);
                throw;
            }
        }

        public async Task<MedicationAlerts> GetMedicationAlertsAsync()
        {
            try
            {
                var alerts = new MedicationAlerts
                {
                    ExpiredMedications = (await GetExpiredMedicationsAsync()).ToList(),
                    ExpiringSoonMedications = (await GetMedicationsExpiringSoonAsync()).ToList(),
                    WithdrawalPeriodAlerts = new List<WithdrawalPeriodAlert>(),
                    LowStockAlerts = new List<LowStockAlert>()
                };

                // Check withdrawal periods for active ponds
                var activePonds = await _unitOfWork.Ponds.FindAsync(p => p.Status == "نشط");
                foreach (var pond in activePonds)
                {
                    if (pond.ExpectedHarvestDate.HasValue)
                    {
                        var check = await CheckWithdrawalPeriodAsync(pond.Id, pond.ExpectedHarvestDate.Value);
                        foreach (var violation in check.Violations)
                        {
                            alerts.WithdrawalPeriodAlerts.Add(new WithdrawalPeriodAlert
                            {
                                PondId = pond.Id,
                                PondNumber = pond.PondNumber,
                                MedicationName = violation.MedicationName,
                                ApplicationDate = violation.ApplicationDate,
                                SafeHarvestDate = violation.SafeHarvestDate,
                                DaysRemaining = violation.DaysRemaining
                            });
                        }
                    }
                }

                // Check low stock (this would require inventory integration)
                // For now, returning empty list

                return alerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تنبيهات الأدوية");
                throw;
            }
        }

        public async Task<bool> UpdateMedicationPriceAsync(int medicationId, decimal newPrice)
        {
            try
            {
                var medication = await _unitOfWork.Medications.GetByIdAsync(medicationId);
                if (medication == null)
                {
                    return false;
                }

                medication.PricePerUnit = newPrice;
                medication.UpdatedDate = DateTime.Now;

                await _unitOfWork.Medications.UpdateAsync(medication);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث سعر الدواء {MedicationName} إلى {NewPrice}",
                    medication.MedicationName, newPrice);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث سعر الدواء {MedicationId}", medicationId);
                throw;
            }
        }

        public async Task<bool> UpdateExpiryDateAsync(int medicationId, DateTime newExpiryDate)
        {
            try
            {
                var medication = await _unitOfWork.Medications.GetByIdAsync(medicationId);
                if (medication == null)
                {
                    return false;
                }

                medication.ExpiryDate = newExpiryDate;
                medication.UpdatedDate = DateTime.Now;

                await _unitOfWork.Medications.UpdateAsync(medication);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث تاريخ انتهاء صلاحية الدواء {MedicationName} إلى {NewExpiryDate}",
                    medication.MedicationName, newExpiryDate);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث تاريخ انتهاء الصلاحية للدواء {MedicationId}", medicationId);
                throw;
            }
        }
    }
}
