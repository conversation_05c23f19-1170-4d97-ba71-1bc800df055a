﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج إضافة/تعديل القيود المحاسبية
    /// Transaction add/edit form
    /// </summary>
    public partial class TransactionAddEditForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;
        private readonly Transaction? _existingTransaction;
        private readonly bool _isEditMode;

        public TransactionAddEditForm(IUnitOfWork unitOfWork, ILogger logger, Transaction? existingTransaction = null)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _existingTransaction = existingTransaction;
            _isEditMode = existingTransaction != null;

            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = _isEditMode ? "تعديل قيد محاسبي" : "إضافة قيد محاسبي جديد";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            var label = new Label
            {
                Text = "نموذج القيود المحاسبية - قيد التطوير",
                Font = new Font("Segoe UI", 16F),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            this.Controls.Add(label);
        }
    }
}

