using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// المعاملة المحاسبية
    /// Accounting transaction
    /// </summary>
    public enum TransactionStatus
    {
        Draft,      // مسودة
        Posted,     // مرحل
        Cancelled   // ملغي
    }

    public class Transaction : BaseEntity
    {
        [Required(ErrorMessage = "معرف الدورة الإنتاجية مطلوب")]
        public int CycleId { get; set; }

        [Required(ErrorMessage = "نوع المعاملة مطلوب")]
        [StringLength(50, ErrorMessage = "نوع المعاملة يجب أن يكون أقل من 50 حرف")]
        public string TransactionType { get; set; } = string.Empty; // شراء، بيع، راتب، مصروف عام

        [Required(ErrorMessage = "الرقم المرجعي مطلوب")]
        [StringLength(50, ErrorMessage = "الرقم المرجعي يجب أن يكون أقل من 50 حرف")]
        public string ReferenceNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime TransactionDate { get; set; }

        [Required(ErrorMessage = "إجمالي المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "إجمالي المبلغ يجب أن يكون أكبر من الصفر")]
        [Column(TypeName = "decimal(15,2)")]
        public decimal TotalAmount { get; set; }

        [Required(ErrorMessage = "الوصف مطلوب")]
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = TransactionStatus.Draft.ToString(); // مسودة، مرحل، ملغي

        /// <summary>
        /// معرف المستخدم الذي أنشأ المعاملة
        /// User ID who created the transaction
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// معرف المستخدم الذي وافق على المعاملة
        /// User ID who approved the transaction
        /// </summary>
        [StringLength(100)]
        public string ApprovedBy { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الموافقة
        /// Approval date
        /// </summary>
        public DateTime? ApprovalDate { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// Additional notes
        /// </summary>
        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        // Navigation Properties
        [ForeignKey("CycleId")]
        public virtual ProductionCycle ProductionCycle { get; set; } = null!;

        public virtual ICollection<TransactionDetail> TransactionDetails { get; set; } = new List<TransactionDetail>();

        /// <summary>
        /// التحقق من توازن المعاملة
        /// Check transaction balance
        /// </summary>
        public bool IsBalanced()
        {
            var totalDebits = TransactionDetails.Sum(d => d.DebitAmount);
            var totalCredits = TransactionDetails.Sum(d => d.CreditAmount);
            return Math.Abs(totalDebits - totalCredits) < 0.01m;
        }

        /// <summary>
        /// حساب إجمالي المدين
        /// Calculate total debits
        /// </summary>
        public decimal GetTotalDebits()
        {
            return TransactionDetails.Sum(d => d.DebitAmount);
        }

        /// <summary>
        /// حساب إجمالي الدائن
        /// Calculate total credits
        /// </summary>
        public decimal GetTotalCredits()
        {
            return TransactionDetails.Sum(d => d.CreditAmount);
        }

        /// <summary>
        /// التحقق من أن المعاملة مرحلة
        /// Check if transaction is posted
        /// </summary>
        public bool IsPosted => Status == "مرحل";

        /// <summary>
        /// التحقق من أن المعاملة معتمدة
        /// Check if transaction is approved
        /// </summary>
        public bool IsApproved => !string.IsNullOrEmpty(ApprovedBy) && ApprovalDate.HasValue;

        /// <summary>
        /// ترحيل المعاملة
        /// Post the transaction
        /// </summary>
        public void Post()
        {
            if (!IsBalanced())
                throw new InvalidOperationException("لا يمكن ترحيل معاملة غير متوازنة");

            if (Status == TransactionStatus.Cancelled.ToString())
                throw new InvalidOperationException("لا يمكن ترحيل معاملة ملغاة");

            Status = TransactionStatus.Posted.ToString();
            UpdatedDate = DateTime.Now;
        }

        /// <summary>
        /// إلغاء المعاملة
        /// Cancel the transaction
        /// </summary>
        public void Cancel()
        {
            if (Status == TransactionStatus.Posted.ToString())
                throw new InvalidOperationException("لا يمكن إلغاء معاملة مرحلة");

            Status = TransactionStatus.Cancelled.ToString();
            UpdatedDate = DateTime.Now;
        }

        /// <summary>
        /// توليد رقم مرجعي تلقائي
        /// Generate automatic reference number
        /// </summary>
        public static string GenerateReferenceNumber(string transactionType)
        {
            var prefix = transactionType switch
            {
                "شراء" => "PUR",
                "بيع" => "SAL",
                "راتب" => "PAY",
                "مصروف عام" => "EXP",
                _ => "TRN"
            };

            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            return $"{prefix}-{timestamp}";
        }
    }
}
