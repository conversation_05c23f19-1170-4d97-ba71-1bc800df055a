using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة إدارة الموظفين
    /// Employee management service
    /// </summary>
    public class EmployeeService : IEmployeeService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<EmployeeService> _logger;

        public EmployeeService(IUnitOfWork unitOfWork, ILogger<EmployeeService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<Employee>> GetAllAsync()
        {
            try
            {
                return await _unitOfWork.Employees.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على جميع الموظفين");
                throw;
            }
        }

        public async Task<Employee?> GetByIdAsync(int id)
        {
            try
            {
                return await _unitOfWork.Employees.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الموظف {EmployeeId}", id);
                throw;
            }
        }

        public async Task<Employee> AddAsync(Employee entity)
        {
            try
            {
                var validationResult = await ValidateAsync(entity);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"بيانات الموظف غير صحيحة: {string.Join(", ", validationResult.Errors)}");
                }

                entity.CreatedDate = DateTime.Now;
                entity.Status = EmployeeStatus.Active;

                var result = await _unitOfWork.Employees.AddAsync(entity);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إضافة موظف جديد: {EmployeeName}", entity.FullName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة الموظف {EmployeeName}", entity.FullName);
                throw;
            }
        }

        public async Task<Employee> UpdateAsync(Employee entity)
        {
            try
            {
                var validationResult = await ValidateAsync(entity);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"بيانات الموظف غير صحيحة: {string.Join(", ", validationResult.Errors)}");
                }

                entity.UpdatedDate = DateTime.Now;

                var result = await _unitOfWork.Employees.UpdateAsync(entity);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث بيانات الموظف: {EmployeeName}", entity.FullName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الموظف {EmployeeId}", entity.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var employee = await _unitOfWork.Employees.GetByIdAsync(id);
                if (employee == null)
                {
                    return false;
                }

                // Check if employee has payroll records
                var hasPayrolls = await _unitOfWork.Payrolls.ExistsAsync(p => p.EmployeeId == id);
                if (hasPayrolls)
                {
                    throw new InvalidOperationException("لا يمكن حذف الموظف لوجود كشوف رواتب مرتبطة به");
                }

                await _unitOfWork.Employees.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم حذف الموظف: {EmployeeName}", employee.FullName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الموظف {EmployeeId}", id);
                throw;
            }
        }

        public async Task<ValidationResult> ValidateAsync(Employee entity)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(entity.FullName))
            {
                result.AddError("اسم الموظف مطلوب");
            }

            if (string.IsNullOrWhiteSpace(entity.Position))
            {
                result.AddError("منصب الموظف مطلوب");
            }

            if (entity.BaseSalary <= 0)
            {
                result.AddError("الراتب الأساسي يجب أن يكون أكبر من الصفر");
            }

            if (entity.JoinDate > DateTime.Now)
            {
                result.AddError("تاريخ التوظيف لا يمكن أن يكون في المستقبل");
            }

            // Check for duplicate national ID
            if (!string.IsNullOrWhiteSpace(entity.NationalId))
            {
                var existingEmployee = await _unitOfWork.Employees
                    .FindAsync(e => e.NationalId == entity.NationalId && e.Id != entity.Id);
                if (existingEmployee.Any())
                {
                    result.AddError("الرقم الوطني مستخدم من قبل موظف آخر");
                }
            }

            return result;
        }

        public async Task<IEnumerable<Employee>> GetActiveEmployeesAsync()
        {
            try
            {
                return await _unitOfWork.Employees.FindAsync(e => e.Status == EmployeeStatus.Active);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الموظفين النشطين");
                throw;
            }
        }

        public async Task<Employee?> GetByNationalIdAsync(string nationalId)
        {
            try
            {
                var employees = await _unitOfWork.Employees.FindAsync(e => e.NationalId == nationalId);
                return employees.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن الموظف بالرقم الوطني {NationalId}", nationalId);
                throw;
            }
        }

        public async Task<IEnumerable<Employee>> SearchEmployeesAsync(string searchTerm)
        {
            try
            {
                return await _unitOfWork.Employees.FindAsync(e => 
                    e.FullName.Contains(searchTerm) || 
                    e.Position.Contains(searchTerm) ||
                    (e.NationalId != null && e.NationalId.Contains(searchTerm)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن الموظفين بالكلمة {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<IEnumerable<Employee>> GetByPositionAsync(string position)
        {
            try
            {
                return await _unitOfWork.Employees.FindAsync(e => e.Position == position);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الموظفين بالمنصب {Position}", position);
                throw;
            }
        }

        public async Task<decimal> CalculateNetSalaryAsync(int employeeId, decimal allowances = 0, decimal deductions = 0)
        {
            try
            {
                var employee = await _unitOfWork.Employees.GetByIdAsync(employeeId);
                if (employee == null)
                {
                    throw new InvalidOperationException("الموظف غير موجود");
                }

                return employee.BaseSalary + allowances - deductions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب الراتب الصافي للموظف {EmployeeId}", employeeId);
                throw;
            }
        }

        public async Task<Payroll> CreatePayrollAsync(int employeeId, int cycleId, int month, int year,
            decimal allowances = 0, decimal deductions = 0, int workingDays = 30,
            decimal overtimeHours = 0, decimal overtimeRate = 0)
        {
            try
            {
                var employee = await _unitOfWork.Employees.GetByIdAsync(employeeId);
                if (employee == null)
                {
                    throw new InvalidOperationException("الموظف غير موجود");
                }

                var overtimePay = overtimeHours * overtimeRate;
                var netSalary = await CalculateNetSalaryAsync(employeeId, allowances + overtimePay, deductions);

                var payroll = new Payroll
                {
                    EmployeeId = employeeId,
                    CycleId = cycleId,
                    Month = month,
                    Year = year,
                    BaseSalary = employee.BaseSalary,
                    Allowances = allowances,
                    Deductions = deductions,
                    NetSalary = netSalary,
                    WorkingDays = workingDays,
                    OvertimeHours = overtimeHours,
                    OvertimeRate = overtimeRate,
                    PaymentDate = DateTime.Now,
                    PaymentStatus = "معلق",
                    CreatedDate = DateTime.Now
                };

                var result = await _unitOfWork.Payrolls.AddAsync(payroll);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء كشف راتب للموظف {EmployeeId} للشهر {Month}/{Year}",
                    employeeId, month, year);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء كشف راتب للموظف {EmployeeId}", employeeId);
                throw;
            }
        }

        public async Task<IEnumerable<Payroll>> GetEmployeePayrollsAsync(int employeeId)
        {
            try
            {
                return await _unitOfWork.Payrolls.FindAsync(p => p.EmployeeId == employeeId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على كشوف رواتب الموظف {EmployeeId}", employeeId);
                throw;
            }
        }

        public async Task<IEnumerable<Payroll>> GetMonthlyPayrollsAsync(int month, int year)
        {
            try
            {
                return await _unitOfWork.Payrolls.FindAsync(p => p.Month == month && p.Year == year);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على كشوف رواتب الشهر {Month}/{Year}", month, year);
                throw;
            }
        }

        public async Task<bool> UpdateEmployeeStatusAsync(int employeeId, string status, DateTime? leaveDate = null)
        {
            try
            {
                var employee = await _unitOfWork.Employees.GetByIdAsync(employeeId);
                if (employee == null)
                {
                    return false;
                }

                employee.Status = status;
                employee.UpdatedDate = DateTime.Now;

                if (status == EmployeeStatus.Resigned || status == EmployeeStatus.Terminated)
                {
                    employee.LeaveDate = leaveDate ?? DateTime.Now;
                }

                await _unitOfWork.Employees.UpdateAsync(employee);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث حالة الموظف {EmployeeId} إلى {Status}", employeeId, status);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث حالة الموظف {EmployeeId}", employeeId);
                throw;
            }
        }

        public async Task<decimal> CalculateMonthlyTotalSalariesAsync(int month, int year)
        {
            try
            {
                var payrolls = await _unitOfWork.Payrolls.FindAsync(p => p.Month == month && p.Year == year);
                return payrolls.Sum(p => p.NetSalary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إجمالي رواتب الشهر {Month}/{Year}", month, year);
                throw;
            }
        }
    }
}
