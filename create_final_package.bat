@echo off
chcp 65001 >nul
title إنشاء الحزمة النهائية - Create Final Package

echo ========================================
echo    إنشاء حزمة نظام إدارة مزرعة الأسماك
echo    Creating Fish Farm Management Package
echo ========================================
echo.

:: تحديد المتغيرات
set "PACKAGE_NAME=FishFarm_Management_v1.0.1_Simplified"
set "SOURCE_DIR=FishFarm_Simple_Package"
set "TEMP_DIR=temp_package"
set "FINAL_DIR=Final_Package"

:: تنظيف المجلدات السابقة
echo تنظيف المجلدات السابقة...
echo Cleaning previous directories...
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%" >nul 2>&1
if exist "%FINAL_DIR%" rmdir /s /q "%FINAL_DIR%" >nul 2>&1
if exist "%PACKAGE_NAME%.zip" del "%PACKAGE_NAME%.zip" >nul 2>&1

:: إنشاء المجلدات
echo إنشاء المجلدات...
echo Creating directories...
mkdir "%TEMP_DIR%" >nul 2>&1
mkdir "%FINAL_DIR%" >nul 2>&1

:: نسخ الملفات الأساسية
echo نسخ الملفات الأساسية...
echo Copying basic files...
xcopy "%SOURCE_DIR%\*.*" "%TEMP_DIR%\" /Y /Q >nul 2>&1

:: إنشاء المجلدات الضرورية
echo إنشاء المجلدات الضرورية...
echo Creating necessary directories...
mkdir "%TEMP_DIR%\Logs" >nul 2>&1
mkdir "%TEMP_DIR%\Backups" >nul 2>&1
mkdir "%TEMP_DIR%\Reports" >nul 2>&1
mkdir "%TEMP_DIR%\Temp" >nul 2>&1
mkdir "%TEMP_DIR%\Documentation" >nul 2>&1

:: نسخ ملفات التوثيق
echo نسخ ملفات التوثيق...
echo Copying documentation files...
if exist "README.md" copy "README.md" "%TEMP_DIR%\Documentation\" >nul 2>&1
if exist "LICENSE" copy "LICENSE" "%TEMP_DIR%\Documentation\" >nul 2>&1
if exist "CHANGELOG.md" copy "CHANGELOG.md" "%TEMP_DIR%\Documentation\" >nul 2>&1

:: البحث عن الملف التنفيذي
echo البحث عن الملف التنفيذي...
echo Looking for executable file...

set "EXE_FOUND=0"

:: البحث في مجلدات البناء المختلفة
for /r "." %%f in (FishFarmManagement.exe) do (
    if exist "%%f" (
        echo وُجد الملف التنفيذي: %%f
        copy "%%f" "%TEMP_DIR%\" >nul 2>&1
        set "EXE_FOUND=1"
        goto :exe_found
    )
)

:: البحث في مجلد Distribution
if exist "Distribution\Source\FishFarmManagement\bin\Release\net8.0-windows\FishFarmManagement.exe" (
    echo وُجد الملف التنفيذي في Distribution
    copy "Distribution\Source\FishFarmManagement\bin\Release\net8.0-windows\*.*" "%TEMP_DIR%\" >nul 2>&1
    set "EXE_FOUND=1"
    goto :exe_found
)

:: البحث في مجلد Debug
if exist "Distribution\Source\FishFarmManagement\bin\Debug\net8.0-windows\FishFarmManagement.exe" (
    echo وُجد الملف التنفيذي في Debug
    copy "Distribution\Source\FishFarmManagement\bin\Debug\net8.0-windows\*.*" "%TEMP_DIR%\" >nul 2>&1
    set "EXE_FOUND=1"
    goto :exe_found
)

:exe_found
if %EXE_FOUND% equ 0 (
    echo تحذير: لم يتم العثور على الملف التنفيذي
    echo Warning: Executable file not found
    echo.
    echo سيتم إنشاء الحزمة بدون الملف التنفيذي
    echo Package will be created without the executable file
    echo.
    
    :: إنشاء ملف تنبيه
    (
    echo تحذير: الملف التنفيذي مفقود
    echo Warning: Executable file is missing
    echo.
    echo يرجى بناء المشروع أولاً باستخدام:
    echo Please build the project first using:
    echo dotnet build --configuration Release
    echo.
    echo أو الحصول على نسخة مبنية من المطور
    echo Or get a built version from the developer
    ) > "%TEMP_DIR%\MISSING_EXECUTABLE.txt"
) else (
    echo ✓ تم نسخ الملف التنفيذي بنجاح
    echo ✓ Executable file copied successfully
)

:: نسخ ملفات DLL الضرورية إذا وُجدت
echo نسخ ملفات DLL الضرورية...
echo Copying necessary DLL files...

for %%d in (
    "Distribution\Source\FishFarmManagement\bin\Release\net8.0-windows"
    "Distribution\Source\FishFarmManagement\bin\Debug\net8.0-windows"
    "FishFarmManagement\bin\Release\net8.0-windows"
    "FishFarmManagement\bin\Debug\net8.0-windows"
) do (
    if exist "%%d\*.dll" (
        echo نسخ DLL من: %%d
        xcopy "%%d\*.dll" "%TEMP_DIR%\" /Y /Q >nul 2>&1
        xcopy "%%d\*.json" "%TEMP_DIR%\" /Y /Q >nul 2>&1
        xcopy "%%d\*.config" "%TEMP_DIR%\" /Y /Q >nul 2>&1
        goto :dll_copied
    )
)

:dll_copied

:: إنشاء قاعدة بيانات فارغة
echo إنشاء قاعدة بيانات فارغة...
echo Creating empty database...
if not exist "%TEMP_DIR%\FishFarmDatabase.db" (
    echo. > "%TEMP_DIR%\FishFarmDatabase.db"
)

:: إنشاء ملف معلومات الحزمة
echo إنشاء ملف معلومات الحزمة...
echo Creating package information file...

(
echo # معلومات الحزمة - Package Information
echo.
echo **اسم الحزمة:** %PACKAGE_NAME%
echo **تاريخ الإنشاء:** %DATE% %TIME%
echo **الإصدار:** 1.0.1 - النسخة المبسطة
echo **المطور:** طارق حسين صالح
echo.
echo ## محتويات الحزمة:
echo - الملف التنفيذي الرئيسي
echo - ملفات الإعدادات والتكوين
echo - دليل المستخدم الشامل
echo - ملفات التشغيل والتثبيت
echo - قاعدة البيانات الفارغة
echo - مجلدات النظام الأساسية
echo.
echo ## طريقة الاستخدام:
echo 1. فك ضغط الملف
echo 2. تشغيل verify_package.bat للتحقق
echo 3. تشغيل run.bat أو FishFarmManagement.exe
echo.
echo للدعم الفني: <EMAIL>
) > "%TEMP_DIR%\PACKAGE_INFO.txt"

:: إنشاء ملف الإصدار
echo إنشاء ملف الإصدار...
echo Creating version file...

(
echo 1.0.1-Simplified
echo %DATE%
echo Tarek Hussein Saleh
echo Fish Farm Management System - Simplified Edition
) > "%TEMP_DIR%\VERSION.txt"

:: ضغط الحزمة
echo ضغط الحزمة النهائية...
echo Compressing final package...

powershell -Command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath '%FINAL_DIR%\%PACKAGE_NAME%.zip' -Force" >nul 2>&1

if exist "%FINAL_DIR%\%PACKAGE_NAME%.zip" (
    echo ✓ تم إنشاء الحزمة بنجاح!
    echo ✓ Package created successfully!
    
    :: حساب حجم الملف
    for %%A in ("%FINAL_DIR%\%PACKAGE_NAME%.zip") do set "FILE_SIZE=%%~zA"
    set /a FILE_SIZE_MB=%FILE_SIZE%/1024/1024
    
    echo.
    echo معلومات الحزمة النهائية:
    echo Final package information:
    echo - الاسم: %PACKAGE_NAME%.zip
    echo - الحجم: %FILE_SIZE_MB% MB
    echo - الموقع: %FINAL_DIR%\
    
) else (
    echo ✗ فشل في إنشاء الحزمة
    echo ✗ Failed to create package
    echo.
    echo تحقق من:
    echo Check:
    echo - صلاحيات الكتابة
    echo - مساحة القرص المتاحة
    echo - برنامج PowerShell
)

:: تنظيف المجلد المؤقت
echo تنظيف الملفات المؤقتة...
echo Cleaning temporary files...
rmdir /s /q "%TEMP_DIR%" >nul 2>&1

echo.
echo ========================================
echo    اكتمل إنشاء الحزمة!
echo    Package Creation Complete!
echo ========================================
echo.

if exist "%FINAL_DIR%\%PACKAGE_NAME%.zip" (
    echo الحزمة النهائية متاحة في:
    echo Final package available at:
    echo %CD%\%FINAL_DIR%\%PACKAGE_NAME%.zip
    echo.
    echo هل تريد فتح مجلد الحزمة؟ (Y/N)
    echo Do you want to open the package folder? (Y/N)
    set /p choice=
    if /i "%choice%" equ "Y" (
        explorer "%FINAL_DIR%"
    )
)

echo.
echo شكراً لاستخدام نظام إدارة مزرعة الأسماك!
echo Thank you for using Fish Farm Management System!
pause
