@echo off
title Create Fish Farm Package

echo Creating Fish Farm Management Package...
echo.

set PACKAGE_NAME=FishFarm_Management_v1.0.1_Simplified
set SOURCE_DIR=FishFarm_Simple_Package
set FINAL_DIR=Final_Package

:: Clean previous directories
if exist "%FINAL_DIR%" rmdir /s /q "%FINAL_DIR%"
if exist "%PACKAGE_NAME%.zip" del "%PACKAGE_NAME%.zip"

:: Create directories
mkdir "%FINAL_DIR%"

:: Copy files from source directory
echo Copying files...
xcopy "%SOURCE_DIR%\*.*" "%FINAL_DIR%\" /Y /Q

:: Create necessary directories
mkdir "%FINAL_DIR%\Logs"
mkdir "%FINAL_DIR%\Backups"
mkdir "%FINAL_DIR%\Reports"
mkdir "%FINAL_DIR%\Temp"
mkdir "%FINAL_DIR%\Documentation"

:: Copy documentation files if they exist
if exist "README.md" copy "README.md" "%FINAL_DIR%\Documentation\"
if exist "LICENSE" copy "LICENSE" "%FINAL_DIR%\Documentation\"
if exist "CHANGELOG.md" copy "CHANGELOG.md" "%FINAL_DIR%\Documentation\"

:: Look for executable file
echo Looking for executable file...
set EXE_FOUND=0

:: Search in different build directories
if exist "Distribution\Source\FishFarmManagement\bin\Release\net8.0-windows\FishFarmManagement.exe" (
    echo Found executable in Release folder
    xcopy "Distribution\Source\FishFarmManagement\bin\Release\net8.0-windows\*.*" "%FINAL_DIR%\" /Y /Q
    set EXE_FOUND=1
)

if %EXE_FOUND% equ 0 (
    if exist "Distribution\Source\FishFarmManagement\bin\Debug\net8.0-windows\FishFarmManagement.exe" (
        echo Found executable in Debug folder
        xcopy "Distribution\Source\FishFarmManagement\bin\Debug\net8.0-windows\*.*" "%FINAL_DIR%\" /Y /Q
        set EXE_FOUND=1
    )
)

if %EXE_FOUND% equ 0 (
    echo Warning: Executable file not found
    echo Creating warning file...
    echo WARNING: Executable file is missing > "%FINAL_DIR%\MISSING_EXECUTABLE.txt"
    echo Please build the project first using: >> "%FINAL_DIR%\MISSING_EXECUTABLE.txt"
    echo dotnet build --configuration Release >> "%FINAL_DIR%\MISSING_EXECUTABLE.txt"
)

:: Create empty database
if not exist "%FINAL_DIR%\FishFarmDatabase.db" (
    echo. > "%FINAL_DIR%\FishFarmDatabase.db"
)

:: Create package info file
echo Creating package info...
(
echo Package: %PACKAGE_NAME%
echo Created: %DATE% %TIME%
echo Version: 1.0.1 - Simplified Edition
echo Developer: Tarek Hussein Saleh
echo.
echo Contents:
echo - Main executable file
echo - Configuration files
echo - User manual
echo - Installation scripts
echo - Empty database
echo - System directories
echo.
echo Usage:
echo 1. Extract the archive
echo 2. Run verify_package.bat to check
echo 3. Run run.bat or FishFarmManagement.exe
echo.
echo Support: <EMAIL>
) > "%FINAL_DIR%\PACKAGE_INFO.txt"

:: Create version file
echo 1.0.1-Simplified > "%FINAL_DIR%\VERSION.txt"
echo %DATE% >> "%FINAL_DIR%\VERSION.txt"
echo Tarek Hussein Saleh >> "%FINAL_DIR%\VERSION.txt"

:: Compress the package
echo Compressing package...
powershell -Command "Compress-Archive -Path '%FINAL_DIR%\*' -DestinationPath '%PACKAGE_NAME%.zip' -Force"

if exist "%PACKAGE_NAME%.zip" (
    echo Package created successfully!
    
    for %%A in ("%PACKAGE_NAME%.zip") do set FILE_SIZE=%%~zA
    set /a FILE_SIZE_MB=%FILE_SIZE%/1024/1024
    
    echo.
    echo Final package information:
    echo - Name: %PACKAGE_NAME%.zip
    echo - Size: %FILE_SIZE_MB% MB
    echo - Location: %CD%\
    
    echo.
    echo Package created at: %CD%\%PACKAGE_NAME%.zip
    
) else (
    echo Failed to create package
    echo Check:
    echo - Write permissions
    echo - Available disk space
    echo - PowerShell availability
)

echo.
echo Package creation complete!
echo.
pause
