using System;

namespace FishFarmManagement.Helpers
{
    /// <summary>
    /// رسائل الخطأ المحسنة
    /// Enhanced error messages
    /// </summary>
    public static class ErrorMessages
    {
        #region Database Errors
        public const string CONNECTION_FAILED = "فشل في الاتصال بقاعدة البيانات. يرجى التحقق من الاتصال والمحاولة مرة أخرى.";
        public const string DATABASE_LOCKED = "قاعدة البيانات مقفلة حالياً. يرجى المحاولة لاحقاً.";
        public const string DATABASE_CORRUPTED = "قاعدة البيانات تالفة. يرجى استعادة نسخة احتياطية.";
        public const string MIGRATION_FAILED = "فشل في تحديث قاعدة البيانات. يرجى التواصل مع الدعم الفني.";
        #endregion

        #region License Errors
        public const string INVALID_LICENSE = "الترخيص غير صالح أو منتهي الصلاحية. يرجى التواصل مع الدعم الفني.";
        public const string LICENSE_EXPIRED = "انتهت صلاحية الترخيص. يرجى تجديد الترخيص للمتابعة.";
        public const string LICENSE_NOT_FOUND = "لم يتم العثور على ترخيص صالح. يرجى تفعيل البرنامج.";
        public const string MAX_ACTIVATIONS_REACHED = "تم الوصول للحد الأقصى لعدد التفعيلات. يرجى التواصل مع الدعم الفني.";
        public const string HARDWARE_MISMATCH = "الترخيص مرتبط بجهاز آخر. يرجى استخدام الترخيص على الجهاز المخصص له.";
        #endregion

        #region Authentication Errors
        public const string INVALID_CREDENTIALS = "اسم المستخدم أو كلمة المرور غير صحيحة.";
        public const string ACCOUNT_LOCKED = "تم قفل الحساب بسبب محاولات دخول فاشلة متعددة. يرجى المحاولة لاحقاً.";
        public const string INSUFFICIENT_PERMISSIONS = "ليس لديك صلاحية للوصول إلى هذه الميزة.";
        public const string SESSION_EXPIRED = "انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.";
        public const string WEAK_PASSWORD = "كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل مع أحرف كبيرة وصغيرة وأرقام ورموز خاصة.";
        #endregion

        #region Validation Errors
        public const string REQUIRED_FIELD = "هذا الحقل مطلوب.";
        public const string INVALID_EMAIL = "عنوان البريد الإلكتروني غير صحيح.";
        public const string INVALID_PHONE = "رقم الهاتف غير صحيح.";
        public const string INVALID_DATE = "التاريخ غير صحيح.";
        public const string INVALID_NUMBER = "الرقم غير صحيح.";
        public const string DUPLICATE_ENTRY = "هذا الإدخال موجود مسبقاً.";
        #endregion

        #region Business Logic Errors
        public const string POND_CAPACITY_EXCEEDED = "تم تجاوز السعة القصوى للحوض.";
        public const string INSUFFICIENT_FEED_STOCK = "المخزون من العلف غير كافي.";
        public const string CYCLE_ALREADY_ACTIVE = "يوجد دورة إنتاجية نشطة بالفعل لهذا الحوض.";
        public const string EMPLOYEE_HAS_ACTIVE_PAYROLL = "لا يمكن حذف الموظف لوجود رواتب نشطة.";
        public const string TRANSACTION_UNBALANCED = "القيد المحاسبي غير متوازن.";
        #endregion

        #region File Operations
        public const string FILE_NOT_FOUND = "الملف غير موجود.";
        public const string FILE_ACCESS_DENIED = "ليس لديك صلاحية للوصول إلى الملف.";
        public const string BACKUP_FAILED = "فشل في إنشاء النسخة الاحتياطية.";
        public const string RESTORE_FAILED = "فشل في استعادة النسخة الاحتياطية.";
        public const string EXPORT_FAILED = "فشل في تصدير البيانات.";
        #endregion

        #region Network Errors
        public const string NO_INTERNET_CONNECTION = "لا يوجد اتصال بالإنترنت.";
        public const string SERVER_UNREACHABLE = "لا يمكن الوصول إلى الخادم.";
        public const string TIMEOUT_ERROR = "انتهت مهلة الاتصال.";
        #endregion

        #region General Errors
        public const string UNEXPECTED_ERROR = "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.";
        public const string OPERATION_CANCELLED = "تم إلغاء العملية.";
        public const string OPERATION_FAILED = "فشلت العملية.";
        public const string ACCESS_DENIED = "تم رفض الوصول.";
        #endregion

        /// <summary>
        /// الحصول على رسالة خطأ مخصصة
        /// Get custom error message
        /// </summary>
        public static string GetCustomMessage(string operation, string details = "")
        {
            var message = $"فشل في {operation}";
            if (!string.IsNullOrEmpty(details))
            {
                message += $": {details}";
            }
            return message;
        }

        /// <summary>
        /// الحصول على رسالة خطأ مع اقتراح حل
        /// Get error message with solution suggestion
        /// </summary>
        public static string GetMessageWithSolution(string error, string solution)
        {
            return $"{error}\n\nالحل المقترح: {solution}";
        }

        /// <summary>
        /// تنسيق رسالة خطأ مع تفاصيل تقنية
        /// Format error message with technical details
        /// </summary>
        public static string FormatTechnicalError(string userMessage, Exception exception)
        {
            return $"{userMessage}\n\nتفاصيل تقنية: {exception.Message}";
        }
    }
}
