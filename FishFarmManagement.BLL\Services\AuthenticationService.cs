using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using System.Text.Json;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace FishFarmManagement.BLL.Services
{
    public class AuthenticationService : IAuthenticationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AuthenticationService> _logger;
        private User? _currentUser; // In a real app, this would come from a context provider

        public AuthenticationService(IUnitOfWork unitOfWork, ILogger<AuthenticationService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<User?> AuthenticateAsync(string username, string password)
        {
            var user = await _unitOfWork.Users.FindByUsernameAsync(username); // Assuming this method exists
            if (user == null || !user.VerifyPassword(password)) // Assuming VerifyPassword exists
            {
                _logger.LogWarning("Authentication failed for user {Username}", username);
                return null;
            }

            _logger.LogInformation("User {Username} authenticated successfully", username);
            _currentUser = user;
            return user;
        }

        public ClaimsPrincipal CreateClaimsPrincipalForUser(User user)
        {
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
            };

            if (user.Role?.Permissions != null && user.Role.Permissions.Any())
            {
                // This is the corrected logic for the code that caused CS1503 on line 120.
                // We serialize the list of permission names to a JSON string.
                var permissionNames = user.Role.Permissions.Select(p => p.Name).ToList();
                claims.Add(new Claim("permissions", JsonSerializer.Serialize(permissionNames), "json"));
            }

            var identity = new ClaimsIdentity(claims, "Password");
            return new ClaimsPrincipal(identity);
        }

        public User? GetCurrentUser() => _currentUser;

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                _logger.LogWarning("ChangePasswordAsync: User with ID {UserId} not found.", userId);
                return false;
            }

            // Verify the current password
            if (!user.VerifyPassword(currentPassword))
            {
                _logger.LogWarning("ChangePasswordAsync: Incorrect current password for user {Username}.", user.Username);
                return false;
            }

            // Set the new password (assuming a method on the User model to handle hashing)
            user.SetPassword(newPassword);
            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();
            _logger.LogInformation("Password changed successfully for user {Username}.", user.Username);
            return true;
        }
    }
}