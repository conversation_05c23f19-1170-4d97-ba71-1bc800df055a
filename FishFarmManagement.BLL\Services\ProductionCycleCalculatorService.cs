using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة حساب إحصائيات الدورة الإنتاجية
    /// Production cycle calculator service
    /// </summary>
    public class ProductionCycleCalculatorService : IProductionCycleCalculatorService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProductionCycleCalculatorService> _logger;
        private readonly ISettingsService _settingsService;

        public ProductionCycleCalculatorService(
            IUnitOfWork unitOfWork, 
            ILogger<ProductionCycleCalculatorService> logger,
            ISettingsService settingsService)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _settingsService = settingsService ?? throw new ArgumentNullException(nameof(settingsService));
        }

        public async Task<decimal> CalculateTotalCostAsync(int cycleId)
        {
            try
            {
                var feedCost = await CalculateFeedCostAsync(cycleId);
                var medicationCost = await CalculateMedicationCostAsync(cycleId);
                var laborCost = await CalculateLaborCostAsync(cycleId);
                var operationalCost = await CalculateOperationalCostAsync(cycleId);

                return feedCost + medicationCost + laborCost + operationalCost;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إجمالي التكلفة للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateTotalProductionAsync(int cycleId)
        {
            try
            {
                var ponds = await _unitOfWork.Ponds.FindAsync(p => p.CycleId == cycleId);
                return ponds.Sum(p => p.FishCount * p.AverageWeight);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إجمالي الإنتاج للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateExpectedProfitabilityAsync(int cycleId)
        {
            try
            {
                var totalProduction = await CalculateTotalProductionAsync(cycleId);
                var totalCost = await CalculateTotalCostAsync(cycleId);
                var expectedRevenue = await CalculateExpectedRevenueAsync(cycleId);

                return expectedRevenue - totalCost;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب الربحية المتوقعة للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateExpectedRevenueAsync(int cycleId)
        {
            try
            {
                var totalProduction = await CalculateTotalProductionAsync(cycleId);
                var businessSettings = _settingsService.GetApplicationSettings().BusinessSettings;
                var sellPrice = businessSettings.DefaultFishSellPricePerKg;

                return totalProduction * sellPrice;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب الإيرادات المتوقعة للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateFeedCostAsync(int cycleId)
        {
            try
            {
                var feedConsumptions = await _unitOfWork.FeedConsumptions.FindAsync(fc =>
                    fc.Pond.CycleId == cycleId);
                
                return feedConsumptions.Sum(fc => fc.Cost);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب تكلفة العلف للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateMedicationCostAsync(int cycleId)
        {
            try
            {
                var medications = await _unitOfWork.PondMedications.FindAsync(pm =>
                    pm.Pond.CycleId == cycleId);
                
                // For now, return a placeholder since PondMedication doesn't have cost info
                // This would need to be implemented based on your medication cost structure
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب تكلفة الأدوية للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateLaborCostAsync(int cycleId)
        {
            try
            {
                // This would need to be implemented based on your payroll system
                // For now, return a placeholder calculation
                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                if (cycle == null) return 0;

                var daysElapsed = (DateTime.Now - cycle.StartDate).Days;
                var estimatedDailyCost = 100m; // This should come from settings or calculations
                
                return daysElapsed * estimatedDailyCost;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب تكلفة العمالة للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateOperationalCostAsync(int cycleId)
        {
            try
            {
                // This would include utilities, maintenance, etc.
                // For now, return a placeholder calculation
                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                if (cycle == null) return 0;

                var daysElapsed = (DateTime.Now - cycle.StartDate).Days;
                var estimatedDailyCost = 50m; // This should come from settings or calculations
                
                return daysElapsed * estimatedDailyCost;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب التكاليف التشغيلية للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateGrowthRateAsync(int cycleId)
        {
            try
            {
                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                if (cycle == null) return 0;

                var ponds = await _unitOfWork.Ponds.FindAsync(p => p.CycleId == cycleId);
                if (!ponds.Any()) return 0;

                // Calculate growth rate based on current weight vs expected initial weight
                // This is a simplified calculation - you might want to track actual initial weights
                var daysElapsed = (DateTime.Now - cycle.StartDate).Days;
                if (daysElapsed <= 0) return 0;

                var currentTotalBiomass = ponds.Sum(p => p.FishCount * p.AverageWeight);
                var estimatedInitialBiomass = ponds.Sum(p => p.FishCount * 0.1m); // Assume 100g initial weight

                if (estimatedInitialBiomass > 0)
                {
                    var totalGrowth = currentTotalBiomass - estimatedInitialBiomass;
                    return (totalGrowth / estimatedInitialBiomass) * 100;
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب معدل النمو للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateMortalityRateAsync(int cycleId)
        {
            try
            {
                var ponds = await _unitOfWork.Ponds.FindAsync(p => p.CycleId == cycleId);
                if (!ponds.Any()) return 0;

                var totalCurrentFish = ponds.Sum(p => p.FishCount);
                var totalDeadFish = 0;

                // Calculate total dead fish from mortality records
                foreach (var pond in ponds)
                {
                    var mortalities = await _unitOfWork.FishMortalities.FindAsync(m => m.PondId == pond.Id);
                    totalDeadFish += mortalities.Sum(m => m.DeadFishCount);
                }

                var totalOriginalFish = totalCurrentFish + totalDeadFish;
                return totalOriginalFish > 0 ? (totalDeadFish / (decimal)totalOriginalFish) * 100 : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب معدل الوفيات للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateFeedConversionRatioAsync(int cycleId)
        {
            try
            {
                var totalFeedConsumed = await CalculateFeedCostAsync(cycleId);
                var totalProduction = await CalculateTotalProductionAsync(cycleId);

                return totalProduction > 0 ? totalFeedConsumed / totalProduction : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب معدل تحويل العلف للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateProfitMarginAsync(int cycleId)
        {
            try
            {
                var expectedRevenue = await CalculateExpectedRevenueAsync(cycleId);
                var expectedProfit = await CalculateExpectedProfitabilityAsync(cycleId);

                return expectedRevenue > 0 ? (expectedProfit / expectedRevenue) * 100 : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب هامش الربح للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateROIAsync(int cycleId)
        {
            try
            {
                var totalCost = await CalculateTotalCostAsync(cycleId);
                var expectedProfit = await CalculateExpectedProfitabilityAsync(cycleId);

                return totalCost > 0 ? (expectedProfit / totalCost) * 100 : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب العائد على الاستثمار للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateProductionEfficiencyAsync(int cycleId)
        {
            try
            {
                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                if (cycle == null) return 0;

                var totalProduction = await CalculateTotalProductionAsync(cycleId);
                var daysElapsed = (DateTime.Now - cycle.StartDate).Days;

                return daysElapsed > 0 ? totalProduction / daysElapsed : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب الكفاءة الإنتاجية للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateCostPerKgAsync(int cycleId)
        {
            try
            {
                var totalCost = await CalculateTotalCostAsync(cycleId);
                var totalProduction = await CalculateTotalProductionAsync(cycleId);

                return totalProduction > 0 ? totalCost / totalProduction : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب التكلفة لكل كيلوجرام للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateAverageFishWeightAsync(int cycleId)
        {
            try
            {
                var ponds = await _unitOfWork.Ponds.FindAsync(p => p.CycleId == cycleId);
                if (!ponds.Any()) return 0;

                var totalBiomass = ponds.Sum(p => p.FishCount * p.AverageWeight);
                var totalFishCount = ponds.Sum(p => p.FishCount);

                return totalFishCount > 0 ? totalBiomass / totalFishCount : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب متوسط وزن السمك للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateFishDensityAsync(int cycleId)
        {
            try
            {
                var ponds = await _unitOfWork.Ponds.FindAsync(p => p.CycleId == cycleId);
                if (!ponds.Any()) return 0;

                var totalFishCount = ponds.Sum(p => p.FishCount);
                // Since Area is not available in the current model, return fish count per pond
                var pondCount = ponds.Count();

                return pondCount > 0 ? totalFishCount / (decimal)pondCount : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب الكثافة السمكية للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateDailyFeedConsumptionAsync(int cycleId)
        {
            try
            {
                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                if (cycle == null) return 0;

                var totalFeedCost = await CalculateFeedCostAsync(cycleId);
                var daysElapsed = (DateTime.Now - cycle.StartDate).Days;

                return daysElapsed > 0 ? totalFeedCost / daysElapsed : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب استهلاك العلف اليومي للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<decimal> CalculateDailyGrowthRateAsync(int cycleId)
        {
            try
            {
                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                if (cycle == null) return 0;

                var growthRate = await CalculateGrowthRateAsync(cycleId);
                var daysElapsed = (DateTime.Now - cycle.StartDate).Days;

                return daysElapsed > 0 ? growthRate / daysElapsed : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب معدل النمو اليومي للدورة {CycleId}", cycleId);
                return 0;
            }
        }

        public async Task<CycleStatistics> CalculateStatisticsAsync(int cycleId)
        {
            try
            {
                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                if (cycle == null)
                {
                    throw new ArgumentException($"لم يتم العثور على الدورة الإنتاجية {cycleId}");
                }

                var ponds = await _unitOfWork.Ponds.FindAsync(p => p.CycleId == cycleId);

                var statistics = new CycleStatistics
                {
                    CycleId = cycleId,
                    CycleName = cycle.CycleName,
                    TotalPonds = ponds.Count(),
                    ActivePonds = ponds.Count(p => p.Status == "نشط"),
                    TotalFishCount = ponds.Sum(p => p.FishCount),
                    TotalBiomass = await CalculateTotalProductionAsync(cycleId),
                    TotalCost = await CalculateTotalCostAsync(cycleId),
                    EstimatedRevenue = await CalculateExpectedRevenueAsync(cycleId),
                    EstimatedProfit = await CalculateExpectedProfitabilityAsync(cycleId),
                    ProfitMargin = await CalculateProfitMarginAsync(cycleId),
                    DaysElapsed = (DateTime.Now - cycle.StartDate).Days,
                    DaysRemaining = cycle.EndDate.HasValue ? 0 : (cycle.ExpectedEndDate - DateTime.Now).Days,
                    CompletionPercentage = cycle.EndDate.HasValue ? 100 :
                        Math.Min(100, ((DateTime.Now - cycle.StartDate).Days /
                        (cycle.ExpectedEndDate - cycle.StartDate).Days) * 100),
                    FeedCost = await CalculateFeedCostAsync(cycleId),
                    MedicationCost = await CalculateMedicationCostAsync(cycleId),
                    LaborCost = await CalculateLaborCostAsync(cycleId),
                    OperationalCost = await CalculateOperationalCostAsync(cycleId),
                    GrowthRate = await CalculateGrowthRateAsync(cycleId),
                    MortalityRate = await CalculateMortalityRateAsync(cycleId),
                    FeedConversionRatio = await CalculateFeedConversionRatioAsync(cycleId),
                    ROI = await CalculateROIAsync(cycleId),
                    ProductionEfficiency = await CalculateProductionEfficiencyAsync(cycleId),
                    CostPerKg = await CalculateCostPerKgAsync(cycleId),
                    AverageFishWeight = await CalculateAverageFishWeightAsync(cycleId),
                    FishDensity = await CalculateFishDensityAsync(cycleId),
                    DailyFeedConsumption = await CalculateDailyFeedConsumptionAsync(cycleId),
                    DailyGrowthRate = await CalculateDailyGrowthRateAsync(cycleId)
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إحصائيات الدورة {CycleId}", cycleId);
                throw;
            }
        }
    }
}
