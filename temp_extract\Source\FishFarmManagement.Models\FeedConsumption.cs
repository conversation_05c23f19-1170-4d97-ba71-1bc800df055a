using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// استهلاك العلف
    /// Feed consumption
    /// </summary>
    public class FeedConsumption : BaseEntity
    {
        [Required(ErrorMessage = "معرف الحوض مطلوب")]
        public int PondId { get; set; }

        [Required(ErrorMessage = "معرف نوع العلف مطلوب")]
        public int FeedTypeId { get; set; }

        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0.1, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,3)")]
        public decimal Quantity { get; set; }

        [Required(ErrorMessage = "تاريخ التغذية مطلوب")]
        public DateTime FeedingDate { get; set; }

        [Required(ErrorMessage = "التكلفة مطلوبة")]
        [Range(0, double.MaxValue, ErrorMessage = "التكلفة يجب أن تكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal Cost { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string Notes { get; set; } = string.Empty;

        // Navigation Properties
        [ForeignKey("PondId")]
        public virtual Pond Pond { get; set; } = null!;

        [ForeignKey("FeedTypeId")]
        public virtual FeedType FeedType { get; set; } = null!;

        /// <summary>
        /// حساب التكلفة لكل كيلو
        /// Calculate cost per kilogram
        /// </summary>
        public decimal GetCostPerKg()
        {
            return Quantity > 0 ? Cost / Quantity : 0;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data consistency
        /// </summary>
        public bool IsDataConsistent()
        {
            // التحقق من أن التكلفة تتناسب مع الكمية وسعر الكيلو
            if (FeedType != null)
            {
                var expectedCost = Quantity * FeedType.PricePerKg;
                var tolerance = expectedCost * 0.05m; // هامش خطأ 5%
                return Math.Abs(Cost - expectedCost) <= tolerance;
            }
            return true;
        }
    }
}
