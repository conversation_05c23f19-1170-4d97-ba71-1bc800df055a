﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using System.IO;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج إدارة النسخ الاحتياطي التلقائي
    /// Automatic backup management form
    /// </summary>
    public partial class AutoBackupManagementForm : Form
    {
        private readonly IBackupService _backupService;
        private readonly ILogger<AutoBackupManagementForm> _logger;
        private System.Windows.Forms.Timer _backupTimer;

        // UI Controls
        private GroupBox settingsGroupBox;
        private CheckBox enableAutoBackupCheckBox;
        private NumericUpDown intervalHoursNumericUpDown;
        private TextBox backupPathTextBox;
        private Button browsePathButton;
        private NumericUpDown maxBackupFilesNumericUpDown;
        private CheckBox compressBackupsCheckBox;
        private CheckBox encryptBackupsCheckBox;

        private GroupBox statusGroupBox;
        private Label lastBackupLabel;
        private Label nextBackupLabel;
        private Label backupStatusLabel;
        private ProgressBar backupProgressBar;

        private GroupBox backupListGroupBox;
        private DataGridView backupListDataGridView;
        private Button createBackupButton;
        private Button restoreBackupButton;
        private Button deleteBackupButton;
        private Button verifyBackupButton;

        private GroupBox logGroupBox;
        private RichTextBox logTextBox;
        private Button clearLogButton;
        private Button exportLogButton;

        private Button saveSettingsButton;
        private Button cancelButton;

        public AutoBackupManagementForm(IBackupService backupService, ILogger<AutoBackupManagementForm> logger)
        {
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            LoadSettings();
            LoadBackupList();
            InitializeTimer();
        }

        private void InitializeComponent()
        {
            this.Text = "إدارة النسخ الاحتياطي التلقائي";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            CreateSettingsGroup();
            CreateStatusGroup();
            CreateBackupListGroup();
            CreateLogGroup();
            CreateActionButtons();

            ArrangeControls();
        }

        private void CreateSettingsGroup()
        {
            settingsGroupBox = new GroupBox
            {
                Text = "إعدادات النسخ الاحتياطي",
                Size = new Size(480, 200),
                Location = new Point(20, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            enableAutoBackupCheckBox = new CheckBox
            {
                Text = "تفعيل النسخ الاحتياطي التلقائي",
                Size = new Size(200, 23),
                Location = new Point(20, 30),
                Checked = true
            };
            enableAutoBackupCheckBox.CheckedChanged += EnableAutoBackup_CheckedChanged;

            var intervalLabel = new Label
            {
                Text = "فترة النسخ الاحتياطي (ساعات):",
                Size = new Size(150, 23),
                Location = new Point(20, 65),
                TextAlign = ContentAlignment.MiddleRight
            };

            intervalHoursNumericUpDown = new NumericUpDown
            {
                Size = new Size(80, 23),
                Location = new Point(180, 62),
                Minimum = 1,
                Maximum = 168, // 7 days
                Value = 24
            };

            var pathLabel = new Label
            {
                Text = "مجلد النسخ الاحتياطي:",
                Size = new Size(120, 23),
                Location = new Point(20, 100),
                TextAlign = ContentAlignment.MiddleRight
            };

            backupPathTextBox = new TextBox
            {
                Size = new Size(250, 23),
                Location = new Point(150, 97),
                Text = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups")
            };

            browsePathButton = new Button
            {
                Text = "استعراض",
                Size = new Size(80, 25),
                Location = new Point(410, 96)
            };
            browsePathButton.Click += BrowsePath_Click;

            var maxFilesLabel = new Label
            {
                Text = "الحد الأقصى للملفات:",
                Size = new Size(120, 23),
                Location = new Point(20, 135),
                TextAlign = ContentAlignment.MiddleRight
            };

            maxBackupFilesNumericUpDown = new NumericUpDown
            {
                Size = new Size(80, 23),
                Location = new Point(150, 132),
                Minimum = 1,
                Maximum = 100,
                Value = 30
            };

            compressBackupsCheckBox = new CheckBox
            {
                Text = "ضغط النسخ الاحتياطية",
                Size = new Size(150, 23),
                Location = new Point(250, 135),
                Checked = true
            };

            encryptBackupsCheckBox = new CheckBox
            {
                Text = "تشفير النسخ الاحتياطية",
                Size = new Size(150, 23),
                Location = new Point(20, 165),
                Checked = false
            };

            settingsGroupBox.Controls.AddRange(new Control[] {
                enableAutoBackupCheckBox, intervalLabel, intervalHoursNumericUpDown,
                pathLabel, backupPathTextBox, browsePathButton,
                maxFilesLabel, maxBackupFilesNumericUpDown,
                compressBackupsCheckBox, encryptBackupsCheckBox
            });
        }

        private void CreateStatusGroup()
        {
            statusGroupBox = new GroupBox
            {
                Text = "حالة النسخ الاحتياطي",
                Size = new Size(480, 120),
                Location = new Point(520, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            lastBackupLabel = new Label
            {
                Text = "آخر نسخة احتياطية: لم يتم إنشاء نسخة بعد",
                Size = new Size(450, 23),
                Location = new Point(20, 30),
                Font = new Font("Segoe UI", 9F)
            };

            nextBackupLabel = new Label
            {
                Text = "النسخة التالية: غير محدد",
                Size = new Size(450, 23),
                Location = new Point(20, 55),
                Font = new Font("Segoe UI", 9F)
            };

            backupStatusLabel = new Label
            {
                Text = "الحالة: جاهز",
                Size = new Size(450, 23),
                Location = new Point(20, 80),
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.Green
            };

            backupProgressBar = new ProgressBar
            {
                Size = new Size(450, 20),
                Location = new Point(20, 95),
                Style = ProgressBarStyle.Continuous
            };

            statusGroupBox.Controls.AddRange(new Control[] {
                lastBackupLabel, nextBackupLabel, backupStatusLabel, backupProgressBar
            });
        }

        private void CreateBackupListGroup()
        {
            backupListGroupBox = new GroupBox
            {
                Text = "قائمة النسخ الاحتياطية",
                Size = new Size(600, 250),
                Location = new Point(20, 240),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            backupListDataGridView = new DataGridView
            {
                Size = new Size(570, 180),
                Location = new Point(15, 25),
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            CreateBackupListColumns();

            var buttonPanel = new Panel
            {
                Size = new Size(570, 40),
                Location = new Point(15, 205)
            };

            createBackupButton = new Button
            {
                Text = "إنشاء نسخة",
                Size = new Size(100, 30),
                Location = new Point(0, 5),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            createBackupButton.Click += CreateBackup_Click;

            restoreBackupButton = new Button
            {
                Text = "استعادة",
                Size = new Size(100, 30),
                Location = new Point(110, 5),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            restoreBackupButton.Click += RestoreBackup_Click;

            deleteBackupButton = new Button
            {
                Text = "حذف",
                Size = new Size(100, 30),
                Location = new Point(220, 5),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            deleteBackupButton.Click += DeleteBackup_Click;

            verifyBackupButton = new Button
            {
                Text = "التحقق",
                Size = new Size(100, 30),
                Location = new Point(330, 5),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            verifyBackupButton.Click += VerifyBackup_Click;

            buttonPanel.Controls.AddRange(new Control[] {
                createBackupButton, restoreBackupButton, deleteBackupButton, verifyBackupButton
            });

            backupListDataGridView.SelectionChanged += BackupList_SelectionChanged;

            backupListGroupBox.Controls.AddRange(new Control[] { backupListDataGridView, buttonPanel });
        }

        private void CreateBackupListColumns()
        {
            backupListDataGridView.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "FileName", HeaderText = "اسم الملف", DataPropertyName = "FileName", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "CreatedDate", HeaderText = "تاريخ الإنشاء", DataPropertyName = "CreatedDate", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "FileSize", HeaderText = "الحجم", DataPropertyName = "FileSize", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Status", HeaderText = "الحالة", DataPropertyName = "Status", Width = 100 }
            });

            backupListDataGridView.Columns["CreatedDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm";
        }

        private void CreateLogGroup()
        {
            logGroupBox = new GroupBox
            {
                Text = "سجل العمليات",
                Size = new Size(360, 250),
                Location = new Point(640, 240),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            logTextBox = new RichTextBox
            {
                Size = new Size(330, 180),
                Location = new Point(15, 25),
                ReadOnly = true,
                BackColor = Color.FromArgb(250, 250, 250),
                Font = new Font("Consolas", 9F)
            };

            var logButtonPanel = new Panel
            {
                Size = new Size(330, 40),
                Location = new Point(15, 205)
            };

            clearLogButton = new Button
            {
                Text = "مسح السجل",
                Size = new Size(100, 30),
                Location = new Point(0, 5),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            clearLogButton.Click += ClearLog_Click;

            exportLogButton = new Button
            {
                Text = "تصدير السجل",
                Size = new Size(100, 30),
                Location = new Point(110, 5),
                BackColor = Color.FromArgb(52, 73, 94),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            exportLogButton.Click += ExportLog_Click;

            logButtonPanel.Controls.AddRange(new Control[] { clearLogButton, exportLogButton });
            logGroupBox.Controls.AddRange(new Control[] { logTextBox, logButtonPanel });
        }

        private void CreateActionButtons()
        {
            saveSettingsButton = new Button
            {
                Text = "حفظ الإعدادات",
                Size = new Size(120, 35),
                Location = new Point(750, 510),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            saveSettingsButton.Click += SaveSettings_Click;

            cancelButton = new Button
            {
                Text = "إلغاء",
                Size = new Size(100, 35),
                Location = new Point(880, 510),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F)
            };
            cancelButton.Click += Cancel_Click;
        }

        private void ArrangeControls()
        {
            this.Controls.AddRange(new Control[] {
                settingsGroupBox, statusGroupBox, backupListGroupBox,
                logGroupBox, saveSettingsButton, cancelButton
            });
        }

        private void InitializeTimer()
        {
            _backupTimer = new System.Windows.Forms.Timer();
            _backupTimer.Interval = (int)TimeSpan.FromHours((double)intervalHoursNumericUpDown.Value).TotalMilliseconds;
            _backupTimer.Tick += BackupTimer_Tick;

            if (enableAutoBackupCheckBox.Checked)
            {
                _backupTimer.Start();
                UpdateNextBackupTime();
            }
        }

        private void LoadSettings()
        {
            try
            {
                // Load settings from configuration or database
                // This is a placeholder implementation
                AddLogEntry("تم تحميل الإعدادات بنجاح", Color.Green);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الإعدادات");
                AddLogEntry($"خطأ في تحميل الإعدادات: {ex.Message}", Color.Red);
            }
        }

        private async void LoadBackupList()
        {
            try
            {
                var backupPath = backupPathTextBox.Text;
                if (!Directory.Exists(backupPath))
                {
                    Directory.CreateDirectory(backupPath);
                }

                var backupFiles = Directory.GetFiles(backupPath, "*.db")
                    .Select(file => new
                    {
                        FileName = Path.GetFileName(file),
                        CreatedDate = File.GetCreationTime(file),
                        FileSize = FormatFileSize(new FileInfo(file).Length),
                        Status = "صالح"
                    })
                    .OrderByDescending(f => f.CreatedDate)
                    .ToList();

                backupListDataGridView.DataSource = backupFiles;
                AddLogEntry($"تم تحميل {backupFiles.Count} نسخة احتياطية", Color.Blue);

                if (backupFiles.Any())
                {
                    var lastBackup = backupFiles.First();
                    lastBackupLabel.Text = $"آخر نسخة احتياطية: {lastBackup.CreatedDate:yyyy/MM/dd HH:mm}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل قائمة النسخ الاحتياطية");
                AddLogEntry($"خطأ في تحميل القائمة: {ex.Message}", Color.Red);
            }
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private void AddLogEntry(string message, Color color)
        {
            if (logTextBox.InvokeRequired)
            {
                logTextBox.Invoke(new Action(() => AddLogEntry(message, color)));
                return;
            }

            var timestamp = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
            logTextBox.SelectionStart = logTextBox.TextLength;
            logTextBox.SelectionLength = 0;
            logTextBox.SelectionColor = Color.Gray;
            logTextBox.AppendText($"[{timestamp}] ");
            logTextBox.SelectionColor = color;
            logTextBox.AppendText($"{message}\n");
            logTextBox.ScrollToCaret();
        }

        private void UpdateNextBackupTime()
        {
            if (enableAutoBackupCheckBox.Checked)
            {
                var nextBackup = DateTime.Now.AddHours((double)intervalHoursNumericUpDown.Value);
                nextBackupLabel.Text = $"النسخة التالية: {nextBackup:yyyy/MM/dd HH:mm}";
            }
            else
            {
                nextBackupLabel.Text = "النسخة التالية: غير محدد";
            }
        }

        // Event Handlers
        private void EnableAutoBackup_CheckedChanged(object sender, EventArgs e)
        {
            if (enableAutoBackupCheckBox.Checked)
            {
                _backupTimer?.Start();
                UpdateNextBackupTime();
                AddLogEntry("تم تفعيل النسخ الاحتياطي التلقائي", Color.Green);
            }
            else
            {
                _backupTimer?.Stop();
                UpdateNextBackupTime();
                AddLogEntry("تم إيقاف النسخ الاحتياطي التلقائي", Color.Orange);
            }
        }

        private void BrowsePath_Click(object sender, EventArgs e)
        {
            using (var folderDialog = new FolderBrowserDialog())
            {
                folderDialog.Description = "اختر مجلد النسخ الاحتياطية";
                folderDialog.SelectedPath = backupPathTextBox.Text;

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    backupPathTextBox.Text = folderDialog.SelectedPath;
                    LoadBackupList();
                }
            }
        }

        private async void CreateBackup_Click(object sender, EventArgs e)
        {
            try
            {
                backupStatusLabel.Text = "الحالة: جاري إنشاء النسخة الاحتياطية...";
                backupStatusLabel.ForeColor = Color.Orange;
                backupProgressBar.Style = ProgressBarStyle.Marquee;
                createBackupButton.Enabled = false;

                AddLogEntry("بدء إنشاء نسخة احتياطية يدوية", Color.Blue);

                var backupPath = Path.Combine(backupPathTextBox.Text,
                    $"backup-{DateTime.Now:yyyyMMdd-HHmmss}.db");

                var result = await _backupService.CreateFullBackupAsync(backupPath);

                if (result.Success)
                {
                    backupStatusLabel.Text = "الحالة: تم إنشاء النسخة الاحتياطية بنجاح";
                    backupStatusLabel.ForeColor = Color.Green;
                    AddLogEntry("تم إنشاء النسخة الاحتياطية بنجاح", Color.Green);
                    LoadBackupList();
                }
                else
                {
                    backupStatusLabel.Text = "الحالة: فشل في إنشاء النسخة الاحتياطية";
                    backupStatusLabel.ForeColor = Color.Red;
                    AddLogEntry("فشل في إنشاء النسخة الاحتياطية", Color.Red);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية");
                backupStatusLabel.Text = "الحالة: خطأ في إنشاء النسخة الاحتياطية";
                backupStatusLabel.ForeColor = Color.Red;
                AddLogEntry($"خطأ: {ex.Message}", Color.Red);
            }
            finally
            {
                backupProgressBar.Style = ProgressBarStyle.Continuous;
                createBackupButton.Enabled = true;
            }
        }

        private async void RestoreBackup_Click(object sender, EventArgs e)
        {
            try
            {
                if (backupListDataGridView.SelectedRows.Count == 0) return;

                var selectedFile = backupListDataGridView.SelectedRows[0].Cells["FileName"].Value.ToString();
                var backupFilePath = Path.Combine(backupPathTextBox.Text, selectedFile);

                var result = MessageBox.Show(
                    $"هل أنت متأكد من استعادة النسخة الاحتياطية '{selectedFile}'؟\n\nسيتم استبدال البيانات الحالية.",
                    "تأكيد الاستعادة",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    backupStatusLabel.Text = "الحالة: جاري استعادة النسخة الاحتياطية...";
                    backupStatusLabel.ForeColor = Color.Orange;
                    backupProgressBar.Style = ProgressBarStyle.Marquee;

                    AddLogEntry($"بدء استعادة النسخة الاحتياطية: {selectedFile}", Color.Blue);

                    var restoreResult = await _backupService.RestoreBackupAsync(backupFilePath);

                    if (restoreResult.Success)
                    {
                        backupStatusLabel.Text = "الحالة: تم استعادة النسخة الاحتياطية بنجاح";
                        backupStatusLabel.ForeColor = Color.Green;
                        AddLogEntry("تم استعادة النسخة الاحتياطية بنجاح", Color.Green);
                        MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح. يُنصح بإعادة تشغيل التطبيق.",
                            "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        backupStatusLabel.Text = "الحالة: فشل في استعادة النسخة الاحتياطية";
                        backupStatusLabel.ForeColor = Color.Red;
                        AddLogEntry("فشل في استعادة النسخة الاحتياطية", Color.Red);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استعادة النسخة الاحتياطية");
                backupStatusLabel.Text = "الحالة: خطأ في استعادة النسخة الاحتياطية";
                backupStatusLabel.ForeColor = Color.Red;
                AddLogEntry($"خطأ: {ex.Message}", Color.Red);
            }
            finally
            {
                backupProgressBar.Style = ProgressBarStyle.Continuous;
            }
        }

        private void DeleteBackup_Click(object sender, EventArgs e)
        {
            try
            {
                if (backupListDataGridView.SelectedRows.Count == 0) return;

                var selectedFile = backupListDataGridView.SelectedRows[0].Cells["FileName"].Value.ToString();
                var backupFilePath = Path.Combine(backupPathTextBox.Text, selectedFile);

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف النسخة الاحتياطية '{selectedFile}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    File.Delete(backupFilePath);
                    AddLogEntry($"تم حذف النسخة الاحتياطية: {selectedFile}", Color.Orange);
                    LoadBackupList();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف النسخة الاحتياطية");
                AddLogEntry($"خطأ في الحذف: {ex.Message}", Color.Red);
            }
        }

        private async void VerifyBackup_Click(object sender, EventArgs e)
        {
            try
            {
                if (backupListDataGridView.SelectedRows.Count == 0) return;

                var selectedFile = backupListDataGridView.SelectedRows[0].Cells["FileName"].Value.ToString();
                var backupFilePath = Path.Combine(backupPathTextBox.Text, selectedFile);

                backupStatusLabel.Text = "الحالة: جاري التحقق من النسخة الاحتياطية...";
                backupStatusLabel.ForeColor = Color.Orange;

                AddLogEntry($"بدء التحقق من النسخة الاحتياطية: {selectedFile}", Color.Blue);

                var verifyResult = await _backupService.VerifyBackupAsync(backupFilePath);

                if (verifyResult.IsValid)
                {
                    backupStatusLabel.Text = "الحالة: النسخة الاحتياطية صالحة";
                    backupStatusLabel.ForeColor = Color.Green;
                    AddLogEntry("النسخة الاحتياطية صالحة ويمكن استعادتها", Color.Green);
                    MessageBox.Show("النسخة الاحتياطية صالحة ويمكن استعادتها", "التحقق",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    backupStatusLabel.Text = "الحالة: النسخة الاحتياطية تالفة";
                    backupStatusLabel.ForeColor = Color.Red;
                    AddLogEntry("النسخة الاحتياطية تالفة أو غير صالحة", Color.Red);
                    MessageBox.Show("النسخة الاحتياطية تالفة أو غير صالحة", "التحقق",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من النسخة الاحتياطية");
                AddLogEntry($"خطأ في التحقق: {ex.Message}", Color.Red);
            }
        }

        private void BackupList_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSelection = backupListDataGridView.SelectedRows.Count > 0;
            restoreBackupButton.Enabled = hasSelection;
            deleteBackupButton.Enabled = hasSelection;
            verifyBackupButton.Enabled = hasSelection;
        }

        private async void BackupTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                AddLogEntry("بدء النسخ الاحتياطي التلقائي", Color.Blue);

                var backupPath = Path.Combine(backupPathTextBox.Text,
                    $"auto-backup-{DateTime.Now:yyyyMMdd-HHmmss}.db");

                var autoBackupResult = await _backupService.CreateFullBackupAsync(backupPath);

                if (autoBackupResult.Success)
                {
                    AddLogEntry("تم إنشاء النسخة الاحتياطية التلقائية بنجاح", Color.Green);
                    LoadBackupList();

                    // Clean up old backups
                    await CleanupOldBackups();
                }
                else
                {
                    AddLogEntry("فشل في إنشاء النسخة الاحتياطية التلقائية", Color.Red);
                }

                UpdateNextBackupTime();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في النسخ الاحتياطي التلقائي");
                AddLogEntry($"خطأ في النسخ التلقائي: {ex.Message}", Color.Red);
            }
        }

        private async Task CleanupOldBackups()
        {
            try
            {
                var backupPath = backupPathTextBox.Text;
                var maxFiles = (int)maxBackupFilesNumericUpDown.Value;

                var backupFiles = Directory.GetFiles(backupPath, "*.db")
                    .Select(f => new FileInfo(f))
                    .OrderByDescending(f => f.CreationTime)
                    .ToList();

                if (backupFiles.Count > maxFiles)
                {
                    var filesToDelete = backupFiles.Skip(maxFiles);
                    foreach (var file in filesToDelete)
                    {
                        file.Delete();
                        AddLogEntry($"تم حذف النسخة الاحتياطية القديمة: {file.Name}", Color.Orange);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنظيف النسخ الاحتياطية القديمة");
                AddLogEntry($"خطأ في التنظيف: {ex.Message}", Color.Red);
            }
        }

        private void ClearLog_Click(object sender, EventArgs e)
        {
            logTextBox.Clear();
            AddLogEntry("تم مسح السجل", Color.Blue);
        }

        private void ExportLog_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Text Files|*.txt|All Files|*.*",
                    DefaultExt = "txt",
                    FileName = $"backup-log-{DateTime.Now:yyyyMMdd-HHmmss}.txt"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    File.WriteAllText(saveDialog.FileName, logTextBox.Text);
                    AddLogEntry($"تم تصدير السجل إلى: {saveDialog.FileName}", Color.Green);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير السجل");
                AddLogEntry($"خطأ في التصدير: {ex.Message}", Color.Red);
            }
        }

        private void SaveSettings_Click(object sender, EventArgs e)
        {
            try
            {
                // Save settings to configuration
                _backupTimer.Interval = (int)TimeSpan.FromHours((double)intervalHoursNumericUpDown.Value).TotalMilliseconds;

                if (enableAutoBackupCheckBox.Checked)
                {
                    _backupTimer.Start();
                }
                else
                {
                    _backupTimer.Stop();
                }

                UpdateNextBackupTime();
                AddLogEntry("تم حفظ الإعدادات بنجاح", Color.Green);
                MessageBox.Show("تم حفظ الإعدادات بنجاح", "حفظ",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ الإعدادات");
                AddLogEntry($"خطأ في الحفظ: {ex.Message}", Color.Red);
            }
        }

        private void Cancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _backupTimer?.Stop();
                _backupTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}

