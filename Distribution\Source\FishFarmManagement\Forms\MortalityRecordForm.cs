﻿using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ ØªØ³Ø¬ÙŠÙ„ Ø§Ù„Ù†ÙÙˆÙ‚
    /// Mortality Record Form
    /// </summary>
    public partial class MortalityRecordForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<MortalityRecordForm> _logger;

        // UI Controls
        private ComboBox pondComboBox;
        private DateTimePicker mortalityDatePicker;
        private NumericUpDown deadFishCountNumericUpDown;
        private ComboBox causeComboBox;
        private TextBox customCauseTextBox;
        private NumericUpDown averageWeightNumericUpDown;
        private Label totalWeightLabel;
        private ComboBox severityComboBox;
        private TextBox symptomsTextBox;
        private TextBox actionTakenTextBox;
        private TextBox notesTextBox;
        private Button saveButton;
        private Button cancelButton;
        private DataGridView mortalityHistoryDataGridView;
        private Label mortalityRateLabel;

        public MortalityRecordForm(IUnitOfWork unitOfWork, ILogger<MortalityRecordForm> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            InitializeComponent();
            LoadInitialData();
        }

        private void InitializeComponent()
        {
            this.Text = "ØªØ³Ø¬ÙŠÙ„ Ø§Ù„Ù†ÙÙˆÙ‚";
            this.Size = new Size(1000, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 2,
                ColumnCount = 1,
                Padding = new Padding(20)
            };

            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 450));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            // Input Panel
            var inputPanel = CreateInputPanel();
            mainPanel.Controls.Add(inputPanel, 0, 0);

            // History Panel
            var historyPanel = CreateHistoryPanel();
            mainPanel.Controls.Add(historyPanel, 0, 1);

            this.Controls.Add(mainPanel);
        }

        private GroupBox CreateInputPanel()
        {
            var panel = new GroupBox
            {
                Text = "ØªØ³Ø¬ÙŠÙ„ Ø­Ø§Ù„Ø© Ù†ÙÙˆÙ‚ Ø¬Ø¯ÙŠØ¯Ø©",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Padding = new Padding(15)
            };

            var leftPanel = new Panel
            {
                Location = new Point(20, 30),
                Size = new Size(400, 380)
            };

            var rightPanel = new Panel
            {
                Location = new Point(450, 30),
                Size = new Size(400, 380)
            };

            // Left Panel Controls
            int y = 10;
            int spacing = 35;

            // Pond selection
            var pondLabel = CreateLabel("Ø§Ù„Ø­ÙˆØ¶:", new Point(300, y));
            pondComboBox = new ComboBox
            {
                Location = new Point(100, y),
                Size = new Size(180, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            pondComboBox.SelectedIndexChanged += PondComboBox_SelectedIndexChanged;
            y += spacing;

            // Mortality date
            var dateLabel = CreateLabel("ØªØ§Ø±ÙŠØ® Ø§Ù„Ù†ÙÙˆÙ‚:", new Point(300, y));
            mortalityDatePicker = new DateTimePicker
            {
                Location = new Point(100, y),
                Size = new Size(180, 23),
                Value = DateTime.Now
            };
            y += spacing;

            // Dead fish count
            var countLabel = CreateLabel("Ø¹Ø¯Ø¯ Ø§Ù„Ø£Ø³Ù…Ø§Ùƒ Ø§Ù„Ù†Ø§ÙÙ‚Ø©:", new Point(300, y));
            deadFishCountNumericUpDown = new NumericUpDown
            {
                Location = new Point(100, y),
                Size = new Size(180, 23),
                Maximum = 100000,
                Minimum = 1,
                Value = 1
            };
            deadFishCountNumericUpDown.ValueChanged += CalculateTotalWeight;
            y += spacing;

            // Average weight
            var weightLabel = CreateLabel("Ù…ØªÙˆØ³Ø· Ø§Ù„ÙˆØ²Ù† (Ø¬Ø±Ø§Ù…):", new Point(300, y));
            averageWeightNumericUpDown = new NumericUpDown
            {
                Location = new Point(100, y),
                Size = new Size(180, 23),
                DecimalPlaces = 2,
                Maximum = 10000,
                Minimum = 0.1m,
                Value = 100
            };
            averageWeightNumericUpDown.ValueChanged += CalculateTotalWeight;
            y += spacing;

            // Total weight
            var totalWeightTitleLabel = CreateLabel("Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„ÙˆØ²Ù† (ÙƒØ¬Ù…):", new Point(300, y));
            totalWeightLabel = new Label
            {
                Location = new Point(100, y),
                Size = new Size(180, 23),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(240, 240, 240),
                TextAlign = ContentAlignment.MiddleLeft,
                Text = "0.10"
            };
            y += spacing;

            // Mortality rate
            var mortalityRateTitleLabel = CreateLabel("Ù…Ø¹Ø¯Ù„ Ø§Ù„Ù†ÙÙˆÙ‚:", new Point(300, y));
            mortalityRateLabel = new Label
            {
                Location = new Point(100, y),
                Size = new Size(180, 23),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(255, 240, 240),
                TextAlign = ContentAlignment.MiddleLeft,
                Text = "0.00%",
                ForeColor = Color.Red,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            y += spacing;

            // Cause of death
            var causeLabel = CreateLabel("Ø³Ø¨Ø¨ Ø§Ù„Ù†ÙÙˆÙ‚:", new Point(300, y));
            causeComboBox = new ComboBox
            {
                Location = new Point(100, y),
                Size = new Size(180, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            causeComboBox.SelectedIndexChanged += CauseComboBox_SelectedIndexChanged;
            y += spacing;

            // Custom cause
            customCauseTextBox = new TextBox
            {
                Location = new Point(100, y),
                Size = new Size(180, 23),
                Visible = false,
                PlaceholderText = "Ø­Ø¯Ø¯ Ø§Ù„Ø³Ø¨Ø¨..."
            };
            y += spacing;

            // Severity
            var severityLabel = CreateLabel("Ø¯Ø±Ø¬Ø© Ø§Ù„Ø®Ø·ÙˆØ±Ø©:", new Point(300, y));
            severityComboBox = new ComboBox
            {
                Location = new Point(100, y),
                Size = new Size(180, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            leftPanel.Controls.AddRange(new Control[]
            {
                pondLabel, pondComboBox,
                dateLabel, mortalityDatePicker,
                countLabel, deadFishCountNumericUpDown,
                weightLabel, averageWeightNumericUpDown,
                totalWeightTitleLabel, totalWeightLabel,
                mortalityRateTitleLabel, mortalityRateLabel,
                causeLabel, causeComboBox,
                customCauseTextBox,
                severityLabel, severityComboBox
            });

            // Right Panel Controls
            y = 10;

            // Symptoms
            var symptomsLabel = CreateLabel("Ø§Ù„Ø£Ø¹Ø±Ø§Ø¶ Ø§Ù„Ù…Ù„Ø§Ø­Ø¸Ø©:", new Point(300, y));
            symptomsTextBox = new TextBox
            {
                Location = new Point(50, y + 25),
                Size = new Size(320, 80),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                PlaceholderText = "Ø§ÙƒØªØ¨ Ø§Ù„Ø£Ø¹Ø±Ø§Ø¶ Ø§Ù„ØªÙŠ Ù„Ø§Ø­Ø¸ØªÙ‡Ø§..."
            };
            y += 110;

            // Action taken
            var actionLabel = CreateLabel("Ø§Ù„Ø¥Ø¬Ø±Ø§Ø¡Ø§Øª Ø§Ù„Ù…ØªØ®Ø°Ø©:", new Point(300, y));
            actionTakenTextBox = new TextBox
            {
                Location = new Point(50, y + 25),
                Size = new Size(320, 80),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                PlaceholderText = "Ø§ÙƒØªØ¨ Ø§Ù„Ø¥Ø¬Ø±Ø§Ø¡Ø§Øª Ø§Ù„ØªÙŠ Ø§ØªØ®Ø°ØªÙ‡Ø§..."
            };
            y += 110;

            // Notes
            var notesLabel = CreateLabel("Ù…Ù„Ø§Ø­Ø¸Ø§Øª Ø¥Ø¶Ø§ÙÙŠØ©:", new Point(300, y));
            notesTextBox = new TextBox
            {
                Location = new Point(50, y + 25),
                Size = new Size(320, 80),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                PlaceholderText = "Ø£ÙŠ Ù…Ù„Ø§Ø­Ø¸Ø§Øª Ø£Ø®Ø±Ù‰..."
            };
            y += 110;

            // Buttons
            saveButton = new Button
            {
                Text = "Ø­ÙØ¸",
                Location = new Point(200, y),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "Ø¥Ù„ØºØ§Ø¡",
                Location = new Point(100, y),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            cancelButton.Click += (s, e) => this.Close();

            rightPanel.Controls.AddRange(new Control[]
            {
                symptomsLabel, symptomsTextBox,
                actionLabel, actionTakenTextBox,
                notesLabel, notesTextBox,
                saveButton, cancelButton
            });

            panel.Controls.AddRange(new Control[] { leftPanel, rightPanel });
            return panel;
        }

        private GroupBox CreateHistoryPanel()
        {
            var panel = new GroupBox
            {
                Text = "Ø³Ø¬Ù„ Ø§Ù„Ù†ÙÙˆÙ‚",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Padding = new Padding(15)
            };

            mortalityHistoryDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F)
            };

            SetupHistoryColumns();
            panel.Controls.Add(mortalityHistoryDataGridView);

            return panel;
        }

        private void SetupHistoryColumns()
        {
            mortalityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PondNumber",
                HeaderText = "Ø±Ù‚Ù… Ø§Ù„Ø­ÙˆØ¶",
                DataPropertyName = "PondNumber",
                Width = 80
            });

            mortalityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "MortalityDate",
                HeaderText = "Ø§Ù„ØªØ§Ø±ÙŠØ®",
                DataPropertyName = "MortalityDate",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            mortalityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DeadFishCount",
                HeaderText = "Ø§Ù„Ø¹Ø¯Ø¯",
                DataPropertyName = "DeadFishCount",
                Width = 60
            });

            mortalityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalWeight",
                HeaderText = "Ø§Ù„ÙˆØ²Ù† (ÙƒØ¬Ù…)",
                DataPropertyName = "TotalWeight",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            mortalityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Cause",
                HeaderText = "Ø§Ù„Ø³Ø¨Ø¨",
                DataPropertyName = "Cause",
                Width = 120
            });

            mortalityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Severity",
                HeaderText = "Ø§Ù„Ø®Ø·ÙˆØ±Ø©",
                DataPropertyName = "Severity",
                Width = 80
            });

            mortalityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "MortalityRate",
                HeaderText = "Ù…Ø¹Ø¯Ù„ Ø§Ù„Ù†ÙÙˆÙ‚",
                DataPropertyName = "MortalityRate",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "P2" }
            });

            mortalityHistoryDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Symptoms",
                HeaderText = "Ø§Ù„Ø£Ø¹Ø±Ø§Ø¶",
                DataPropertyName = "Symptoms",
                Width = 150
            });
        }

        private Label CreateLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
        }

        private async void LoadInitialData()
        {
            try
            {
                // Load ponds
                var ponds = await _unitOfWork.Ponds.GetAllAsync();
                var pondList = ponds.Select(p => new { Value = p.Id, Text = $"Ø­ÙˆØ¶ {p.PondNumber}" }).ToList();
                
                pondComboBox.DataSource = pondList;
                pondComboBox.DisplayMember = "Text";
                pondComboBox.ValueMember = "Value";

                // Load causes of death
                var causes = new[]
                {
                    "Ù…Ø±Ø¶",
                    "Ù†Ù‚Øµ Ø§Ù„Ø£ÙƒØ³Ø¬ÙŠÙ†",
                    "ØªÙ„ÙˆØ« Ø§Ù„Ù…ÙŠØ§Ù‡",
                    "ØªØºÙŠØ± Ø¯Ø±Ø¬Ø© Ø§Ù„Ø­Ø±Ø§Ø±Ø©",
                    "Ø³ÙˆØ¡ Ø§Ù„ØªØºØ°ÙŠØ©",
                    "Ø¥ØµØ§Ø¨Ø© Ø¬Ø³Ø¯ÙŠØ©",
                    "Ø´ÙŠØ®ÙˆØ®Ø© Ø·Ø¨ÙŠØ¹ÙŠØ©",
                    "Ù…Ø¬Ù‡ÙˆÙ„ Ø§Ù„Ø³Ø¨Ø¨",
                    "Ø£Ø®Ø±Ù‰..."
                };
                causeComboBox.Items.AddRange(causes);
                causeComboBox.SelectedIndex = 0;

                // Load severity levels
                var severityLevels = new[]
                {
                    "Ù…Ù†Ø®ÙØ¶Ø©",
                    "Ù…ØªÙˆØ³Ø·Ø©",
                    "Ø¹Ø§Ù„ÙŠØ©",
                    "Ø­Ø±Ø¬Ø©"
                };
                severityComboBox.Items.AddRange(severityLevels);
                severityComboBox.SelectedIndex = 0;

                // Load mortality history
                await LoadMortalityHistory();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ø£ÙˆÙ„ÙŠØ©");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadMortalityHistory()
        {
            try
            {
                // Placeholder for mortality records
                var historyData = new List<object>();
                mortalityHistoryDataGridView.DataSource = historyData;

                await Task.CompletedTask; // Ù„ØªØ¬Ù†Ø¨ ØªØ­Ø°ÙŠØ± async
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø³Ø¬Ù„ Ø§Ù„Ù†ÙÙˆÙ‚");
            }
        }

        private async void PondComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            await CalculateMortalityRate();
        }

        private void CauseComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            customCauseTextBox.Visible = causeComboBox.SelectedItem?.ToString() == "Ø£Ø®Ø±Ù‰...";
        }

        private void CalculateTotalWeight(object sender, EventArgs e)
        {
            var count = deadFishCountNumericUpDown.Value;
            var avgWeight = averageWeightNumericUpDown.Value;
            var totalWeight = count * avgWeight / 1000; // Convert to kg
            
            totalWeightLabel.Text = totalWeight.ToString("N2");
        }

        private async Task CalculateMortalityRate()
        {
            try
            {
                if (pondComboBox.SelectedValue == null)
                    return;

                var pondId = (int)pondComboBox.SelectedValue;
                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                
                if (pond != null && pond.FishCount > 0)
                {
                    var mortalityRate = (double)deadFishCountNumericUpDown.Value / pond.FishCount * 100;
                    mortalityRateLabel.Text = $"{mortalityRate:F2}%";
                    
                    // Color coding based on mortality rate
                    if (mortalityRate < 2)
                        mortalityRateLabel.ForeColor = Color.Green;
                    else if (mortalityRate < 5)
                        mortalityRateLabel.ForeColor = Color.Orange;
                    else
                        mortalityRateLabel.ForeColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­Ø³Ø§Ø¨ Ù…Ø¹Ø¯Ù„ Ø§Ù„Ù†ÙÙˆÙ‚");
            }
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                // Create mortality record
                var mortalityRecord = new
                {
                    PondId = (int)pondComboBox.SelectedValue,
                    MortalityDate = mortalityDatePicker.Value.Date,
                    DeadFishCount = (int)deadFishCountNumericUpDown.Value,
                    AverageWeight = averageWeightNumericUpDown.Value,
                    TotalWeight = deadFishCountNumericUpDown.Value * averageWeightNumericUpDown.Value / 1000,
                    Cause = causeComboBox.SelectedItem.ToString() == "Ø£Ø®Ø±Ù‰..." ? 
                           customCauseTextBox.Text : causeComboBox.SelectedItem.ToString(),
                    Severity = severityComboBox.SelectedItem.ToString(),
                    Symptoms = symptomsTextBox.Text,
                    ActionTaken = actionTakenTextBox.Text,
                    Notes = notesTextBox.Text,
                    RecordedDate = DateTime.Now
                };

                // Update pond fish count
                var pondId = (int)pondComboBox.SelectedValue;
                var pond = await _unitOfWork.Ponds.GetByIdAsync(pondId);
                if (pond != null)
                {
                    pond.FishCount -= (int)deadFishCountNumericUpDown.Value;
                    await _unitOfWork.Ponds.UpdateAsync(pond);
                    await _unitOfWork.SaveChangesAsync();
                }

                MessageBox.Show("ØªÙ… Ø­ÙØ¸ Ø³Ø¬Ù„ Ø§Ù„Ù†ÙÙˆÙ‚ Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ClearForm();
                await LoadMortalityHistory();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­ÙØ¸ Ø³Ø¬Ù„ Ø§Ù„Ù†ÙÙˆÙ‚");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø­ÙØ¸: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (pondComboBox.SelectedValue == null)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ø§Ù„Ø­ÙˆØ¶", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (deadFishCountNumericUpDown.Value <= 0)
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø¥Ø¯Ø®Ø§Ù„ Ø¹Ø¯Ø¯ ØµØ­ÙŠØ­ Ù„Ù„Ø£Ø³Ù…Ø§Ùƒ Ø§Ù„Ù†Ø§ÙÙ‚Ø©", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (causeComboBox.SelectedItem?.ToString() == "Ø£Ø®Ø±Ù‰..." && string.IsNullOrWhiteSpace(customCauseTextBox.Text))
            {
                MessageBox.Show("ÙŠØ±Ø¬Ù‰ ØªØ­Ø¯ÙŠØ¯ Ø³Ø¨Ø¨ Ø§Ù„Ù†ÙÙˆÙ‚", "ØªÙ†Ø¨ÙŠÙ‡", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            mortalityDatePicker.Value = DateTime.Now;
            deadFishCountNumericUpDown.Value = 1;
            averageWeightNumericUpDown.Value = 100;
            causeComboBox.SelectedIndex = 0;
            severityComboBox.SelectedIndex = 0;
            customCauseTextBox.Clear();
            symptomsTextBox.Clear();
            actionTakenTextBox.Clear();
            notesTextBox.Clear();
            totalWeightLabel.Text = "0.10";
            mortalityRateLabel.Text = "0.00%";
        }
    }
}



