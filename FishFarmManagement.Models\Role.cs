using System.Collections.Generic;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// Represents a user role within the system (e.g., <PERSON><PERSON>, Manager).
    /// </summary>
    public class Role
    {
        public int Id { get; set; }
        public string RoleName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public ICollection<User> Users { get; set; } = new List<User>();
        public ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
        public ICollection<Permission> Permissions { get; set; } = new List<Permission>();
    }
}