﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.Forms
{
    public partial class ProductionReportsForm : Form
    {
        private readonly ILogger<ProductionReportsForm> _logger;
        private TabControl reportsTabControl;
        private TabPage productionSummaryTab;
        private TabPage pondPerformanceTab;
        private TabPage feedConsumptionTab;
        private TabPage mortalityAnalysisTab;
        private TabPage growthAnalysisTab;

        public ProductionReportsForm(ILogger<ProductionReportsForm> logger)
        {
            _logger = logger;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "تقارير الإنتاج";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            CreateControls();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(46, 204, 113),
                Padding = new Padding(20, 10, 20, 10)
            };

            var titleLabel = new Label
            {
                Text = "تقارير الإنتاج",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 25)
            };

            headerPanel.Controls.Add(titleLabel);

            // Tab control
            reportsTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            // Create tabs
            CreateProductionSummaryTab();
            CreatePondPerformanceTab();
            CreateFeedConsumptionTab();
            CreateMortalityAnalysisTab();
            CreateGrowthAnalysisTab();

            reportsTabControl.TabPages.AddRange(new TabPage[] 
            { 
                productionSummaryTab, pondPerformanceTab, feedConsumptionTab, 
                mortalityAnalysisTab, growthAnalysisTab 
            });

            this.Controls.AddRange(new Control[] { reportsTabControl, headerPanel });
        }

        private void CreateProductionSummaryTab()
        {
            productionSummaryTab = new TabPage("ملخص الإنتاج");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // Date range controls
            var datePanel = new Panel { Height = 60, Dock = DockStyle.Top };
            
            var fromLabel = new Label
            {
                Text = "من تاريخ:",
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var fromDatePicker = new DateTimePicker
            {
                Size = new Size(150, 25),
                Location = new Point(80, 17),
                Value = DateTime.Now.AddMonths(-1)
            };

            var toLabel = new Label
            {
                Text = "إلى تاريخ:",
                AutoSize = true,
                Location = new Point(250, 20)
            };

            var toDatePicker = new DateTimePicker
            {
                Size = new Size(150, 25),
                Location = new Point(310, 17),
                Value = DateTime.Now
            };

            var generateButton = new Button
            {
                Text = "إنشاء التقرير",
                Size = new Size(120, 30),
                Location = new Point(480, 15),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            generateButton.Click += (s, e) => GenerateProductionSummary(fromDatePicker.Value, toDatePicker.Value);

            datePanel.Controls.AddRange(new Control[] { fromLabel, fromDatePicker, toLabel, toDatePicker, generateButton });

            // Report content area
            var reportPanel = new Panel { Dock = DockStyle.Fill, BackColor = Color.White };
            
            var summaryGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                ReadOnly = true,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                Font = new Font("Segoe UI", 9F)
            };

            // Setup summary grid columns
            summaryGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "PondName", HeaderText = "اسم الحوض", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "FishCount", HeaderText = "عدد الأسماك", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "TotalWeight", HeaderText = "الوزن الإجمالي (كجم)", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "AverageWeight", HeaderText = "متوسط الوزن (جم)", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "FeedConsumed", HeaderText = "العلف المستهلك (كجم)", Width = 130 },
                new DataGridViewTextBoxColumn { Name = "FCR", HeaderText = "معدل التحويل الغذائي", Width = 130 },
                new DataGridViewTextBoxColumn { Name = "Mortality", HeaderText = "النفوق (%)", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "GrowthRate", HeaderText = "معدل النمو (%)", Width = 120 }
            });

            reportPanel.Controls.Add(summaryGrid);
            panel.Controls.AddRange(new Control[] { reportPanel, datePanel });
            productionSummaryTab.Controls.Add(panel);
        }

        private void CreatePondPerformanceTab()
        {
            pondPerformanceTab = new TabPage("أداء الأحواض");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // Pond selection and controls
            var controlPanel = new Panel { Height = 60, Dock = DockStyle.Top };
            
            var pondLabel = new Label
            {
                Text = "اختر الحوض:",
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var pondComboBox = new ComboBox
            {
                Size = new Size(200, 25),
                Location = new Point(100, 17),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            pondComboBox.Items.AddRange(new[] { "جميع الأحواض", "حوض رقم 1", "حوض رقم 2", "حوض رقم 3" });
            pondComboBox.SelectedIndex = 0;

            var periodLabel = new Label
            {
                Text = "الفترة:",
                AutoSize = true,
                Location = new Point(320, 20)
            };

            var periodComboBox = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(370, 17),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            periodComboBox.Items.AddRange(new[] { "آخر شهر", "آخر 3 أشهر", "آخر 6 أشهر", "السنة الحالية" });
            periodComboBox.SelectedIndex = 0;

            var analyzeButton = new Button
            {
                Text = "تحليل الأداء",
                Size = new Size(120, 30),
                Location = new Point(540, 15),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            controlPanel.Controls.AddRange(new Control[] { pondLabel, pondComboBox, periodLabel, periodComboBox, analyzeButton });

            // Performance metrics panel
            var metricsPanel = new Panel { Dock = DockStyle.Fill };
            
            // Create performance charts placeholder
            var chartsLabel = new Label
            {
                Text = "سيتم عرض مخططات الأداء هنا\n\n• مخطط نمو الأسماك\n• مخطط استهلاك العلف\n• مخطط معدل النفوق\n• مخطط جودة المياه",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            metricsPanel.Controls.Add(chartsLabel);
            panel.Controls.AddRange(new Control[] { metricsPanel, controlPanel });
            pondPerformanceTab.Controls.Add(panel);
        }

        private void CreateFeedConsumptionTab()
        {
            feedConsumptionTab = new TabPage("استهلاك العلف");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            
            var placeholderLabel = new Label
            {
                Text = "تقرير استهلاك العلف\n\n• استهلاك العلف اليومي\n• معدل التحويل الغذائي\n• تكلفة العلف\n• مقارنة أنواع العلف",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            panel.Controls.Add(placeholderLabel);
            feedConsumptionTab.Controls.Add(panel);
        }

        private void CreateMortalityAnalysisTab()
        {
            mortalityAnalysisTab = new TabPage("تحليل النفوق");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            
            var placeholderLabel = new Label
            {
                Text = "تحليل النفوق\n\n• معدل النفوق اليومي\n• أسباب النفوق\n• النفوق حسب الحوض\n• الاتجاهات الزمنية للنفوق",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            panel.Controls.Add(placeholderLabel);
            mortalityAnalysisTab.Controls.Add(panel);
        }

        private void CreateGrowthAnalysisTab()
        {
            growthAnalysisTab = new TabPage("تحليل النمو");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            
            var placeholderLabel = new Label
            {
                Text = "تحليل النمو\n\n• منحنيات النمو\n• معدل النمو اليومي\n• مقارنة الأحواض\n• توقعات النمو",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            panel.Controls.Add(placeholderLabel);
            growthAnalysisTab.Controls.Add(panel);
        }

        private void GenerateProductionSummary(DateTime fromDate, DateTime toDate)
        {
            try
            {
                // Sample production data
                var sampleData = new List<dynamic>
                {
                    new { PondName = "حوض رقم 1", FishCount = 5000, TotalWeight = 2500.0, AverageWeight = 500.0, FeedConsumed = 3000.0, FCR = 1.2, Mortality = 2.5, GrowthRate = 15.2 },
                    new { PondName = "حوض رقم 2", FishCount = 4500, TotalWeight = 2250.0, AverageWeight = 500.0, FeedConsumed = 2800.0, FCR = 1.24, Mortality = 3.1, GrowthRate = 14.8 },
                    new { PondName = "حوض رقم 3", FishCount = 4800, TotalWeight = 2400.0, AverageWeight = 500.0, FeedConsumed = 2900.0, FCR = 1.21, Mortality = 2.8, GrowthRate = 15.0 }
                };

                var summaryGrid = (DataGridView)productionSummaryTab.Controls[0].Controls[0].Controls[0];
                summaryGrid.DataSource = sampleData;

                _logger.LogInformation($"تم إنشاء تقرير ملخص الإنتاج للفترة من {fromDate:dd/MM/yyyy} إلى {toDate:dd/MM/yyyy}");
                
                MessageBox.Show("تم إنشاء تقرير ملخص الإنتاج بنجاح", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير ملخص الإنتاج");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}

