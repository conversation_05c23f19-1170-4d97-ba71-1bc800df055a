﻿using FishFarmManagement.DAL.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// نموذج تقرير ميزان المراجعة
    /// Trial balance report form
    /// </summary>
    public partial class TrialBalanceReportForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;

        public TrialBalanceReportForm(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "تقرير ميزان المراجعة";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            var label = new Label
            {
                Text = "تقرير ميزان المراجعة - قيد التطوير",
                Font = new Font("Segoe UI", 16F),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            this.Controls.Add(label);
        }
    }
}

