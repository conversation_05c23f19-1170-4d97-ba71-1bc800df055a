﻿﻿using System;
using System.IO;
using System.Windows.Forms;
using System.Drawing;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using FishFarmManagement.BLL.Services.Interfaces;

namespace FishFarmManagement.Forms
{
    public partial class BackupRestoreForm : Form
    {
        private readonly ILogger<BackupRestoreForm> _logger;
        private readonly IConfiguration _configuration;
        private readonly IBackupService _backupService;
        private TabControl tabControl;
        private TabPage backupTab;
        private TabPage restoreTab;

        public BackupRestoreForm(ILogger<BackupRestoreForm> logger, IConfiguration configuration, IBackupService backupService)
        {
            _logger = logger;
            _configuration = configuration;
            _backupService = backupService;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "النسخ الاحتياطي والاستعادة";
            this.Size = new Size(600, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            // Create tab control
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            // Create backup tab
            backupTab = new TabPage("إنشاء نسخة احتياطية");
            CreateBackupTab();

            // Create restore tab
            restoreTab = new TabPage("استعادة نسخة احتياطية");
            CreateRestoreTab();

            tabControl.TabPages.Add(backupTab);
            tabControl.TabPages.Add(restoreTab);

            this.Controls.Add(tabControl);
        }

        private void CreateBackupTab()
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var titleLabel = new Label
            {
                Text = "إنشاء نسخة احتياطية من قاعدة البيانات",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var descLabel = new Label
            {
                Text = "سيتم إنشاء نسخة احتياطية من جميع البيانات في الملف المحدد",
                AutoSize = true,
                Location = new Point(20, 50),
                ForeColor = Color.Gray
            };

            var pathLabel = new Label
            {
                Text = "مسار حفظ النسخة الاحتياطية:",
                AutoSize = true,
                Location = new Point(20, 90)
            };

            var pathTextBox = new TextBox
            {
                Size = new Size(400, 25),
                Location = new Point(20, 115),
                Text = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), 
                    $"FishFarm_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.db")
            };

            var browseButton = new Button
            {
                Text = "استعراض...",
                Size = new Size(80, 25),
                Location = new Point(430, 115)
            };
            browseButton.Click += (s, e) => BrowseBackupPath(pathTextBox);

            var createButton = new Button
            {
                Text = "إنشاء النسخة الاحتياطية",
                Size = new Size(150, 35),
                Location = new Point(20, 160),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            createButton.Click += (s, e) => CreateBackup(pathTextBox.Text);

            var progressBar = new ProgressBar
            {
                Size = new Size(400, 20),
                Location = new Point(20, 210),
                Visible = false
            };

            var statusLabel = new Label
            {
                Text = "",
                AutoSize = true,
                Location = new Point(20, 240),
                ForeColor = Color.Green
            };

            panel.Controls.AddRange(new Control[] 
            { 
                titleLabel, descLabel, pathLabel, pathTextBox, browseButton, 
                createButton, progressBar, statusLabel 
            });

            // Store references for later use
            panel.Tag = new { ProgressBar = progressBar, StatusLabel = statusLabel };

            backupTab.Controls.Add(panel);
        }

        private void CreateRestoreTab()
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var titleLabel = new Label
            {
                Text = "استعادة نسخة احتياطية",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var warningLabel = new Label
            {
                Text = "تحذير: سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية",
                AutoSize = true,
                Location = new Point(20, 50),
                ForeColor = Color.Red,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            var pathLabel = new Label
            {
                Text = "ملف النسخة الاحتياطية:",
                AutoSize = true,
                Location = new Point(20, 90)
            };

            var pathTextBox = new TextBox
            {
                Size = new Size(400, 25),
                Location = new Point(20, 115)
            };

            var browseButton = new Button
            {
                Text = "استعراض...",
                Size = new Size(80, 25),
                Location = new Point(430, 115)
            };
            browseButton.Click += (s, e) => BrowseRestorePath(pathTextBox);

            var restoreButton = new Button
            {
                Text = "استعادة النسخة الاحتياطية",
                Size = new Size(150, 35),
                Location = new Point(20, 160),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            restoreButton.Click += (s, e) => RestoreBackup(pathTextBox.Text);

            var progressBar = new ProgressBar
            {
                Size = new Size(400, 20),
                Location = new Point(20, 210),
                Visible = false
            };

            var statusLabel = new Label
            {
                Text = "",
                AutoSize = true,
                Location = new Point(20, 240),
                ForeColor = Color.Green
            };

            panel.Controls.AddRange(new Control[] 
            { 
                titleLabel, warningLabel, pathLabel, pathTextBox, browseButton, 
                restoreButton, progressBar, statusLabel 
            });

            // Store references for later use
            panel.Tag = new { ProgressBar = progressBar, StatusLabel = statusLabel };

            restoreTab.Controls.Add(panel);
        }

        private void BrowseBackupPath(TextBox pathTextBox)
        {
            using (var dialog = new SaveFileDialog())
            {
                dialog.Filter = "Database files (*.db)|*.db|All files (*.*)|*.*";
                dialog.DefaultExt = "db";
                dialog.FileName = $"FishFarm_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.db";
                
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    pathTextBox.Text = dialog.FileName;
                }
            }
        }

        private void BrowseRestorePath(TextBox pathTextBox)
        {
            using (var dialog = new OpenFileDialog())
            {
                dialog.Filter = "Database files (*.db)|*.db|All files (*.*)|*.*";
                dialog.DefaultExt = "db";
                
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    pathTextBox.Text = dialog.FileName;
                }
            }
        }

        private async void CreateBackup(string backupPath)
        {
            if (string.IsNullOrWhiteSpace(backupPath))
            {
                MessageBox.Show("يرجى تحديد مسار حفظ النسخة الاحتياطية", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var controls = (dynamic)backupTab.Controls[0].Tag;
                var progressBar = (ProgressBar)controls.ProgressBar;
                var statusLabel = (Label)controls.StatusLabel;

                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                statusLabel.Text = "جاري إنشاء النسخة الاحتياطية...";
                statusLabel.ForeColor = Color.Blue;

                // Use the backup service for a safe backup
                await _backupService.BackupDatabaseAsync(backupPath);

                progressBar.Visible = false;
                statusLabel.Text = "تم إنشاء النسخة الاحتياطية بنجاح";
                statusLabel.ForeColor = Color.Green;

                _logger.LogInformation($"تم إنشاء نسخة احتياطية في: {backupPath}");
                
                MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                var controls = (dynamic)backupTab.Controls[0].Tag;
                var progressBar = (ProgressBar)controls.ProgressBar;
                var statusLabel = (Label)controls.StatusLabel;

                progressBar.Visible = false;
                statusLabel.Text = "فشل في إنشاء النسخة الاحتياطية";
                statusLabel.ForeColor = Color.Red;

                _logger.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية");
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void RestoreBackup(string backupPath)
        {
            if (string.IsNullOrWhiteSpace(backupPath))
            {
                MessageBox.Show("يرجى تحديد ملف النسخة الاحتياطية", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!File.Exists(backupPath))
            {
                MessageBox.Show("الملف المحدد غير موجود", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show(
                "هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.",
                "تأكيد الاستعادة", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result != DialogResult.Yes)
                return;

            try
            {
                var controls = (dynamic)restoreTab.Controls[0].Tag;
                var progressBar = (ProgressBar)controls.ProgressBar;
                var statusLabel = (Label)controls.StatusLabel;

                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                statusLabel.Text = "جاري استعادة النسخة الاحتياطية...";
                statusLabel.ForeColor = Color.Blue;

                // Use the backup service for a safe restore
                await _backupService.RestoreDatabaseAsync(backupPath);

                progressBar.Visible = false;
                statusLabel.Text = "تم استعادة النسخة الاحتياطية بنجاح";
                statusLabel.ForeColor = Color.Green;

                _logger.LogInformation($"تم استعادة النسخة الاحتياطية من: {backupPath}");
                
                MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل البرنامج.", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                var controls = (dynamic)restoreTab.Controls[0].Tag;
                var progressBar = (ProgressBar)controls.ProgressBar;
                var statusLabel = (Label)controls.StatusLabel;

                progressBar.Visible = false;
                statusLabel.Text = "فشل في استعادة النسخة الاحتياطية";
                statusLabel.ForeColor = Color.Red;

                _logger.LogError(ex, "خطأ في استعادة النسخة الاحتياطية");
                MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
