﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;

namespace FishFarmManagement.Forms
{
    public partial class CostCentersForm : Form
    {
        private readonly ILogger<CostCentersForm> _logger;
        private DataGridView costCentersGrid;
        private Button addButton;
        private Button editButton;
        private Button deleteButton;
        private Button refreshButton;
        private Button viewReportButton;
        private TextBox searchTextBox;
        private ComboBox statusComboBox;
        private Label totalCentersLabel;
        private Label activeCentersLabel;

        public CostCentersForm(ILogger<CostCentersForm> logger)
        {
            _logger = logger;
            InitializeComponent();
            LoadCostCentersData();
        }

        private void InitializeComponent()
        {
            this.Text = "مراكز التكلفة";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
            SetupLayout();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(155, 89, 182),
                Padding = new Padding(20, 10, 20, 10)
            };

            var titleLabel = new Label
            {
                Text = "مراكز التكلفة",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 25)
            };

            headerPanel.Controls.Add(titleLabel);

            // Toolbar panel
            var toolbarPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(236, 240, 241),
                Padding = new Padding(20, 10, 20, 10)
            };

            // Search controls
            var searchLabel = new Label
            {
                Text = "البحث:",
                AutoSize = true,
                Location = new Point(20, 18),
                Font = new Font("Segoe UI", 10F)
            };

            searchTextBox = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(70, 15),
                Font = new Font("Segoe UI", 10F)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            var statusLabel = new Label
            {
                Text = "الحالة:",
                AutoSize = true,
                Location = new Point(290, 18),
                Font = new Font("Segoe UI", 10F)
            };

            statusComboBox = new ComboBox
            {
                Size = new Size(120, 25),
                Location = new Point(330, 15),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 10F)
            };
            statusComboBox.Items.AddRange(new[] { "الكل", "نشط", "غير نشط" });
            statusComboBox.SelectedIndex = 0;
            statusComboBox.SelectedIndexChanged += StatusComboBox_SelectedIndexChanged;

            // Action buttons
            addButton = new Button
            {
                Text = "إضافة مركز",
                Size = new Size(100, 30),
                Location = new Point(470, 12),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };
            addButton.Click += AddButton_Click;

            editButton = new Button
            {
                Text = "تعديل",
                Size = new Size(80, 30),
                Location = new Point(580, 12),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F),
                Enabled = false
            };
            editButton.Click += EditButton_Click;

            deleteButton = new Button
            {
                Text = "حذف",
                Size = new Size(80, 30),
                Location = new Point(670, 12),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F),
                Enabled = false
            };
            deleteButton.Click += DeleteButton_Click;

            viewReportButton = new Button
            {
                Text = "تقرير التكاليف",
                Size = new Size(100, 30),
                Location = new Point(760, 12),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F),
                Enabled = false
            };
            viewReportButton.Click += ViewReportButton_Click;

            refreshButton = new Button
            {
                Text = "تحديث",
                Size = new Size(80, 30),
                Location = new Point(870, 12),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };
            refreshButton.Click += RefreshButton_Click;

            toolbarPanel.Controls.AddRange(new Control[] 
            { 
                searchLabel, searchTextBox, statusLabel, statusComboBox,
                addButton, editButton, deleteButton, viewReportButton, refreshButton 
            });

            // Status panel
            var statusPanel = new Panel
            {
                Height = 40,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(236, 240, 241),
                Padding = new Padding(20, 10, 20, 10)
            };

            totalCentersLabel = new Label
            {
                Text = "إجمالي المراكز: 0",
                AutoSize = true,
                Location = new Point(20, 12),
                Font = new Font("Segoe UI", 9F)
            };

            activeCentersLabel = new Label
            {
                Text = "المراكز النشطة: 0",
                AutoSize = true,
                Location = new Point(200, 12),
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.FromArgb(46, 204, 113)
            };

            statusPanel.Controls.AddRange(new Control[] { totalCentersLabel, activeCentersLabel });

            // Data grid
            costCentersGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                Font = new Font("Segoe UI", 9F)
            };

            SetupGridColumns();
            costCentersGrid.SelectionChanged += CostCentersGrid_SelectionChanged;
            costCentersGrid.CellDoubleClick += CostCentersGrid_CellDoubleClick;

            this.Controls.AddRange(new Control[] { costCentersGrid, statusPanel, toolbarPanel, headerPanel });
        }

        private void SetupGridColumns()
        {
            costCentersGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "الرقم", Width = 60, Visible = false },
                new DataGridViewTextBoxColumn { Name = "CenterCode", HeaderText = "رمز المركز", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "CenterName", HeaderText = "اسم المركز", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "CenterType", HeaderText = "نوع المركز", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Manager", HeaderText = "المسؤول", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Budget", HeaderText = "الميزانية المخصصة", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "ActualCost", HeaderText = "التكلفة الفعلية", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Variance", HeaderText = "الانحراف", Width = 100 },
                new DataGridViewCheckBoxColumn { Name = "IsActive", HeaderText = "نشط", Width = 60 },
                new DataGridViewTextBoxColumn { Name = "CreatedDate", HeaderText = "تاريخ الإنشاء", Width = 120 }
            });

            // Format currency columns
            costCentersGrid.Columns["Budget"].DefaultCellStyle.Format = "C2";
            costCentersGrid.Columns["ActualCost"].DefaultCellStyle.Format = "C2";
            costCentersGrid.Columns["Variance"].DefaultCellStyle.Format = "C2";
            costCentersGrid.Columns["CreatedDate"].DefaultCellStyle.Format = "dd/MM/yyyy";

            // Set column alignments
            costCentersGrid.Columns["Budget"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            costCentersGrid.Columns["ActualCost"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            costCentersGrid.Columns["Variance"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        }

        private void SetupLayout()
        {
            // Apply RTL layout
            foreach (Control control in this.Controls)
            {
                control.RightToLeft = RightToLeft.Yes;
            }
        }

        private void LoadCostCentersData()
        {
            try
            {
                // Sample cost center data
                var sampleData = new List<dynamic>
                {
                    new { Id = 1, CenterCode = "CC001", CenterName = "حوض الإنتاج رقم 1", CenterType = "إنتاج", Manager = "أحمد محمد", Budget = 50000.00m, ActualCost = 45000.00m, Variance = -5000.00m, IsActive = true, CreatedDate = DateTime.Now.AddMonths(-6) },
                    new { Id = 2, CenterCode = "CC002", CenterName = "حوض الإنتاج رقم 2", CenterType = "إنتاج", Manager = "فاطمة علي", Budget = 60000.00m, ActualCost = 62000.00m, Variance = 2000.00m, IsActive = true, CreatedDate = DateTime.Now.AddMonths(-5) },
                    new { Id = 3, CenterCode = "CC003", CenterName = "قسم الأعلاف", CenterType = "خدمي", Manager = "محمد حسن", Budget = 30000.00m, ActualCost = 28000.00m, Variance = -2000.00m, IsActive = true, CreatedDate = DateTime.Now.AddMonths(-4) },
                    new { Id = 4, CenterCode = "CC004", CenterName = "قسم الصيانة", CenterType = "خدمي", Manager = "سارة أحمد", Budget = 25000.00m, ActualCost = 27000.00m, Variance = 2000.00m, IsActive = true, CreatedDate = DateTime.Now.AddMonths(-3) },
                    new { Id = 5, CenterCode = "CC005", CenterName = "الإدارة العامة", CenterType = "إداري", Manager = "خالد محمود", Budget = 40000.00m, ActualCost = 38000.00m, Variance = -2000.00m, IsActive = true, CreatedDate = DateTime.Now.AddMonths(-2) },
                    new { Id = 6, CenterCode = "CC006", CenterName = "حوض التجارب", CenterType = "بحث وتطوير", Manager = "نور الدين", Budget = 20000.00m, ActualCost = 15000.00m, Variance = -5000.00m, IsActive = false, CreatedDate = DateTime.Now.AddMonths(-1) }
                };

                costCentersGrid.DataSource = sampleData;
                UpdateStatusLabels();
                HighlightVariances();

                _logger.LogInformation("تم تحميل بيانات مراكز التكلفة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل بيانات مراكز التكلفة");
                MessageBox.Show($"خطأ في تحميل بيانات مراكز التكلفة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateStatusLabels()
        {
            var totalCenters = costCentersGrid.Rows.Count;
            var activeCenters = 0;

            foreach (DataGridViewRow row in costCentersGrid.Rows)
            {
                if (row.Cells["IsActive"].Value != null && (bool)row.Cells["IsActive"].Value)
                    activeCenters++;
            }

            totalCentersLabel.Text = $"إجمالي المراكز: {totalCenters}";
            activeCentersLabel.Text = $"المراكز النشطة: {activeCenters}";
        }

        private void HighlightVariances()
        {
            foreach (DataGridViewRow row in costCentersGrid.Rows)
            {
                if (row.Cells["Variance"].Value != null)
                {
                    var variance = Convert.ToDecimal(row.Cells["Variance"].Value);
                    
                    if (variance > 0) // Over budget
                    {
                        row.Cells["Variance"].Style.BackColor = Color.FromArgb(255, 235, 235);
                        row.Cells["Variance"].Style.ForeColor = Color.FromArgb(169, 68, 66);
                    }
                    else if (variance < 0) // Under budget
                    {
                        row.Cells["Variance"].Style.BackColor = Color.FromArgb(235, 255, 235);
                        row.Cells["Variance"].Style.ForeColor = Color.FromArgb(60, 118, 61);
                    }
                }
            }
        }

        // Event handlers
        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            FilterData();
        }

        private void StatusComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterData();
        }

        private void FilterData()
        {
            // TODO: Implement filtering logic
        }

        private void CostCentersGrid_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSelection = costCentersGrid.SelectedRows.Count > 0;
            editButton.Enabled = hasSelection;
            deleteButton.Enabled = hasSelection;
            viewReportButton.Enabled = hasSelection;
        }

        private void CostCentersGrid_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditSelectedCostCenter();
            }
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                var form = new CostCenterAddEditForm(_logger);
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadCostCentersData();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نموذج إضافة مركز التكلفة");
                MessageBox.Show($"خطأ في فتح النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            EditSelectedCostCenter();
        }

        private void EditSelectedCostCenter()
        {
            if (costCentersGrid.SelectedRows.Count > 0)
            {
                var selectedRow = costCentersGrid.SelectedRows[0];
                var centerName = selectedRow.Cells["CenterName"].Value?.ToString();
                MessageBox.Show($"سيتم فتح نموذج تعديل مركز التكلفة: {centerName}", "معلومات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (costCentersGrid.SelectedRows.Count > 0)
            {
                var selectedRow = costCentersGrid.SelectedRows[0];
                var centerName = selectedRow.Cells["CenterName"].Value?.ToString();
                
                var result = MessageBox.Show($"هل أنت متأكد من حذف مركز التكلفة: {centerName}؟", 
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    MessageBox.Show("تم حذف مركز التكلفة بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadCostCentersData();
                }
            }
        }

        private void ViewReportButton_Click(object sender, EventArgs e)
        {
            if (costCentersGrid.SelectedRows.Count > 0)
            {
                var selectedRow = costCentersGrid.SelectedRows[0];
                var centerName = selectedRow.Cells["CenterName"].Value?.ToString();
                MessageBox.Show($"سيتم عرض تقرير التكاليف لمركز: {centerName}", "معلومات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void RefreshButton_Click(object sender, EventArgs e)
        {
            LoadCostCentersData();
        }
    }
}

