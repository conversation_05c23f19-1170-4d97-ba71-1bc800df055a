﻿using Microsoft.Extensions.Logging;
using FishFarmManagement.Models;
using FishFarmManagement.DAL.Interfaces;

namespace FishFarmManagement.Forms
{
    /// <summary>
    /// Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ù…ÙˆØ¸ÙÙŠÙ†
    /// Employee management form
    /// </summary>
    public partial class EmployeeManagementForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<EmployeeManagementForm> _logger;

        // UI Controls
        private DataGridView employeesDataGridView;
        private ToolStripButton addButton;
        private ToolStripButton editButton;
        private ToolStripButton deleteButton;
        private ToolStripButton refreshButton;
        private ToolStripButton viewDetailsButton;
        private ToolStripButton payrollButton;
        private TextBox searchTextBox;
        private ComboBox statusFilterComboBox;
        private ComboBox positionFilterComboBox;
        private ToolStrip toolStrip;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusStripLabel;

        public EmployeeManagementForm(IUnitOfWork unitOfWork, ILogger<EmployeeManagementForm> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            LoadEmployeesAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ù…ÙˆØ¸ÙÙŠÙ†";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            CreateToolStrip();
            CreateMainPanel();
            CreateStatusStrip();
        }

        private void CreateToolStrip()
        {
            toolStrip = new ToolStrip
            {
                ImageScalingSize = new Size(24, 24),
                Font = new Font("Segoe UI", 9F)
            };

            addButton = new ToolStripButton("Ø¥Ø¶Ø§ÙØ© Ù…ÙˆØ¸Ù Ø¬Ø¯ÙŠØ¯", null, AddEmployee_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };

            editButton = new ToolStripButton("ØªØ¹Ø¯ÙŠÙ„", null, EditEmployee_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            deleteButton = new ToolStripButton("Ø­Ø°Ù", null, DeleteEmployee_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            payrollButton = new ToolStripButton("Ø§Ù„Ø±ÙˆØ§ØªØ¨", null, ManagePayroll_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            viewDetailsButton = new ToolStripButton("Ø¹Ø±Ø¶ Ø§Ù„ØªÙØ§ØµÙŠÙ„", null, ViewDetails_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            refreshButton = new ToolStripButton("ØªØ­Ø¯ÙŠØ«", null, RefreshData_Click)
            {
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };

            toolStrip.Items.AddRange(new ToolStripItem[]
            {
                addButton,
                new ToolStripSeparator(),
                editButton,
                deleteButton,
                new ToolStripSeparator(),
                payrollButton,
                new ToolStripSeparator(),
                viewDetailsButton,
                new ToolStripSeparator(),
                refreshButton
            });

            this.Controls.Add(toolStrip);
        }

        private void CreateMainPanel()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            // Create filter panel
            var filterPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top
            };

            // Search textbox
            var searchLabel = new Label
            {
                Text = "Ø§Ù„Ø¨Ø­Ø«:",
                Location = new Point(10, 15),
                Size = new Size(50, 20)
            };

            searchTextBox = new TextBox
            {
                Location = new Point(70, 12),
                Size = new Size(200, 25)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            // Status filter
            var statusLabel = new Label
            {
                Text = "Ø§Ù„Ø­Ø§Ù„Ø©:",
                Location = new Point(290, 15),
                Size = new Size(50, 20)
            };

            statusFilterComboBox = new ComboBox
            {
                Location = new Point(350, 12),
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            statusFilterComboBox.Items.AddRange(new[] { "Ø§Ù„ÙƒÙ„", "Ù†Ø´Ø·", "Ù…ØªÙˆÙ‚Ù", "Ù…Ø³ØªÙ‚ÙŠÙ„", "Ù…ÙØµÙˆÙ„" });
            statusFilterComboBox.SelectedIndex = 0;
            statusFilterComboBox.SelectedIndexChanged += StatusFilter_SelectedIndexChanged;

            // Position filter
            var positionLabel = new Label
            {
                Text = "Ø§Ù„Ù…Ù†ØµØ¨:",
                Location = new Point(490, 15),
                Size = new Size(50, 20)
            };

            positionFilterComboBox = new ComboBox
            {
                Location = new Point(550, 12),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            positionFilterComboBox.Items.Add("Ø§Ù„ÙƒÙ„");
            positionFilterComboBox.SelectedIndex = 0;
            positionFilterComboBox.SelectedIndexChanged += PositionFilter_SelectedIndexChanged;

            filterPanel.Controls.AddRange(new Control[] {
                searchLabel, searchTextBox,
                statusLabel, statusFilterComboBox,
                positionLabel, positionFilterComboBox
            });

            // Create data grid view
            employeesDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            SetupDataGridViewColumns();
            employeesDataGridView.SelectionChanged += EmployeesDataGridView_SelectionChanged;
            employeesDataGridView.CellDoubleClick += EmployeesDataGridView_CellDoubleClick;

            mainPanel.Controls.Add(employeesDataGridView);
            mainPanel.Controls.Add(filterPanel);
            this.Controls.Add(mainPanel);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();
            statusStripLabel = new ToolStripStatusLabel("Ø¬Ø§Ù‡Ø²")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };
            statusStrip.Items.Add(statusStripLabel);
            this.Controls.Add(statusStrip);
        }

        private void SetupDataGridViewColumns()
        {
            employeesDataGridView.Columns.Clear();

            employeesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "Ø§Ù„Ù…Ø¹Ø±Ù",
                DataPropertyName = "Id",
                Visible = false
            });

            employeesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FullName",
                HeaderText = "Ø§Ù„Ø§Ø³Ù… Ø§Ù„ÙƒØ§Ù…Ù„",
                DataPropertyName = "FullName",
                Width = 150
            });

            employeesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Position",
                HeaderText = "Ø§Ù„Ù…Ù†ØµØ¨",
                DataPropertyName = "Position",
                Width = 120
            });

            employeesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Nationality",
                HeaderText = "Ø§Ù„Ø¬Ù†Ø³ÙŠØ©",
                DataPropertyName = "Nationality",
                Width = 100
            });

            employeesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "JoinDate",
                HeaderText = "ØªØ§Ø±ÙŠØ® Ø§Ù„Ø§Ù†Ø¶Ù…Ø§Ù…",
                DataPropertyName = "JoinDate",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            employeesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "BaseSalary",
                HeaderText = "Ø§Ù„Ø±Ø§ØªØ¨ Ø§Ù„Ø£Ø³Ø§Ø³ÙŠ",
                DataPropertyName = "BaseSalary",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" }
            });

            employeesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "Ø§Ù„Ø­Ø§Ù„Ø©",
                DataPropertyName = "Status",
                Width = 80
            });

            employeesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "Ø§Ù„Ù‡Ø§ØªÙ",
                DataPropertyName = "Phone",
                Width = 120
            });

            employeesDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "Ø§Ù„Ø¨Ø±ÙŠØ¯ Ø§Ù„Ø¥Ù„ÙƒØªØ±ÙˆÙ†ÙŠ",
                DataPropertyName = "Email",
                Width = 150
            });
        }

        private async Task LoadEmployeesAsync()
        {
            try
            {
                statusStripLabel.Text = "Ø¬Ø§Ø±ÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª...";
                this.Cursor = Cursors.WaitCursor;

                var employees = await _unitOfWork.Employees.GetAllAsync();
                employeesDataGridView.DataSource = employees.ToList();

                // Load positions for filter
                var positions = employees.Select(e => e.Position).Distinct().OrderBy(p => p);
                positionFilterComboBox.Items.Clear();
                positionFilterComboBox.Items.Add("Ø§Ù„ÙƒÙ„");
                foreach (var position in positions)
                {
                    positionFilterComboBox.Items.Add(position);
                }
                positionFilterComboBox.SelectedIndex = 0;

                statusStripLabel.Text = $"ØªÙ… ØªØ­Ù…ÙŠÙ„ {employees.Count()} Ù…ÙˆØ¸Ù";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ù…ÙˆØ¸ÙÙŠÙ†");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusStripLabel.Text = "Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª";
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void EmployeesDataGridView_SelectionChanged(object? sender, EventArgs e)
        {
            bool hasSelection = employeesDataGridView.SelectedRows.Count > 0;
            editButton.Enabled = hasSelection;
            deleteButton.Enabled = hasSelection;
            viewDetailsButton.Enabled = hasSelection;
            payrollButton.Enabled = hasSelection;
        }

        private void EmployeesDataGridView_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                ViewDetails_Click(sender, e);
            }
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void StatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void PositionFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            try
            {
                var bindingSource = employeesDataGridView.DataSource as List<Employee>;
                if (bindingSource == null) return;

                var filteredData = bindingSource.AsEnumerable();

                // Apply search filter
                if (!string.IsNullOrWhiteSpace(searchTextBox.Text))
                {
                    var searchTerm = searchTextBox.Text.ToLower();
                    filteredData = filteredData.Where(e =>
                        e.FullName.ToLower().Contains(searchTerm) ||
                        e.Position.ToLower().Contains(searchTerm) ||
                        e.Nationality.ToLower().Contains(searchTerm) ||
                        e.Phone.ToLower().Contains(searchTerm) ||
                        e.Email.ToLower().Contains(searchTerm));
                }

                // Apply status filter
                if (statusFilterComboBox.SelectedIndex > 0)
                {
                    var selectedStatus = statusFilterComboBox.SelectedItem.ToString();
                    filteredData = filteredData.Where(e => e.Status == selectedStatus);
                }

                // Apply position filter
                if (positionFilterComboBox.SelectedIndex > 0)
                {
                    var selectedPosition = positionFilterComboBox.SelectedItem.ToString();
                    filteredData = filteredData.Where(e => e.Position == selectedPosition);
                }

                employeesDataGridView.DataSource = filteredData.ToList();
                statusStripLabel.Text = $"Ø¹Ø±Ø¶ {filteredData.Count()} Ù…Ù† Ø£ØµÙ„ {bindingSource.Count} Ù…ÙˆØ¸Ù";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ØªØ·Ø¨ÙŠÙ‚ Ø§Ù„Ù…Ø±Ø´Ø­Ø§Øª");
            }
        }

        private async void private async void private async void AddEmployee_Click(object? sender, EventArgs e)
        {
            try
            {
                var addForm = new EmployeeAddEditForm(_unitOfWork, _logger);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadEmployeesAsync();
                    statusStripLabel.Text = "ØªÙ… Ø¥Ø¶Ø§ÙØ© Ù…ÙˆØ¸Ù Ø¬Ø¯ÙŠØ¯ Ø¨Ù†Ø¬Ø§Ø­";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¶Ø§ÙØ© Ù…ÙˆØ¸Ù");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ø§Ù„Ù†Ù…ÙˆØ°Ø¬: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void private async void private async void EditEmployee_Click(object? sender, EventArgs e)
        {
            try
            {
                if (employeesDataGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ù…ÙˆØ¸Ù Ù„Ù„ØªØ¹Ø¯ÙŠÙ„", "ØªÙ†Ø¨ÙŠÙ‡",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedEmployee = employeesDataGridView.SelectedRows[0].DataBoundItem as Employee;
                if (selectedEmployee == null) return;

                var editForm = new EmployeeAddEditForm(_unitOfWork, _logger, selectedEmployee);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadEmployeesAsync();
                    statusStripLabel.Text = $"ØªÙ… ØªØ­Ø¯ÙŠØ« Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ù…ÙˆØ¸Ù {selectedEmployee.FullName} Ø¨Ù†Ø¬Ø§Ø­";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ ØªØ¹Ø¯ÙŠÙ„ Ù…ÙˆØ¸Ù");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ø§Ù„Ù†Ù…ÙˆØ°Ø¬: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteEmployee_Click(object? sender, EventArgs e)
        {
            try
            {
                if (employeesDataGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("ÙŠØ±Ø¬Ù‰ Ø§Ø®ØªÙŠØ§Ø± Ù…ÙˆØ¸Ù Ù„Ù„Ø­Ø°Ù", "ØªÙ†Ø¨ÙŠÙ‡",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedEmployee = employeesDataGridView.SelectedRows[0].DataBoundItem as Employee;
                if (selectedEmployee == null) return;

                // Check if employee has payroll records
                var hasPayrollRecords = await CheckEmployeeHasPayrollRecords(selectedEmployee.Id);
                var warningMessage = hasPayrollRecords
                    ? $"ØªØ­Ø°ÙŠØ±: Ø§Ù„Ù…ÙˆØ¸Ù '{selectedEmployee.FullName}' Ù„Ø¯ÙŠÙ‡ Ø³Ø¬Ù„Ø§Øª Ø±ÙˆØ§ØªØ¨.\n\nÙ‡Ù„ Ø£Ù†Øª Ù…ØªØ£ÙƒØ¯ Ù…Ù† Ø­Ø°Ù Ø§Ù„Ù…ÙˆØ¸Ù ÙˆØ¬Ù…ÙŠØ¹ Ø³Ø¬Ù„Ø§ØªÙ‡ØŸ\n\nÙ‡Ø°Ø§ Ø§Ù„Ø¥Ø¬Ø±Ø§Ø¡ Ù„Ø§ ÙŠÙ…ÙƒÙ† Ø§Ù„ØªØ±Ø§Ø¬Ø¹ Ø¹Ù†Ù‡."
                    : $"Ù‡Ù„ Ø£Ù†Øª Ù…ØªØ£ÙƒØ¯ Ù…Ù† Ø­Ø°Ù Ø§Ù„Ù…ÙˆØ¸Ù '{selectedEmployee.FullName}'ØŸ\n\nÙ‡Ø°Ø§ Ø§Ù„Ø¥Ø¬Ø±Ø§Ø¡ Ù„Ø§ ÙŠÙ…ÙƒÙ† Ø§Ù„ØªØ±Ø§Ø¬Ø¹ Ø¹Ù†Ù‡.";

                var result = MessageBox.Show(warningMessage, "ØªØ£ÙƒÙŠØ¯ Ø§Ù„Ø­Ø°Ù",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    statusStripLabel.Text = "Ø¬Ø§Ø±ÙŠ Ø­Ø°Ù Ø§Ù„Ù…ÙˆØ¸Ù...";
                    this.Cursor = Cursors.WaitCursor;

                    await _unitOfWork.Employees.DeleteAsync(selectedEmployee);
                    await _unitOfWork.SaveChangesAsync();

                    await LoadEmployeesAsync();
                    statusStripLabel.Text = $"ØªÙ… Ø­Ø°Ù Ø§Ù„Ù…ÙˆØ¸Ù {selectedEmployee.FullName} Ø¨Ù†Ø¬Ø§Ø­";
                    MessageBox.Show("ØªÙ… Ø­Ø°Ù Ø§Ù„Ù…ÙˆØ¸Ù Ø¨Ù†Ø¬Ø§Ø­", "Ù†Ø¬Ø­",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„Ù…ÙˆØ¸Ù");
                statusStripLabel.Text = "ÙØ´Ù„ ÙÙŠ Ø­Ø°Ù Ø§Ù„Ù…ÙˆØ¸Ù";
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø­Ø°Ù Ø§Ù„Ù…ÙˆØ¸Ù: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void ManagePayroll_Click(object? sender, EventArgs e)
        {
            try
            {
                if (employeesDataGridView.SelectedRows.Count == 0) return;

                var selectedEmployee = employeesDataGridView.SelectedRows[0].DataBoundItem as Employee;
                if (selectedEmployee == null) return;

                var payrollForm = new PayrollManagementForm(_unitOfWork, _logger, selectedEmployee);
                payrollForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ù†Ù…ÙˆØ°Ø¬ Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ø±ÙˆØ§ØªØ¨");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ ÙØªØ­ Ø§Ù„Ù†Ù…ÙˆØ°Ø¬: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewDetails_Click(object? sender, EventArgs e)
        {
            try
            {
                if (employeesDataGridView.SelectedRows.Count == 0) return;

                var selectedEmployee = employeesDataGridView.SelectedRows[0].DataBoundItem as Employee;
                if (selectedEmployee == null) return;

                var detailsForm = new EmployeeDetailsForm(selectedEmployee, _unitOfWork);
                detailsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø¹Ø±Ø¶ ØªÙØ§ØµÙŠÙ„ Ø§Ù„Ù…ÙˆØ¸Ù");
                MessageBox.Show($"Ø®Ø·Ø£ ÙÙŠ Ø¹Ø±Ø¶ Ø§Ù„ØªÙØ§ØµÙŠÙ„: {ex.Message}", "Ø®Ø·Ø£",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void private async void private async void RefreshData_Click(object? sender, EventArgs e)
        {
            await LoadEmployeesAsync();
            statusStripLabel.Text = "ØªÙ… ØªØ­Ø¯ÙŠØ« Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª";
        }

        /// <summary>
        /// Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† ÙˆØ¬ÙˆØ¯ Ø³Ø¬Ù„Ø§Øª Ø±ÙˆØ§ØªØ¨ Ù„Ù„Ù…ÙˆØ¸Ù
        /// Check if employee has payroll records
        /// </summary>
        private async Task<bool> CheckEmployeeHasPayrollRecords(int employeeId)
        {
            try
            {
                var payrollRecords = await _unitOfWork.Payrolls.FindAsync(p => p.EmployeeId == employeeId);
                return payrollRecords.Any();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ø®Ø·Ø£ ÙÙŠ Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† Ø³Ø¬Ù„Ø§Øª Ø§Ù„Ø±ÙˆØ§ØªØ¨ Ù„Ù„Ù…ÙˆØ¸Ù {EmployeeId}", employeeId);
                return false;
            }
        }

        /// <summary>
        /// ØªØ­Ø¯ÙŠØ« Ø´Ø±ÙŠØ· Ø§Ù„Ø­Ø§Ù„Ø©
        /// Update status bar
        /// </summary>
        private void UpdateStatusBar(string? message = null)
        {
            if (!string.IsNullOrEmpty(message))
            {
                statusStripLabel.Text = message;
            }
            else
            {
                var totalEmployees = employeesDataGridView.Rows.Count;
                statusStripLabel.Text = $"Ø¥Ø¬Ù…Ø§Ù„ÙŠ Ø§Ù„Ù…ÙˆØ¸ÙÙŠÙ†: {totalEmployees}";
            }
        }
    }
}



