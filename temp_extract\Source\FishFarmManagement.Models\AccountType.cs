using System.ComponentModel.DataAnnotations;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// نوع الحساب المحاسبي
    /// Account type
    /// </summary>
    public class AccountType : BaseEntity
    {
        [Required(ErrorMessage = "اسم نوع الحساب مطلوب")]
        [StringLength(100, ErrorMessage = "اسم نوع الحساب يجب أن يكون أقل من 100 حرف")]
        public string TypeName { get; set; } = string.Empty;

        [Required(ErrorMessage = "رمز نوع الحساب مطلوب")]
        [StringLength(10, ErrorMessage = "رمز نوع الحساب يجب أن يكون أقل من 10 أحرف")]
        public string TypeCode { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// طبيعة الحساب (مدين/دائن)
        /// Account nature (Debit/Credit)
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Nature { get; set; } = "مدين"; // مدين، دائن

        /// <summary>
        /// ترتيب العرض
        /// Display order
        /// </summary>
        public int DisplayOrder { get; set; }

        // Navigation Properties
        public virtual ICollection<Account> Accounts { get; set; } = new List<Account>();

        /// <summary>
        /// الحصول على أنواع الحسابات الافتراضية
        /// Get default account types
        /// </summary>
        public static List<AccountType> GetDefaultAccountTypes()
        {
            return new List<AccountType>
            {
                new AccountType { TypeName = "الأصول", TypeCode = "1", Nature = "مدين", DisplayOrder = 1, Description = "الأصول الثابتة والمتداولة" },
                new AccountType { TypeName = "الخصوم", TypeCode = "2", Nature = "دائن", DisplayOrder = 2, Description = "الالتزامات والديون" },
                new AccountType { TypeName = "حقوق الملكية", TypeCode = "3", Nature = "دائن", DisplayOrder = 3, Description = "رأس المال والأرباح المحتجزة" },
                new AccountType { TypeName = "الإيرادات", TypeCode = "4", Nature = "دائن", DisplayOrder = 4, Description = "إيرادات المبيعات والخدمات" },
                new AccountType { TypeName = "المصروفات", TypeCode = "5", Nature = "مدين", DisplayOrder = 5, Description = "مصروفات التشغيل والإدارة" }
            };
        }
    }
}
