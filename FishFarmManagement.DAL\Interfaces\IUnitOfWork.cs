using FishFarmManagement.Models;

namespace FishFarmManagement.DAL.Interfaces
{
    /// <summary>
    /// واجهة وحدة العمل
    /// Unit of Work interface
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        // Repository properties for all entities
        IRepository<FarmInfo> FarmInfos { get; }
        IRepository<ProductionCycle> ProductionCycles { get; }
        IRepository<Pond> Ponds { get; }
        IRepository<FeedType> FeedTypes { get; }
        IRepository<FeedConsumption> FeedConsumptions { get; }
        IRepository<FishMortality> FishMortalities { get; }
        IRepository<Medication> Medications { get; }
        IRepository<PondMedication> PondMedications { get; }
        IRepository<Employee> Employees { get; }
        IRepository<Payroll> Payrolls { get; }
        IRepository<AccountType> AccountTypes { get; }
        IRepository<Account> Accounts { get; }
        IRepository<Transaction> Transactions { get; }
        IRepository<TransactionDetail> TransactionDetails { get; }
        IRepository<CostCenter> CostCenters { get; }
        IRepository<Inventory> Inventories { get; }
        IRepository<InventoryMovement> InventoryMovements { get; }
        IRepository<User> Users { get; }
        IRepository<Role> Roles { get; }
        IRepository<UserRole> UserRoles { get; }

        /// <summary>
        /// حفظ جميع التغييرات
        /// Save all changes
        /// </summary>
        Task<int> SaveChangesAsync();

        /// <summary>
        /// بدء معاملة قاعدة البيانات
        /// Begin database transaction
        /// </summary>
        Task BeginTransactionAsync();

        /// <summary>
        /// تأكيد المعاملة
        /// Commit transaction
        /// </summary>
        Task CommitTransactionAsync();

        /// <summary>
        /// إلغاء المعاملة
        /// Rollback transaction
        /// </summary>
        Task RollbackTransactionAsync();

        /// <summary>
        /// تنفيذ استعلام SQL خام
        /// Execute raw SQL query
        /// </summary>
        Task<int> ExecuteSqlRawAsync(string sql, params object[] parameters);

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// Create database backup
        /// </summary>
        Task<bool> CreateBackupAsync(string backupPath);

        /// <summary>
        /// استعادة قاعدة البيانات من نسخة احتياطية
        /// Restore database from backup
        /// </summary>
        Task<bool> RestoreBackupAsync(string backupPath);

        /// <summary>
        /// تحسين قاعدة البيانات
        /// Optimize database
        /// </summary>
        Task OptimizeDatabaseAsync();

        /// <summary>
        /// التحقق من سلامة قاعدة البيانات
        /// Check database integrity
        /// </summary>
        Task<bool> CheckDatabaseIntegrityAsync();
    }
}
