# دليل التثبيت والتشغيل v1.0.1
## Installation and Setup Guide v1.0.1

> **🎉 جديد في الإصدار 1.0.1**: نظام التحديث التلقائي، مراقب الأداء، تحسينات الأمان، والمزيد!

### 📋 متطلبات النظام
#### System Requirements

**الحد الأدنى:**
- نظام التشغيل: Windows 10 (64-bit)
- المعالج: Intel Core i3 أو AMD Ryzen 3
- الذاكرة: 4 GB RAM
- المساحة: 500 MB مساحة فارغة
- .NET 8.0 Runtime
- **لا حاجة لتثبيت قاعدة بيانات منفصلة** - يستخدم SQLite

**المستحسن:**
- نظا<PERSON> التشغيل: Windows 11 (64-bit)
- المعالج: Intel Core i5 أو AMD Ryzen 5
- الذاكرة: 8 GB RAM
- المساحة: 2 GB مساحة فارغة
- SSD للأداء الأفضل

### 🔧 التثبيت للمطورين
#### Developer Installation

#### 1. تثبيت المتطلبات الأساسية

**تثبيت .NET 8.0 SDK:**
```bash
# تحميل من الموقع الرسمي
https://dotnet.microsoft.com/download/dotnet/8.0

# أو باستخدام winget
winget install Microsoft.DotNet.SDK.8
```

**تثبيت Visual Studio 2022 (اختياري):**
```bash
# Community Edition (مجاني)
winget install Microsoft.VisualStudio.2022.Community

# أو Visual Studio Code
winget install Microsoft.VisualStudioCode
```

#### 2. استنساخ المشروع

```bash
# استنساخ المستودع
git clone https://github.com/your-repo/fish-farm-management.git

# الانتقال إلى مجلد المشروع
cd fish-farm-management
```

#### 3. استعادة الحزم

```bash
# استعادة جميع الحزم المطلوبة
dotnet restore

# التحقق من صحة المشروع
dotnet build
```

#### 4. إعداد قاعدة البيانات

```bash
# إنشاء قاعدة البيانات (تلقائي عند أول تشغيل)
dotnet run --project FishFarmManagement

# أو تشغيل سكريبت SQL يدوياً
sqlite3 FishFarmDatabase.db < Database/CreateDatabase.sql
```

#### 5. تشغيل التطبيق

```bash
# تشغيل في وضع التطوير
dotnet run --project FishFarmManagement

# أو بناء وتشغيل النسخة المحسنة
dotnet build -c Release
dotnet run -c Release --project FishFarmManagement
```

### 📦 التثبيت للمستخدمين النهائيين
#### End User Installation

#### الطريقة الأولى: تثبيت من الملف التنفيذي

1. **تحميل النسخة المبنية:**
   - تحميل ملف `FishFarmManagement-Setup.exe`
   - تشغيل الملف كمدير

2. **اتباع معالج التثبيت:**
   - اختيار مجلد التثبيت
   - الموافقة على الترخيص
   - إنهاء التثبيت

3. **تشغيل التطبيق:**
   - من قائمة ابدأ
   - أو من أيقونة سطح المكتب

#### الطريقة الثانية: تشغيل محمول

1. **تحميل النسخة المحمولة:**
   - تحميل ملف `FishFarmManagement-Portable.zip`
   - استخراج الملفات إلى مجلد

2. **تشغيل التطبيق:**
   - تشغيل `FishFarmManagement.exe`

### ⚙️ الإعدادات الأولية
#### Initial Configuration

#### 1. إعداد معلومات المزرعة

عند أول تشغيل:
1. انتقل إلى **أدوات** > **معلومات المزرعة**
2. أدخل البيانات التالية:
   - اسم المزرعة
   - الموقع
   - معلومات الاتصال
   - تحميل الشعار (اختياري)

#### 2. إعداد الحسابات المحاسبية

1. انتقل إلى **المحاسبة** > **الحسابات**
2. راجع الحسابات الافتراضية
3. أضف حسابات إضافية حسب الحاجة

#### 3. إعداد أنواع العلف والأدوية

1. **العلف:**
   - انتقل إلى **الإدارة** > **إدارة الأحواض**
   - تبويب **أنواع العلف**
   - أضف أنواع العلف المستخدمة

2. **الأدوية:**
   - تبويب **الأدوية**
   - أضف الأدوية المتاحة

### 🗃️ إعداد قاعدة البيانات
#### Database Setup

#### إعداد تلقائي (مستحسن)

قاعدة البيانات تُنشأ تلقائياً عند أول تشغيل مع:
- الجداول الأساسية
- البيانات الافتراضية
- الفهارس المطلوبة

#### إعداد يدوي

```bash
# إنشاء قاعدة بيانات فارغة
sqlite3 FishFarmDatabase.db

# تشغيل سكريبت الإنشاء
.read Database/CreateDatabase.sql

# التحقق من الجداول
.tables

# الخروج
.quit
```

### 🔧 استكشاف الأخطاء
#### Troubleshooting

#### مشاكل شائعة وحلولها

**1. خطأ في بدء التطبيق:**
```
الحل:
- تأكد من تثبيت .NET 8.0 Runtime
- تشغيل التطبيق كمدير
- التحقق من ملف appsettings.json
```

**2. خطأ في قاعدة البيانات:**
```
الحل:
- حذف ملف FishFarmDatabase.db
- إعادة تشغيل التطبيق
- أو استعادة من نسخة احتياطية
```

**3. مشاكل في الواجهة:**
```
الحل:
- تحديث برامج تشغيل الرسوميات
- تغيير إعدادات DPI
- إعادة تشغيل التطبيق
```

**4. بطء في الأداء:**
```
الحل:
- تشغيل تحسين قاعدة البيانات
- إغلاق البرامج غير المستخدمة
- زيادة الذاكرة المتاحة
```

### 📁 هيكل الملفات
#### File Structure

```
FishFarmManagement/
├── FishFarmManagement.exe          # الملف التنفيذي الرئيسي
├── appsettings.json                # ملف الإعدادات
├── FishFarmDatabase.db             # قاعدة البيانات
├── Logs/                           # ملفات السجلات
│   └── fishfarm-YYYYMMDD.txt
├── Backups/                        # النسخ الاحتياطية
│   └── backup-YYYYMMDD-HHMMSS.db
├── Reports/                        # التقارير المُصدرة
└── Resources/                      # الموارد والصور
```

### 🔄 التحديث
#### Updates

#### تحديث تلقائي (مستقبلي)
- التحقق من التحديثات عند بدء التشغيل
- تحميل وتثبيت التحديثات تلقائياً

#### تحديث يدوي
1. تحميل النسخة الجديدة
2. إنشاء نسخة احتياطية من البيانات
3. تثبيت النسخة الجديدة
4. استعادة البيانات إذا لزم الأمر

### 🔒 النسخ الاحتياطي
#### Backup and Restore

#### نسخ احتياطي تلقائي
- يتم إنشاء نسخة احتياطية يومياً
- الاحتفاظ بـ 30 نسخة احتياطية
- مجلد الحفظ: `Backups/`

#### نسخ احتياطي يدوي
1. **ملف** > **نسخ احتياطي**
2. اختيار موقع الحفظ
3. تأكيد العملية

#### الاستعادة
1. **ملف** > **استعادة**
2. اختيار ملف النسخة الاحتياطية
3. تأكيد الاستعادة
4. إعادة تشغيل التطبيق

### 📞 الدعم الفني
#### Technical Support

**للحصول على المساعدة:**
- البريد الإلكتروني: <EMAIL>
- الوثائق: راجع ملف README.md
- المشاكل: أنشئ issue في GitHub

**معلومات مطلوبة عند طلب الدعم:**
- إصدار التطبيق
- نظام التشغيل
- وصف المشكلة
- ملف السجل (Logs)
- لقطة شاشة (إن أمكن)

### 🔐 الأمان
#### Security

**حماية البيانات:**
- نسخ احتياطية منتظمة
- تشفير قاعدة البيانات (مستقبلي)
- تسجيل العمليات

**أفضل الممارسات:**
- تحديث النظام بانتظام
- استخدام كلمات مرور قوية
- تقييد الوصول للملفات الحساسة

---

**© 2024 طارق حسين صالح - جميع الحقوق محفوظة**
