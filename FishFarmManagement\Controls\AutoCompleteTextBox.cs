using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace FishFarmManagement.Controls
{
    /// <summary>
    /// مكون نص مع اقتراح تلقائي محسن
    /// Enhanced AutoComplete TextBox component
    /// </summary>
    public partial class AutoCompleteTextBox : UserControl
    {
        private TextBox textBox;
        private ListBox suggestionListBox;
        private Panel suggestionPanel;
        private System.Windows.Forms.Timer searchTimer;
        private bool isDropDownVisible = false;
        private List<AutoCompleteItem> allItems = new List<AutoCompleteItem>();
        private List<AutoCompleteItem> filteredItems = new List<AutoCompleteItem>();

        // Events
        public event EventHandler<AutoCompleteItemSelectedEventArgs>? ItemSelected;
        public event EventHandler<string>? SearchRequested;

        // Properties
        public string DisplayText
        {
            get => textBox.Text;
            set => textBox.Text = value;
        }

        public object? SelectedValue { get; private set; }
        public AutoCompleteItem? SelectedItem { get; private set; }

        public int MinSearchLength { get; set; } = 2;
        public int SearchDelay { get; set; } = 300; // milliseconds
        public int MaxSuggestions { get; set; } = 10;
        public bool ShowDetailsInDropdown { get; set; } = true;

        public AutoCompleteTextBox()
        {
            InitializeComponent();
            SetupControls();
            SetupEvents();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(250, 23);
            this.BackColor = Color.White;
            this.BorderStyle = BorderStyle.FixedSingle;
        }

        private void SetupControls()
        {
            // Main TextBox
            textBox = new TextBox
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.None,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes
            };

            // Suggestion Panel
            suggestionPanel = new Panel
            {
                Visible = false,
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = false
            };

            // Suggestion ListBox
            suggestionListBox = new ListBox
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.None,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes,
                DrawMode = DrawMode.OwnerDrawFixed,
                ItemHeight = 35
            };

            // Search Timer
            searchTimer = new System.Windows.Forms.Timer
            {
                Interval = SearchDelay
            };

            suggestionPanel.Controls.Add(suggestionListBox);
            this.Controls.Add(textBox);
        }

        private void SetupEvents()
        {
            textBox.TextChanged += TextBox_TextChanged;
            textBox.KeyDown += TextBox_KeyDown;
            textBox.Leave += TextBox_Leave;
            textBox.Enter += TextBox_Enter;

            suggestionListBox.Click += SuggestionListBox_Click;
            suggestionListBox.KeyDown += SuggestionListBox_KeyDown;
            suggestionListBox.DrawItem += SuggestionListBox_DrawItem;

            searchTimer.Tick += SearchTimer_Tick;

            // Handle form events to position dropdown correctly
            this.ParentChanged += (s, e) => SetupParentEvents();
        }

        private void SetupParentEvents()
        {
            if (this.Parent != null)
            {
                var form = this.FindForm();
                if (form != null)
                {
                    form.Controls.Add(suggestionPanel);
                    suggestionPanel.BringToFront();
                }
            }
        }

        private void TextBox_TextChanged(object? sender, EventArgs e)
        {
            searchTimer.Stop();
            
            if (textBox.Text.Length >= MinSearchLength)
            {
                searchTimer.Start();
            }
            else
            {
                HideDropDown();
            }
        }

        private void SearchTimer_Tick(object? sender, EventArgs e)
        {
            searchTimer.Stop();
            PerformSearch();
        }

        private void PerformSearch()
        {
            var searchText = textBox.Text.Trim();
            if (string.IsNullOrEmpty(searchText) || searchText.Length < MinSearchLength)
            {
                HideDropDown();
                return;
            }

            // Filter existing items
            filteredItems = allItems
                .Where(item => item.DisplayText.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                              item.SecondaryText.Contains(searchText, StringComparison.OrdinalIgnoreCase))
                .Take(MaxSuggestions)
                .ToList();

            // Request additional search if needed
            SearchRequested?.Invoke(this, searchText);

            UpdateSuggestionList();
        }

        public void SetDataSource(IEnumerable<AutoCompleteItem> items)
        {
            allItems = items.ToList();
            if (textBox.Text.Length >= MinSearchLength)
            {
                PerformSearch();
            }
        }

        public void AddItems(IEnumerable<AutoCompleteItem> items)
        {
            allItems.AddRange(items);
            if (textBox.Text.Length >= MinSearchLength)
            {
                PerformSearch();
            }
        }

        private void UpdateSuggestionList()
        {
            suggestionListBox.Items.Clear();
            
            if (filteredItems.Any())
            {
                foreach (var item in filteredItems)
                {
                    suggestionListBox.Items.Add(item);
                }
                ShowDropDown();
            }
            else
            {
                HideDropDown();
            }
        }

        private void ShowDropDown()
        {
            if (isDropDownVisible) return;

            var form = this.FindForm();
            if (form == null) return;

            var screenLocation = this.PointToScreen(new Point(0, this.Height));
            var formLocation = form.PointToClient(screenLocation);

            suggestionPanel.Location = formLocation;
            suggestionPanel.Size = new Size(this.Width, Math.Min(suggestionListBox.Items.Count * suggestionListBox.ItemHeight + 2, 200));
            suggestionPanel.Visible = true;
            suggestionPanel.BringToFront();

            isDropDownVisible = true;
        }

        private void HideDropDown()
        {
            if (!isDropDownVisible) return;

            suggestionPanel.Visible = false;
            isDropDownVisible = false;
        }

        private void TextBox_KeyDown(object? sender, KeyEventArgs e)
        {
            if (!isDropDownVisible) return;

            switch (e.KeyCode)
            {
                case Keys.Down:
                    if (suggestionListBox.SelectedIndex < suggestionListBox.Items.Count - 1)
                        suggestionListBox.SelectedIndex++;
                    e.Handled = true;
                    break;

                case Keys.Up:
                    if (suggestionListBox.SelectedIndex > 0)
                        suggestionListBox.SelectedIndex--;
                    e.Handled = true;
                    break;

                case Keys.Enter:
                    if (suggestionListBox.SelectedItem != null)
                    {
                        SelectItem((AutoCompleteItem)suggestionListBox.SelectedItem);
                        e.Handled = true;
                    }
                    break;

                case Keys.Escape:
                    HideDropDown();
                    e.Handled = true;
                    break;
            }
        }

        private void SuggestionListBox_Click(object? sender, EventArgs e)
        {
            if (suggestionListBox.SelectedItem != null)
            {
                SelectItem((AutoCompleteItem)suggestionListBox.SelectedItem);
            }
        }

        private void SuggestionListBox_KeyDown(object? sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter && suggestionListBox.SelectedItem != null)
            {
                SelectItem((AutoCompleteItem)suggestionListBox.SelectedItem);
                e.Handled = true;
            }
        }

        private void SelectItem(AutoCompleteItem item)
        {
            SelectedItem = item;
            SelectedValue = item.Value;
            textBox.Text = item.DisplayText;
            HideDropDown();

            ItemSelected?.Invoke(this, new AutoCompleteItemSelectedEventArgs(item));
        }

        private void TextBox_Leave(object? sender, EventArgs e)
        {
            // Delay hiding to allow click on suggestion
            Task.Delay(150).ContinueWith(_ => 
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(HideDropDown));
                }
                else
                {
                    HideDropDown();
                }
            });
        }

        private void TextBox_Enter(object? sender, EventArgs e)
        {
            if (textBox.Text.Length >= MinSearchLength && filteredItems.Any())
            {
                ShowDropDown();
            }
        }

        private void SuggestionListBox_DrawItem(object? sender, DrawItemEventArgs e)
        {
            if (e.Index < 0 || e.Index >= suggestionListBox.Items.Count) return;

            var item = (AutoCompleteItem)suggestionListBox.Items[e.Index];
            
            e.DrawBackground();

            var textBrush = new SolidBrush(e.ForeColor);
            var secondaryBrush = new SolidBrush(Color.Gray);

            // Draw main text
            var mainFont = new Font(e.Font, FontStyle.Regular);
            e.Graphics.DrawString(item.DisplayText, mainFont, textBrush, e.Bounds.X + 5, e.Bounds.Y + 3);

            // Draw secondary text if enabled and available
            if (ShowDetailsInDropdown && !string.IsNullOrEmpty(item.SecondaryText))
            {
                var secondaryFont = new Font(e.Font.FontFamily, e.Font.Size - 1, FontStyle.Regular);
                e.Graphics.DrawString(item.SecondaryText, secondaryFont, secondaryBrush, e.Bounds.X + 5, e.Bounds.Y + 18);
            }

            e.DrawFocusRectangle();

            textBrush.Dispose();
            secondaryBrush.Dispose();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                searchTimer?.Dispose();
                suggestionPanel?.Dispose();
            }
            base.Dispose(disposing);
        }
    }

    /// <summary>
    /// عنصر الاقتراح التلقائي
    /// AutoComplete item
    /// </summary>
    public class AutoCompleteItem
    {
        public object Value { get; set; } = null!;
        public string DisplayText { get; set; } = string.Empty;
        public string SecondaryText { get; set; } = string.Empty;
        public object? Tag { get; set; }

        public AutoCompleteItem() { }

        public AutoCompleteItem(object value, string displayText, string secondaryText = "")
        {
            Value = value;
            DisplayText = displayText;
            SecondaryText = secondaryText;
        }

        public override string ToString() => DisplayText;
    }

    /// <summary>
    /// معاملات حدث اختيار العنصر
    /// Item selected event arguments
    /// </summary>
    public class AutoCompleteItemSelectedEventArgs : EventArgs
    {
        public AutoCompleteItem SelectedItem { get; }

        public AutoCompleteItemSelectedEventArgs(AutoCompleteItem selectedItem)
        {
            SelectedItem = selectedItem;
        }
    }
}
