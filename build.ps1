# Fish Farm Management System Build Script
param(
    [switch]$Clean,
    [switch]$Run
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Fish Farm Management System Build" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if command succeeded
function Test-LastCommand {
    param($Message)
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: $Message" -ForegroundColor Red
        exit 1
    }
}

# Clean if requested
if ($Clean) {
    Write-Host "[CLEAN] Removing bin and obj directories..." -ForegroundColor Yellow
    Get-ChildItem -Path . -Recurse -Directory -Name "bin" | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
    Get-ChildItem -Path . -Recurse -Directory -Name "obj" | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "[CLEAN] Clean completed." -ForegroundColor Green
}

Write-Host "[1/6] Cleaning previous builds..." -ForegroundColor Yellow
dotnet clean
Test-LastCommand "Failed to clean solution"

Write-Host "[2/6] Restoring NuGet packages..." -ForegroundColor Yellow
dotnet restore
Test-LastCommand "Failed to restore packages"

Write-Host "[3/6] Building Models project..." -ForegroundColor Yellow
dotnet build "FishFarmManagement.Models\FishFarmManagement.Models.csproj"
Test-LastCommand "Failed to build Models project"

Write-Host "[4/6] Building DAL project..." -ForegroundColor Yellow
dotnet build "FishFarmManagement.DAL\FishFarmManagement.DAL.csproj"
Test-LastCommand "Failed to build DAL project"

Write-Host "[5/6] Building BLL project..." -ForegroundColor Yellow
dotnet build "FishFarmManagement.BLL\FishFarmManagement.BLL.csproj"
Test-LastCommand "Failed to build BLL project"

Write-Host "[6/6] Building main application..." -ForegroundColor Yellow
dotnet build "FishFarmManagement\FishFarmManagement.csproj"
Test-LastCommand "Failed to build main application"

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "   Build completed successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

if ($Run) {
    Write-Host "Starting application..." -ForegroundColor Cyan
    dotnet run --project FishFarmManagement
} else {
    Write-Host "To run the application, execute:" -ForegroundColor Cyan
    Write-Host "dotnet run --project FishFarmManagement" -ForegroundColor White
    Write-Host ""
    Write-Host "Or use: .\build.ps1 -Run" -ForegroundColor White
}
