using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FishFarmManagement.DAL;
using FishFarmManagement.BLL.Services;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.Models;

namespace FishFarmManagement.Tests
{
    /// <summary>
    /// تكوين الاختبارات
    /// Test configuration
    /// </summary>
    public static class TestConfiguration
    {
        /// <summary>
        /// إنشاء مزود الخدمات للاختبارات
        /// Create service provider for tests
        /// </summary>
        public static ServiceProvider CreateServiceProvider()
        {
            var services = new ServiceCollection();

            // Configuration
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["Application:Name"] = "Fish Farm Management System - Test",
                    ["Application:Version"] = "1.0.0-test",
                    ["ConnectionStrings:DefaultConnection"] = "Data Source=:memory:",
                    ["Logging:LogLevel:Default"] = "Information"
                })
                .Build();

            services.AddSingleton<IConfiguration>(configuration);

            // Database
            services.AddDbContext<FishFarmDbContext>(options =>
                options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

            // Logging
            services.AddLogging(builder =>
            {
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Business Services
            services.AddScoped<IUserManagementService, UserManagementService>();
            services.AddScoped<IPondService, PondService>();
            services.AddScoped<IProductionCycleService, ProductionCycleService>();
            services.AddScoped<IFeedService, FeedService>();
            services.AddScoped<IMedicationService, MedicationService>();
            services.AddScoped<IEmployeeService, EmployeeService>();
            services.AddScoped<IAccountingService, AccountingService>();
            services.AddScoped<IInventoryService, InventoryService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<INotificationService, NotificationService>();
            services.AddScoped<IBackupService, BackupService>();
            services.AddScoped<IContentService, ContentService>();
            services.AddScoped<ISettingsService, SettingsService>();
            services.AddScoped<IStatisticsService, StatisticsService>();

            return services.BuildServiceProvider();
        }

        /// <summary>
        /// إنشاء سياق قاعدة بيانات للاختبارات
        /// Create database context for tests
        /// </summary>
        public static FishFarmDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<FishFarmDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            return new FishFarmDbContext(options);
        }

        /// <summary>
        /// إنشاء بيانات اختبار أساسية
        /// Create basic test data
        /// </summary>
        public static async Task SeedTestDataAsync(FishFarmDbContext context)
        {
            // Create test user
            var testUser = new User
            {
                Username = "testuser",
                FullName = "Test User",
                Email = "<EMAIL>",
                Status = UserStatus.Active
            };
            testUser.SetPassword("Test123!");

            context.Users.Add(testUser);
            await context.SaveChangesAsync();
        }

        /// <summary>
        /// تنظيف بيانات الاختبار
        /// Clean up test data
        /// </summary>
        public static async Task CleanupTestDataAsync(FishFarmDbContext context)
        {
            context.Users.RemoveRange(context.Users);
            await context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// فئة أساسية للاختبارات مع إعداد مشترك
    /// Base test class with common setup
    /// </summary>
    public abstract class BaseTestClass : IDisposable
    {
        protected readonly ServiceProvider ServiceProvider;
        protected readonly FishFarmDbContext Context;
        protected readonly ILogger Logger;

        protected BaseTestClass()
        {
            ServiceProvider = TestConfiguration.CreateServiceProvider();
            Context = ServiceProvider.GetRequiredService<FishFarmDbContext>();
            Logger = ServiceProvider.GetRequiredService<ILogger<BaseTestClass>>();
        }

        protected async Task SeedTestDataAsync()
        {
            await TestConfiguration.SeedTestDataAsync(Context);
        }

        protected async Task CleanupTestDataAsync()
        {
            await TestConfiguration.CleanupTestDataAsync(Context);
        }

        public virtual void Dispose()
        {
            Context.Dispose();
            ServiceProvider.Dispose();
        }
    }
}
