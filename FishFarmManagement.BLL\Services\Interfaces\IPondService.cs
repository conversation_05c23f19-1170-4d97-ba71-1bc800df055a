using FishFarmManagement.Models;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة الأحواض
    /// Pond management service interface
    /// </summary>
    public interface IPondService : IBusinessService<Pond, int>
    {
        /// <summary>
        /// الحصول على أحواض دورة إنتاجية معينة
        /// Get ponds for specific production cycle
        /// </summary>
        Task<IEnumerable<Pond>> GetPondsByCycleAsync(int cycleId);

        /// <summary>
        /// الحصول على الأحواض النشطة
        /// Get active ponds
        /// </summary>
        Task<IEnumerable<Pond>> GetActivePondsAsync();

        /// <summary>
        /// إضافة استهلاك علف لحوض
        /// Add feed consumption to pond
        /// </summary>
        Task<bool> AddFeedConsumptionAsync(int pondId, FeedConsumption feedConsumption);

        /// <summary>
        /// تسجيل نفوق أسماك في حوض
        /// Record fish mortality in pond
        /// </summary>
        Task<bool> RecordFishMortalityAsync(int pondId, FishMortality mortality);

        /// <summary>
        /// إضافة دواء لحوض
        /// Add medication to pond
        /// </summary>
        Task<bool> AddMedicationAsync(int pondId, PondMedication medication);

        /// <summary>
        /// حساب إجمالي استهلاك العلف لحوض
        /// Calculate total feed consumption for pond
        /// </summary>
        Task<decimal> GetTotalFeedConsumptionAsync(int pondId);

        /// <summary>
        /// حساب إجمالي تكلفة العلف لحوض
        /// Calculate total feed cost for pond
        /// </summary>
        Task<decimal> GetTotalFeedCostAsync(int pondId);

        /// <summary>
        /// حساب معدل النفوق لحوض
        /// Calculate mortality rate for pond
        /// </summary>
        Task<decimal> GetMortalityRateAsync(int pondId);

        /// <summary>
        /// الحصول على تقرير حالة الحوض
        /// Get pond status report
        /// </summary>
        Task<PondStatusReport> GetPondStatusReportAsync(int pondId);

        /// <summary>
        /// التحقق من إمكانية الحصاد
        /// Check if pond is ready for harvest
        /// </summary>
        Task<bool> IsReadyForHarvestAsync(int pondId);

        /// <summary>
        /// توليد طلب شراء علف تلقائي
        /// Generate automatic feed purchase order
        /// </summary>
        Task<FeedPurchaseOrder> GenerateFeedPurchaseOrderAsync(int pondId);
    }

    /// <summary>
    /// تقرير حالة الحوض
    /// Pond status report
    /// </summary>
    public class PondStatusReport
    {
        public int PondId { get; set; }
        public string PondNumber { get; set; } = string.Empty;
        public int InitialFishCount { get; set; }
        public int CurrentFishCount { get; set; }
        public int DeadFishCount { get; set; }
        public decimal MortalityRate { get; set; }
        public decimal TotalFeedConsumption { get; set; }
        public decimal TotalFeedCost { get; set; }
        public decimal AverageWeight { get; set; }
        public decimal EstimatedTotalWeight { get; set; }
        public int DaysInProduction { get; set; }
        public DateTime? ExpectedHarvestDate { get; set; }
        public bool IsReadyForHarvest { get; set; }
        public List<string> Alerts { get; set; } = new List<string>();
    }

    /// <summary>
    /// طلب شراء العلف
    /// Feed purchase order
    /// </summary>
    public class FeedPurchaseOrder
    {
        public int PondId { get; set; }
        public string PondNumber { get; set; } = string.Empty;
        public int FeedTypeId { get; set; }
        public string FeedTypeName { get; set; } = string.Empty;
        public decimal RequiredQuantity { get; set; }
        public decimal EstimatedCost { get; set; }
        public DateTime RequiredDate { get; set; }
        public string Justification { get; set; } = string.Empty;
    }
}
