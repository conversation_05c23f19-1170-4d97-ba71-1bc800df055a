using System.Linq.Expressions;

namespace FishFarmManagement.DAL.Interfaces
{
    /// <summary>
    /// الواجهة العامة للمستودع
    /// Generic repository interface
    /// </summary>
    /// <typeparam name="T">نوع الكيان</typeparam>
    public interface IRepository<T> where T : class
    {
        /// <summary>
        /// الحصول على جميع الكيانات
        /// Get all entities
        /// </summary>
        Task<IEnumerable<T>> GetAllAsync();

        /// <summary>
        /// الحصول على كيان بالمعرف
        /// Get entity by ID
        /// </summary>
        Task<T?> GetByIdAsync(int id);

        /// <summary>
        /// البحث عن الكيانات بشرط
        /// Find entities with condition
        /// </summary>
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// الحصول على كيان واحد بشرط
        /// Get single entity with condition
        /// </summary>
        Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// إضافة كيان جديد
        /// Add new entity
        /// </summary>
        Task<T> AddAsync(T entity);

        /// <summary>
        /// إضافة مجموعة من الكيانات
        /// Add range of entities
        /// </summary>
        Task AddRangeAsync(IEnumerable<T> entities);

        /// <summary>
        /// تحديث كيان
        /// Update entity
        /// </summary>
        Task<T> UpdateAsync(T entity);

        /// <summary>
        /// حذف كيان
        /// Delete entity
        /// </summary>
        Task DeleteAsync(T entity);

        /// <summary>
        /// حذف كيان بالمعرف
        /// Delete entity by ID
        /// </summary>
        Task DeleteAsync(int id);

        /// <summary>
        /// حذف مجموعة من الكيانات
        /// Delete range of entities
        /// </summary>
        Task DeleteRangeAsync(IEnumerable<T> entities);

        /// <summary>
        /// عد الكيانات
        /// Count entities
        /// </summary>
        Task<int> CountAsync();

        /// <summary>
        /// عد الكيانات بشرط
        /// Count entities with condition
        /// </summary>
        Task<int> CountAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// التحقق من وجود كيان
        /// Check if entity exists
        /// </summary>
        Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// التحقق من وجود أي كيان يطابق الشرط
        /// Check if any entity matches the condition
        /// </summary>
        Task<bool> AnyAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// البحث عن مستخدم باسم المستخدم
        /// Find user by username
        /// </summary>
        Task<T?> FindByUsernameAsync(string username);

        /// <summary>
        /// الحصول على الكيانات مع التصفح
        /// Get entities with pagination
        /// </summary>
        Task<IEnumerable<T>> GetPagedAsync(int pageNumber, int pageSize);

        /// <summary>
        /// الحصول على الكيانات مع التصفح والشرط
        /// Get entities with pagination and condition
        /// </summary>
        Task<IEnumerable<T>> GetPagedAsync(int pageNumber, int pageSize, Expression<Func<T, bool>> predicate);

        /// <summary>
        /// الحصول على الكيانات مع الترتيب
        /// Get entities with ordering
        /// </summary>
        Task<IEnumerable<T>> GetOrderedAsync<TKey>(Expression<Func<T, TKey>> orderBy, bool ascending = true);

        /// <summary>
        /// الحصول على الكيانات مع الشرط والترتيب
        /// Get entities with condition and ordering
        /// </summary>
        Task<IEnumerable<T>> GetOrderedAsync<TKey>(Expression<Func<T, bool>> predicate, Expression<Func<T, TKey>> orderBy, bool ascending = true);
    }
}
