using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FishFarmManagement.Models
{
    /// <summary>
    /// أدوية الأحواض
    /// Pond medications
    /// </summary>
    public class PondMedication : BaseEntity
    {
        [Required(ErrorMessage = "معرف الحوض مطلوب")]
        public int PondId { get; set; }

        [Required(ErrorMessage = "معرف الدواء مطلوب")]
        public int MedicationId { get; set; }

        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0.1, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,3)")]
        public decimal Quantity { get; set; }

        [Required(ErrorMessage = "تاريخ التطبيق مطلوب")]
        public DateTime ApplicationDate { get; set; }

        [Required(ErrorMessage = "التكلفة مطلوبة")]
        [Range(0, double.MaxValue, ErrorMessage = "التكلفة يجب أن تكون أكبر من الصفر")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal Cost { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// سبب الاستخدام
        /// Reason for use
        /// </summary>
        [Required(ErrorMessage = "سبب الاستخدام مطلوب")]
        [StringLength(200, ErrorMessage = "سبب الاستخدام يجب أن يكون أقل من 200 حرف")]
        public string ReasonForUse { get; set; } = string.Empty;

        /// <summary>
        /// الطبيب البيطري المسؤول
        /// Responsible veterinarian
        /// </summary>
        [StringLength(100)]
        public string VeterinarianName { get; set; } = string.Empty;

        /// <summary>
        /// فترة الانتظار قبل الحصاد (بالأيام)
        /// Withdrawal period before harvest (in days)
        /// </summary>
        [Range(0, 365, ErrorMessage = "فترة الانتظار يجب أن تكون بين 0 و 365 يوم")]
        public int WithdrawalPeriodDays { get; set; }

        // Navigation Properties
        [ForeignKey("PondId")]
        public virtual Pond Pond { get; set; } = null!;

        [ForeignKey("MedicationId")]
        public virtual Medication Medication { get; set; } = null!;

        /// <summary>
        /// حساب التكلفة لكل وحدة
        /// Calculate cost per unit
        /// </summary>
        public decimal GetCostPerUnit()
        {
            return Quantity > 0 ? Cost / Quantity : 0;
        }

        /// <summary>
        /// حساب تاريخ انتهاء فترة الانتظار
        /// Calculate withdrawal period end date
        /// </summary>
        public DateTime GetWithdrawalEndDate()
        {
            return ApplicationDate.AddDays(WithdrawalPeriodDays);
        }

        /// <summary>
        /// التحقق من انتهاء فترة الانتظار
        /// Check if withdrawal period has ended
        /// </summary>
        public bool IsWithdrawalPeriodEnded()
        {
            return DateTime.Now >= GetWithdrawalEndDate();
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data consistency
        /// </summary>
        public bool IsDataConsistent()
        {
            if (Medication != null)
            {
                var expectedCost = Quantity * Medication.PricePerUnit;
                var tolerance = expectedCost * 0.05m; // هامش خطأ 5%
                return Math.Abs(Cost - expectedCost) <= tolerance;
            }
            return true;
        }
    }
}
