using Microsoft.Extensions.Logging;
using FishFarmManagement.BLL.Services.Interfaces;
using FishFarmManagement.DAL.Interfaces;
using FishFarmManagement.Models;


namespace FishFarmManagement.BLL.Services
{
    /// <summary>
    /// خدمة المحاسبة
    /// Accounting service
    /// </summary>
    public class AccountingService : IAccountingService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AccountingService> _logger;

        public AccountingService(IUnitOfWork unitOfWork, ILogger<AccountingService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<Transaction> CreateTransactionAsync(TransactionRequest request)
        {
            try
            {
                // Validate request
                if (request.Details.Count == 0)
                    throw new ArgumentException("يجب أن يحتوي القيد على تفاصيل");

                var totalDebits = request.Details.Sum(d => d.DebitAmount);
                var totalCredits = request.Details.Sum(d => d.CreditAmount);

                if (Math.Abs(totalDebits - totalCredits) > 0.01m)
                    throw new ArgumentException("مجموع المدين يجب أن يساوي مجموع الدائن");

                // Create transaction
                var transaction = new Transaction
                {
                    CycleId = request.CycleId,
                    ReferenceNumber = await GenerateTransactionNumberAsync(),
                    TransactionDate = request.TransactionDate,
                    TransactionType = request.TransactionType,
                    Description = request.Description,
                    TotalAmount = totalDebits,
                    Status = TransactionStatus.Draft
                };

                await _unitOfWork.Transactions.AddAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                // Create transaction details
                foreach (var detail in request.Details)
                {
                    var transactionDetail = new TransactionDetail
                    {
                        TransactionId = transaction.Id,
                        AccountId = detail.AccountId,
                        DebitAmount = detail.DebitAmount,
                        CreditAmount = detail.CreditAmount,
                        Description = detail.Description,
                        CostCenterId = detail.CostCenterId
                    };

                    await _unitOfWork.TransactionDetails.AddAsync(transactionDetail);
                }

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء القيد المحاسبي {ReferenceNumber}", transaction.ReferenceNumber);
                return transaction;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء القيد المحاسبي");
                throw;
            }
        }

        public async Task<bool> PostTransactionAsync(int transactionId)
        {
            try
            {
                var transaction = await _unitOfWork.Transactions.GetByIdAsync(transactionId);
                if (transaction == null)
                    throw new InvalidOperationException("القيد المحاسبي غير موجود");

                if (transaction.Status != TransactionStatus.Draft)
                    throw new InvalidOperationException("لا يمكن ترحيل القيد إلا إذا كان في حالة مسودة");

                // Get transaction details
                var details = await _unitOfWork.TransactionDetails.FindAsync(td => td.TransactionId == transactionId);

                // Update account balances
                foreach (var detail in details)
                {
                    var account = await _unitOfWork.Accounts.GetByIdAsync(detail.AccountId);
                    if (account != null)
                    {
                        account.UpdateBalance(detail.DebitAmount, detail.CreditAmount);
                        await _unitOfWork.Accounts.UpdateAsync(account);
                    }
                }

                // Update transaction status
                transaction.Status = TransactionStatus.Cancelled;
                transaction.UpdateModificationDate();
                await _unitOfWork.Transactions.UpdateAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم ترحيل القيد المحاسبي {ReferenceNumber}", transaction.ReferenceNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في ترحيل القيد المحاسبي {TransactionId}", transactionId);
                throw;
            }
        }

        public async Task<bool> CancelTransactionAsync(int transactionId)
        {
            try
            {
                var transaction = await _unitOfWork.Transactions.GetByIdAsync(transactionId);
                if (transaction == null)
                    throw new InvalidOperationException("القيد المحاسبي غير موجود");

                if (transaction.Status == TransactionStatus.Cancelled)
                    throw new InvalidOperationException("القيد محاسبي ملغي بالفعل");

                // If posted, reverse the account balances
                if (transaction.Status == TransactionStatus.Posted)
                {
                    var details = await _unitOfWork.TransactionDetails.FindAsync(td => td.TransactionId == transactionId);
                    foreach (var detail in details)
                    {
                        var account = await _unitOfWork.Accounts.GetByIdAsync(detail.AccountId);
                        if (account != null)
                        {
                            // Reverse the balance update
                            account.UpdateBalance(detail.CreditAmount, detail.DebitAmount);
                            await _unitOfWork.Accounts.UpdateAsync(account);
                        }
                    }
                }

                // Update transaction status
                transaction.Status = TransactionStatus.Cancelled;
                transaction.UpdateModificationDate();
                await _unitOfWork.Transactions.UpdateAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم إلغاء القيد المحاسبي {ReferenceNumber}", transaction.ReferenceNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إلغاء القيد المحاسبي {TransactionId}", transactionId);
                throw;
            }
        }

        public async Task<TrialBalance> GetTrialBalanceAsync(DateTime? asOfDate = null)
        {
            try
            {
                var date = asOfDate ?? DateTime.Now;
                var accounts = await _unitOfWork.Accounts.GetAllAsync();

                var trialBalance = new TrialBalance
                {
                    AsOfDate = date
                };

                foreach (var account in accounts.Where(a => a.IsPostable))
                {
                    var item = new TrialBalanceItem
                    {
                        AccountCode = account.AccountCode,
                        AccountName = account.AccountName
                    };

                    if (account.Balance >= 0)
                    {
                        if (account.AccountType.Nature == "مدين")
                            item.DebitBalance = account.Balance;
                        else
                            item.CreditBalance = account.Balance;
                    }
                    else
                    {
                        if (account.AccountType.Nature == "مدين")
                            item.CreditBalance = Math.Abs(account.Balance);
                        else
                            item.DebitBalance = Math.Abs(account.Balance);
                    }

                    trialBalance.Items.Add(item);
                }

                trialBalance.TotalDebits = trialBalance.Items.Sum(i => i.DebitBalance);
                trialBalance.TotalCredits = trialBalance.Items.Sum(i => i.CreditBalance);

                return trialBalance;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء ميزان المراجعة");
                throw;
            }
        }

        public async Task<IncomeStatement> GetIncomeStatementAsync(DateTime fromDate, DateTime toDate, int? cycleId = null)
        {
            try
            {
                var transactions = await _unitOfWork.Transactions.GetPostedTransactionsInDateRangeAsync(fromDate, toDate, cycleId);
                var transactionIds = transactions.Select(t => t.Id).ToList();
                var details = await _unitOfWork.TransactionDetails.GetByTransactionIdsAsync(transactionIds);

                var revenues = details.Where(d => d.Account.AccountType.Name == "إيرادات").Sum(d => d.CreditAmount - d.DebitAmount);
                var expenses = details.Where(d => d.Account.AccountType.Name == "مصروفات").Sum(d => d.DebitAmount - d.CreditAmount);

                var incomeStatement = new IncomeStatement
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    CycleId = cycleId,
                    TotalRevenue = revenues,
                    TotalExpense = expenses,
                    NetIncome = revenues - expenses
                };

                return incomeStatement;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء قائمة الدخل");
                throw;
            }
        }

        public async Task<BalanceSheet> GetBalanceSheetAsync(DateTime asOfDate)
        {
            try
            {
                var accounts = await _unitOfWork.Accounts.GetAllAsync();
                var transactions = await _unitOfWork.Transactions.GetPostedTransactionsInDateRangeAsync(DateTime.MinValue, asOfDate);
                var transactionIds = transactions.Select(t => t.Id).ToList();
                var details = await _unitOfWork.TransactionDetails.GetByTransactionIdsAsync(transactionIds);

                var assets = new List<BalanceSheetItem>();
                var liabilities = new List<BalanceSheetItem>();
                var equity = new List<BalanceSheetItem>();

                foreach (var account in accounts)
                {
                    var balance = details.Where(d => d.AccountId == account.Id).Sum(d => d.DebitAmount - d.CreditAmount);
                    if (account.AccountType.Nature == "دائن") balance *= -1;

                    var item = new BalanceSheetItem { AccountName = account.AccountName, Balance = balance };

                    if (account.AccountType.Name == "أصول") assets.Add(item);
                    else if (account.AccountType.Name == "خصوم") liabilities.Add(item);
                    else if (account.AccountType.Name == "حقوق ملكية") equity.Add(item);
                }

                var balanceSheet = new BalanceSheet
                {
                    AsOfDate = asOfDate
                };

                // TODO: Implement actual calculation logic
                return balanceSheet;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الميزانية العمومية");
                throw;
            }
        }

        public async Task<CycleProfitability> GetCycleProfitabilityAsync(int cycleId)
        {
            try
            {
                var cycle = await _unitOfWork.ProductionCycles.GetByIdAsync(cycleId);
                if (cycle == null)
                    throw new InvalidOperationException("الدورة الإنتاجية غير موجودة");

                var incomeStatement = await GetIncomeStatementAsync(cycle.StartDate, cycle.EndDate ?? DateTime.MaxValue, cycleId);

                var profitability = new CycleProfitability
                {
                    CycleId = cycleId,
                    CycleName = cycle.CycleName,
                    StartDate = cycle.StartDate,
                    EndDate = cycle.EndDate,
                    TotalRevenue = incomeStatement.TotalRevenue,
                    TotalExpense = incomeStatement.TotalExpense,
                    NetProfit = incomeStatement.NetIncome
                };

                return profitability;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب ربحية الدورة {CycleId}", cycleId);
                throw;
            }
        }

        public async Task<IEnumerable<CycleProfitability>> CompareCyclesProfitabilityAsync(IEnumerable<int> cycleIds)
        {
            var results = new List<CycleProfitability>();
            foreach (var cycleId in cycleIds)
            {
                var profitability = await GetCycleProfitabilityAsync(cycleId);
                results.Add(profitability);
            }
            return results;
        }

        public async Task<CostCenterAnalysis> GetCostCenterAnalysisAsync(int costCenterId)
        {
            try
            {
                var costCenter = await _unitOfWork.CostCenters.GetByIdAsync(costCenterId);
                if (costCenter == null)
                    throw new InvalidOperationException("مركز التكلفة غير موجود");

                var details = await _unitOfWork.TransactionDetails.GetByCostCenterAsync(costCenterId);
                var totalActualCost = details.Sum(d => d.DebitAmount - d.CreditAmount);

                var analysis = new CostCenterAnalysis
                {
                    CostCenterId = costCenterId,
                    CenterName = costCenter.CenterName,
                    AllocatedBudget = costCenter.AllocatedBudget,
                    ActualCost = totalActualCost,
                    Variance = costCenter.AllocatedBudget - totalActualCost,
                    Transactions = details.Select(d => d.Transaction).Distinct()
                };

                return analysis;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحليل مركز التكلفة {CostCenterId}", costCenterId);
                throw;
            }
        }

        public async Task<Transaction> CreatePayrollTransactionAsync(int payrollId)
        {
            try
            {
                var payroll = await _unitOfWork.Payrolls.GetByIdAsync(payrollId);
                if (payroll == null)
                    throw new InvalidOperationException("الراتب غير موجود");

                var employee = await _unitOfWork.Employees.GetByIdAsync(payroll.EmployeeId);
                if (employee == null)
                    throw new InvalidOperationException("الموظف غير موجود");

                // Get accounts by code instead of hardcoded ID
                var salaryExpenseAccount = await GetAccountByCodeAsync("SALARY_EXP");
                var cashAccount = await GetAccountByCodeAsync("CASH");

                // Create payroll transaction
                var payrollDate = new DateTime(payroll.Year, payroll.Month, 1);
                var request = new TransactionRequest
                {
                    CycleId = 1, // Default cycle
                    TransactionType = "راتب",
                    TransactionDate = payrollDate,
                    Description = $"راتب {employee.FullName} - {payroll.GetMonthName()} {payroll.Year}",
                    Details = new List<TransactionDetailRequest>
                    {
                        new TransactionDetailRequest
                        {
                            AccountId = salaryExpenseAccount.Id,
                            DebitAmount = payroll.NetSalary,
                            CreditAmount = 0,
                            Description = $"راتب {employee.FullName}"
                        },
                        new TransactionDetailRequest
                        {
                            AccountId = cashAccount.Id,
                            DebitAmount = 0,
                            CreditAmount = payroll.NetSalary,
                            Description = $"دفع راتب {employee.FullName}"
                        }
                    }
                };

                return await CreateTransactionAsync(request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء قيد الراتب {PayrollId}", payrollId);
                throw;
            }
        }

        public async Task<Transaction> CreateFeedPurchaseTransactionAsync(FeedPurchaseTransaction request)
        {
            try
            {
                // Get accounts by code
                var medExpenseAccount = await GetAccountByCodeAsync("MED_EXP");
                var cashAccount = await GetAccountByCodeAsync("CASH");

                var transactionRequest = new TransactionRequest
                {
                    CycleId = request.CycleId,
                    TransactionType = "شراء علف",
                    TransactionDate = request.PurchaseDate,
                    Description = $"شراء علف - فاتورة {request.InvoiceNumber}",
                    Details = new List<TransactionDetailRequest>
                    {
                        new TransactionDetailRequest
                        {
                            AccountId = feedExpenseAccount.Id,
                            DebitAmount = request.TotalAmount,
                            CreditAmount = 0,
                            Description = $"شراء علف من {request.SupplierName}"
                        },
                        new TransactionDetailRequest
                        {
                            AccountId = paymentAccount.Id,
                            DebitAmount = 0,
                            CreditAmount = request.TotalAmount,
                            Description = $"دفع لـ {request.SupplierName}"
                        }
                    }
                };

                return await CreateTransactionAsync(transactionRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء قيد شراء العلف");
                throw;
            }
        }

        public async Task<Transaction> CreateMedicationPurchaseTransactionAsync(MedicationPurchaseTransaction request)
        {
            try
            {
                // Get accounts by code
                var feedExpenseAccount = await GetAccountByCodeAsync("FEED_EXP");
                var paymentAccount = await GetAccountByCodeAsync(request.IsCashPayment ? "CASH" : "SUPPLIERS");

                var transactionRequest = new TransactionRequest
                {
                    CycleId = request.CycleId,
                    TransactionType = "شراء دواء",
                    TransactionDate = request.PurchaseDate,
                    Description = $"شراء دواء - فاتورة {request.InvoiceNumber}",
                    Details = new List<TransactionDetailRequest>
                    {
                        new TransactionDetailRequest
                        {
                            AccountId = medExpenseAccount.Id,
                            DebitAmount = request.TotalAmount,
                            CreditAmount = 0,
                            Description = $"شراء دواء من {request.SupplierName}"
                        },
                        new TransactionDetailRequest
                        {
                            AccountId = cashAccount.Id,
                            DebitAmount = 0,
                            CreditAmount = request.TotalAmount,
                            Description = $"دفع لـ {request.SupplierName}"
                        }
                    }
                };

                return await CreateTransactionAsync(transactionRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء قيد شراء الدواء");
                throw;
            }
        }

        public async Task<bool> CheckAccountsBalanceAsync()
        {
            try
            {
                var trialBalance = await GetTrialBalanceAsync();
                return trialBalance.IsBalanced;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من توازن الحسابات");
                throw;
            }
        }

        public Task<bool> CloseAccountingPeriodAsync(int cycleId)
        {
            try
            {
                // This is a simplified implementation
                // In a real application, you would perform period closing procedures
                _logger.LogInformation("تم إقفال الدورة المحاسبية {CycleId}", cycleId);
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إقفال الدورة المحاسبية {CycleId}", cycleId);
                throw;
            }
        }

        private async Task<string> GenerateTransactionNumberAsync()
        {
            var lastTransaction = await _unitOfWork.Transactions
                .FindAsync(t => t.ReferenceNumber.StartsWith("JE"));

            var lastNumber = lastTransaction
                .OrderByDescending(t => t.ReferenceNumber)
                .FirstOrDefault()?.ReferenceNumber;

            if (string.IsNullOrEmpty(lastNumber))
                return "JE001";

            var numberPart = lastNumber.Substring(2);
            if (int.TryParse(numberPart, out int number))
            {
                return $"JE{(number + 1):D3}";
            }

            return "JE001";
        }

        private async Task<Account> GetAccountByCodeAsync(string code)
        {
            var account = (await _unitOfWork.Accounts.FindAsync(a => a.AccountCode == code)).FirstOrDefault();
            if (account == null)
                throw new InvalidOperationException($"الحساب ذو الكود '{code}' غير موجود. يرجى التأكد من إعداده في دليل الحسابات.");
            
            return account;
        }
    }
}
