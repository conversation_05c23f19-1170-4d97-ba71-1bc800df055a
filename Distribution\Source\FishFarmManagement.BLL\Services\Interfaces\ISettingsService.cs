using FishFarmManagement.Models.Configuration;

namespace FishFarmManagement.BLL.Services.Interfaces
{
    /// <summary>
    /// خدمة إدارة الإعدادات
    /// Settings management service interface
    /// </summary>
    public interface ISettingsService
    {
        /// <summary>
        /// الحصول على إعدادات التطبيق
        /// Get application settings
        /// </summary>
        ApplicationSettings GetApplicationSettings();

        /// <summary>
        /// الحصول على إعدادات قاعدة البيانات
        /// Get database settings
        /// </summary>
        DatabaseSettings GetDatabaseSettings();

        /// <summary>
        /// الحصول على إعدادات الأمان
        /// Get security settings
        /// </summary>
        SecuritySettings GetSecuritySettings();

        /// <summary>
        /// الحصول على إعدادات التقارير
        /// Get reports settings
        /// </summary>
        ReportsSettings GetReportsSettings();

        /// <summary>
        /// الحصول على إعدادات الإشعارات
        /// Get notifications settings
        /// </summary>
        NotificationsSettings GetNotificationsSettings();

        /// <summary>
        /// الحصول على إعدادات التسجيل
        /// Get logging settings
        /// </summary>
        LoggingSettings GetLoggingSettings();

        /// <summary>
        /// تحديث إعدادات التطبيق
        /// Update application settings
        /// </summary>
        Task<bool> UpdateApplicationSettingsAsync(ApplicationSettings settings);

        /// <summary>
        /// تحديث إعدادات الأمان
        /// Update security settings
        /// </summary>
        Task<bool> UpdateSecuritySettingsAsync(SecuritySettings settings);

        /// <summary>
        /// تحديث إعدادات التقارير
        /// Update reports settings
        /// </summary>
        Task<bool> UpdateReportsSettingsAsync(ReportsSettings settings);

        /// <summary>
        /// تحديث إعدادات الإشعارات
        /// Update notifications settings
        /// </summary>
        Task<bool> UpdateNotificationsSettingsAsync(NotificationsSettings settings);

        /// <summary>
        /// إعادة تحميل الإعدادات من الملف
        /// Reload settings from file
        /// </summary>
        Task ReloadSettingsAsync();

        /// <summary>
        /// التحقق من صحة الإعدادات
        /// Validate settings
        /// </summary>
        SettingsValidationResult ValidateSettings();
    }

    /// <summary>
    /// نتيجة التحقق من صحة الإعدادات
    /// Settings validation result
    /// </summary>
    public class SettingsValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }
}
