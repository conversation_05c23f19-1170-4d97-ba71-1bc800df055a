# نظام إدارة مزرعة الأسماك - النسخة المبسطة
# Fish Farm Management System - Simplified Version

## معلومات النسخة | Version Information
- الإصدار: 1.0.1 مبسط
- تاريخ الإنشاء: 29 يوليو 2025
- النوع: نسخة محمولة (Portable)

## متطلبات التشغيل | System Requirements
- نظام التشغيل: Windows 10/11 (64-bit)
- .NET 8.0 Runtime (مضمن في الحزمة)
- ذاكرة الوصول العشوائي: 4 جيجابايت على الأقل
- مساحة القرص الصلب: 500 ميجابايت

## طريقة التشغيل | How to Run
1. فك ضغط الملف في أي مجلد
2. انقر مرتين على FishFarmManagement.exe
3. سيتم إنشاء قاعدة بيانات تلقائياً عند التشغيل الأول
4. استخدم بيانات الدخول الافتراضية:
   - اسم المستخدم: admin
   - كلمة المرور: Admin123!

## الوظائف المتاحة | Available Features
✓ إدارة الأحواض والمزارع
✓ تسجيل دورات الإنتاج
✓ إدارة الأعلاف والأدوية
✓ تتبع نفوق الأسماك
✓ إدارة المخزون
✓ إدارة الموظفين
✓ التقارير الأساسية
✓ النسخ الاحتياطي والاستعادة

## الوظائف المحذوفة في هذه النسخة | Removed Features
✗ النظام المحاسبي المتقدم
✗ مراقبة الأداء التفصيلية
✗ نظام التراخيص المعقد
✗ التحديثات التلقائية
✗ التكامل مع الأنظمة الخارجية

## ملفات النظام | System Files
- FishFarmManagement.exe: الملف التنفيذي الرئيسي
- FishFarmDatabase.db: قاعدة البيانات المحلية
- appsettings.json: ملف الإعدادات
- Logs/: مجلد ملفات السجل
- Backups/: مجلد النسخ الاحتياطية
- Reports/: مجلد التقارير المُصدرة

## استكشاف الأخطاء | Troubleshooting
1. إذا لم يعمل البرنامج:
   - تأكد من وجود .NET 8.0 Runtime
   - تشغيل البرنامج كمدير (Run as Administrator)
   - فحص ملفات السجل في مجلد Logs

2. مشاكل قاعدة البيانات:
   - احذف ملف FishFarmDatabase.db وأعد تشغيل البرنامج
   - سيتم إنشاء قاعدة بيانات جديدة تلقائياً

3. مشاكل الأداء:
   - أغلق البرامج الأخرى غير الضرورية
   - تأكد من توفر مساحة كافية على القرص الصلب

## الدعم الفني | Technical Support
- البريد الإلكتروني: <EMAIL>
- الموقع: github.com/fishfarm-management
- التوثيق: راجع ملفات Documentation

## الترخيص | License
هذا البرنامج مجاني للاستخدام الشخصي والتجاري.
للحصول على الدعم الفني المتقدم، يرجى التواصل معنا.

## إخلاء المسؤولية | Disclaimer
يُستخدم هذا البرنامج على مسؤولية المستخدم.
المطور غير مسؤول عن أي أضرار قد تنتج عن استخدام البرنامج.

---
© 2025 طارق حسين صالح - جميع الحقوق محفوظة
© 2025 Tarek Hussein Saleh - All Rights Reserved
